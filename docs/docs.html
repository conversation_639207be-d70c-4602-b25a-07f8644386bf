<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation - WebOTR</title>
    <meta name="description" content="Complete documentation for WebOTR - End-to-end encryption for web chat platforms.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/docs.css">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="assets/logo.svg" alt="WebOTR" class="nav-logo">
                <span class="nav-title">WebOTR</span>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="#installation" class="nav-link">Installation</a>
                <a href="#usage" class="nav-link">Usage</a>
                <a href="#api" class="nav-link">API</a>
                <a href="#security" class="nav-link">Security</a>
                <a href="https://github.com/forkrul/webOTteR" class="nav-link" target="_blank">
                    <i class="fab fa-github"></i> GitHub
                </a>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Documentation Content -->
    <div class="docs-container">
        <!-- Sidebar -->
        <aside class="docs-sidebar">
            <div class="sidebar-content">
                <h3>Getting Started</h3>
                <ul>
                    <li><a href="#installation">Installation</a></li>
                    <li><a href="#quick-start">Quick Start</a></li>
                    <li><a href="#configuration">Configuration</a></li>
                </ul>
                
                <h3>Usage</h3>
                <ul>
                    <li><a href="#basic-usage">Basic Usage</a></li>
                    <li><a href="#advanced-features">Advanced Features</a></li>
                    <li><a href="#troubleshooting">Troubleshooting</a></li>
                </ul>
                
                <h3>API Reference</h3>
                <ul>
                    <li><a href="#core-api">Core API</a></li>
                    <li><a href="#protocol-api">Protocol API</a></li>
                    <li><a href="#platform-api">Platform API</a></li>
                </ul>
                
                <h3>Security</h3>
                <ul>
                    <li><a href="#security-model">Security Model</a></li>
                    <li><a href="#threat-analysis">Threat Analysis</a></li>
                    <li><a href="#best-practices">Best Practices</a></li>
                </ul>
                
                <h3>Development</h3>
                <ul>
                    <li><a href="#contributing">Contributing</a></li>
                    <li><a href="#building">Building</a></li>
                    <li><a href="#testing">Testing</a></li>
                </ul>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="docs-main">
            <div class="docs-content">
                <header class="docs-header">
                    <h1>WebOTR Documentation</h1>
                    <p>Complete guide to using WebOTR for secure messaging on web chat platforms.</p>
                </header>

                <section id="installation" class="docs-section">
                    <h2>Installation</h2>
                    <p>WebOTR can be installed as a browser extension or used as a JavaScript library.</p>
                    
                    <h3>Browser Extension</h3>
                    <div class="code-block">
                        <div class="code-header">
                            <span>Installation Steps</span>
                            <button class="copy-btn" onclick="copyToClipboard('extension-install')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <pre id="extension-install"><code>1. Download the extension from the Chrome Web Store
2. Click "Add to Chrome" or "Add to Firefox"
3. Navigate to a supported chat platform
4. Click the WebOTR icon to enable encryption</code></pre>
                    </div>
                    
                    <h3>Development Setup</h3>
                    <div class="code-block">
                        <div class="code-header">
                            <span>Terminal</span>
                            <button class="copy-btn" onclick="copyToClipboard('dev-setup')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <pre id="dev-setup"><code># Clone the repository
git clone https://github.com/forkrul/webOTteR.git
cd webOTteR

# Install dependencies
npm install

# Start development server
npm start</code></pre>
                    </div>
                </section>

                <section id="quick-start" class="docs-section">
                    <h2>Quick Start</h2>
                    <p>Get up and running with WebOTR in minutes.</p>
                    
                    <div class="step-guide">
                        <div class="step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h3>Install Extension</h3>
                                <p>Add WebOTR to your browser from the extension store.</p>
                            </div>
                        </div>
                        
                        <div class="step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h3>Open Chat Platform</h3>
                                <p>Navigate to Teams, Discord, Slack, or another supported platform.</p>
                            </div>
                        </div>
                        
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h3>Start Secure Session</h3>
                                <p>Click the WebOTR button to initiate encrypted messaging.</p>
                            </div>
                        </div>
                        
                        <div class="step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h3>Verify Identity</h3>
                                <p>Use SMP to verify your conversation partner's identity.</p>
                            </div>
                        </div>
                    </div>
                </section>

                <section id="basic-usage" class="docs-section">
                    <h2>Basic Usage</h2>
                    <p>Learn the fundamentals of using WebOTR for secure messaging.</p>
                    
                    <h3>Starting an OTR Session</h3>
                    <div class="code-block">
                        <div class="code-header">
                            <span>JavaScript</span>
                            <button class="copy-btn" onclick="copyToClipboard('start-session')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <pre id="start-session"><code>// Initialize WebOTR
const otr = new WebOTR({
    platform: 'teams', // or 'discord', 'slack'
    debug: false
});

// Start OTR session
otr.startSession()
    .then(() => {
        console.log('OTR session started');
    })
    .catch(error => {
        console.error('Failed to start session:', error);
    });</code></pre>
                    </div>
                    
                    <h3>Sending Encrypted Messages</h3>
                    <div class="code-block">
                        <div class="code-header">
                            <span>JavaScript</span>
                            <button class="copy-btn" onclick="copyToClipboard('send-message')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <pre id="send-message"><code>// Send encrypted message
otr.sendMessage('Hello, this message is encrypted!')
    .then(() => {
        console.log('Message sent securely');
    })
    .catch(error => {
        console.error('Failed to send message:', error);
    });</code></pre>
                    </div>
                </section>

                <section id="security-model" class="docs-section">
                    <h2>Security Model</h2>
                    <p>Understanding WebOTR's security guarantees and limitations.</p>
                    
                    <div class="security-features">
                        <div class="security-feature">
                            <div class="security-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="security-content">
                                <h3>End-to-End Encryption</h3>
                                <p>Messages are encrypted on your device and can only be decrypted by the intended recipient using AES-256.</p>
                            </div>
                        </div>
                        
                        <div class="security-feature">
                            <div class="security-icon">
                                <i class="fas fa-key"></i>
                            </div>
                            <div class="security-content">
                                <h3>Perfect Forward Secrecy</h3>
                                <p>Encryption keys are rotated regularly, ensuring that compromised keys cannot decrypt past messages.</p>
                            </div>
                        </div>
                        
                        <div class="security-feature">
                            <div class="security-icon">
                                <i class="fas fa-user-secret"></i>
                            </div>
                            <div class="security-content">
                                <h3>Deniable Authentication</h3>
                                <p>Messages are authenticated during conversation but cannot be proven authentic to third parties.</p>
                            </div>
                        </div>
                    </div>
                </section>

                <section id="api" class="docs-section">
                    <h2>API Reference</h2>
                    <p>Complete API documentation for WebOTR.</p>
                    
                    <h3>Core Classes</h3>
                    <div class="api-section">
                        <h4>WebOTR</h4>
                        <p>Main class for WebOTR functionality.</p>
                        
                        <div class="method">
                            <h5><code>constructor(options)</code></h5>
                            <p>Creates a new WebOTR instance.</p>
                            <div class="parameters">
                                <h6>Parameters:</h6>
                                <ul>
                                    <li><code>options.platform</code> - Target platform ('teams', 'discord', 'slack')</li>
                                    <li><code>options.debug</code> - Enable debug logging (boolean)</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="method">
                            <h5><code>startSession()</code></h5>
                            <p>Initiates an OTR session with the current conversation partner.</p>
                            <div class="returns">
                                <h6>Returns:</h6>
                                <p><code>Promise&lt;void&gt;</code> - Resolves when session is established</p>
                            </div>
                        </div>
                        
                        <div class="method">
                            <h5><code>sendMessage(message)</code></h5>
                            <p>Sends an encrypted message through the OTR session.</p>
                            <div class="parameters">
                                <h6>Parameters:</h6>
                                <ul>
                                    <li><code>message</code> - The message to encrypt and send (string)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                <section id="contributing" class="docs-section">
                    <h2>Contributing</h2>
                    <p>Help improve WebOTR by contributing to the project.</p>
                    
                    <h3>Development Environment</h3>
                    <div class="code-block">
                        <div class="code-header">
                            <span>Terminal</span>
                            <button class="copy-btn" onclick="copyToClipboard('dev-env')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <pre id="dev-env"><code># Fork and clone the repository
git clone https://github.com/yourusername/webOTteR.git
cd webOTteR

# Install dependencies
npm install

# Run tests
npm test

# Start development server
npm run dev</code></pre>
                    </div>
                    
                    <h3>Submitting Changes</h3>
                    <ol>
                        <li>Fork the repository on GitHub</li>
                        <li>Create a feature branch: <code>git checkout -b feature-name</code></li>
                        <li>Make your changes and add tests</li>
                        <li>Run the test suite: <code>npm test</code></li>
                        <li>Commit your changes: <code>git commit -am 'Add feature'</code></li>
                        <li>Push to the branch: <code>git push origin feature-name</code></li>
                        <li>Submit a pull request</li>
                    </ol>
                </section>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="assets/script.js"></script>
    <script src="assets/docs.js"></script>
</body>
</html>
