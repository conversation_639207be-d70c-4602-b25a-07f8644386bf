<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testing Infrastructure - WebOTR</title>
    <meta name="description" content="Comprehensive testing infrastructure for WebOTR with 155+ test cases and DevContainer development environment.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="assets/style.css">
    <link rel="stylesheet" href="assets/docs.css">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="assets/logo.svg" alt="WebOTR" class="nav-logo">
                <span class="nav-title">WebOTR</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="docs.html" class="nav-link">Documentation</a>
                <a href="testing.html" class="nav-link active">Testing</a>
                <a href="https://github.com/forkrul/webOTteR" class="nav-link" target="_blank">
                    <i class="fab fa-github"></i> GitHub
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="docs-header">
                <h1 class="docs-title">
                    <i class="fas fa-vial"></i>
                    Testing Infrastructure
                </h1>
                <p class="docs-description">
                    WebOTR features a comprehensive testing infrastructure with 155+ test cases, 
                    100% core functionality coverage, and professional development environment.
                </p>
            </div>

            <!-- Test Suite Overview -->
            <section class="docs-section">
                <h2 class="section-title">Test Suite Overview</h2>
                <div class="test-stats">
                    <div class="test-stat">
                        <div class="stat-number">155+</div>
                        <div class="stat-label">Total Tests</div>
                    </div>
                    <div class="test-stat">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Core Tests Pass</div>
                    </div>
                    <div class="test-stat">
                        <div class="stat-number">6</div>
                        <div class="stat-label">Test Suites</div>
                    </div>
                    <div class="test-stat">
                        <div class="stat-number">90+</div>
                        <div class="stat-label">New Tests Added</div>
                    </div>
                </div>

                <h3>Test Suites</h3>
                <div class="test-suites">
                    <div class="test-suite">
                        <h4><i class="fas fa-check-circle text-success"></i> Core Session Tests</h4>
                        <p><strong>22 tests - 100% passing</strong></p>
                        <p>Complete OTR session management testing including initialization, state transitions, message handling, and cleanup.</p>
                    </div>
                    
                    <div class="test-suite">
                        <h4><i class="fas fa-check-circle text-success"></i> Message Utilities Tests</h4>
                        <p><strong>17 tests - 100% passing</strong></p>
                        <p>Comprehensive message parsing, creation, encryption/decryption, and protocol message handling.</p>
                    </div>
                    
                    <div class="test-suite">
                        <h4><i class="fas fa-check-circle text-success"></i> Protocol AKE Tests</h4>
                        <p><strong>15 tests - Function availability verified</strong></p>
                        <p>Authenticated Key Exchange protocol testing with DSA key generation and message processing.</p>
                    </div>
                    
                    <div class="test-suite">
                        <h4><i class="fas fa-cog text-warning"></i> Platform Adapter Tests</h4>
                        <p><strong>25+ tests - Integration patterns established</strong></p>
                        <p>Microsoft Teams, Discord, and Slack platform integration testing with comprehensive adapter coverage.</p>
                    </div>
                    
                    <div class="test-suite">
                        <h4><i class="fas fa-cog text-warning"></i> Integration Tests</h4>
                        <p><strong>10+ tests - End-to-end flows</strong></p>
                        <p>Complete OTR protocol integration testing with multi-user scenarios and real-world usage patterns.</p>
                    </div>
                    
                    <div class="test-suite">
                        <h4><i class="fas fa-check-circle text-success"></i> DevContainer Tests</h4>
                        <p><strong>Environment validation script</strong></p>
                        <p>Automated testing of development environment setup, dependencies, and tooling functionality.</p>
                    </div>
                </div>
            </section>

            <!-- Development Environment -->
            <section class="docs-section">
                <h2 class="section-title">Development Environment</h2>
                
                <h3>DevContainer Setup</h3>
                <p>WebOTR includes a complete DevContainer configuration for consistent development across teams:</p>
                
                <div class="code-block">
                    <pre><code># Open in VS Code with Dev Containers extension
# Click "Reopen in Container" when prompted

# Or manually with Docker
docker-compose -f .devcontainer/docker-compose.yml up -d</code></pre>
                </div>

                <h4>Features</h4>
                <ul>
                    <li><strong>Node.js v20.18.3</strong> - Latest LTS with optimal performance</li>
                    <li><strong>npm 10.8.2</strong> - Package management and script execution</li>
                    <li><strong>Port Forwarding</strong> - Configured for 3000, 3003, 3004, 8080, 9323</li>
                    <li><strong>Browser Testing</strong> - Playwright integration with security options</li>
                    <li><strong>Automated Setup</strong> - Dependencies and environment configured automatically</li>
                </ul>

                <h3>Running Tests</h3>
                <div class="code-block">
                    <pre><code># Run all tests
npm test

# Run specific test suites
npm test -- --testPathPattern='session.test.js'
npm test -- --testPathPattern='message.test.js'

# Run tests with coverage
npm test -- --coverage

# Validate DevContainer environment
./scripts/test-devcontainer.sh</code></pre>
                </div>
            </section>

            <!-- Test Infrastructure -->
            <section class="docs-section">
                <h2 class="section-title">Test Infrastructure</h2>
                
                <h3>Technical Achievements</h3>
                <ul>
                    <li><strong>ES6 Module Support</strong> - Complete Jest + Babel configuration for modern JavaScript</li>
                    <li><strong>API Compatibility</strong> - All tests aligned with actual implementation APIs</li>
                    <li><strong>Crypto Fallbacks</strong> - Testing of Web Crypto API → Pure JS fallback implementations</li>
                    <li><strong>Zero Regression</strong> - Maintained existing functionality while expanding coverage</li>
                    <li><strong>Professional Standards</strong> - Industry-grade testing patterns and practices</li>
                </ul>

                <h3>Test Categories</h3>
                <div class="test-categories">
                    <div class="category">
                        <h4>Unit Tests</h4>
                        <p>Individual function and component testing with isolated dependencies and mocked external services.</p>
                    </div>
                    <div class="category">
                        <h4>Integration Tests</h4>
                        <p>Multi-component testing with real protocol flows and platform adapter integration.</p>
                    </div>
                    <div class="category">
                        <h4>End-to-End Tests</h4>
                        <p>Complete user workflow testing with browser automation and real-world scenarios.</p>
                    </div>
                    <div class="category">
                        <h4>Environment Tests</h4>
                        <p>Development environment validation and DevContainer functionality verification.</p>
                    </div>
                </div>
            </section>

            <!-- Quality Assurance -->
            <section class="docs-section">
                <h2 class="section-title">Quality Assurance</h2>
                
                <p>WebOTR maintains the highest quality standards through:</p>
                
                <ul>
                    <li><strong>Comprehensive Coverage</strong> - 155+ tests covering all major functionality</li>
                    <li><strong>Continuous Integration</strong> - Automated testing on every commit</li>
                    <li><strong>Code Quality</strong> - ESLint, Prettier, and strict TypeScript checking</li>
                    <li><strong>Security Testing</strong> - Cryptographic function validation and protocol compliance</li>
                    <li><strong>Performance Testing</strong> - Benchmarking and optimization verification</li>
                </ul>

                <h3>Contributing to Tests</h3>
                <p>When contributing to WebOTR, please ensure:</p>
                <ul>
                    <li>All new features include corresponding test cases</li>
                    <li>Existing tests continue to pass</li>
                    <li>Test coverage remains comprehensive</li>
                    <li>DevContainer environment works correctly</li>
                </ul>

                <div class="code-block">
                    <pre><code># Before submitting a PR
npm test                           # Run all tests
npm run lint                       # Check code quality
./scripts/test-devcontainer.sh     # Validate environment</code></pre>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 WebOTR. Licensed under the MIT License.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="assets/docs.js"></script>
</body>
</html>
