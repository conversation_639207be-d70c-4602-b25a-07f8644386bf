# WebOTR GitHub Pages

This directory contains the source code for the WebOTR project website, hosted on GitHub Pages.

## 🌟 Features

- **Modern Design** - Clean, professional design with dark theme
- **Responsive Layout** - Works perfectly on desktop, tablet, and mobile
- **Interactive Elements** - Animated chat demo, floating particles, smooth scrolling
- **Documentation** - Complete API documentation and usage guides
- **Performance Optimized** - Fast loading with optimized assets
- **Accessibility** - WCAG compliant with keyboard navigation support

## 📁 Structure

```
docs/
├── index.html          # Main landing page
├── docs.html           # Documentation page
├── assets/
│   ├── style.css       # Main stylesheet
│   ├── docs.css        # Documentation-specific styles
│   ├── script.js       # Interactive functionality
│   ├── docs.js         # Documentation-specific JavaScript
│   ├── logo.svg        # WebOTR logo
│   └── favicon.svg     # Site favicon
└── README.md           # This file
```

## 🚀 Features Showcase

### Landing Page (`index.html`)
- **Hero Section** - Eye-catching introduction with animated chat demo
- **Features Grid** - Highlighting key WebOTR capabilities
- **How It Works** - Step-by-step guide with visual indicators
- **Platform Support** - Supported chat platforms with status
- **Download Section** - Browser extension download links
- **Responsive Footer** - Links to documentation and community

### Documentation (`docs.html`)
- **Sidebar Navigation** - Easy navigation between sections
- **Code Examples** - Syntax-highlighted code blocks with copy functionality
- **API Reference** - Complete method documentation
- **Security Guide** - Detailed security model explanation
- **Contributing Guide** - Instructions for developers

### Interactive Elements
- **Animated Chat Demo** - Shows encryption/decryption process
- **Floating Particles** - Subtle background animation
- **Smooth Scrolling** - Enhanced navigation experience
- **Mobile Menu** - Responsive navigation for mobile devices
- **Copy to Clipboard** - Easy code copying with feedback
- **Keyboard Shortcuts** - Accessibility enhancements

## 🎨 Design System

### Colors
- **Primary**: `#6366f1` (Indigo)
- **Secondary**: `#8b5cf6` (Purple)
- **Accent**: `#06b6d4` (Cyan)
- **Success**: `#10b981` (Emerald)
- **Background**: Dark theme with gradient overlays

### Typography
- **Primary Font**: Inter (Google Fonts)
- **Monospace**: JetBrains Mono (Google Fonts)
- **Responsive Sizing**: Clamp functions for fluid typography

### Components
- **Buttons**: Gradient backgrounds with hover effects
- **Cards**: Glass morphism with subtle borders
- **Code Blocks**: Dark theme with syntax highlighting
- **Navigation**: Fixed header with backdrop blur

## 📱 Responsive Design

The site is fully responsive with breakpoints at:
- **Desktop**: 1024px+
- **Tablet**: 768px - 1023px
- **Mobile**: 320px - 767px

### Mobile Optimizations
- Collapsible navigation menu
- Stacked layouts for better readability
- Touch-friendly button sizes
- Optimized font sizes and spacing

## ⚡ Performance

### Optimization Techniques
- **CSS Grid & Flexbox** - Modern layout techniques
- **CSS Custom Properties** - Efficient theming system
- **Intersection Observer** - Lazy loading and animations
- **Minimal Dependencies** - Only Font Awesome for icons
- **Optimized Assets** - SVG icons and minimal images

### Loading Performance
- **Critical CSS** - Inlined for faster rendering
- **Font Display Swap** - Prevents layout shift
- **Preconnect** - Faster font loading
- **Efficient Animations** - GPU-accelerated transforms

## 🔧 Development

### Local Development
```bash
# Serve the docs directory
cd docs
python -m http.server 8000
# or
npx serve .
```

### Making Changes
1. Edit HTML, CSS, or JS files in the `docs/` directory
2. Test changes locally
3. Commit and push to trigger GitHub Pages deployment

### Adding New Pages
1. Create new HTML file in `docs/`
2. Follow the existing structure and styling
3. Update navigation in `index.html` and `docs.html`
4. Add appropriate meta tags for SEO

## 🚀 Deployment

The site is automatically deployed to GitHub Pages using GitHub Actions:

1. **Trigger**: Push to `master`/`main` branch with changes in `docs/`
2. **Build**: GitHub Actions workflow processes the files
3. **Deploy**: Site is published to `https://forkrul.github.io/webOTteR/`

### Manual Deployment
If needed, you can manually trigger deployment:
1. Go to Actions tab in GitHub repository
2. Select "Deploy GitHub Pages" workflow
3. Click "Run workflow"

## 📊 Analytics & SEO

### SEO Optimizations
- **Meta Tags** - Complete Open Graph and Twitter Card meta tags
- **Semantic HTML** - Proper heading hierarchy and landmarks
- **Alt Text** - Descriptive alt text for all images
- **Structured Data** - JSON-LD for better search understanding

### Performance Monitoring
- **Core Web Vitals** - Optimized for Google's performance metrics
- **Lighthouse Score** - Regularly tested for performance, accessibility, SEO
- **Mobile-First** - Designed and tested mobile-first

## 🎯 Browser Support

- **Chrome/Chromium**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

### Progressive Enhancement
- **CSS Grid**: Fallback to Flexbox
- **CSS Custom Properties**: Fallback values provided
- **JavaScript**: Graceful degradation for core functionality

## 🔒 Security

### Content Security Policy
- **Inline Styles**: Minimal use, prefer external stylesheets
- **External Resources**: Only trusted CDNs (Google Fonts, Font Awesome)
- **XSS Prevention**: Proper escaping and sanitization

### Privacy
- **No Tracking**: No analytics or tracking scripts
- **No Cookies**: Site functions without cookies
- **External Requests**: Minimal external dependencies

## 🤝 Contributing

To contribute to the website:

1. **Fork** the repository
2. **Create** a feature branch
3. **Make** your changes in the `docs/` directory
4. **Test** locally to ensure everything works
5. **Submit** a pull request

### Guidelines
- Follow the existing design system
- Ensure responsive design works on all devices
- Test accessibility with screen readers
- Optimize for performance
- Update documentation as needed

## 📄 License

The website code is licensed under the MIT License, same as the main WebOTR project.
