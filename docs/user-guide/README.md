# WebOTR User Guide

## Welcome to Secure Messaging

WebOTR provides end-to-end encrypted messaging with identity verification, ensuring your conversations remain private and secure. This guide will help you get started and make the most of WebOTR's security features.

## Table of Contents

- [Getting Started](#getting-started)
- [Starting a Secure Conversation](#starting-a-secure-conversation)
- [Verifying Your Contact's Identity](#verifying-your-contacts-identity)
- [Understanding Security Indicators](#understanding-security-indicators)
- [Sending and Receiving Messages](#sending-and-receiving-messages)
- [Troubleshooting](#troubleshooting)
- [Frequently Asked Questions](#frequently-asked-questions)

## Getting Started

### What is OTR?

Off-the-Record (OTR) messaging provides:

- **End-to-end encryption**: Only you and your contact can read messages
- **Perfect forward secrecy**: Past messages remain secure even if keys are compromised
- **Deniable authentication**: Messages cannot be proven to come from you
- **Identity verification**: Confirm you're talking to the right person

### System Requirements

WebOTR works in modern web browsers:

- **Chrome** 70 or later
- **Firefox** 65 or later
- **Safari** 12 or later
- **Edge** 79 or later

For the best experience, ensure your browser is up to date.

### Accessibility

WebOTR is designed to be accessible to everyone:

- **Screen reader compatible** with full ARIA support
- **Keyboard navigation** throughout the interface
- **High contrast** design for visual accessibility
- **Multiple languages** supported

## Starting a Secure Conversation

### Step 1: Open WebOTR

Navigate to the WebOTR interface in your web browser. You'll see the main screen with:

- **WebOTR Secure Messaging** title
- **Connection status** indicator (initially "Not Connected")
- **Start Secure Chat** button

### Step 2: Initiate Connection

1. Click the **"Start Secure Chat"** button
2. You'll see a progress indicator showing connection establishment
3. The process typically takes 2-5 seconds

### Step 3: Connection Established

Once connected, you'll see:

- Status changes to **"Connected"**
- A warning that **identity is not yet verified**
- Option to **"Verify Identity"** or continue unverified

⚠️ **Important**: While your messages are encrypted, you should verify your contact's identity to ensure you're communicating with the right person.

## Verifying Your Contact's Identity

Identity verification confirms you're talking to the intended person and not an imposter. WebOTR offers three verification methods:

### Method 1: QR Code (Recommended)

**Best for**: In-person meetings, video calls

1. Click **"Verify Identity"** → **"QR Code"**
2. Choose to either:
   - **Show QR code** to your contact
   - **Scan your contact's QR code**
3. If scanning:
   - Click **"Start Camera"**
   - Position the QR code within the frame
   - Verification completes automatically
4. If showing:
   - Have your contact scan your displayed QR code
   - Confirm when they've completed scanning

**Advantages**: Fast, automatic, works great for video calls

### Method 2: Question & Answer

**Best for**: Remote verification with shared knowledge

1. Click **"Verify Identity"** → **"Question & Answer"**
2. **Create a question** only you and your contact know:
   - "What was the name of our first pet?"
   - "Where did we first meet?"
   - "What movie did we watch on our first date?"
3. **Provide your answer** to the same question
4. The system verifies answers match without revealing them

**Advantages**: Works remotely, uses shared memories, cryptographically secure

### Method 3: Manual Verification

**Best for**: Technical users, phone/email verification

1. Click **"Verify Identity"** → **"Manual Verification"**
2. **Compare fingerprints** through a secure channel:
   - Phone call
   - In person
   - Encrypted email
   - Another secure messaging app
3. **Confirm** if fingerprints match exactly

**Advantages**: Works with any secure communication channel, highest security

### Verification Success

Once verification completes successfully:

- Status changes to **"Verified"**
- Messages show **"Verified"** badges
- Green success notification appears
- Your conversation is now fully secure

## Understanding Security Indicators

WebOTR uses clear visual indicators to show your security status:

### Connection Status

| Indicator | Meaning | Action Needed |
|-----------|---------|---------------|
| 🔴 **Not Connected** | No secure session | Click "Start Secure Chat" |
| 🟡 **Connecting** | Establishing session | Wait for completion |
| 🟢 **Connected** | Encrypted but unverified | Consider verifying identity |
| ✅ **Verified** | Fully secure | None - you're protected |
| ❌ **Error** | Connection problem | Check connection, retry |

### Message Badges

Each message shows security information:

- **🔒 Encrypted**: Message sent through secure channel
- **🔓 Unencrypted**: Plain text message (avoid if possible)
- **✓ Verified**: From verified contact
- **? Unverified**: From unverified contact

### Security Alerts

WebOTR shows contextual alerts:

- **Blue (Info)**: General information, no action needed
- **Yellow (Warning)**: Attention recommended, consider action
- **Red (Error)**: Problem detected, action required
- **Green (Success)**: Positive confirmation, all good

## Sending and Receiving Messages

### Sending Messages

1. **Type your message** in the input field at the bottom
2. **Press Enter** or click **"Send"**
3. Your message appears with security badges
4. Messages are automatically encrypted when connected

### Message Security

- **Encrypted messages** show a lock icon 🔒
- **Verified messages** show a checkmark ✓
- **Message timestamps** show when sent/received
- **Message history** is maintained during the session

### Best Practices

- **Verify identity** before sharing sensitive information
- **Check security indicators** regularly
- **End sessions** when finished for maximum security
- **Start fresh sessions** for important conversations

## Troubleshooting

### Connection Issues

**Problem**: "Connection failed" error
**Solutions**:
- Check your internet connection
- Refresh the page and try again
- Ensure your browser is supported
- Disable browser extensions that might interfere

**Problem**: Connection takes too long
**Solutions**:
- Wait up to 30 seconds for completion
- Check for network connectivity issues
- Try refreshing and starting again

### Verification Issues

**Problem**: QR code scanning fails
**Solutions**:
- Ensure good lighting
- Hold camera steady
- Try manual input option
- Use a different verification method

**Problem**: Question-Answer verification fails
**Solutions**:
- Check spelling and capitalization
- Ensure both people understand the question
- Try a different question
- Use exact same answer format

**Problem**: Manual verification confusion
**Solutions**:
- Compare fingerprints character by character
- Use a secure channel (phone call, in person)
- Double-check you're comparing the right fingerprints

### Camera Issues

**Problem**: Camera not working for QR codes
**Solutions**:
- Grant camera permission when prompted
- Check browser camera settings
- Try a different browser
- Use manual input as fallback

### Performance Issues

**Problem**: Interface feels slow
**Solutions**:
- Close other browser tabs
- Restart your browser
- Check available memory
- Try a different browser

## Frequently Asked Questions

### Security Questions

**Q: How secure is WebOTR?**
A: WebOTR uses industry-standard OTR protocol with end-to-end encryption, perfect forward secrecy, and deniable authentication. When properly verified, it provides excellent security.

**Q: Can anyone intercept my messages?**
A: No, messages are encrypted end-to-end. Only you and your verified contact can read them.

**Q: What if I skip identity verification?**
A: Your messages are still encrypted, but you can't be certain you're talking to the intended person. Verification is strongly recommended.

**Q: How often should I verify identities?**
A: Verify once per contact relationship. Re-verify if you suspect any security issues.

### Technical Questions

**Q: Does WebOTR work on mobile?**
A: Yes, WebOTR is fully responsive and works on mobile browsers with touch-friendly interfaces.

**Q: Can I use WebOTR offline?**
A: No, WebOTR requires an internet connection to establish secure sessions and exchange messages.

**Q: Are my messages stored anywhere?**
A: Messages are only stored temporarily in your browser during the session. They're not saved on servers.

**Q: What browsers are supported?**
A: Chrome 70+, Firefox 65+, Safari 12+, and Edge 79+. Older browsers may work with reduced functionality.

### Usage Questions

**Q: Can I have multiple conversations?**
A: Currently, WebOTR supports one conversation at a time. End your current session to start a new one.

**Q: How do I end a conversation?**
A: Click "End Session" to securely close your OTR session.

**Q: Can I save my conversation history?**
A: For security, conversations are not automatically saved. Copy important information before ending sessions.

**Q: What languages are supported?**
A: WebOTR supports 10+ languages including English, Spanish, French, German, Italian, Portuguese, Russian, Chinese, Japanese, and Korean.

## Getting Help

If you need additional assistance:

1. **Check this guide** for common solutions
2. **Review error messages** for specific guidance
3. **Try different browsers** if issues persist
4. **Contact support** through the project's GitHub repository

## Security Best Practices

1. **Always verify identities** for sensitive conversations
2. **Use strong verification methods** (QR code or shared secrets)
3. **Check security indicators** before sharing sensitive information
4. **End sessions** when conversations are complete
5. **Keep your browser updated** for latest security features
6. **Use secure networks** when possible
7. **Be cautious** of unexpected verification requests

## Privacy Notice

WebOTR is designed with privacy in mind:

- **No message storage** on servers
- **No tracking** or analytics
- **Local processing** of all sensitive data
- **Open source** for transparency and security auditing

Your privacy and security are our top priorities.

---

**Need more help?** Visit our [GitHub repository](https://github.com/forkrul/webOTteR) for technical support and community discussions.
