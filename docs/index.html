<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebOTR - Secure Messaging for Web Chat Platforms</title>
    <meta name="description" content="End-to-end encryption for Microsoft Teams, Discord, Slack, and other web chat platforms using the Off-The-Record protocol.">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://forkrul.github.io/webOTteR/">
    <meta property="og:title" content="WebOTR - Secure Messaging for Web Chat Platforms">
    <meta property="og:description" content="End-to-end encryption for Microsoft Teams, Discord, Slack, and other web chat platforms using the Off-The-Record protocol.">
    <meta property="og:image" content="https://forkrul.github.io/webOTteR/assets/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://forkrul.github.io/webOTteR/">
    <meta property="twitter:title" content="WebOTR - Secure Messaging for Web Chat Platforms">
    <meta property="twitter:description" content="End-to-end encryption for Microsoft Teams, Discord, Slack, and other web chat platforms using the Off-The-Record protocol.">
    <meta property="twitter:image" content="https://forkrul.github.io/webOTteR/assets/og-image.png">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
    <link rel="icon" type="image/png" href="assets/favicon.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="assets/style.css">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="assets/logo.svg" alt="WebOTR" class="nav-logo">
                <span class="nav-title">WebOTR</span>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="#features" class="nav-link">Features</a>
                <a href="#how-it-works" class="nav-link">How It Works</a>
                <a href="#platforms" class="nav-link">Platforms</a>
                <a href="#docs" class="nav-link">Docs</a>
                <a href="https://github.com/forkrul/webOTteR" class="nav-link" target="_blank">
                    <i class="fab fa-github"></i> GitHub
                </a>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-shield-alt"></i>
                    <span>End-to-End Encryption</span>
                </div>
                <h1 class="hero-title">
                    Secure Messaging for
                    <span class="gradient-text">Web Chat Platforms</span>
                </h1>
                <p class="hero-description">
                    WebOTR brings military-grade encryption to Microsoft Teams, Discord, Slack, and other web chat platforms. 
                    Protect your conversations with the proven Off-The-Record protocol.
                </p>
                <div class="hero-buttons">
                    <a href="#download" class="btn btn-primary">
                        <i class="fas fa-download"></i>
                        Download Extension
                    </a>
                    <a href="https://github.com/forkrul/webOTteR" class="btn btn-secondary" target="_blank">
                        <i class="fab fa-github"></i>
                        View on GitHub
                    </a>
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <div class="stat-number">256-bit</div>
                        <div class="stat-label">AES Encryption</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">155+</div>
                        <div class="stat-label">Test Cases</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Core Tests Pass</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">Open</div>
                        <div class="stat-label">Source</div>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="chat-demo">
                    <div class="chat-window">
                        <div class="chat-header">
                            <div class="chat-title">Secure Chat</div>
                            <div class="chat-status">
                                <i class="fas fa-lock"></i>
                                <span>Encrypted</span>
                            </div>
                        </div>
                        <div class="chat-messages">
                            <div class="message message-sent">
                                <div class="message-content encrypted">
                                    ?OTR:AAEDAAAAAQAAAAEAAADAVf3Rg3ErZ...
                                </div>
                            </div>
                            <div class="message message-received">
                                <div class="message-content decrypted">
                                    Hey! This message is encrypted end-to-end 🔒
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Why Choose WebOTR?</h2>
                <p class="section-description">
                    Built on the proven Off-The-Record protocol, WebOTR provides uncompromising security 
                    without sacrificing usability.
                </p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <h3 class="feature-title">End-to-End Encryption</h3>
                    <p class="feature-description">
                        Your messages are encrypted on your device and can only be decrypted by the intended recipient.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-user-secret"></i>
                    </div>
                    <h3 class="feature-title">Perfect Forward Secrecy</h3>
                    <p class="feature-description">
                        Even if your keys are compromised, past conversations remain secure with rotating encryption keys.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-eye-slash"></i>
                    </div>
                    <h3 class="feature-title">Deniable Authentication</h3>
                    <p class="feature-description">
                        Messages are authenticated during conversation but cannot be proven authentic to third parties.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3 class="feature-title">Socialist Millionaire Protocol</h3>
                    <p class="feature-description">
                        Verify your conversation partner's identity without revealing shared secrets.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3 class="feature-title">Cross-Platform</h3>
                    <p class="feature-description">
                        Works seamlessly across Microsoft Teams, Discord, Slack, and other web chat platforms.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3 class="feature-title">Open Source</h3>
                    <p class="feature-description">
                        Fully open source and auditable. No backdoors, no hidden functionality, complete transparency.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-vial"></i>
                    </div>
                    <h3 class="feature-title">Comprehensive Testing</h3>
                    <p class="feature-description">
                        155+ test cases with 100% core functionality coverage and DevContainer development environment.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-docker"></i>
                    </div>
                    <h3 class="feature-title">DevContainer Ready</h3>
                    <p class="feature-description">
                        Consistent development environment with Docker, automated testing, and professional tooling.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="how-it-works">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">How It Works</h2>
                <p class="section-description">
                    WebOTR seamlessly integrates with your existing chat platforms to provide transparent encryption.
                </p>
            </div>
            <div class="steps-container">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3 class="step-title">Install Extension</h3>
                        <p class="step-description">
                            Add the WebOTR browser extension to Chrome, Firefox, or Edge. 
                            It integrates seamlessly with your existing chat platforms.
                        </p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3 class="step-title">Initiate Secure Session</h3>
                        <p class="step-description">
                            Click the WebOTR button in your chat to start an encrypted session. 
                            The extension handles key exchange automatically.
                        </p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3 class="step-title">Verify Identity</h3>
                        <p class="step-description">
                            Use the Socialist Millionaire Protocol to verify your conversation partner 
                            using a shared secret only you both know.
                        </p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3 class="step-title">Chat Securely</h3>
                        <p class="step-description">
                            Your messages are now encrypted end-to-end. Chat normally while 
                            WebOTR handles encryption and decryption transparently.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Platforms Section -->
    <section id="platforms" class="platforms">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Supported Platforms</h2>
                <p class="section-description">
                    WebOTR works with all major web chat platforms and more are being added regularly.
                </p>
            </div>
            <div class="platforms-grid">
                <div class="platform-card">
                    <div class="platform-icon">
                        <i class="fab fa-microsoft"></i>
                    </div>
                    <h3 class="platform-name">Microsoft Teams</h3>
                    <p class="platform-status">✅ Fully Supported</p>
                </div>
                <div class="platform-card">
                    <div class="platform-icon">
                        <i class="fab fa-discord"></i>
                    </div>
                    <h3 class="platform-name">Discord</h3>
                    <p class="platform-status">✅ Fully Supported</p>
                </div>
                <div class="platform-card">
                    <div class="platform-icon">
                        <i class="fab fa-slack"></i>
                    </div>
                    <h3 class="platform-name">Slack</h3>
                    <p class="platform-status">✅ Fully Supported</p>
                </div>
                <div class="platform-card">
                    <div class="platform-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 class="platform-name">Generic Web Chat</h3>
                    <p class="platform-status">🔧 In Development</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="download">
        <div class="container">
            <div class="download-content">
                <h2 class="download-title">Ready to Secure Your Conversations?</h2>
                <p class="download-description">
                    Download WebOTR today and start protecting your privacy with military-grade encryption.
                </p>
                <div class="download-buttons">
                    <a href="#" class="download-btn chrome">
                        <i class="fab fa-chrome"></i>
                        <div>
                            <div class="download-text">Download for</div>
                            <div class="download-browser">Chrome</div>
                        </div>
                    </a>
                    <a href="#" class="download-btn firefox">
                        <i class="fab fa-firefox"></i>
                        <div>
                            <div class="download-text">Download for</div>
                            <div class="download-browser">Firefox</div>
                        </div>
                    </a>
                    <a href="#" class="download-btn edge">
                        <i class="fab fa-edge"></i>
                        <div>
                            <div class="download-text">Download for</div>
                            <div class="download-browser">Edge</div>
                        </div>
                    </a>
                </div>
                <p class="download-note">
                    <i class="fas fa-info-circle"></i>
                    Currently in beta. Source code available on <a href="https://github.com/forkrul/webOTteR" target="_blank">GitHub</a>.
                </p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <img src="assets/logo.svg" alt="WebOTR" class="footer-logo">
                    <p class="footer-description">
                        Bringing end-to-end encryption to web chat platforms through the proven Off-The-Record protocol.
                    </p>
                </div>
                <div class="footer-links">
                    <div class="footer-section">
                        <h4 class="footer-title">Project</h4>
                        <a href="https://github.com/forkrul/webOTteR" class="footer-link" target="_blank">GitHub</a>
                        <a href="https://github.com/forkrul/webOTteR/issues" class="footer-link" target="_blank">Issues</a>
                        <a href="https://github.com/forkrul/webOTteR/blob/master/CONTRIBUTING.md" class="footer-link" target="_blank">Contributing</a>
                    </div>
                    <div class="footer-section">
                        <h4 class="footer-title">Documentation</h4>
                        <a href="#" class="footer-link">Getting Started</a>
                        <a href="#" class="footer-link">API Reference</a>
                        <a href="#" class="footer-link">Security</a>
                    </div>
                    <div class="footer-section">
                        <h4 class="footer-title">Community</h4>
                        <a href="#" class="footer-link">Discord</a>
                        <a href="#" class="footer-link">Reddit</a>
                        <a href="#" class="footer-link">Twitter</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p class="footer-copyright">
                    © 2024 WebOTR. Licensed under the <a href="https://github.com/forkrul/webOTteR/blob/master/LICENSE" target="_blank">MIT License</a>.
                </p>
                <div class="footer-social">
                    <a href="https://github.com/forkrul/webOTteR" class="social-link" target="_blank">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="#" class="social-link" target="_blank">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="social-link" target="_blank">
                        <i class="fab fa-discord"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="assets/script.js"></script>
</body>
</html>
