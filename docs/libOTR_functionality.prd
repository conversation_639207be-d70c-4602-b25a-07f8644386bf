# libOTR Functionality Extraction PRD

## Executive Summary

This PRD outlines the systematic extraction and analysis of all cryptographic algorithms, protocol flows, and implementation specifics from the reference libOTR implementation to ensure WebOTR's complete compatibility and security equivalence.

## Objectives

### Primary Goals
- **Complete Algorithm Extraction**: Document all cryptographic algorithms used in libOTR
- **Protocol Flow Analysis**: Map all OTR protocol state transitions and message flows
- **Implementation Compatibility**: Ensure WebOTR matches libOTR's security properties
- **Security Equivalence**: Verify cryptographic implementation correctness
- **Interoperability Assurance**: Guarantee compatibility with existing OTR implementations

### Success Criteria
- [ ] All libOTR cryptographic algorithms documented and implemented
- [ ] Complete protocol state machine extracted and verified
- [ ] Message format specifications fully documented
- [ ] Security properties mathematically verified
- [ ] Interoperability testing with libOTR completed
- [ ] Performance benchmarks meet or exceed libOTR

## Scope

### In Scope
1. **Cryptographic Algorithms**
   - AES encryption/decryption implementations
   - SHA-1 and SHA-256 hash functions
   - HMAC-SHA1 and HMAC-SHA256 implementations
   - Di<PERSON><PERSON><PERSON><PERSON><PERSON> key exchange (MODP groups)
   - DSA signature generation and verification
   - Key derivation functions (KDF)
   - Secure random number generation

2. **Protocol Implementation**
   - OTR protocol versions 2 and 3
   - AKE (Authenticated Key Exchange) protocol
   - Message encryption/decryption flows
   - Key rotation mechanisms
   - Socialist Millionaire Protocol (SMP)
   - Protocol state machine transitions

3. **Message Formats**
   - OTR message encoding/decoding
   - TLV (Type-Length-Value) structures
   - Fragment handling and reassembly
   - Error message formats
   - Query message handling

4. **Security Features**
   - Perfect forward secrecy implementation
   - Deniable authentication mechanisms
   - Replay attack prevention
   - Instance tag handling
   - Secure memory management

### Out of Scope
- libOTR's C-specific memory management
- Platform-specific implementations
- Legacy protocol versions (OTR v1)
- Non-standard extensions

## Technical Analysis

### Algorithm Extraction Matrix

| Algorithm | libOTR Implementation | WebOTR Status | Priority | Complexity |
|-----------|----------------------|---------------|----------|------------|
| AES-128 CTR | `aes.c` | ✅ Implemented | P0 | Medium |
| SHA-1 | `sha1hash.c` | ✅ Implemented | P0 | Low |
| SHA-256 | `sha256hash.c` | ✅ Implemented | P0 | Low |
| HMAC-SHA1 | `sha1hmac.c` | ✅ Implemented | P0 | Low |
| HMAC-SHA256 | `sha256hmac.c` | ✅ Implemented | P0 | Low |
| DH Key Exchange | `dh.c` | ✅ Implemented | P0 | High |
| DSA Signatures | `dsa.c` | ✅ Implemented | P0 | High |
| Secure Random | `random.c` | ✅ Implemented | P0 | Medium |
| Key Derivation | `kdf.c` | 🔄 In Progress | P0 | Medium |
| SMP Protocol | `sm.c` | 📋 Planned | P1 | High |

### Protocol Flow Extraction

#### AKE Protocol States
```
PLAINTEXT → AWAITING_DHKEY → AWAITING_REVEALSIG → AWAITING_SIG → ENCRYPTED
```

**State Transition Analysis:**
- `PLAINTEXT`: Initial state, no encryption
- `AWAITING_DHKEY`: Sent DH commit, waiting for DH key
- `AWAITING_REVEALSIG`: Sent DH key, waiting for reveal signature
- `AWAITING_SIG`: Sent reveal signature, waiting for signature
- `ENCRYPTED`: AKE complete, secure messaging enabled

#### Message Type Mapping

| libOTR Message Type | Hex Value | WebOTR Implementation | Status |
|-------------------|-----------|----------------------|--------|
| DH_COMMIT | 0x02 | `MESSAGE_TYPE.DH_COMMIT` | ✅ |
| DH_KEY | 0x0a | `MESSAGE_TYPE.DH_KEY` | ✅ |
| REVEAL_SIGNATURE | 0x11 | `MESSAGE_TYPE.REVEAL_SIGNATURE` | ✅ |
| SIGNATURE | 0x12 | `MESSAGE_TYPE.SIGNATURE` | ✅ |
| DATA | 0x03 | `MESSAGE_TYPE.DATA` | ✅ |
| ERROR | 0x05 | `MESSAGE_TYPE.ERROR` | ✅ |

### Cryptographic Parameter Extraction

#### Diffie-Hellman Parameters
```c
// libOTR MODP Group 14 (from dh.c)
static const char DH1536_MODULUS[] = "FFFFFFFFFFFFFFFFC90FDAA22168C234...";
static const char DH1536_GENERATOR[] = "2";
```

**WebOTR Implementation:**
```javascript
const MODP_GROUP_14 = {
  P: new Uint8Array([0xFF, 0xFF, 0xFF, 0xFF, ...]), // 2048-bit prime
  G: new Uint8Array([0x02])                          // Generator = 2
};
```

#### Key Derivation Specification
```c
// libOTR key derivation (from kdf.c)
void derive_keys(unsigned char *sendingkey, unsigned char *receivingkey,
                 unsigned char *sendingmackey, unsigned char *receivingmackey,
                 unsigned char *ssid, const unsigned char *s, size_t slen)
```

**Extraction Requirements:**
- HKDF-based key derivation
- 256-bit AES keys for encryption
- 256-bit HMAC keys for authentication
- 64-bit session ID (SSID) generation

## Implementation Plan

### Phase 1: Core Algorithm Verification (Week 1-2)
**Deliverables:**
- [ ] AES-128 CTR mode implementation verification
- [ ] SHA-1/SHA-256 hash function verification
- [ ] HMAC implementation verification
- [ ] Test vectors from libOTR extracted and verified

**Tasks:**
1. Extract test vectors from libOTR test suite
2. Verify WebOTR crypto implementations against libOTR
3. Document any discrepancies and resolve
4. Create comprehensive test suite

### Phase 2: Protocol Flow Analysis (Week 3-4)
**Deliverables:**
- [ ] Complete AKE protocol state machine documentation
- [ ] Message format specifications
- [ ] Error handling procedures
- [ ] Fragment handling implementation

**Tasks:**
1. Analyze libOTR state machine in `proto.c`
2. Extract message parsing logic from `message.c`
3. Document fragment handling from `fragment.c`
4. Verify WebOTR protocol compliance

### Phase 3: Advanced Features (Week 5-6)
**Deliverables:**
- [ ] Socialist Millionaire Protocol implementation
- [ ] Instance tag handling
- [ ] Heartbeat mechanism
- [ ] Extra symmetric key derivation

**Tasks:**
1. Extract SMP implementation from `sm.c`
2. Implement instance tag logic from `instag.c`
3. Add heartbeat mechanism from `proto.c`
4. Implement extra symmetric key features

### Phase 4: Security Verification (Week 7-8)
**Deliverables:**
- [ ] Security property verification
- [ ] Timing attack resistance analysis
- [ ] Memory security implementation
- [ ] Interoperability testing

**Tasks:**
1. Verify perfect forward secrecy implementation
2. Analyze timing attack resistance
3. Implement secure memory handling
4. Conduct interoperability tests with libOTR

## Technical Specifications

### Algorithm Implementation Requirements

#### AES-128 CTR Mode
```c
// libOTR AES implementation (aes.c)
void aes_set_key(aes_context *ctx, const unsigned char *key, int keysize);
void aes_ctr_crypt(aes_context *ctx, const unsigned char *input, 
                   unsigned char *output, int length, unsigned char *counter);
```

**WebOTR Equivalent:**
```javascript
class AESCTRCipher {
  constructor(key) {
    this.key = key;
    this.counter = new Uint8Array(16);
  }
  
  async encrypt(plaintext) {
    return await crypto.subtle.encrypt(
      { name: 'AES-CTR', counter: this.counter, length: 128 },
      this.key,
      plaintext
    );
  }
}
```

#### Diffie-Hellman Implementation
```c
// libOTR DH implementation (dh.c)
int dh_generate_key(DH_keypair *kp);
int dh_compute_shared_secret(unsigned char *shared_secret, 
                            const DH_pubkey *pubkey, const DH_privkey *privkey);
```

**WebOTR Equivalent:**
```javascript
async function generateDHKeyPair() {
  const privateKey = generateRandomBytes(256);
  const publicKey = modularExponentiation(MODP_GROUP_14.G, privateKey, MODP_GROUP_14.P);
  return { privateKey, publicKey };
}

async function computeSharedSecret(privateKey, publicKey) {
  return modularExponentiation(publicKey, privateKey, MODP_GROUP_14.P);
}
```

### Message Format Specifications

#### OTR Message Header
```c
// libOTR message header (message.c)
typedef struct {
    unsigned short version;
    unsigned char msgtype;
    unsigned int sender_instance;
    unsigned int receiver_instance;
} OtrlMessageHeader;
```

**WebOTR Implementation:**
```javascript
class OTRMessageHeader {
  constructor(version, messageType, senderInstance, receiverInstance) {
    this.version = version;
    this.messageType = messageType;
    this.senderInstance = senderInstance;
    this.receiverInstance = receiverInstance;
  }
  
  serialize() {
    const buffer = new ArrayBuffer(12);
    const view = new DataView(buffer);
    view.setUint16(0, this.version, false);
    view.setUint8(2, this.messageType);
    view.setUint32(4, this.senderInstance, false);
    view.setUint32(8, this.receiverInstance, false);
    return new Uint8Array(buffer);
  }
}
```

## Security Considerations

### Cryptographic Equivalence
- **Algorithm Compliance**: All algorithms must match libOTR's implementation exactly
- **Parameter Verification**: All cryptographic parameters must be identical
- **Test Vector Validation**: All implementations must pass libOTR's test vectors
- **Security Property Preservation**: Perfect forward secrecy and deniability must be maintained

### Implementation Security
- **Timing Attack Resistance**: Constant-time implementations where required
- **Memory Security**: Secure memory handling and cleanup
- **Side-Channel Resistance**: Protection against side-channel attacks
- **Error Handling**: Secure error handling without information leakage

## Testing Strategy

### Unit Testing
- [ ] Individual algorithm testing against libOTR test vectors
- [ ] Protocol state machine testing
- [ ] Message format parsing/serialization testing
- [ ] Error condition handling testing

### Integration Testing
- [ ] Full protocol flow testing
- [ ] Interoperability testing with libOTR
- [ ] Multi-session testing
- [ ] Fragment handling testing

### Security Testing
- [ ] Cryptographic property verification
- [ ] Timing attack resistance testing
- [ ] Memory security testing
- [ ] Protocol security analysis

### Performance Testing
- [ ] Algorithm performance benchmarking
- [ ] Protocol performance testing
- [ ] Memory usage analysis
- [ ] Scalability testing

## Risk Assessment

### High Risk
- **Cryptographic Implementation Errors**: Could compromise security
- **Protocol Incompatibility**: Could break interoperability
- **Timing Attacks**: Could leak sensitive information
- **Memory Leaks**: Could expose cryptographic material

### Medium Risk
- **Performance Degradation**: Could impact user experience
- **Fragment Handling Issues**: Could cause message loss
- **Error Handling Problems**: Could cause protocol failures

### Low Risk
- **Documentation Gaps**: Could impact maintenance
- **Test Coverage Issues**: Could miss edge cases

## Success Metrics

### Functional Metrics
- [ ] 100% algorithm compatibility with libOTR
- [ ] 100% protocol compliance
- [ ] 100% interoperability with existing OTR clients
- [ ] Zero security vulnerabilities

### Performance Metrics
- [ ] AKE completion time ≤ 200ms
- [ ] Message encryption/decryption ≤ 10ms
- [ ] Memory usage ≤ 10MB per session
- [ ] CPU usage ≤ 5% during normal operation

### Quality Metrics
- [ ] 100% test coverage for critical paths
- [ ] Zero critical security issues
- [ ] Documentation completeness ≥ 95%
- [ ] Code review approval rate ≥ 95%

## Conclusion

This PRD provides a comprehensive framework for extracting and implementing all libOTR functionality in WebOTR. The systematic approach ensures security equivalence, protocol compliance, and interoperability while maintaining the performance and usability advantages of the WebOTR implementation.

The phased implementation plan allows for incremental verification and testing, reducing risk and ensuring quality throughout the development process.

## Detailed Technical Analysis

### libOTR Source Code Structure Analysis

#### Core Files and Their Functions

**Authentication and Key Exchange (`auth.c`)**
```c
// Key functions to extract:
OtrlAuthInfo *otrl_auth_new(OtrlUserState us);
void otrl_auth_clear(OtrlAuthInfo *auth);
gcry_error_t otrl_auth_start_v23(OtrlAuthInfo *auth, DH_keypair *our_dh);
gcry_error_t otrl_auth_handle_commit(OtrlAuthInfo *auth, const char *msg);
```

**WebOTR Implementation Requirements:**
- Complete AKE state machine with identical transitions
- Exact message format compatibility
- Identical cryptographic parameter handling
- Error condition handling matching libOTR behavior

**Protocol Implementation (`proto.c`)**
```c
// Critical protocol functions:
OtrlMessageType otrl_proto_message_type(const char *message);
gcry_error_t otrl_proto_create_commit(char **commitp, DH_keypair *our_dh);
gcry_error_t otrl_proto_create_key(char **keyp, DH_keypair *our_dh);
gcry_error_t otrl_proto_create_revealsig(char **revealsigp, ...);
```

**Message Handling (`message.c`)**
```c
// Message processing functions:
OtrlTLV *otrl_tlv_parse(const unsigned char *serialized, size_t seriallen);
gcry_error_t otrl_message_sending(OtrlUserState us, const OtrlMessageAppOps *ops, ...);
int otrl_message_receiving(OtrlUserState us, const OtrlMessageAppOps *ops, ...);
```

### Cryptographic Algorithm Deep Dive

#### AES Implementation Analysis
```c
// libOTR AES context structure (aes.h)
typedef struct {
    unsigned long erk[64];     /* encryption round keys */
    unsigned long drk[64];     /* decryption round keys */
    int nr;                    /* number of rounds */
} aes_context;

// Key scheduling function
int aes_set_key(aes_context *ctx, const unsigned char *key, int keysize);
```

**WebOTR Equivalent Implementation:**
```javascript
class AESContext {
  constructor() {
    this.encryptionRoundKeys = new Uint32Array(64);
    this.decryptionRoundKeys = new Uint32Array(64);
    this.numberOfRounds = 0;
  }

  setKey(key, keySize) {
    // Implement AES key schedule algorithm
    this.numberOfRounds = (keySize === 128) ? 10 : (keySize === 192) ? 12 : 14;
    this.expandKey(key, keySize);
  }

  expandKey(key, keySize) {
    // AES key expansion algorithm implementation
    // Must match libOTR's key schedule exactly
  }
}
```

#### Diffie-Hellman Parameter Extraction
```c
// libOTR DH parameters (dh.c)
static const char DH1536_MODULUS_S[] =
    "FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD1"
    "29024E088A67CC74020BBEA63B139B22514A08798E3404DD"
    // ... (complete 1536-bit prime)
    ;

static const char DH1536_GENERATOR_S[] = "2";
```

**Parameter Verification Requirements:**
- Exact prime modulus matching (1536-bit for OTR v2, 2048-bit for OTR v3)
- Generator value verification (g = 2)
- Subgroup order validation
- Safe prime property verification

#### DSA Implementation Details
```c
// libOTR DSA structure (dsa.h)
typedef struct {
    gcry_mpi_t p, q, g;        /* prime, subprime, generator */
    gcry_mpi_t y;              /* public key */
    gcry_mpi_t x;              /* private key */
} DSA_keypair;

// DSA signature generation
gcry_error_t otrl_dsa_sign(unsigned char signature[40],
                          const unsigned char hash[20], DSA_keypair *keypair);
```

**WebOTR DSA Implementation:**
```javascript
class DSAKeyPair {
  constructor() {
    this.p = null;  // Prime modulus
    this.q = null;  // Prime divisor
    this.g = null;  // Generator
    this.y = null;  // Public key
    this.x = null;  // Private key
  }

  async generateKeys() {
    // Generate DSA parameters matching libOTR
    // p: 1024-bit prime
    // q: 160-bit prime divisor of (p-1)
    // g: generator of order q
  }

  async sign(hash) {
    // DSA signature algorithm
    // Must produce identical signatures for same inputs
    const k = this.generateNonce();
    const r = this.g.modPow(k, this.p).mod(this.q);
    const s = k.modInverse(this.q).multiply(hash.add(this.x.multiply(r))).mod(this.q);
    return { r, s };
  }
}
```

### Protocol State Machine Detailed Analysis

#### State Transition Matrix
```
Current State    | Message Type      | Next State        | Action
-----------------|-------------------|-------------------|------------------
PLAINTEXT        | DH_COMMIT         | AWAITING_REVEALSIG| Send DH_KEY
PLAINTEXT        | QUERY             | AWAITING_DHKEY    | Send DH_COMMIT
AWAITING_DHKEY   | DH_KEY            | AWAITING_SIG      | Send REVEAL_SIG
AWAITING_REVEALSIG| REVEAL_SIGNATURE | AWAITING_SIG      | Send SIGNATURE
AWAITING_SIG     | SIGNATURE         | ENCRYPTED         | Complete AKE
ENCRYPTED        | DATA              | ENCRYPTED         | Process message
ENCRYPTED        | ERROR             | PLAINTEXT         | Handle error
```

#### Message Format Specifications

**DH Commit Message Structure:**
```c
// libOTR DH commit format (proto.c)
struct {
    unsigned short protocol_version;    // 0x0003 for OTR v3
    unsigned char message_type;         // 0x02 for DH_COMMIT
    unsigned int sender_instance;       // Sender instance tag
    unsigned int receiver_instance;     // Receiver instance tag
    unsigned char encrypted_gx[...];    // Encrypted DH public key
    unsigned char hashed_gx[32];        // SHA-256 hash of encrypted_gx
} dh_commit_message;
```

**WebOTR Message Implementation:**
```javascript
class DHCommitMessage {
  constructor(protocolVersion, senderInstance, receiverInstance) {
    this.protocolVersion = protocolVersion;
    this.messageType = MESSAGE_TYPE.DH_COMMIT;
    this.senderInstance = senderInstance;
    this.receiverInstance = receiverInstance;
    this.encryptedGx = null;
    this.hashedGx = null;
  }

  serialize() {
    const buffer = new ArrayBuffer(this.calculateSize());
    const view = new DataView(buffer);
    let offset = 0;

    view.setUint16(offset, this.protocolVersion, false); offset += 2;
    view.setUint8(offset, this.messageType); offset += 1;
    view.setUint32(offset, this.senderInstance, false); offset += 4;
    view.setUint32(offset, this.receiverInstance, false); offset += 4;

    // Add encrypted_gx and hashed_gx
    // Must match libOTR format exactly

    return new Uint8Array(buffer);
  }
}
```

### Socialist Millionaire Protocol (SMP) Analysis

#### SMP State Machine
```c
// libOTR SMP states (sm.h)
typedef enum {
    OTRL_SMP_EXPECT1,    /* Waiting for SMP1 message */
    OTRL_SMP_EXPECT2,    /* Waiting for SMP2 message */
    OTRL_SMP_EXPECT3,    /* Waiting for SMP3 message */
    OTRL_SMP_EXPECT4,    /* Waiting for SMP4 message */
    OTRL_SMP_EXPECT5     /* SMP completed */
} SMPEvent;
```

**SMP Message Types:**
- SMP1: Initial SMP message with g2a, c2, d2, g3a, c3, d3
- SMP2: Response with g2b, c2, d2, g3b, c3, d3, pb, qb
- SMP3: Third message with pa, qa, cp, d5, d6
- SMP4: Final message with rb, cr, d7

#### SMP Cryptographic Operations
```c
// libOTR SMP functions (sm.c)
gcry_error_t otrl_sm_step1(OtrlSMState *state, const unsigned char *secret,
                          int secretlen, unsigned char **output, int *outputlen);
gcry_error_t otrl_sm_step2a(OtrlSMState *state, const unsigned char *input,
                           int inputlen, int received_question);
```

**WebOTR SMP Implementation Requirements:**
```javascript
class SMPProtocol {
  constructor(secret) {
    this.state = SMP_STATE.EXPECT1;
    this.secret = secret;
    this.g1 = MODP_GROUP_14.G;
    this.p = MODP_GROUP_14.P;
  }

  async step1() {
    // Generate random exponents
    const a2 = this.generateRandomExponent();
    const a3 = this.generateRandomExponent();

    // Compute g2a = g1^a2, g3a = g1^a3
    const g2a = this.g1.modPow(a2, this.p);
    const g3a = this.g1.modPow(a3, this.p);

    // Generate zero-knowledge proofs
    const proof2 = this.generateZKProof(g2a, a2);
    const proof3 = this.generateZKProof(g3a, a3);

    return this.createSMP1Message(g2a, g3a, proof2, proof3);
  }
}
```

### Memory Management and Security

#### Secure Memory Handling
```c
// libOTR secure memory functions (mem.c)
void otrl_mem_clear(void *data, size_t len);
void *otrl_mem_alloc(size_t size);
void otrl_mem_free(void *ptr);
```

**WebOTR Secure Memory Implementation:**
```javascript
class SecureMemory {
  static clear(buffer) {
    if (buffer instanceof Uint8Array) {
      // Overwrite with random data multiple times
      for (let pass = 0; pass < 3; pass++) {
        crypto.getRandomValues(buffer);
      }
      // Final overwrite with zeros
      buffer.fill(0);
    }
  }

  static allocate(size) {
    const buffer = new Uint8Array(size);
    // Register for automatic cleanup
    this.registerForCleanup(buffer);
    return buffer;
  }

  static registerForCleanup(buffer) {
    // Use WeakRef and FinalizationRegistry for cleanup
    const cleanup = new FinalizationRegistry((buffer) => {
      this.clear(buffer);
    });
    cleanup.register(buffer, buffer);
  }
}
```

### Fragment Handling Implementation

#### Fragment Message Format
```c
// libOTR fragment structure (fragment.c)
typedef struct {
    unsigned short k;        /* Fragment number */
    unsigned short n;        /* Total fragments */
    char *fragment;          /* Fragment data */
} OtrlFragment;
```

**WebOTR Fragment Implementation:**
```javascript
class FragmentHandler {
  constructor() {
    this.fragments = new Map();
    this.maxFragmentSize = 1400; // MTU consideration
  }

  fragmentMessage(message, instanceTag) {
    const fragments = [];
    const totalLength = message.length;
    const fragmentCount = Math.ceil(totalLength / this.maxFragmentSize);

    for (let i = 0; i < fragmentCount; i++) {
      const start = i * this.maxFragmentSize;
      const end = Math.min(start + this.maxFragmentSize, totalLength);
      const fragmentData = message.slice(start, end);

      const fragment = this.createFragment(i + 1, fragmentCount, fragmentData, instanceTag);
      fragments.push(fragment);
    }

    return fragments;
  }

  createFragment(k, n, data, instanceTag) {
    // Format: ?OTR|instanceTag|k,n,fragmentData,
    return `?OTR|${instanceTag.toString(16)}|${k},${n},${data},`;
  }
}
```

This detailed analysis provides the foundation for complete libOTR compatibility while maintaining WebOTR's modern JavaScript implementation advantages.
