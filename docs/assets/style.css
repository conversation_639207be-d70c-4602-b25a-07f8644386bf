/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary: #6366f1;
    --primary-dark: #4f46e5;
    --secondary: #8b5cf6;
    --accent: #06b6d4;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    
    /* Grays */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Dark theme */
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --text-muted: #71717a;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent) 0%, var(--primary) 100%);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Typography */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
    
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;
    
    /* Border radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-normal: 250ms ease;
    --transition-slow: 350ms ease;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-sans);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: var(--transition-normal);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 700;
    font-size: 1.25rem;
}

.nav-logo {
    width: 32px;
    height: 32px;
}

.nav-menu {
    display: flex;
    gap: var(--space-xl);
    align-items: center;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.nav-link:hover {
    color: var(--text-primary);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: radial-gradient(ellipse at center, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3xl);
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid rgba(99, 102, 241, 0.2);
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-xl);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--primary);
    margin-bottom: var(--space-lg);
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--space-lg);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: var(--space-2xl);
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: var(--space-lg);
    margin-bottom: var(--space-3xl);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.hero-stats {
    display: flex;
    gap: var(--space-2xl);
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary);
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-top: var(--space-xs);
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.chat-demo {
    position: relative;
}

.chat-window {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    width: 400px;
}

.chat-header {
    background: var(--bg-tertiary);
    padding: var(--space-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-title {
    font-weight: 600;
}

.chat-status {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--success);
    font-size: 0.875rem;
}

.chat-messages {
    padding: var(--space-lg);
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.message {
    display: flex;
}

.message-sent {
    justify-content: flex-end;
}

.message-received {
    justify-content: flex-start;
}

.message-content {
    max-width: 80%;
    padding: var(--space-md);
    border-radius: var(--radius-lg);
    font-family: var(--font-mono);
    font-size: 0.875rem;
}

.message-content.encrypted {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: var(--error);
}

.message-content.decrypted {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: var(--success);
    font-family: var(--font-sans);
}

/* Sections */
.section-header {
    text-align: center;
    margin-bottom: var(--space-3xl);
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    margin-bottom: var(--space-lg);
}

.section-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features {
    padding: 100px 0;
    background: var(--bg-secondary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-2xl);
}

.feature-card {
    background: var(--bg-primary);
    padding: var(--space-2xl);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-5px);
    border-color: rgba(99, 102, 241, 0.3);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-lg);
    font-size: 1.5rem;
    color: white;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: var(--space-md);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* How It Works Section */
.how-it-works {
    padding: 100px 0;
}

.steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-2xl);
}

.step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 800;
    color: white;
    margin: 0 auto var(--space-lg);
}

.step-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: var(--space-md);
}

.step-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Platforms Section */
.platforms {
    padding: 100px 0;
    background: var(--bg-secondary);
}

.platforms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-xl);
}

.platform-card {
    background: var(--bg-primary);
    padding: var(--space-2xl);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    transition: var(--transition-normal);
}

.platform-card:hover {
    transform: translateY(-5px);
    border-color: rgba(99, 102, 241, 0.3);
}

.platform-icon {
    font-size: 3rem;
    color: var(--primary);
    margin-bottom: var(--space-lg);
}

.platform-name {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: var(--space-md);
}

.platform-status {
    color: var(--success);
    font-weight: 500;
}

/* Download Section */
.download {
    padding: 100px 0;
    background: var(--gradient-primary);
    text-align: center;
}

.download-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    margin-bottom: var(--space-lg);
    color: white;
}

.download-description {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--space-2xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.download-buttons {
    display: flex;
    justify-content: center;
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
    flex-wrap: wrap;
}

.download-btn {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: var(--space-lg) var(--space-xl);
    border-radius: var(--radius-lg);
    color: white;
    text-decoration: none;
    transition: var(--transition-normal);
    min-width: 180px;
}

.download-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.download-btn i {
    font-size: 2rem;
}

.download-text {
    font-size: 0.875rem;
    opacity: 0.9;
}

.download-browser {
    font-size: 1.125rem;
    font-weight: 600;
}

.download-note {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

.download-note a {
    color: white;
    text-decoration: underline;
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    padding: 60px 0 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--space-3xl);
    margin-bottom: var(--space-2xl);
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.footer-logo {
    width: 40px;
    height: 40px;
}

.footer-description {
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 300px;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-2xl);
}

.footer-title {
    font-weight: 600;
    margin-bottom: var(--space-lg);
}

.footer-link {
    display: block;
    color: var(--text-secondary);
    text-decoration: none;
    margin-bottom: var(--space-sm);
    transition: var(--transition-fast);
}

.footer-link:hover {
    color: var(--text-primary);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-xl);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-copyright {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.footer-copyright a {
    color: var(--primary);
    text-decoration: none;
}

.footer-social {
    display: flex;
    gap: var(--space-md);
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-link:hover {
    background: var(--primary);
    color: white;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: var(--bg-secondary);
        width: 100%;
        text-align: center;
        transition: var(--transition-normal);
        box-shadow: var(--shadow-lg);
        padding: var(--space-xl) 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-2xl);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .chat-window {
        width: 100%;
        max-width: 350px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .steps-container {
        grid-template-columns: 1fr;
    }
    
    .platforms-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .download-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: var(--space-lg);
        text-align: center;
    }
}

@media (max-width: 480px) {
    .platforms-grid {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 0 var(--space-md);
    }
    
    .nav-container {
        padding: 0 var(--space-md);
    }
}
