/* Documentation Specific Styles */

.docs-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    min-height: 100vh;
    padding-top: 70px;
}

/* Sidebar */
.docs-sidebar {
    background: var(--bg-secondary);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    position: fixed;
    top: 70px;
    left: 0;
    width: 280px;
    height: calc(100vh - 70px);
    overflow-y: auto;
    z-index: 100;
}

.sidebar-content {
    padding: var(--space-xl);
}

.sidebar-content h3 {
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-md);
    margin-top: var(--space-xl);
}

.sidebar-content h3:first-child {
    margin-top: 0;
}

.sidebar-content ul {
    list-style: none;
    margin-bottom: var(--space-lg);
}

.sidebar-content li {
    margin-bottom: var(--space-xs);
}

.sidebar-content a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    display: block;
    transition: var(--transition-fast);
}

.sidebar-content a:hover,
.sidebar-content a.active {
    color: var(--text-primary);
    background: rgba(99, 102, 241, 0.1);
}

/* Main Content */
.docs-main {
    margin-left: 280px;
    padding: var(--space-2xl);
    max-width: 800px;
}

.docs-content {
    line-height: 1.7;
}

.docs-header {
    margin-bottom: var(--space-3xl);
    padding-bottom: var(--space-xl);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.docs-header h1 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: var(--space-md);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.docs-header p {
    font-size: 1.125rem;
    color: var(--text-secondary);
}

/* Sections */
.docs-section {
    margin-bottom: var(--space-3xl);
}

.docs-section h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--space-lg);
    color: var(--text-primary);
}

.docs-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--space-md);
    margin-top: var(--space-xl);
    color: var(--text-primary);
}

.docs-section h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--space-md);
    margin-top: var(--space-lg);
    color: var(--text-primary);
}

.docs-section h5 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--space-sm);
    margin-top: var(--space-lg);
    color: var(--primary);
}

.docs-section p {
    margin-bottom: var(--space-md);
    color: var(--text-secondary);
}

.docs-section ul,
.docs-section ol {
    margin-bottom: var(--space-md);
    padding-left: var(--space-xl);
}

.docs-section li {
    margin-bottom: var(--space-xs);
    color: var(--text-secondary);
}

/* Code Blocks */
.code-block {
    background: var(--bg-tertiary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    margin: var(--space-lg) 0;
    overflow: hidden;
}

.code-header {
    background: rgba(255, 255, 255, 0.05);
    padding: var(--space-md) var(--space-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.875rem;
    font-weight: 500;
}

.copy-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.copy-btn:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.code-block pre {
    padding: var(--space-lg);
    margin: 0;
    overflow-x: auto;
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.5;
}

.code-block code {
    color: var(--text-primary);
    font-family: var(--font-mono);
}

/* Inline code */
:not(pre) > code {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary);
    padding: 0.125rem 0.375rem;
    border-radius: var(--radius-sm);
    font-family: var(--font-mono);
    font-size: 0.875em;
}

/* Step Guide */
.step-guide {
    display: grid;
    gap: var(--space-xl);
    margin: var(--space-xl) 0;
}

.step {
    display: flex;
    gap: var(--space-lg);
    align-items: flex-start;
}

.step-number {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: white;
    flex-shrink: 0;
}

.step-content h3 {
    margin-top: 0;
    margin-bottom: var(--space-sm);
}

/* Security Features */
.security-features {
    display: grid;
    gap: var(--space-xl);
    margin: var(--space-xl) 0;
}

.security-feature {
    display: flex;
    gap: var(--space-lg);
    align-items: flex-start;
    padding: var(--space-xl);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.security-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    flex-shrink: 0;
}

.security-content h3 {
    margin-top: 0;
    margin-bottom: var(--space-sm);
}

/* API Documentation */
.api-section {
    background: var(--bg-secondary);
    padding: var(--space-xl);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin: var(--space-lg) 0;
}

.method {
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.method:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.method h5 {
    font-family: var(--font-mono);
    background: rgba(99, 102, 241, 0.1);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-sm);
    margin-bottom: var(--space-md);
    font-size: 1rem;
}

.parameters,
.returns {
    margin-top: var(--space-md);
}

.parameters h6,
.returns h6 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.parameters ul {
    margin-top: var(--space-sm);
    margin-bottom: 0;
}

.parameters li {
    font-family: var(--font-mono);
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .docs-container {
        grid-template-columns: 1fr;
    }
    
    .docs-sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
    }
    
    .docs-sidebar.active {
        transform: translateX(0);
    }
    
    .docs-main {
        margin-left: 0;
        padding: var(--space-lg);
    }
    
    .sidebar-toggle {
        display: block;
        position: fixed;
        top: 80px;
        left: var(--space-lg);
        z-index: 1001;
        background: var(--primary);
        color: white;
        border: none;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        cursor: pointer;
        box-shadow: var(--shadow-lg);
    }
}

@media (max-width: 768px) {
    .docs-main {
        padding: var(--space-md);
    }
    
    .docs-header h1 {
        font-size: 2rem;
    }
    
    .docs-section h2 {
        font-size: 1.5rem;
    }
    
    .step {
        flex-direction: column;
        text-align: center;
    }
    
    .security-feature {
        flex-direction: column;
        text-align: center;
    }
    
    .code-block pre {
        font-size: 0.75rem;
    }
}

/* Smooth scrolling for documentation links */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 100px;
}

/* Highlight active section in sidebar */
.sidebar-content a.active {
    background: var(--primary);
    color: white;
}

/* Table styles for API documentation */
table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--space-lg) 0;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

th,
td {
    padding: var(--space-md);
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

th {
    background: rgba(255, 255, 255, 0.05);
    font-weight: 600;
    color: var(--text-primary);
}

td {
    color: var(--text-secondary);
}

tr:last-child td {
    border-bottom: none;
}

/* Blockquote styles */
blockquote {
    border-left: 4px solid var(--primary);
    padding: var(--space-md) var(--space-lg);
    margin: var(--space-lg) 0;
    background: rgba(99, 102, 241, 0.05);
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

blockquote p {
    margin: 0;
    font-style: italic;
}

/* Alert boxes */
.alert {
    padding: var(--space-lg);
    border-radius: var(--radius-lg);
    margin: var(--space-lg) 0;
    border-left: 4px solid;
}

.alert-info {
    background: rgba(6, 182, 212, 0.1);
    border-color: var(--accent);
    color: var(--accent);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: var(--warning);
    color: var(--warning);
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--error);
    color: var(--error);
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--success);
    color: var(--success);
}
