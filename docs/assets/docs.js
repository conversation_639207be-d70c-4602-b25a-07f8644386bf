// Documentation specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Sidebar navigation
    const sidebarLinks = document.querySelectorAll('.sidebar-content a');
    const sections = document.querySelectorAll('.docs-section');
    
    // Update active sidebar link based on scroll position
    function updateActiveLink() {
        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop - 100;
            const sectionHeight = section.offsetHeight;
            if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                current = section.getAttribute('id');
            }
        });
        
        sidebarLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
    }
    
    // Update on scroll
    window.addEventListener('scroll', updateActiveLink);
    updateActiveLink(); // Initial call
    
    // Smooth scrolling for sidebar links
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 90;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Mobile sidebar toggle
    if (window.innerWidth <= 1024) {
        const sidebar = document.querySelector('.docs-sidebar');
        const main = document.querySelector('.docs-main');
        
        // Create toggle button
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'sidebar-toggle';
        toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
        toggleBtn.setAttribute('aria-label', 'Toggle sidebar');
        document.body.appendChild(toggleBtn);
        
        toggleBtn.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            this.innerHTML = sidebar.classList.contains('active') 
                ? '<i class="fas fa-times"></i>' 
                : '<i class="fas fa-bars"></i>';
        });
        
        // Close sidebar when clicking outside
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !toggleBtn.contains(e.target)) {
                sidebar.classList.remove('active');
                toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
            }
        });
    }
    
    // Copy code functionality
    window.copyToClipboard = function(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            const text = element.textContent || element.innerText;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    showCopySuccess();
                }).catch(function() {
                    fallbackCopyToClipboard(text);
                });
            } else {
                fallbackCopyToClipboard(text);
            }
        }
    };
    
    function fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            showCopySuccess();
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }
        
        document.body.removeChild(textArea);
    }
    
    function showCopySuccess() {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = 'copy-toast';
        toast.innerHTML = '<i class="fas fa-check"></i> Copied to clipboard!';
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--success);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-weight: 500;
            box-shadow: var(--shadow-lg);
            display: flex;
            align-items: center;
            gap: 8px;
            animation: slideInRight 0.3s ease, fadeOut 0.3s ease 2.7s;
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
    
    // Add animations for copy toast
    const toastStyles = document.createElement('style');
    toastStyles.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes fadeOut {
            from {
                opacity: 1;
            }
            to {
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(toastStyles);
    
    // Syntax highlighting for code blocks (simple)
    const codeBlocks = document.querySelectorAll('pre code');
    codeBlocks.forEach(block => {
        let html = block.innerHTML;
        
        // Simple syntax highlighting patterns
        html = html.replace(/\/\/.*$/gm, '<span style="color: #6b7280; font-style: italic;">$&</span>');
        html = html.replace(/(['"`])((?:(?!\1)[^\\]|\\.)*)(\1)/g, '<span style="color: #10b981;">$&</span>');
        html = html.replace(/\b(const|let|var|function|class|if|else|for|while|return|import|export|from|async|await|try|catch|throw|new)\b/g, '<span style="color: #8b5cf6;">$&</span>');
        html = html.replace(/\b(true|false|null|undefined)\b/g, '<span style="color: #f59e0b;">$&</span>');
        html = html.replace(/\b\d+\b/g, '<span style="color: #06b6d4;">$&</span>');
        
        block.innerHTML = html;
    });
    
    // Add line numbers to code blocks
    const preElements = document.querySelectorAll('.code-block pre');
    preElements.forEach(pre => {
        const code = pre.querySelector('code');
        if (code) {
            const lines = code.textContent.split('\n');
            if (lines.length > 1) {
                const lineNumbers = document.createElement('div');
                lineNumbers.className = 'line-numbers';
                lineNumbers.style.cssText = `
                    position: absolute;
                    left: 0;
                    top: 0;
                    padding: var(--space-lg) var(--space-sm);
                    background: rgba(255, 255, 255, 0.05);
                    border-right: 1px solid rgba(255, 255, 255, 0.1);
                    color: var(--text-muted);
                    font-family: var(--font-mono);
                    font-size: 0.75rem;
                    line-height: 1.5;
                    user-select: none;
                    min-width: 40px;
                    text-align: right;
                `;
                
                for (let i = 1; i <= lines.length; i++) {
                    lineNumbers.innerHTML += i + '\n';
                }
                
                pre.style.position = 'relative';
                pre.style.paddingLeft = '60px';
                pre.insertBefore(lineNumbers, code);
            }
        }
    });
    
    // Table of contents generation
    const tocContainer = document.querySelector('.table-of-contents');
    if (tocContainer) {
        const headings = document.querySelectorAll('.docs-content h2, .docs-content h3');
        const tocList = document.createElement('ul');
        tocList.className = 'toc-list';
        
        headings.forEach(heading => {
            const li = document.createElement('li');
            const a = document.createElement('a');
            a.href = '#' + heading.id;
            a.textContent = heading.textContent;
            a.className = heading.tagName.toLowerCase() === 'h3' ? 'toc-sub' : 'toc-main';
            li.appendChild(a);
            tocList.appendChild(li);
        });
        
        tocContainer.appendChild(tocList);
    }
    
    // Search functionality (if search input exists)
    const searchInput = document.querySelector('.docs-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            const searchResults = document.querySelector('.search-results');
            
            if (query.length < 2) {
                if (searchResults) searchResults.style.display = 'none';
                return;
            }
            
            // Simple search implementation
            const allText = document.querySelector('.docs-content').textContent.toLowerCase();
            const sections = document.querySelectorAll('.docs-section');
            const results = [];
            
            sections.forEach(section => {
                const sectionText = section.textContent.toLowerCase();
                if (sectionText.includes(query)) {
                    const title = section.querySelector('h2').textContent;
                    const id = section.id;
                    results.push({ title, id, section });
                }
            });
            
            // Display results
            if (searchResults) {
                searchResults.innerHTML = '';
                if (results.length > 0) {
                    results.forEach(result => {
                        const resultItem = document.createElement('div');
                        resultItem.className = 'search-result-item';
                        resultItem.innerHTML = `<a href="#${result.id}">${result.title}</a>`;
                        searchResults.appendChild(resultItem);
                    });
                    searchResults.style.display = 'block';
                } else {
                    searchResults.innerHTML = '<div class="no-results">No results found</div>';
                    searchResults.style.display = 'block';
                }
            }
        });
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('.docs-search');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Escape to close search results
        if (e.key === 'Escape') {
            const searchResults = document.querySelector('.search-results');
            if (searchResults) {
                searchResults.style.display = 'none';
            }
        }
    });
    
    // Print styles
    const printStyles = document.createElement('style');
    printStyles.textContent = `
        @media print {
            .docs-sidebar,
            .navbar,
            .sidebar-toggle {
                display: none !important;
            }
            
            .docs-main {
                margin-left: 0 !important;
                max-width: none !important;
            }
            
            .docs-container {
                grid-template-columns: 1fr !important;
            }
            
            .code-block {
                break-inside: avoid;
            }
            
            .docs-section {
                break-inside: avoid;
            }
        }
    `;
    document.head.appendChild(printStyles);
});
