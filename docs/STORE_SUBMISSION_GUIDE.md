# Browser Store Submission Guide

This guide provides step-by-step instructions for submitting WebOTR to various browser extension stores.

## Prerequisites

Before submitting to any store, ensure you have:

- [ ] Completed extension build: `npm run build:extension`
- [ ] Prepared store packages: `node scripts/store-submission/prepare-stores.js`
- [ ] Created developer accounts for each store
- [ ] Prepared marketing materials (screenshots, descriptions, icons)
- [ ] Completed privacy policy and terms of service

## Store Submission Checklist

### 🟢 Chrome Web Store

**Developer Account**: [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)

**Requirements**:
- [ ] One-time $5 developer registration fee
- [ ] Manifest V3 compliance
- [ ] Privacy policy URL
- [ ] Detailed description (132 characters minimum)
- [ ] Screenshots (1280x800 or 640x400)
- [ ] Store icon (128x128)

**Submission Steps**:
1. Log into Chrome Web Store Developer Dashboard
2. Click "Add new item"
3. Upload `store-packages/chrome/webOTR-chrome.zip`
4. Fill in store listing details:
   - **Name**: WebOTR - Secure Messaging
   - **Summary**: Military-grade OTR encryption for chat platforms
   - **Description**: [Use detailed description from marketing materials]
   - **Category**: Productivity
   - **Language**: English (add others as needed)
5. Upload screenshots and promotional images
6. Set privacy policy URL
7. Submit for review

**Review Time**: 1-3 business days

### 🟠 Firefox Add-ons (AMO)

**Developer Account**: [Firefox Add-on Developer Hub](https://addons.mozilla.org/developers/)

**Requirements**:
- [ ] Free developer account
- [ ] Manifest V2/V3 compatibility
- [ ] Source code submission (if using minified code)
- [ ] Privacy policy
- [ ] Detailed description

**Submission Steps**:
1. Log into AMO Developer Hub
2. Click "Submit a New Add-on"
3. Upload `store-packages/firefox/webOTR-firefox.zip`
4. Choose distribution channel (AMO recommended)
5. Fill in listing details:
   - **Name**: WebOTR - Secure Messaging
   - **Summary**: Military-grade OTR encryption for chat platforms
   - **Description**: [Use detailed description]
   - **Categories**: Privacy & Security, Social & Communication
6. Upload screenshots
7. Submit for review

**Review Time**: 1-5 business days

### 🔵 Safari Extensions

**Developer Account**: [Apple Developer Program](https://developer.apple.com/programs/)

**Requirements**:
- [ ] $99/year Apple Developer Program membership
- [ ] macOS with Xcode
- [ ] App Store Connect access
- [ ] Safari Extension conversion

**Submission Steps**:
1. Convert extension using Safari Web Extension Converter:
   ```bash
   xcrun safari-web-extension-converter store-packages/safari/
   ```
2. Open generated Xcode project
3. Configure app settings and capabilities
4. Build and archive the app
5. Upload to App Store Connect
6. Fill in App Store listing details
7. Submit for review

**Review Time**: 1-7 business days

### 🟣 Edge Add-ons

**Developer Account**: [Microsoft Partner Center](https://partner.microsoft.com/dashboard/)

**Requirements**:
- [ ] Free Microsoft Partner Center account
- [ ] Manifest V3 compliance
- [ ] Privacy policy
- [ ] Detailed description

**Submission Steps**:
1. Log into Microsoft Partner Center
2. Go to "Edge Add-ons" section
3. Click "Create new extension"
4. Upload `store-packages/edge/webOTR-edge.zip`
5. Fill in store listing:
   - **Name**: WebOTR - Secure Messaging
   - **Short description**: Military-grade OTR encryption
   - **Description**: [Use detailed description]
   - **Category**: Productivity
6. Upload screenshots and icons
7. Set privacy policy URL
8. Submit for review

**Review Time**: 1-7 business days

## Marketing Materials

### Store Descriptions

**Short Description** (for summaries):
```
Military-grade OTR encryption for Discord, Slack, Teams, and WhatsApp Web. Zero-knowledge security with forward secrecy.
```

**Detailed Description**:
```
WebOTR brings military-grade Off-the-Record (OTR) encryption to your favorite chat platforms with zero-knowledge security and perfect forward secrecy.

🔐 FEATURES:
• End-to-end encryption for Discord, Slack, Teams, WhatsApp Web
• Perfect forward secrecy - past messages stay secure even if keys are compromised
• Zero-knowledge verification with QR codes and security questions
• DoD 5220.22-M compliant secure deletion
• Real-time security indicators
• Cross-platform compatibility

🛡️ SECURITY:
• AES-256-GCM encryption
• ChaCha20 key generation
• HMAC message authentication
• Secure key rotation
• Memory sanitization

🌟 USER EXPERIENCE:
• One-click setup
• Transparent operation
• Visual security indicators
• Accessibility compliant (WCAG 2.1 AA)
• Multi-language support

Perfect for privacy-conscious users, security professionals, and organizations requiring secure communications.
```

### Screenshots Required

1. **Main Interface**: Extension popup showing security status
2. **Chat Integration**: Encrypted message in Discord/Slack
3. **Verification Process**: QR code verification dialog
4. **Security Settings**: Extension options page
5. **Status Indicators**: Security indicators in chat interface

### Privacy Policy

Create a comprehensive privacy policy covering:
- Data collection (minimal - only necessary for functionality)
- Data usage and storage
- Third-party integrations
- User rights and controls
- Contact information

## Post-Submission

### Monitoring Reviews
- Check store dashboards daily for review status
- Respond promptly to reviewer questions
- Address any compliance issues quickly

### Update Process
1. Increment version number in manifest.json
2. Rebuild extension packages
3. Upload updated packages to stores
4. Update store listings if needed

### Analytics and Feedback
- Monitor download statistics
- Track user reviews and ratings
- Collect user feedback for improvements
- Plan regular updates and feature additions

## Troubleshooting Common Issues

### Chrome Web Store
- **Manifest V3 compliance**: Ensure no Manifest V2 features
- **Permissions justification**: Provide clear explanations for all permissions
- **Content Security Policy**: Avoid inline scripts and unsafe-eval

### Firefox Add-ons
- **Source code review**: Be prepared to provide unminified source code
- **API compatibility**: Test with latest Firefox versions
- **Performance impact**: Minimize memory and CPU usage

### Safari Extensions
- **macOS compatibility**: Test on multiple macOS versions
- **App Store guidelines**: Follow Apple's strict review guidelines
- **Native app wrapper**: Ensure proper iOS/macOS integration

### Edge Add-ons
- **Microsoft compliance**: Follow Microsoft's security requirements
- **Enterprise features**: Consider enterprise deployment options
- **Windows integration**: Test with Windows security features

## Support and Resources

- **Chrome**: [Chrome Web Store Developer Support](https://support.google.com/chrome_webstore/)
- **Firefox**: [Firefox Add-on Developer Support](https://extensionworkshop.com/)
- **Safari**: [Safari Extensions Development](https://developer.apple.com/safari/extensions/)
- **Edge**: [Microsoft Edge Extensions Support](https://docs.microsoft.com/en-us/microsoft-edge/extensions-chromium/)

---

**Next Steps**: After successful store submissions, monitor reviews and prepare for production deployment scaling.
