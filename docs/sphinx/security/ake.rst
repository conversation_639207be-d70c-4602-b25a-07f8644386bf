Authenticated Key Exchange (AKE)
==================================

WebOTR's Authenticated Key Exchange (AKE) implementation provides secure, authenticated establishment of cryptographic keys between two parties. This implementation follows the OTR protocol specification with enhanced security features and performance optimizations.

.. contents:: Table of Contents
   :local:
   :depth: 3

Overview
--------

The AKE protocol enables two parties to establish a secure communication channel with mutual authentication and perfect forward secrecy. WebOTR's implementation combines Di<PERSON><PERSON>-<PERSON><PERSON> key exchange with digital signatures to provide both confidentiality and authenticity.

.. mermaid::

   graph TB
       A[Alice] --> B[DH Commit Message]
       B --> C[Bob]
       C --> D[DH Key Message]
       D --> A
       A --> E[Reveal Signature Message]
       E --> C
       C --> F[Signature Message]
       F --> A
       A --> G[Encrypted Communication]
       C --> G
       
       style A fill:#e1f5fe
       style C fill:#f3e5f5
       style G fill:#e8f5e8

Protocol Architecture
---------------------

Core Components
~~~~~~~~~~~~~~~

**AKE Protocol Engine**
   Manages the four-message AKE handshake with state transitions and error handling.

**Diffie-Hellman Key Exchange**
   Provides ephemeral key generation and shared secret computation using MODP groups.

**Digital Signature Authentication**
   Uses DSA/ECDSA signatures for mutual authentication and non-repudiation.

**Key Derivation Functions**
   Derives session keys from shared secrets using HKDF with SHA-256.

**State Management**
   Tracks protocol state transitions and maintains security context.

AKE Protocol Flow
-----------------

Four-Message Handshake
~~~~~~~~~~~~~~~~~~~~~~

The AKE protocol consists of four messages that establish authenticated encryption:

.. mermaid::

   sequenceDiagram
       participant Alice
       participant Bob
       
       Note over Alice: Generate DH key pair (x, g^x)
       Alice->>Bob: 1. DH Commit (Enc(g^x), Hash(Enc(g^x)))
       
       Note over Bob: Generate DH key pair (y, g^y)
       Bob->>Alice: 2. DH Key (g^y)
       
       Note over Alice: Compute shared secret s = (g^y)^x
       Note over Alice: Derive session keys from s
       Alice->>Bob: 3. Reveal Signature (r, Sig_A(g^x, g^y, s))
       
       Note over Bob: Verify Alice's signature
       Note over Bob: Compute shared secret s = (g^x)^y
       Note over Bob: Derive session keys from s
       Bob->>Alice: 4. Signature (Sig_B(g^x, g^y, s))
       
       Note over Alice: Verify Bob's signature
       Note over Alice,Bob: Secure communication established

Message Structure
~~~~~~~~~~~~~~~~~

**1. DH Commit Message**

.. code-block:: javascript

   {
     protocolVersion: 3,
     messageType: 'DH_COMMIT',
     senderInstanceTag: 0x12345678,
     receiverInstanceTag: 0x00000000,
     encryptedGx: Uint8Array, // AES-encrypted g^x
     hashOfEncryptedGx: Uint8Array // SHA-256 hash
   }

**2. DH Key Message**

.. code-block:: javascript

   {
     protocolVersion: 3,
     messageType: 'DH_KEY',
     senderInstanceTag: 0x87654321,
     receiverInstanceTag: 0x12345678,
     publicKey: Uint8Array // g^y
   }

**3. Reveal Signature Message**

.. code-block:: javascript

   {
     protocolVersion: 3,
     messageType: 'REVEAL_SIGNATURE',
     senderInstanceTag: 0x12345678,
     receiverInstanceTag: 0x87654321,
     revealedKey: Uint8Array, // AES key from step 1
     encryptedSignature: Uint8Array, // Encrypted DSA signature
     macKey: Uint8Array // HMAC for integrity
   }

**4. Signature Message**

.. code-block:: javascript

   {
     protocolVersion: 3,
     messageType: 'SIGNATURE',
     senderInstanceTag: 0x87654321,
     receiverInstanceTag: 0x12345678,
     encryptedSignature: Uint8Array, // Encrypted DSA signature
     macKey: Uint8Array // HMAC for integrity
   }

State Machine
~~~~~~~~~~~~~

The AKE protocol uses a state machine to track progress:

.. mermaid::

   stateDiagram-v2
       [*] --> PLAINTEXT
       PLAINTEXT --> AWAITING_DHKEY : Send DH Commit
       PLAINTEXT --> AWAITING_REVEALSIG : Receive DH Commit
       
       AWAITING_DHKEY --> AWAITING_SIG : Receive DH Key
       AWAITING_REVEALSIG --> AWAITING_SIG : Send Reveal Signature
       
       AWAITING_SIG --> ENCRYPTED : Receive/Send Signature
       
       ENCRYPTED --> FINISHED : End Session
       FINISHED --> PLAINTEXT : Reset
       
       ENCRYPTED --> PLAINTEXT : Error/Reset

Cryptographic Implementation
----------------------------

Diffie-Hellman Key Exchange
~~~~~~~~~~~~~~~~~~~~~~~~~~~

WebOTR uses the MODP (Modular Exponentiation) group for DH key exchange:

.. code-block:: javascript

   // MODP Group 14 (2048-bit)
   const MODP_GROUP = {
     P: new Uint8Array([...]), // 2048-bit prime
     G: new Uint8Array([0x02]) // Generator = 2
   };
   
   // Generate DH key pair
   async function generateDHKeyPair() {
     const privateKey = generateRandomBytes(256); // 2048-bit private key
     const publicKey = modularExponentiation(G, privateKey, P);
     
     return {
       privateKey,
       publicKey
     };
   }
   
   // Compute shared secret
   async function dhExchange(privateKey, theirPublicKey) {
     return modularExponentiation(theirPublicKey, privateKey, P);
   }

Digital Signatures
~~~~~~~~~~~~~~~~~~

Authentication uses DSA or ECDSA signatures:

.. code-block:: javascript

   // Create signature for authentication
   async function createSignature(dhKeyPair, dsaKeyPair, sharedSecret, instanceTag) {
     const signatureData = {
       publicKey: dhKeyPair.publicKey,
       dhSharedSecret: sharedSecret,
       instanceTag: instanceTag
     };
     
     const signature = await sign(
       JSON.stringify(signatureData),
       dsaKeyPair.privateKey
     );
     
     return signature;
   }
   
   // Verify signature for authentication
   async function verifySignature(signature, publicKey, data) {
     return await verify(data, signature, publicKey);
   }

Key Derivation
~~~~~~~~~~~~~~

Session keys are derived using HKDF (HMAC-based Key Derivation Function):

.. code-block:: javascript

   async function deriveKeys(sharedSecret) {
     // Use HKDF to derive multiple keys from shared secret
     const salt = new Uint8Array(32); // Zero salt
     const info = new TextEncoder().encode('WebOTR-v3-key-derivation');
     
     // Derive 128 bytes of key material
     const keyMaterial = await hkdf(sharedSecret, salt, info, 128);
     
     return {
       sendingAESKey: keyMaterial.slice(0, 32),    // 256-bit AES key
       receivingAESKey: keyMaterial.slice(32, 64), // 256-bit AES key
       sendingMACKey: keyMaterial.slice(64, 96),   // 256-bit HMAC key
       receivingMACKey: keyMaterial.slice(96, 128), // 256-bit HMAC key
       ssid: keyMaterial.slice(0, 8) // 64-bit session ID
     };
   }

Implementation Details
----------------------

AKE Protocol Engine
~~~~~~~~~~~~~~~~~~~

The main AKE implementation provides a complete protocol engine:

.. code-block:: javascript

   import { 
     createDHCommit,
     createDHKey,
     createRevealSignature,
     createSignature,
     processDHCommit,
     processDHKey,
     processRevealSignature,
     processSignature,
     startAKE
   } from 'webOTR/core/protocol/ake';

Message Creation Functions
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Create DH Commit Message**

.. code-block:: javascript

   async function createDHCommit(dhKeyPair, protocolVersion, instanceTag, receiverInstanceTag) {
     // Generate random AES key and IV for encryption
     const aesKey = generateRandomBytes(32);
     const iv = generateRandomBytes(16);
     
     // Encrypt our DH public key
     const encryptedGx = await encrypt(dhKeyPair.publicKey, aesKey, iv);
     
     // Create hash of encrypted public key
     const hashOfEncryptedGx = await sha256(encryptedGx);
     
     return {
       protocolVersion,
       messageType: MESSAGE_TYPE.DH_COMMIT,
       senderInstanceTag: instanceTag,
       receiverInstanceTag,
       encryptedGx,
       hashOfEncryptedGx,
       aesKey, // Stored for later reveal
       iv      // Stored for later reveal
     };
   }

**Create DH Key Message**

.. code-block:: javascript

   async function createDHKey(dhKeyPair, protocolVersion, instanceTag, receiverInstanceTag) {
     return {
       protocolVersion,
       messageType: MESSAGE_TYPE.DH_KEY,
       senderInstanceTag: instanceTag,
       receiverInstanceTag,
       publicKey: dhKeyPair.publicKey
     };
   }

Message Processing Functions
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Process DH Commit**

.. code-block:: javascript

   async function processDHCommit(message, state) {
     // Store the DH commit for later verification
     state.dhCommitMessage = message;
     
     // Generate our DH key pair
     if (!state.dhKeyPair) {
       state.dhKeyPair = await generateDHKeyPair();
     }
     
     // Create DH key response
     const dhKeyMessage = await createDHKey(
       state.dhKeyPair,
       state.protocolVersion,
       state.instanceTag,
       message.senderInstanceTag
     );
     
     // Update state
     state.handleDHCommit();
     
     return {
       state,
       message: dhKeyMessage
     };
   }

**Process DH Key**

.. code-block:: javascript

   async function processDHKey(message, state) {
     // Store their public key
     state.theirPublicKey = message.publicKey;
     
     // Compute shared secret
     const sharedSecret = await dhExchange(
       state.dhKeyPair.privateKey,
       message.publicKey
     );
     
     // Derive session keys
     const keys = await deriveKeys(sharedSecret);
     
     // Create reveal signature message
     const revealSignatureMessage = await createRevealSignature(
       state.dhKeyPair,
       state.dsaKeyPair,
       sharedSecret,
       state.protocolVersion,
       state.instanceTag,
       message.senderInstanceTag,
       state.dhCommitMessage.aesKey,
       state.dhCommitMessage.iv
     );
     
     // Store keys and update state
     state.setSessionKeys(keys);
     state.handleDHKey();
     
     return {
       state,
       message: revealSignatureMessage
     };
   }

Security Features
-----------------

Perfect Forward Secrecy
~~~~~~~~~~~~~~~~~~~~~~~

The AKE protocol provides perfect forward secrecy through:

- **Ephemeral DH Keys**: New key pairs generated for each session
- **Secure Key Deletion**: Old keys are securely erased after use
- **No Long-term Key Reuse**: Session keys are never reused

Mutual Authentication
~~~~~~~~~~~~~~~~~~~~~

Both parties authenticate each other through:

- **Digital Signatures**: DSA/ECDSA signatures prove identity
- **Key Binding**: Signatures include DH public keys and shared secret
- **Replay Protection**: Instance tags prevent replay attacks

Deniable Authentication
~~~~~~~~~~~~~~~~~~~~~~~

The protocol provides deniable authentication:

- **Malleable Signatures**: Signatures can be forged after the fact
- **No Non-repudiation**: Participants cannot prove messages to third parties
- **Plausible Deniability**: Messages could have been sent by either party

Performance Optimizations
--------------------------

Cryptographic Optimizations
~~~~~~~~~~~~~~~~~~~~~~~~~~~

- **Web Crypto API**: Uses browser's native crypto when available
- **Fallback Implementation**: Pure JavaScript for compatibility
- **Efficient Modular Arithmetic**: Optimized big integer operations
- **Key Caching**: Reuses expensive computations when possible

Protocol Optimizations
~~~~~~~~~~~~~~~~~~~~~~

- **Parallel Processing**: Concurrent signature verification and key derivation
- **Early Validation**: Input validation before expensive operations
- **State Caching**: Efficient state management and transitions
- **Memory Management**: Secure cleanup of sensitive data

Error Handling
--------------

The AKE implementation includes comprehensive error handling:

**Protocol Errors**

.. code-block:: javascript

   try {
     const result = await processDHCommit(message, state);
   } catch (error) {
     if (error instanceof AKEProtocolError) {
       // Handle protocol-specific errors
       console.error('AKE Protocol Error:', error.code, error.message);
       state.goPlaintext(); // Reset to plaintext state
     }
   }

**Cryptographic Errors**

.. code-block:: javascript

   try {
     const sharedSecret = await dhExchange(privateKey, publicKey);
   } catch (error) {
     if (error instanceof CryptographicError) {
       // Handle crypto failures
       console.error('Cryptographic Error:', error.message);
       throw new AKEError('Key exchange failed', 'CRYPTO_ERROR');
     }
   }

**State Validation**

.. code-block:: javascript

   function validateState(state, expectedState) {
     if (state.getState() !== expectedState) {
       throw new AKEError(
         `Invalid state: expected ${expectedState}, got ${state.getState()}`,
         'INVALID_STATE'
       );
     }
   }

Testing and Validation
----------------------

The AKE implementation includes comprehensive test suites:

**Unit Tests**

.. code-block:: javascript

   describe('AKE Protocol', () => {
     test('should complete full AKE handshake', async () => {
       const alice = new OtrState();
       const bob = new OtrState();
       
       // Alice starts AKE
       const akeStart = await startAKE(alice);
       
       // Bob processes DH commit
       const dhKeyResponse = await processDHCommit(akeStart.dhCommit, bob);
       
       // Alice processes DH key
       const revealSigResponse = await processDHKey(dhKeyResponse.message, alice);
       
       // Bob processes reveal signature
       const sigResponse = await processRevealSignature(revealSigResponse.message, bob);
       
       // Alice processes signature
       await processSignature(sigResponse.message, alice);
       
       // Both parties should be in encrypted state
       expect(alice.getState()).toBe(STATE.ENCRYPTED);
       expect(bob.getState()).toBe(STATE.ENCRYPTED);
     });
   });

**Integration Tests**

.. code-block:: javascript

   describe('AKE Integration', () => {
     test('should establish secure communication', async () => {
       const session1 = new OTRSession();
       const session2 = new OTRSession();
       
       // Establish AKE
       await establishAKE(session1, session2);
       
       // Test encrypted communication
       const plaintext = 'Hello, secure world!';
       const encrypted = await session1.encrypt(plaintext);
       const decrypted = await session2.decrypt(encrypted);
       
       expect(decrypted).toBe(plaintext);
     });
   });

This comprehensive AKE implementation provides secure, authenticated key exchange with perfect forward secrecy, mutual authentication, and deniable authentication properties essential for private communication.
