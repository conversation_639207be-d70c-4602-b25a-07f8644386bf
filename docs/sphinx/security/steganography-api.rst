Steganography API Reference
============================

Complete API documentation for WebOTR's steganography system components.

.. contents:: Table of Contents
   :local:
   :depth: 3

SteganographyEngine
-------------------

The main steganography engine for hiding and revealing messages in images.

Constructor
~~~~~~~~~~~

.. code-block:: javascript

   new SteganographyEngine(options)

**Parameters:**

- ``options`` (Object): Configuration options

  - ``method`` (string): Steganographic method ('LSB_ALPHA', 'ADAPTIVE_LSB') (default: 'LSB_ALPHA')
  - ``bitsPerPixel`` (number): Bits to use per pixel (default: 1)
  - ``compressionLevel`` (number): Compression level 0-9 (default: 0)
  - ``noiseInjection`` (boolean): Enable statistical noise injection (default: true)
  - ``adaptiveLSB`` (boolean): Enable adaptive LSB positioning (default: true)
  - ``antiDetection`` (boolean): Enable anti-detection measures (default: true)
  - ``useWebWorkers`` (boolean): Use web workers for processing (default: true)
  - ``chunkSize`` (number): Processing chunk size (default: 100000)
  - ``progressiveProcessing`` (boolean): Enable progressive processing (default: true)

Methods
~~~~~~~

hideMessage()
^^^^^^^^^^^^^

.. code-block:: javascript

   async hideMessage(coverImage, message, options = {})

Hide a message within a cover image using steganography.

**Parameters:**

- ``coverImage`` (Blob|File|ImageData): Cover image for hiding the message
- ``message`` (string|Uint8Array): Message to hide
- ``options`` (Object): Hiding options

  - ``password`` (string): Optional password for encryption
  - ``format`` (string): Output format ('PNG', 'BMP') (default: 'PNG')
  - ``quality`` (number): Output quality 0-1 (default: 1.0)
  - ``sessionKey`` (Uint8Array): Session key for adaptive positioning

**Returns:** Promise<Blob>

.. code-block:: javascript

   {
     // Stego image blob with hidden message
   }

**Example:**

.. code-block:: javascript

   const stego = new SteganographyEngine();
   const coverImage = await loadImage('cover.png');
   const stegoImage = await stego.hideMessage(coverImage, 'Secret message');

revealMessage()
^^^^^^^^^^^^^^^

.. code-block:: javascript

   async revealMessage(stegoImage, options = {})

Extract a hidden message from a steganographic image.

**Parameters:**

- ``stegoImage`` (Blob|File|ImageData): Steganographic image containing hidden message
- ``options`` (Object): Extraction options

  - ``password`` (string): Password for decryption if used during hiding
  - ``sessionKey`` (Uint8Array): Session key for adaptive positioning
  - ``maxLength`` (number): Maximum expected message length

**Returns:** Promise<string|Uint8Array|null>

.. code-block:: javascript

   // Returns hidden message or null if none found
   "Secret message"

detectMessage()
^^^^^^^^^^^^^^^

.. code-block:: javascript

   async detectMessage(image)

Detect if an image contains a hidden message.

**Parameters:**

- ``image`` (Blob|File|ImageData): Image to analyze

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     hasMessage: boolean,        // True if message detected
     confidence: number,         // Confidence level 0-1
     estimatedLength: number,    // Estimated message length
     method: string             // Detected steganographic method
   }

analyzeCapacity()
^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async analyzeCapacity(image)

Analyze the steganographic capacity of an image.

**Parameters:**

- ``image`` (Blob|File|ImageData): Image to analyze

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     totalPixels: number,        // Total pixels in image
     usablePixels: number,       // Pixels suitable for embedding
     maxCapacity: number,        // Maximum bytes that can be hidden
     recommendedCapacity: number, // Recommended capacity for security
     qualityScore: number        // Image quality score 0-1
   }

OTRSteganographySession
-----------------------

OTR session with integrated steganography capabilities.

Constructor
~~~~~~~~~~~

.. code-block:: javascript

   new OTRSteganographySession(options)

**Parameters:**

- ``options`` (Object): Session configuration

  - ``steganography`` (Object): Steganography configuration
  - ``autoSelectCover`` (boolean): Automatically select cover images (default: true)
  - ``multiImageSupport`` (boolean): Enable multi-image distribution (default: true)
  - ``coverImageDatabase`` (string): Path to cover image database

Methods
~~~~~~~

sendStegoMessage()
^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async sendStegoMessage(plaintext, coverImage = null, options = {})

Send an encrypted message hidden in an image.

**Parameters:**

- ``plaintext`` (string): Message to send
- ``coverImage`` (Blob|File): Optional cover image (auto-selected if null)
- ``options`` (Object): Sending options

**Returns:** Promise<Blob|Array<Blob>>

.. code-block:: javascript

   // Returns stego image(s) ready for transmission
```

processStegoImage()
^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async processStegoImage(stegoImage)

Process an incoming steganographic image and extract any hidden OTR message.

**Parameters:**

- ``stegoImage`` (Blob|File): Steganographic image to process

**Returns:** Promise<string|null>

.. code-block:: javascript

   // Returns decrypted message or null if none found
   "Decrypted message content"

CoverImageManager
-----------------

Manages cover image selection and generation.

Constructor
~~~~~~~~~~~

.. code-block:: javascript

   new CoverImageManager(options)

**Parameters:**

- ``options`` (Object): Manager configuration

  - ``databasePath`` (string): Path to cover image database
  - ``generatorEnabled`` (boolean): Enable synthetic cover generation (default: true)
  - ``analysisEnabled`` (boolean): Enable cover image analysis (default: true)

Methods
~~~~~~~

selectOptimalCover()
^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async selectOptimalCover(messageSize, requirements = {})

Select the optimal cover image for a given message.

**Parameters:**

- ``messageSize`` (number): Size of message to hide in bytes
- ``requirements`` (Object): Cover image requirements

  - ``minDimensions`` (Object): Minimum width/height
  - ``maxFileSize`` (number): Maximum file size in bytes
  - ``format`` (string): Required format ('PNG', 'BMP')
  - ``style`` (string): Image style preference

**Returns:** Promise<Blob>

analyzeCover()
^^^^^^^^^^^^^^

.. code-block:: javascript

   async analyzeCover(image)

Analyze a cover image for steganographic suitability.

**Parameters:**

- ``image`` (Blob|File): Cover image to analyze

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     capacity: number,           // Steganographic capacity in bytes
     complexity: number,         // Image complexity score 0-1
     noiseLevel: number,         // Natural noise level 0-1
     suitabilityScore: number,   // Overall suitability 0-1
     recommendations: Array      // Improvement recommendations
   }

generateCover()
^^^^^^^^^^^^^^^

.. code-block:: javascript

   async generateCover(specifications)

Generate a synthetic cover image.

**Parameters:**

- ``specifications`` (Object): Generation specifications

  - ``width`` (number): Image width
  - ``height`` (number): Image height
  - ``style`` (string): Generation style ('natural', 'abstract', 'texture')
  - ``complexity`` (number): Desired complexity level 0-1
  - ``format`` (string): Output format

**Returns:** Promise<Blob>

StatisticalSecurity
-------------------

Provides statistical security measures for steganography.

Constructor
~~~~~~~~~~~

.. code-block:: javascript

   new StatisticalSecurity(sessionKey)

**Parameters:**

- ``sessionKey`` (Uint8Array): Session key for deterministic randomness

Methods
~~~~~~~

enhanceSecurityBeforeEmbedding()
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async enhanceSecurityBeforeEmbedding(imageData, messageData)

Apply security enhancements before message embedding.

**Parameters:**

- ``imageData`` (ImageData): Cover image data
- ``messageData`` (Uint8Array): Message data to hide

**Returns:** Promise<Object>

.. code-block:: javascript

   {
     imageData: ImageData,       // Enhanced image data
     messageData: Uint8Array,    // Processed message data
     parameters: Object          // Embedding parameters
   }

injectStatisticalNoise()
^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async injectStatisticalNoise(imageData, intensity = 0.1)

Inject statistical noise to mask steganographic signatures.

**Parameters:**

- ``imageData`` (ImageData): Image data to modify
- ``intensity`` (number): Noise intensity 0-1

**Returns:** Promise<void>

generateEmbeddingPositions()
^^^^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   generateEmbeddingPositions(imageData, bitCount)

Generate adaptive embedding positions based on image characteristics.

**Parameters:**

- ``imageData`` (ImageData): Image data for analysis
- ``bitCount`` (number): Number of bit positions needed

**Returns:** Array<number>

.. code-block:: javascript

   // Array of pixel positions for embedding
   [1234, 5678, 9012, ...]

PlatformIntegration
-------------------

Handles integration with various platforms and services.

Constructor
~~~~~~~~~~~

.. code-block:: javascript

   new PlatformIntegration(options)

**Parameters:**

- ``options`` (Object): Integration configuration

  - ``enabledPlatforms`` (Array): List of enabled platforms
  - ``autoDetection`` (boolean): Enable automatic platform detection
  - ``uploadInterception`` (boolean): Intercept image uploads

Methods
~~~~~~~

detectPlatform()
^^^^^^^^^^^^^^^^

.. code-block:: javascript

   detectPlatform()

Detect the current platform/website.

**Returns:** string

.. code-block:: javascript

   // Platform identifier
   "facebook.com"

interceptUpload()
^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async interceptUpload(fileInput, platform)

Intercept image upload and optionally apply steganography.

**Parameters:**

- ``fileInput`` (HTMLInputElement): File input element
- ``platform`` (string): Platform identifier

**Returns:** Promise<Array<File>>

processDownloadedImages()
^^^^^^^^^^^^^^^^^^^^^^^^^

.. code-block:: javascript

   async processDownloadedImages()

Process images on the current page for hidden messages.

**Returns:** Promise<Array<Object>>

.. code-block:: javascript

   [
     {
       element: HTMLImageElement,  // Image element
       hasMessage: boolean,        // Whether message was found
       message: string            // Extracted message (if any)
     }
   ]

Error Classes
-------------

SteganographyError
~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   class SteganographyError extends Error {
     constructor(message, code, details = {})
   }

**Properties:**

- ``message`` (string): Error message
- ``code`` (string): Error code
- ``details`` (Object): Additional error details
- ``timestamp`` (number): Error timestamp

**Error Codes:**

- ``INVALID_IMAGE``: Invalid or unsupported image format
- ``INSUFFICIENT_CAPACITY``: Image too small for message
- ``MESSAGE_TOO_LARGE``: Message exceeds capacity limits
- ``EXTRACTION_FAILED``: Failed to extract hidden message
- ``INVALID_FORMAT``: Unsupported image format
- ``SECURITY_VIOLATION``: Security constraint violation

Constants
---------

Steganographic Methods
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   const STEGO_METHODS = {
     LSB_ALPHA: 'LSB_ALPHA',           // LSB in alpha channel
     LSB_RGB: 'LSB_RGB',               // LSB in RGB channels
     ADAPTIVE_LSB: 'ADAPTIVE_LSB',     // Adaptive LSB positioning
     DCT: 'DCT',                       // DCT domain hiding
     SPREAD_SPECTRUM: 'SPREAD_SPECTRUM' // Spread spectrum method
   };

Image Formats
~~~~~~~~~~~~~

.. code-block:: javascript

   const SUPPORTED_FORMATS = {
     PNG: 'image/png',
     BMP: 'image/bmp',
     TIFF: 'image/tiff'
   };

Quality Metrics
~~~~~~~~~~~~~~~

.. code-block:: javascript

   const QUALITY_THRESHOLDS = {
     PSNR_MIN: 40,                     // Minimum PSNR (dB)
     SSIM_MIN: 0.95,                   // Minimum SSIM
     FILE_SIZE_INCREASE_MAX: 0.05      // Maximum 5% size increase
   };

Type Definitions
----------------

StegoMessage
~~~~~~~~~~~~

.. code-block:: typescript

   interface StegoMessage {
     header: {
       magic: number;                  // Magic number for identification
       version: number;                // Format version
       length: number;                 // Message length
       checksum: number;               // Message checksum
       flags: number;                  // Feature flags
     };
     payload: Uint8Array;              // Actual message data
   }

CoverAnalysis
~~~~~~~~~~~~~

.. code-block:: typescript

   interface CoverAnalysis {
     capacity: number;                 // Steganographic capacity
     complexity: number;               // Image complexity score
     noiseLevel: number;               // Natural noise level
     suitabilityScore: number;         // Overall suitability
     recommendations: string[];        // Improvement suggestions
   }

EmbeddingParameters
~~~~~~~~~~~~~~~~~~~

.. code-block:: typescript

   interface EmbeddingParameters {
     method: string;                   // Steganographic method
     positions: number[];              // Embedding positions
     noiseIntensity: number;           // Statistical noise level
     adaptiveSettings: Object;         // Adaptive algorithm settings
   }

Usage Examples
--------------

Basic Steganography
~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // Initialize steganography engine
   const stego = new SteganographyEngine({
     method: 'LSB_ALPHA',
     noiseInjection: true
   });
   
   // Hide message
   const coverImage = await loadImage('cover.png');
   const stegoImage = await stego.hideMessage(coverImage, 'Secret message');
   
   // Reveal message
   const hiddenMessage = await stego.revealMessage(stegoImage);
   console.log('Hidden message:', hiddenMessage);

OTR Integration
~~~~~~~~~~~~~~~

.. code-block:: javascript

   // Create OTR session with steganography
   const session = new OTRSteganographySession({
     steganography: {
       autoSelectCover: true,
       multiImageSupport: true
     }
   });
   
   // Send encrypted message via steganography
   const stegoImage = await session.sendStegoMessage('Hello, world!');
   
   // Process incoming stego image
   const decryptedMessage = await session.processStegoImage(stegoImage);

Platform Integration
~~~~~~~~~~~~~~~~~~~~

.. code-block:: javascript

   // Set up platform integration
   const platform = new PlatformIntegration({
     enabledPlatforms: ['facebook.com', 'instagram.com'],
     autoDetection: true
   });
   
   // Process images on current page
   const results = await platform.processDownloadedImages();
   results.forEach(result => {
     if (result.hasMessage) {
       console.log('Found hidden message:', result.message);
     }
   });

This API reference provides complete documentation for all steganography system components, methods, and data structures.
