Steganography Implementation
============================

WebOTR's steganography system enables covert communication by hiding encrypted OTR messages inside innocent-looking images. This implementation provides true steganographic security while maintaining full OTR protocol guarantees.

.. contents:: Table of Contents
   :local:
   :depth: 3

Overview
--------

The steganography system allows users to communicate securely through image sharing on social media platforms, email, and file sharing services. By embedding encrypted OTR messages within image data, communications appear as normal image sharing while providing military-grade security.

.. mermaid::

   graph TB
       A[Plaintext Message] --> B[OTR Encryption]
       B --> C[Base64 Encoding]
       C --> D[Steganographic Embedding]
       D --> E[Stego Image]
       
       F[Cover Image] --> D
       
       E --> G[Social Media/File Sharing]
       G --> H[Recipient]
       
       H --> I[Steganographic Extraction]
       I --> J[Base64 Decoding]
       J --> K[OTR Decryption]
       K --> L[Plaintext Message]
       
       style A fill:#e1f5fe
       style L fill:#e1f5fe
       style E fill:#f3e5f5
       style G fill:#e8f5e8

Core Architecture
-----------------

System Components
~~~~~~~~~~~~~~~~~

**Steganography Engine**
   Core LSB (Least Significant Bit) implementation for hiding data in image alpha channels.

**OTR Integration Layer**
   Seamless integration with existing OTR protocol for encryption and authentication.

**Image Processing Pipeline**
   Canvas-based image manipulation for embedding and extraction operations.

**Platform Integration**
   Browser extension hooks for automatic detection and processing of images.

**Cover Image Management**
   Intelligent selection and generation of suitable cover images.

Steganographic Methods
~~~~~~~~~~~~~~~~~~~~~~

**LSB Alpha Channel Embedding**
   Primary method using least significant bits of alpha channel pixels for maximum capacity and minimal visual impact.

**Adaptive LSB**
   Dynamic bit selection based on image characteristics and OTR session keys for enhanced security.

**Multi-Image Distribution**
   Large messages split across multiple images with redundancy and error correction.

**Noise Injection**
   Statistical noise added to mask steganographic signatures and improve security.

Implementation Details
----------------------

Core Steganography Engine
~~~~~~~~~~~~~~~~~~~~~~~~~

The steganography engine provides the fundamental hiding and revealing capabilities:

.. code-block:: javascript

   class SteganographyEngine {
     constructor(options = {}) {
       this.options = {
         method: 'LSB_ALPHA',
         bitsPerPixel: 1,
         compressionLevel: 0,
         noiseInjection: true,
         adaptiveLSB: true,
         ...options
       };
       
       this.canvas = document.createElement('canvas');
       this.context = this.canvas.getContext('2d');
     }
     
     async hideMessage(coverImage, message, password = null) {
       // Load cover image
       const imageData = await this.loadImage(coverImage);
       
       // Prepare message for embedding
       const messageData = await this.prepareMessage(message, password);
       
       // Embed message using LSB
       const stegoImageData = await this.embedLSB(imageData, messageData);
       
       // Apply noise injection for security
       if (this.options.noiseInjection) {
         await this.injectNoise(stegoImageData);
       }
       
       return this.imageDataToBlob(stegoImageData);
     }
     
     async revealMessage(stegoImage, password = null) {
       // Load stego image
       const imageData = await this.loadImage(stegoImage);
       
       // Extract message using LSB
       const messageData = await this.extractLSB(imageData);
       
       if (!messageData) {
         return null;
       }
       
       // Decode and verify message
       return await this.decodeMessage(messageData, password);
     }
   }

LSB Embedding Algorithm
~~~~~~~~~~~~~~~~~~~~~~~

The LSB embedding algorithm modifies the least significant bits of pixel values:

.. code-block:: javascript

   async embedLSB(imageData, messageData) {
     const pixels = imageData.data;
     const messageBits = this.messageToBits(messageData);
     
     // Add header with message length and checksum
     const header = this.createHeader(messageData.length);
     const fullMessage = [...header, ...messageBits];
     
     let bitIndex = 0;
     let pixelIndex = 0;
     
     // Embed in alpha channel (every 4th byte)
     while (bitIndex < fullMessage.length && pixelIndex < pixels.length) {
       const alphaIndex = pixelIndex * 4 + 3; // Alpha channel
       
       if (alphaIndex < pixels.length) {
         // Modify LSB of alpha channel
         pixels[alphaIndex] = (pixels[alphaIndex] & 0xFE) | fullMessage[bitIndex];
         bitIndex++;
       }
       
       pixelIndex++;
       
       // Skip pixels based on adaptive algorithm
       if (this.options.adaptiveLSB) {
         pixelIndex += this.getAdaptiveSkip(pixelIndex, imageData);
       }
     }
     
     if (bitIndex < fullMessage.length) {
       throw new Error('Image too small for message');
     }
     
     return imageData;
   }

OTR Integration
~~~~~~~~~~~~~~~

Integration with the OTR protocol maintains all security properties:

.. code-block:: javascript

   class OTRSteganographySession extends OtrSession {
     constructor(options = {}) {
       super(options);
       this.stego = new SteganographyEngine(options.steganography);
       this.coverImageManager = new CoverImageManager();
     }
     
     async sendStegoMessage(plaintext, coverImage = null) {
       // Encrypt message using OTR
       const encryptedMessage = await this.encryptMessage(plaintext);
       
       // Get or generate cover image
       const cover = coverImage || await this.coverImageManager.selectCover();
       
       // Hide encrypted message in image
       const stegoImage = await this.stego.hideMessage(cover, encryptedMessage);
       
       // Emit stego image for transmission
       this.emit('stegoImageReady', {
         image: stegoImage,
         metadata: {
           timestamp: Date.now(),
           coverType: coverImage ? 'user' : 'generated',
           messageLength: encryptedMessage.length
         }
       });
       
       return stegoImage;
     }
     
     async processStegoImage(stegoImage) {
       try {
         // Extract encrypted message
         const encryptedMessage = await this.stego.revealMessage(stegoImage);
         
         if (!encryptedMessage) {
           return null; // No hidden message found
         }
         
         // Decrypt using OTR
         const decryptedMessage = await this.decryptMessage(encryptedMessage);
         
         this.emit('messageReceived', {
           message: decryptedMessage,
           source: 'steganography',
           timestamp: Date.now()
         });
         
         return decryptedMessage;
         
       } catch (error) {
         this.emit('stegoError', {
           error: error.message,
           image: stegoImage
         });
         return null;
       }
     }
   }

Message Format Specification
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Steganographic messages use a structured format for reliability:

.. code-block:: javascript

   // Message Header (32 bits)
   const STEGO_HEADER = {
     MAGIC: 0x574F5452,        // 'WOTR' magic number
     VERSION: 0x01,            // Format version
     FLAGS: 0x00,              // Feature flags
     LENGTH_OFFSET: 8,         // Message length field offset
     CHECKSUM_OFFSET: 12       // Checksum field offset
   };
   
   function createStegoMessage(otrMessage) {
     const header = new ArrayBuffer(32);
     const view = new DataView(header);
     
     // Magic number
     view.setUint32(0, STEGO_HEADER.MAGIC, false);
     
     // Version and flags
     view.setUint8(4, STEGO_HEADER.VERSION);
     view.setUint8(5, STEGO_HEADER.FLAGS);
     
     // Message length
     view.setUint32(STEGO_HEADER.LENGTH_OFFSET, otrMessage.length, false);
     
     // Checksum (CRC32)
     const checksum = calculateCRC32(otrMessage);
     view.setUint32(STEGO_HEADER.CHECKSUM_OFFSET, checksum, false);
     
     // Combine header and message
     const fullMessage = new Uint8Array(header.byteLength + otrMessage.length);
     fullMessage.set(new Uint8Array(header), 0);
     fullMessage.set(otrMessage, header.byteLength);
     
     return fullMessage;
   }

Security Features
-----------------

Statistical Security
~~~~~~~~~~~~~~~~~~~~

Multiple techniques ensure steganographic security:

**Adaptive LSB Selection**
   Bit positions selected based on image characteristics and OTR session keys.

**Noise Injection**
   Random noise added to mask statistical signatures of embedded data.

**Cover Image Analysis**
   Automatic analysis of cover images to optimize embedding parameters.

**Anti-Detection Measures**
   Countermeasures against common steganalysis techniques.

.. code-block:: javascript

   class StatisticalSecurity {
     constructor(sessionKey) {
       this.sessionKey = sessionKey;
       this.prng = new SecurePRNG(sessionKey);
     }
     
     getAdaptiveBitPositions(imageData, messageLength) {
       const positions = [];
       const totalPixels = imageData.width * imageData.height;
       const requiredPositions = messageLength * 8;
       
       // Use session key to seed position selection
       this.prng.seed(this.sessionKey);
       
       while (positions.length < requiredPositions) {
         const position = this.prng.nextInt(totalPixels);
         
         // Check if position is suitable for embedding
         if (this.isPositionSuitable(imageData, position)) {
           positions.push(position);
         }
       }
       
       return positions;
     }
     
     injectStatisticalNoise(imageData) {
       const pixels = imageData.data;
       
       // Add subtle noise to alpha channel
       for (let i = 3; i < pixels.length; i += 4) {
         if (this.prng.nextFloat() < 0.1) { // 10% of pixels
           const noise = this.prng.nextInt(3) - 1; // -1, 0, or 1
           pixels[i] = Math.max(0, Math.min(255, pixels[i] + noise));
         }
       }
     }
   }

Cover Image Management
~~~~~~~~~~~~~~~~~~~~~~

Intelligent cover image selection and generation:

.. code-block:: javascript

   class CoverImageManager {
     constructor() {
       this.imageDatabase = new CoverImageDatabase();
       this.generator = new CoverImageGenerator();
     }
     
     async selectOptimalCover(messageSize, requirements = {}) {
       const criteria = {
         minCapacity: messageSize * 8, // bits needed
         format: 'PNG',
         minDimensions: { width: 800, height: 600 },
         maxFileSize: 5 * 1024 * 1024, // 5MB
         ...requirements
       };
       
       // Try to find suitable image from database
       let cover = await this.imageDatabase.findSuitable(criteria);
       
       if (!cover) {
         // Generate synthetic cover image
         cover = await this.generator.createCover(criteria);
       }
       
       // Analyze cover suitability
       const analysis = await this.analyzeCover(cover);
       
       if (analysis.suitabilityScore < 0.7) {
         // Enhance cover image for better steganography
         cover = await this.enhanceCover(cover, analysis);
       }
       
       return cover;
     }
     
     async analyzeCover(image) {
       const imageData = await this.loadImageData(image);
       
       return {
         capacity: this.calculateCapacity(imageData),
         complexity: this.calculateComplexity(imageData),
         noiseLevel: this.calculateNoiseLevel(imageData),
         suitabilityScore: this.calculateSuitability(imageData)
       };
     }
   }

Platform Integration
--------------------

Browser Extension Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Seamless integration with social media and file sharing platforms:

.. code-block:: javascript

   class PlatformIntegration {
     constructor() {
       this.platforms = new Map();
       this.setupPlatformHandlers();
     }
     
     setupPlatformHandlers() {
       // Facebook integration
       this.platforms.set('facebook.com', {
         imageSelector: 'img[src*="scontent"]',
         uploadHandler: this.handleFacebookUpload.bind(this),
         downloadHandler: this.handleFacebookDownload.bind(this)
       });
       
       // Instagram integration
       this.platforms.set('instagram.com', {
         imageSelector: 'img[src*="cdninstagram"]',
         uploadHandler: this.handleInstagramUpload.bind(this),
         downloadHandler: this.handleInstagramDownload.bind(this)
       });
       
       // Discord integration
       this.platforms.set('discord.com', {
         imageSelector: 'img[src*="cdn.discordapp.com"]',
         uploadHandler: this.handleDiscordUpload.bind(this),
         downloadHandler: this.handleDiscordDownload.bind(this)
       });
     }
     
     async handleImageUpload(file, platform) {
       if (!this.isImageFile(file)) {
         return file; // Pass through non-images
       }
       
       // Check if user wants to hide a message
       const shouldHideMessage = await this.promptForSteganography();
       
       if (shouldHideMessage) {
         const message = await this.getMessageToHide();
         const stegoImage = await this.otrStego.sendStegoMessage(message, file);
         return stegoImage;
       }
       
       return file;
     }
     
     async handleImageDownload(imageUrl, platform) {
       try {
         // Download image
         const response = await fetch(imageUrl);
         const imageBlob = await response.blob();
         
         // Check for hidden messages
         const hiddenMessage = await this.otrStego.processStegoImage(imageBlob);
         
         if (hiddenMessage) {
           this.displayHiddenMessage(hiddenMessage);
         }
         
       } catch (error) {
         console.error('Error processing image:', error);
       }
     }
   }

Performance Optimization
------------------------

Image Processing Optimization
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Efficient algorithms for real-time steganography:

.. code-block:: javascript

   class PerformanceOptimizer {
     constructor() {
       this.workerPool = new WorkerPool(4); // 4 web workers
       this.imageCache = new LRUCache(50);
     }
     
     async optimizedEmbedding(imageData, messageData) {
       // Use web workers for large images
       if (imageData.width * imageData.height > 1000000) {
         return await this.workerEmbedding(imageData, messageData);
       }
       
       // Use main thread for smaller images
       return await this.directEmbedding(imageData, messageData);
     }
     
     async workerEmbedding(imageData, messageData) {
       // Split image into chunks for parallel processing
       const chunks = this.splitImageData(imageData, 4);
       const messageChunks = this.splitMessage(messageData, 4);
       
       const promises = chunks.map((chunk, index) => {
         return this.workerPool.execute('embedChunk', {
           imageChunk: chunk,
           messageChunk: messageChunks[index]
         });
       });
       
       const results = await Promise.all(promises);
       return this.mergeImageChunks(results);
     }
     
     // Progressive loading for large images
     async progressiveProcessing(image, onProgress) {
       const totalPixels = image.width * image.height;
       const chunkSize = 100000; // Process 100k pixels at a time
       
       for (let offset = 0; offset < totalPixels; offset += chunkSize) {
         const chunk = this.getImageChunk(image, offset, chunkSize);
         await this.processChunk(chunk);
         
         const progress = Math.min(100, (offset / totalPixels) * 100);
         onProgress(progress);
         
         // Yield control to prevent UI blocking
         await new Promise(resolve => setTimeout(resolve, 0));
       }
     }
   }

This steganography implementation provides covert communication capabilities while maintaining the full security guarantees of the OTR protocol, enabling truly invisible secure messaging through innocent image sharing.
