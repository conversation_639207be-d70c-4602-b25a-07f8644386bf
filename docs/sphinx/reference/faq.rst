Frequently Asked Questions
===========================

This page answers common questions about WebOTR.

General Questions
-----------------

**What is WebOTR?**

WebOTR is a browser extension that provides end-to-end encryption for popular web chat platforms like <PERSON>rd, Slack, and Microsoft Teams.

**How does WebOTR work?**

WebOTR integrates directly with your chat platform's web interface, intercepting outgoing messages to encrypt them and decrypting incoming encrypted messages.

**Is WebOTR free?**

Yes, WebOTR is completely free and open-source.

**Which platforms does WebOTR support?**

Currently supported platforms:

- Discord (Full support)
- Slack (Full support)  
- Microsoft Teams (Beta support)
- Generic web chat (Experimental support)

Installation and Setup
----------------------

**How do I install WebOTR?**

1. Visit your browser's extension store
2. Search for "WebOTR"
3. Click "Add to Browser"
4. The WebOTR icon will appear in your toolbar

**Do I need to create an account?**

No, WebOTR doesn't require any account creation. All encryption keys are generated locally on your device.

Security and Privacy
--------------------

**How secure is WebOTR?**

WebOTR uses military-grade encryption based on the Signal Protocol with AES-256-GCM encryption and forward secrecy.

**Can chat platform providers read my encrypted messages?**

No, chat platform providers cannot read your encrypted messages even if they have access to their servers.

**What about metadata?**

While WebOTR encrypts message content, metadata such as who you're communicating with and when may still be visible to platform providers.

Usage and Features
------------------

**How do I start an encrypted conversation?**

1. Navigate to a supported chat platform
2. Look for the WebOTR lock icon
3. Click to enable encryption
4. Begin typing encrypted messages

**Do both participants need WebOTR?**

Yes, all participants need WebOTR installed to participate in encrypted conversations.

**Can I encrypt group conversations?**

Yes, WebOTR supports group encryption on supported platforms.
