Architecture Overview
====================

WebOTR is designed as a modular browser extension that provides end-to-end encryption 
for web-based chat platforms.

System Architecture
-------------------

WebOTR consists of several key components:

**Content Scripts**
   Injected into chat platform pages to detect and modify chat interfaces.

**Background Service Worker**
   Handles cryptographic operations and key management.

**User Interface Components**
   React-based UI elements for encryption controls.

**Storage Layer**
   Secure browser storage for encryption keys and settings.

Platform Integration Layer
---------------------------

WebOTR uses a plugin architecture to support different chat platforms:

- Discord adapter
- Slack adapter  
- Teams adapter
- Generic web chat adapter

Cryptographic Architecture
--------------------------

WebOTR implements a Signal Protocol-inspired cryptographic system:

- AES-256-GCM for message encryption
- X25519 for key exchange
- Ed25519 for digital signatures
- Double Ratchet for forward secrecy
