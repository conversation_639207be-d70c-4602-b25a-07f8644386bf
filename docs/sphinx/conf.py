# Configuration file for the Sphinx documentation builder.

# -- Project information -----------------------------------------------------
project = 'WebOTR'
copyright = '2024, WebOTR Team'
author = 'WebOTR Team'
release = '0.1.0'

# -- General configuration ---------------------------------------------------
extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.viewcode',
    'sphinx.ext.napoleon',
    'sphinx.ext.intersphinx',
    'sphinx.ext.todo',
    'sphinx.ext.coverage',
    'sphinx.ext.ifconfig',
    'sphinx.ext.githubpages',
    'myst_parser',
    'sphinxcontrib.mermaid',
]

templates_path = ['_templates']
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']

# -- Options for HTML output -------------------------------------------------
html_theme = 'furo'
html_static_path = ['_static']
html_title = 'WebOTR Documentation'
html_short_title = 'WebOTR'

# Theme options
html_theme_options = {
    "sidebar_hide_name": False,
    "light_css_variables": {
        "color-brand-primary": "#6366f1",
        "color-brand-content": "#6366f1",
    },
    "dark_css_variables": {
        "color-brand-primary": "#8b5cf6",
        "color-brand-content": "#8b5cf6",
    },
    "source_repository": "https://github.com/forkrul/webOTteR/",
    "source_branch": "main",
    "source_directory": "docs/sphinx/",
}

# Custom CSS
html_css_files = [
    'custom.css',
]

# Favicon and Logo
html_favicon = '../assets/favicon.svg'
html_logo = '../assets/logo.svg'

# MyST Parser configuration
myst_enable_extensions = [
    "colon_fence",
    "deflist",
    "dollarmath",
    "fieldlist",
    "html_admonition",
    "html_image",
    "linkify",
    "replacements",
    "smartquotes",
    "strikethrough",
    "substitution",
    "tasklist",
]

# Source file suffixes
source_suffix = {
    '.rst': None,
    '.md': 'myst_parser',
}

# Master document
master_doc = 'index'

# Language
language = 'en'

# Pygments style
pygments_style = 'sphinx'
pygments_dark_style = 'monokai'

# HTML context
html_context = {
    "display_github": True,
    "github_user": "forkrul",
    "github_repo": "webOTteR",
    "github_version": "main",
    "conf_py_path": "/docs/sphinx/",
}

# Mermaid configuration
mermaid_version = "10.6.1"
mermaid_init_js = """
mermaid.initialize({
    startOnLoad: true,
    theme: 'default',
    themeVariables: {
        primaryColor: '#6366f1',
        primaryTextColor: '#1f2937',
        primaryBorderColor: '#6366f1',
        lineColor: '#6b7280',
        secondaryColor: '#f3f4f6',
        tertiaryColor: '#ffffff'
    },
    flowchart: {
        curve: 'basis',
        padding: 20
    },
    sequence: {
        diagramMarginX: 50,
        diagramMarginY: 10,
        actorMargin: 50,
        width: 150,
        height: 65,
        boxMargin: 10,
        boxTextMargin: 5,
        noteMargin: 10,
        messageMargin: 35
    },
    gantt: {
        titleTopMargin: 25,
        barHeight: 20,
        fontFamily: '"Inter", sans-serif',
        fontSize: 11,
        gridLineStartPadding: 35,
        bottomPadding: 25,
        leftPadding: 75,
        rightPadding: 35
    }
});
"""
