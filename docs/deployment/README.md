# WebOTR UX Deployment Guide

## Overview

This guide covers deploying the WebOTR UX system to production environments with optimal security, performance, and reliability.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Build Process](#build-process)
- [Environment Configuration](#environment-configuration)
- [Security Configuration](#security-configuration)
- [Performance Optimization](#performance-optimization)
- [Monitoring and Analytics](#monitoring-and-analytics)
- [Deployment Strategies](#deployment-strategies)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

**Server Requirements:**
- Node.js 18+ or compatible runtime
- 2GB+ RAM for build process
- 10GB+ disk space for dependencies and builds
- HTTPS-capable web server (nginx, Apache, or CDN)

**Development Tools:**
- npm 8+ or yarn 1.22+
- Git for version control
- Modern browser for testing

### Dependencies

```bash
# Install production dependencies
npm install --production

# Install development dependencies (for building)
npm install
```

## Build Process

### Production Build

```bash
# Create optimized production build
npm run build

# Verify build output
ls -la dist/
```

### Build Configuration

Create `webpack.config.prod.js`:

```javascript
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');

module.exports = {
  mode: 'production',
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].[contenthash].js',
    chunkFilename: '[name].[contenthash].chunk.js',
    clean: true
  },
  optimization: {
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true
          }
        }
      }),
      new CssMinimizerPlugin()
    ],
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        },
        verification: {
          test: /[\\/]src[\\/]ui[\\/]components[\\/]verification[\\/]/,
          name: 'verification',
          chunks: 'async'
        }
      }
    }
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: './public/index.html',
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeRedundantAttributes: true,
        useShortDoctype: true,
        removeEmptyAttributes: true,
        removeStyleLinkTypeAttributes: true,
        keepClosingSlash: true,
        minifyJS: true,
        minifyCSS: true,
        minifyURLs: true
      }
    }),
    new MiniCssExtractPlugin({
      filename: '[name].[contenthash].css',
      chunkFilename: '[name].[contenthash].chunk.css'
    })
  ]
};
```

### Build Scripts

Add to `package.json`:

```json
{
  "scripts": {
    "build": "webpack --config webpack.config.prod.js",
    "build:analyze": "webpack-bundle-analyzer dist/static/js/*.js",
    "build:stats": "webpack --config webpack.config.prod.js --json > stats.json",
    "test:build": "npm run build && npm run test:e2e",
    "deploy:staging": "npm run build && npm run deploy:staging:upload",
    "deploy:production": "npm run build && npm run deploy:production:upload"
  }
}
```

## Environment Configuration

### Environment Variables

Create `.env.production`:

```bash
# Application Configuration
NODE_ENV=production
REACT_APP_VERSION=1.0.0
REACT_APP_BUILD_DATE=2024-12-19

# API Configuration
REACT_APP_API_BASE_URL=https://api.webottr.com
REACT_APP_WS_URL=wss://ws.webottr.com

# Security Configuration
REACT_APP_CSP_NONCE=auto-generated
REACT_APP_ENABLE_HTTPS_ONLY=true

# Performance Configuration
REACT_APP_ENABLE_SERVICE_WORKER=true
REACT_APP_ENABLE_CODE_SPLITTING=true
REACT_APP_ENABLE_COMPRESSION=true

# Monitoring Configuration
REACT_APP_SENTRY_DSN=https://your-sentry-dsn
REACT_APP_ANALYTICS_ID=your-analytics-id
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true

# Feature Flags
REACT_APP_ENABLE_EXPERIMENTAL_FEATURES=false
REACT_APP_ENABLE_DEBUG_MODE=false
```

### Configuration Validation

Create `config/validate.js`:

```javascript
const requiredEnvVars = [
  'REACT_APP_API_BASE_URL',
  'REACT_APP_WS_URL'
];

const optionalEnvVars = [
  'REACT_APP_SENTRY_DSN',
  'REACT_APP_ANALYTICS_ID'
];

function validateConfig() {
  const missing = requiredEnvVars.filter(
    varName => !process.env[varName]
  );
  
  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing);
    process.exit(1);
  }
  
  const warnings = optionalEnvVars.filter(
    varName => !process.env[varName]
  );
  
  if (warnings.length > 0) {
    console.warn('Optional environment variables not set:', warnings);
  }
  
  console.log('Configuration validation passed');
}

module.exports = { validateConfig };
```

## Security Configuration

### Content Security Policy (CSP)

Configure CSP headers in your web server:

```nginx
# nginx configuration
add_header Content-Security-Policy "
  default-src 'self';
  script-src 'self' 'unsafe-inline' 'unsafe-eval';
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: blob:;
  font-src 'self';
  connect-src 'self' wss: https:;
  media-src 'self' blob:;
  worker-src 'self' blob:;
  frame-ancestors 'none';
  base-uri 'self';
  form-action 'self';
" always;
```

### HTTPS Configuration

```nginx
# nginx HTTPS configuration
server {
    listen 443 ssl http2;
    server_name webottr.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header Referrer-Policy strict-origin-when-cross-origin always;
    
    location / {
        root /var/www/webottr/dist;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name webottr.com;
    return 301 https://$server_name$request_uri;
}
```

### Security Headers

```javascript
// Express.js security middleware
const helmet = require('helmet');

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "blob:"],
      connectSrc: ["'self'", "wss:", "https:"],
      mediaSrc: ["'self'", "blob:"],
      workerSrc: ["'self'", "blob:"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

## Performance Optimization

### Compression

```nginx
# nginx compression
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/javascript
    application/xml+rss
    application/json;

# Brotli compression (if available)
brotli on;
brotli_comp_level 6;
brotli_types
    text/plain
    text/css
    application/json
    application/javascript
    text/xml
    application/xml
    application/xml+rss
    text/javascript;
```

### Caching Strategy

```nginx
# Cache configuration
location ~* \.(js|css)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary "Accept-Encoding";
}

location ~* \.(png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location = /index.html {
    expires -1;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
}
```

### Service Worker

Create `public/sw.js`:

```javascript
const CACHE_NAME = 'webottr-v1.0.0';
const urlsToCache = [
  '/',
  '/static/js/main.js',
  '/static/css/main.css',
  '/manifest.json'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
  );
});
```

## Monitoring and Analytics

### Error Monitoring

```javascript
// Sentry configuration
import * as Sentry from '@sentry/react';

Sentry.init({
  dsn: process.env.REACT_APP_SENTRY_DSN,
  environment: process.env.NODE_ENV,
  integrations: [
    new Sentry.BrowserTracing(),
  ],
  tracesSampleRate: 0.1,
  beforeSend(event) {
    // Filter out sensitive information
    if (event.exception) {
      const error = event.exception.values[0];
      if (error.value && error.value.includes('fingerprint')) {
        return null; // Don't send fingerprint-related errors
      }
    }
    return event;
  }
});
```

### Performance Monitoring

```javascript
// Performance monitoring
import { performanceMonitor } from './src/ui/utils/performance.js';

// Track Core Web Vitals
function trackWebVitals() {
  import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
    getCLS(console.log);
    getFID(console.log);
    getFCP(console.log);
    getLCP(console.log);
    getTTFB(console.log);
  });
}

// Track custom metrics
setInterval(() => {
  const metrics = performanceMonitor.getMetrics();
  const score = performanceMonitor.getPerformanceScore();
  
  // Send to analytics
  if (window.gtag) {
    window.gtag('event', 'performance_score', {
      value: score,
      custom_parameter: metrics
    });
  }
}, 60000); // Every minute
```

### Health Checks

Create `public/health.json`:

```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2024-12-19T00:00:00Z",
  "checks": {
    "frontend": "ok",
    "assets": "ok",
    "dependencies": "ok"
  }
}
```

## Deployment Strategies

### Blue-Green Deployment

```bash
#!/bin/bash
# deploy.sh

set -e

ENVIRONMENT=${1:-staging}
VERSION=$(date +%Y%m%d-%H%M%S)

echo "Deploying WebOTR UX to $ENVIRONMENT (version: $VERSION)"

# Build application
npm run build

# Create deployment package
tar -czf webottr-ux-$VERSION.tar.gz dist/

# Upload to staging slot
if [ "$ENVIRONMENT" = "production" ]; then
    # Deploy to blue slot
    scp webottr-ux-$VERSION.tar.gz server:/var/www/webottr-blue/
    ssh server "cd /var/www/webottr-blue && tar -xzf webottr-ux-$VERSION.tar.gz"
    
    # Health check
    curl -f https://blue.webottr.com/health.json
    
    # Switch traffic to blue
    ssh server "ln -sfn /var/www/webottr-blue/dist /var/www/webottr/current"
    
    # Verify production
    curl -f https://webottr.com/health.json
    
    echo "Production deployment complete"
else
    # Deploy to staging
    scp webottr-ux-$VERSION.tar.gz staging:/var/www/webottr/
    ssh staging "cd /var/www/webottr && tar -xzf webottr-ux-$VERSION.tar.gz"
    
    echo "Staging deployment complete"
fi
```

### Docker Deployment

Create `Dockerfile`:

```dockerfile
FROM nginx:alpine

# Copy built application
COPY dist/ /usr/share/nginx/html/

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health.json || exit 1

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  webottr-ux:
    build: .
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    
  webottr-api:
    image: webottr/api:latest
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
```

### CDN Deployment

```bash
# Deploy to AWS CloudFront
aws s3 sync dist/ s3://webottr-static-assets/ --delete
aws cloudfront create-invalidation --distribution-id E1234567890 --paths "/*"

# Deploy to Cloudflare
curl -X POST "https://api.cloudflare.com/client/v4/zones/$ZONE_ID/purge_cache" \
  -H "Authorization: Bearer $CF_API_TOKEN" \
  -H "Content-Type: application/json" \
  --data '{"purge_everything":true}'
```

## Troubleshooting

### Common Issues

**Build Failures:**
```bash
# Clear cache and rebuild
rm -rf node_modules package-lock.json
npm install
npm run build
```

**Performance Issues:**
```bash
# Analyze bundle size
npm run build:analyze

# Check for memory leaks
node --inspect-brk=0.0.0.0:9229 build.js
```

**SSL Certificate Issues:**
```bash
# Test SSL configuration
openssl s_client -connect webottr.com:443 -servername webottr.com

# Verify certificate chain
curl -I https://webottr.com
```

### Monitoring Commands

```bash
# Check application health
curl -f https://webottr.com/health.json

# Monitor performance
curl -s https://webottr.com | grep -o 'performance.*'

# Check error rates
tail -f /var/log/nginx/error.log | grep webottr
```

### Rollback Procedure

```bash
#!/bin/bash
# rollback.sh

PREVIOUS_VERSION=${1:-previous}

echo "Rolling back to $PREVIOUS_VERSION"

# Switch symlink to previous version
ssh server "ln -sfn /var/www/webottr-$PREVIOUS_VERSION/dist /var/www/webottr/current"

# Verify rollback
curl -f https://webottr.com/health.json

echo "Rollback complete"
```

## Security Checklist

- [ ] HTTPS enabled with valid certificates
- [ ] Security headers configured
- [ ] CSP policy implemented
- [ ] Sensitive data excluded from builds
- [ ] Error monitoring configured
- [ ] Access logs enabled
- [ ] Regular security updates scheduled

## Performance Checklist

- [ ] Compression enabled (gzip/brotli)
- [ ] Static assets cached properly
- [ ] Code splitting implemented
- [ ] Service worker configured
- [ ] CDN configured for static assets
- [ ] Performance monitoring enabled
- [ ] Core Web Vitals tracked

## Maintenance

### Regular Tasks

- **Weekly**: Review error logs and performance metrics
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Performance audit and optimization review
- **Annually**: Security audit and penetration testing

### Backup Strategy

```bash
# Backup configuration
tar -czf webottr-config-$(date +%Y%m%d).tar.gz \
  nginx.conf \
  .env.production \
  ssl/

# Backup application
tar -czf webottr-app-$(date +%Y%m%d).tar.gz dist/
```

---

For additional deployment support, consult the [GitHub repository](https://github.com/forkrul/webOTteR) or contact the development team.
