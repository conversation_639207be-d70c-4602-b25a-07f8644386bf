# WebOTR API Documentation

## Overview

WebOTR provides a comprehensive JavaScript implementation of the Off-the-Record (OTR) messaging protocol with Socialist Millionaire Protocol (SMP) support.

## Core Classes

### OtrSession

The main class for managing OTR conversations.

#### Constructor

```javascript
const session = new OtrSession(options);
```

**Parameters:**
- `options` (Object): Configuration options
  - `versions` (Array): Supported OTR versions (default: [3, 4])
  - `testing` (Boolean): Enable testing mode (default: false)

#### Methods

##### startOtr()

Initiates an OTR session.

```javascript
await session.startOtr();
```

**Returns:** Promise<Boolean> - Success status

##### processIncoming(message)

Processes incoming OTR messages.

```javascript
const result = await session.processIncoming(message);
```

**Parameters:**
- `message` (String): Incoming OTR message

**Returns:** Promise<String|null> - Decrypted message or null

##### encryptMessage(plaintext)

Encrypts a message for transmission.

```javascript
const encrypted = await session.encryptMessage(plaintext);
```

**Parameters:**
- `plaintext` (String): Message to encrypt

**Returns:** Promise<String> - Encrypted OTR message

##### onStateChange(callback)

Registers a callback for state changes.

```javascript
session.onStateChange((state) => {
  console.log('OTR state changed:', state);
});
```

**Parameters:**
- `callback` (Function): Callback function receiving state object

##### destroy()

Securely destroys the session and clears sensitive data.

```javascript
session.destroy();
```

### SMPHandler

Handles Socialist Millionaire Protocol for authentication.

#### Constructor

```javascript
const smpHandler = new SMPHandler(options);
```

**Parameters:**
- `options` (Object): Configuration options
  - `testing` (Boolean): Enable testing mode

#### Methods

##### initiateSMP(secret, question)

Initiates SMP authentication.

```javascript
const smp1 = await smpHandler.initiateSMP(secret, question);
```

**Parameters:**
- `secret` (String): Shared secret
- `question` (String, optional): Question to ask the other party

**Returns:** Promise<Object> - SMP1 message

##### respondToSMP(secret)

Responds to SMP initiation.

```javascript
const smp2 = await smpHandler.respondToSMP(secret);
```

**Parameters:**
- `secret` (String): Shared secret

**Returns:** Promise<Object> - SMP2 message

##### processSMPMessage(message)

Processes SMP messages.

```javascript
const response = await smpHandler.processSMPMessage(message);
```

**Parameters:**
- `message` (Object): SMP message

**Returns:** Promise<Object|null> - Response message or null

##### onSMPResult(callback)

Registers a callback for SMP results.

```javascript
smpHandler.onSMPResult((result) => {
  console.log('SMP result:', result);
});
```

**Parameters:**
- `callback` (Function): Callback function receiving result object

## Constants

### OTR States

```javascript
const OTR_STATE = {
  PLAINTEXT: 0,
  ENCRYPTED: 1,
  FINISHED: 2
};
```

### SMP Results

```javascript
const SMP_RESULT = {
  NONE: 0,
  SUCCESS: 1,
  FAILURE: 2,
  ABORTED: 3,
  IN_PROGRESS: 4,
  ERROR: 5
};
```

### Message Types

```javascript
const SMP_MESSAGE_TYPE = {
  SMP1: 2,
  SMP2: 3,
  SMP3: 4,
  SMP4: 5,
  SMP_ABORT: 6
};
```

## Cryptographic Functions

### Key Generation

```javascript
import { generateKeys } from './src/core/crypto';

const keys = await generateKeys();
// Returns: { dsa: {...}, dh: {...} }
```

### Key Derivation

```javascript
import { deriveKeys } from './src/core/crypto';

const derivedKeys = await deriveKeys(sharedSecret, salt, info);
// Returns: { sendingAESKey, receivingAESKey, sendingMACKey, receivingMACKey }
```

### Secure Operations

```javascript
import { secureClear, constantTimeEqual } from './src/core/crypto';

// Securely clear sensitive data
secureClear(sensitiveArray);

// Constant-time comparison
const isEqual = constantTimeEqual(array1, array2);
```

## Usage Examples

### Basic OTR Session

```javascript
import { OtrSession } from './src/core/session';

// Create session
const session = new OtrSession({
  versions: [3, 4]
});

// Set up callbacks
session.onStateChange((state) => {
  console.log('State:', state);
});

// Start OTR
await session.startOtr();

// Send message
const encrypted = await session.encryptMessage('Hello, world!');
console.log('Encrypted:', encrypted);

// Process incoming message
const decrypted = await session.processIncoming(incomingMessage);
console.log('Decrypted:', decrypted);
```

### SMP Authentication

```javascript
import { SMPHandler } from './src/core/protocol/smp';

// Create SMP handler
const smp = new SMPHandler();

// Set up result callback
smp.onSMPResult((result) => {
  if (result.result === SMP_RESULT.SUCCESS) {
    console.log('Authentication successful!');
  } else {
    console.log('Authentication failed!');
  }
});

// Initiate SMP
const smp1 = await smp.initiateSMP('shared-secret', 'What is our secret?');
// Send smp1 to other party

// Process SMP2 response
const smp3 = await smp.processSMPMessage(smp2);
// Send smp3 to other party

// Process SMP4 response
await smp.processSMPMessage(smp4);
// Check result in callback
```

## Error Handling

### Common Errors

- `Invalid OTR state`: Operation not valid in current state
- `Invalid SMP state`: SMP operation not valid in current state
- `Cryptographic verification failed`: Message authentication failed
- `Unknown message type`: Unsupported message format

### Best Practices

1. Always check return values
2. Handle errors gracefully
3. Use try-catch blocks for async operations
4. Validate input parameters
5. Clear sensitive data when done

## Security Considerations

### Memory Management

- Use `secureClear()` for sensitive data
- Call `destroy()` on sessions when done
- Avoid logging sensitive information

### Timing Attacks

- Use `constantTimeEqual()` for comparisons
- Avoid conditional operations on secrets
- Be aware of JavaScript timing limitations

### State Management

- Validate state transitions
- Handle unexpected messages gracefully
- Implement proper timeout handling

## Browser Extension Integration

### Content Script

```javascript
// Inject OTR functionality into web pages
import { OtrSession } from './src/core/session';

const session = new OtrSession();
// Integrate with page messaging
```

### Background Script

```javascript
// Handle OTR sessions across tabs
import { OtrSession } from './src/core/session';

// Manage multiple sessions
const sessions = new Map();
```

### Popup Interface

```javascript
// Provide user interface for OTR controls
// Show session status, initiate SMP, etc.
```

## Testing

### Unit Tests

```javascript
import { SMPHandler } from './src/core/protocol/smp';

describe('SMP', () => {
  test('should complete successfully', async () => {
    const alice = new SMPHandler({ testing: true });
    const bob = new SMPHandler({ testing: true });
    
    // Test SMP flow
    const smp1 = await alice.initiateSMP('secret');
    const smp2 = await bob.respondToSMP('secret');
    // ... continue test
  });
});
```

### Integration Tests

```javascript
// Test full OTR + SMP flow
// Test browser extension integration
// Test error conditions
```

## Performance Considerations

### Optimization Tips

1. Reuse sessions when possible
2. Implement message batching
3. Use Web Workers for heavy crypto
4. Cache derived keys appropriately
5. Implement proper cleanup

### Memory Usage

- Monitor memory usage in long sessions
- Implement periodic cleanup
- Use weak references where appropriate
- Profile memory leaks regularly

## Compatibility

### Browser Support

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

### Node.js Support

- Node.js 14+
- Requires crypto polyfills
- Limited to testing/development

## Migration Guide

### From Previous Versions

[To be added when applicable]

### Breaking Changes

[To be documented as they occur]

---

For more information, see the [Security Policy](SECURITY.md) and [Contributing Guidelines](CONTRIBUTING.md).
