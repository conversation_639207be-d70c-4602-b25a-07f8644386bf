# WebOTR UX API Documentation

## Overview

The WebOTR UX system provides a comprehensive, accessible, and secure user interface for OTR (Off-the-Record) messaging. This documentation covers all APIs, components, and integration patterns for developers.

## Table of Contents

- [Quick Start](#quick-start)
- [Core Components](#core-components)
- [Session Management](#session-management)
- [Verification System](#verification-system)
- [Internationalization](#internationalization)
- [Accessibility](#accessibility)
- [Performance](#performance)
- [Browser Compatibility](#browser-compatibility)

## Quick Start

### Installation

```bash
npm install @webottr/ux-components
```

### Basic Usage

```jsx
import React from 'react';
import { UXController } from '@webottr/ux-components';

function App() {
  return (
    <div className="app">
      <UXController />
    </div>
  );
}

export default App;
```

### With Custom Configuration

```jsx
import React from 'react';
import { UXController, UXOtrSession } from '@webottr/ux-components';

function App() {
  const sessionConfig = {
    autoConnect: false,
    progressUpdates: true,
    verificationRequired: true
  };

  return (
    <UXController sessionConfig={sessionConfig} />
  );
}
```

## Core Components

### UXController

The main controller component that orchestrates the entire OTR user experience.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `sessionConfig` | `Object` | `{}` | Configuration for the OTR session |
| `theme` | `string` | `'light'` | UI theme ('light' or 'dark') |
| `language` | `string` | `'auto'` | Interface language |
| `onSessionChange` | `function` | `undefined` | Callback for session state changes |
| `onMessageReceived` | `function` | `undefined` | Callback for incoming messages |
| `onError` | `function` | `undefined` | Callback for error handling |

#### Example

```jsx
<UXController
  sessionConfig={{
    autoConnect: true,
    verificationRequired: true
  }}
  theme="dark"
  language="es"
  onSessionChange={(state) => console.log('Session state:', state)}
  onMessageReceived={(message) => console.log('New message:', message)}
  onError={(error) => console.error('OTR Error:', error)}
/>
```

### VerificationDialog

Modal dialog for identity verification with multiple methods.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `isOpen` | `boolean` | `false` | Whether the dialog is open |
| `onClose` | `function` | **required** | Callback to close the dialog |
| `onComplete` | `function` | **required** | Callback when verification completes |
| `verificationMethod` | `string` | `'qr-code'` | Default verification method |
| `contactName` | `string` | `'Contact'` | Name of the contact being verified |
| `fingerprint` | `string` | `undefined` | Local fingerprint for verification |

#### Example

```jsx
<VerificationDialog
  isOpen={showVerification}
  onClose={() => setShowVerification(false)}
  onComplete={(result) => handleVerificationComplete(result)}
  verificationMethod="qr-code"
  contactName="Alice"
  fingerprint="ABCD1234..."
/>
```

### StatusIndicator

Visual indicator for OTR session status and security level.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `status` | `string` | `'disconnected'` | Current session status |
| `size` | `string` | `'md'` | Size variant ('xs', 'sm', 'md', 'lg', 'xl') |
| `showLabel` | `boolean` | `true` | Whether to show status label |
| `showDetails` | `boolean` | `false` | Whether to show detailed description |
| `onClick` | `function` | `undefined` | Click handler for interactive status |

#### Status Values

- `'disconnected'` - No OTR session active
- `'connecting'` - Establishing OTR session
- `'connected'` - Session active, messages encrypted
- `'verified'` - Identity verified, secure communication
- `'unverified'` - Connected but identity not verified
- `'error'` - Connection error or security issue

#### Example

```jsx
<StatusIndicator
  status="verified"
  size="lg"
  showDetails={true}
  onClick={(status) => handleStatusClick(status)}
/>
```

### SecurityAlert

Contextual alerts for security events and user guidance.

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `level` | `string` | `'info'` | Alert level ('info', 'success', 'warning', 'error') |
| `message` | `string` | **required** | Alert message text |
| `action` | `string` | `undefined` | Action button text |
| `onAction` | `function` | `undefined` | Action button callback |
| `onDismiss` | `function` | `undefined` | Dismiss button callback |
| `autoHide` | `boolean` | `false` | Auto-hide after timeout |
| `timeout` | `number` | `5000` | Auto-hide timeout in milliseconds |

#### Example

```jsx
<SecurityAlert
  level="warning"
  message="Connection established but identity not verified."
  action="Verify Now"
  onAction={() => startVerification()}
  onDismiss={() => dismissAlert()}
  autoHide={false}
/>
```

## Session Management

### UXOtrSession

Enhanced OTR session with UX integration and real-time updates.

#### Constructor

```javascript
const session = new UXOtrSession(options);
```

#### Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `autoConnect` | `boolean` | `false` | Automatically start OTR on creation |
| `progressUpdates` | `boolean` | `true` | Enable connection progress updates |
| `verificationRequired` | `boolean` | `true` | Require identity verification |

#### Methods

##### `startOTR()`

Starts the OTR session with progress tracking.

```javascript
await session.startOTR();
```

##### `endOTR()`

Ends the OTR session and cleans up resources.

```javascript
await session.endOTR();
```

##### `sendMessage(content)`

Sends an encrypted message through the OTR session.

```javascript
const message = await session.sendMessage('Hello, secure world!');
```

##### `verifyIdentity(method, data)`

Verifies contact identity using specified method.

```javascript
const verified = await session.verifyIdentity('qr-code', {
  fingerprint: 'ABCD1234...'
});
```

#### Events

The session emits various events for real-time updates:

```javascript
session.on('stateChange', (stateData) => {
  console.log('State changed:', stateData.to);
});

session.on('messageReceived', (message) => {
  console.log('Message received:', message);
});

session.on('verificationCompleted', (result) => {
  console.log('Verification result:', result);
});

session.on('connectionProgress', (progress) => {
  console.log('Connection progress:', progress.progress + '%');
});
```

## Verification System

### QR Code Verification

Camera-based QR code scanning with fallback options.

```jsx
import { QRCodeVerifier } from '@webottr/ux-components';

<QRCodeVerifier
  step={1}
  onComplete={(result) => handleQRComplete(result)}
  fingerprint="ABCD1234..."
  contactName="Alice"
  mode="both" // 'scan', 'show', or 'both'
/>
```

### Question-Answer Verification (SMP)

Socialist Millionaire Protocol verification with guided workflow.

```jsx
import { QuestionAnswerVerifier } from '@webottr/ux-components';

<QuestionAnswerVerifier
  step={1}
  onComplete={(result) => handleSMPComplete(result)}
  contactName="Alice"
  isInitiator={true}
/>
```

### Manual Fingerprint Verification

Side-by-side fingerprint comparison with secure channel guidance.

```jsx
import { ManualFingerprintVerifier } from '@webottr/ux-components';

<ManualFingerprintVerifier
  step={1}
  onComplete={(result) => handleManualComplete(result)}
  fingerprint="ABCD1234..."
  contactName="Alice"
/>
```

## Internationalization

### Language Support

WebOTR UX supports 10+ languages with automatic detection:

- English (en)
- Spanish (es)
- French (fr)
- German (de)
- Italian (it)
- Portuguese (pt)
- Russian (ru)
- Chinese (zh)
- Japanese (ja)
- Korean (ko)

### Usage

```jsx
import { useTranslation, setLanguage } from '@webottr/ux-components';

function MyComponent() {
  const { t, currentLanguage, setLanguage } = useTranslation();
  
  return (
    <div>
      <h1>{t('main.title')}</h1>
      <p>{t('main.subtitle')}</p>
      <button onClick={() => setLanguage('es')}>
        Español
      </button>
    </div>
  );
}
```

### Translation Keys

Common translation keys:

```javascript
// Main interface
t('main.title') // "WebOTR Secure Messaging"
t('main.startSecureChat') // "Start Secure Chat"
t('main.verifyIdentity') // "Verify Identity"

// Status indicators
t('status.connected') // "Connected"
t('status.verified') // "Verified"
t('status.disconnected') // "Not Connected"

// Verification
t('verification.title') // "Verify Identity"
t('verification.qrCode') // "QR Code"
t('verification.questionAnswer') // "Question & Answer"
```

## Accessibility

### WCAG 2.1 AA Compliance

All components meet WCAG 2.1 AA standards:

- **Color Contrast**: 4.5:1 minimum ratio
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Complete ARIA implementation
- **Focus Management**: Proper focus indication and trapping

### Screen Reader Utilities

```javascript
import { ScreenReaderUtils } from '@webottr/ux-components';

// Announce messages to screen readers
ScreenReaderUtils.announce('Connection established');
ScreenReaderUtils.announceError('Verification failed');
ScreenReaderUtils.announceSuccess('Identity verified');
```

### Keyboard Navigation

```javascript
import { KeyboardNavigation } from '@webottr/ux-components';

// Trap focus within a dialog
const cleanup = KeyboardNavigation.trapFocus(dialogElement);

// Handle escape key
const cleanup = KeyboardNavigation.handleEscapeKey(() => {
  closeDialog();
});
```

## Performance

### Code Splitting

Components are automatically code-split for optimal loading:

```jsx
import { LazyVerificationDialog } from '@webottr/ux-components';

// Automatically loads verification components on demand
<LazyVerificationDialog
  isOpen={showDialog}
  onComplete={handleComplete}
/>
```

### Performance Monitoring

```javascript
import { performanceMonitor } from '@webottr/ux-components';

// Get performance metrics
const metrics = performanceMonitor.getMetrics();
console.log('Component load times:', metrics.componentLoadTimes);
console.log('Render times:', metrics.renderTimes);

// Get performance score
const score = performanceMonitor.getPerformanceScore();
console.log('Performance score:', score + '/100');
```

## Browser Compatibility

### Feature Detection

```javascript
import { browserCompatibility } from '@webottr/ux-components';

// Check browser compatibility
const report = browserCompatibility.generateCompatibilityReport();
console.log('Browser:', report.browser);
console.log('Compatibility score:', report.score);
console.log('Warnings:', report.warnings);

// Check specific features
if (browserCompatibility.features.getUserMedia) {
  // Camera is available for QR scanning
} else {
  // Show manual input fallback
}
```

### Minimum Requirements

- **Chrome**: 70+
- **Firefox**: 65+
- **Safari**: 12+
- **Edge**: 79+

### Polyfills

Polyfills are automatically loaded for legacy browsers:

```javascript
import { initializeCompatibility } from '@webottr/ux-components';

// Initialize compatibility and load polyfills
await initializeCompatibility();
```

## Error Handling

### Error Types

```javascript
// Session errors
session.on('error', (error) => {
  switch (error.code) {
    case 'CONNECTION_ERROR':
      // Handle connection failure
      break;
    case 'VERIFICATION_FAILED':
      // Handle verification failure
      break;
    case 'MESSAGE_FAILED':
      // Handle message send failure
      break;
  }
});
```

### Error Recovery

```javascript
// Automatic retry with exponential backoff
const retryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000
};

session.startOTR(retryConfig);
```

## Best Practices

### Security

1. **Always verify identities** before sensitive communications
2. **Use strong verification methods** (QR code or SMP preferred)
3. **Monitor session status** for security changes
4. **Handle errors gracefully** without exposing sensitive information

### Performance

1. **Use lazy loading** for verification components
2. **Monitor performance metrics** in production
3. **Optimize for mobile** devices and slow networks
4. **Clean up resources** when components unmount

### Accessibility

1. **Test with screen readers** during development
2. **Ensure keyboard navigation** works throughout
3. **Provide alternative text** for all visual elements
4. **Use semantic HTML** and proper ARIA labels

### Internationalization

1. **Use translation keys** instead of hardcoded text
2. **Test with different languages** and text lengths
3. **Consider RTL languages** for global deployment
4. **Provide context** for translators

## Support

For questions, issues, or contributions:

- **GitHub**: https://github.com/forkrul/webOTteR
- **Documentation**: https://webottr.dev/docs
- **Issues**: https://github.com/forkrul/webOTteR/issues
- **Discussions**: https://github.com/forkrul/webOTteR/discussions

## License

MIT License - see LICENSE file for details.
