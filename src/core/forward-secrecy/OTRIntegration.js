/**
 * OTR Protocol Integration for Forward Secrecy
 * 
 * Integrates enhanced forward secrecy with the OTR protocol by:
 * - Adding KEY_ROTATION and KEY_ROTATION_ACK TLV messages
 * - Extending AKE to negotiate forward secrecy capabilities
 * - Maintaining backward compatibility with standard OTR
 * - Following libOTR protocol specifications
 */

import { EventEmitter } from 'events';

// TLV Types for Forward Secrecy (extending OTR v3 specification)
export const FS_TLV_TYPES = {
  KEY_ROTATION: 0x0009,
  KEY_ROTATION_ACK: 0x000A,
  FS_CAPABILITY: 0x000B,
  EMERGENCY_ROTATION: 0x000C
};

// Forward Secrecy Capability Flags
export const FS_CAPABILITIES = {
  AUTOMATIC_ROTATION: 0x01,
  MANUAL_ROTATION: 0x02,
  EMERGENCY_ROTATION: 0x04,
  ZERO_KNOWLEDGE_PROOFS: 0x08,
  SECURE_DELETION: 0x10,
  AUDIT_TRAILS: 0x20
};

export class OTRForwardSecrecyIntegration extends EventEmitter {
  constructor(otrSession, forwardSecrecyManager) {
    super();
    
    this.otrSession = otrSession;
    this.fsManager = forwardSecrecyManager;
    this.capabilities = 0;
    this.peerCapabilities = 0;
    this.rotationInProgress = false;
    
    // Set our capabilities
    this.capabilities = 
      FS_CAPABILITIES.AUTOMATIC_ROTATION |
      FS_CAPABILITIES.MANUAL_ROTATION |
      FS_CAPABILITIES.EMERGENCY_ROTATION |
      FS_CAPABILITIES.ZERO_KNOWLEDGE_PROOFS |
      FS_CAPABILITIES.SECURE_DELETION |
      FS_CAPABILITIES.AUDIT_TRAILS;
    
    this.setupEventHandlers();
  }

  /**
   * Setup event handlers for OTR session and Forward Secrecy Manager
   */
  setupEventHandlers() {
    // Listen for OTR session events
    this.otrSession.on('encrypted', () => {
      this.negotiateForwardSecrecyCapabilities();
    });
    
    this.otrSession.on('tlvReceived', (tlv) => {
      this.handleForwardSecrecyTLV(tlv);
    });
    
    // Listen for Forward Secrecy Manager events
    this.fsManager.on('rotationTriggered', (data) => {
      this.handleRotationTriggered(data);
    });
    
    this.fsManager.on('emergencyRotation', (data) => {
      this.handleEmergencyRotation(data);
    });
  }

  /**
   * Negotiate Forward Secrecy capabilities after OTR session is established
   */
  async negotiateForwardSecrecyCapabilities() {
    try {
      const capabilityTLV = this.createCapabilityTLV(this.capabilities);
      await this.sendTLVMessage(capabilityTLV);
      
      this.emit('capabilityNegotiationStarted', {
        ourCapabilities: this.capabilities,
        timestamp: Date.now()
      });
      
    } catch (error) {
      this.emit('error', {
        type: 'CAPABILITY_NEGOTIATION_FAILED',
        error: error.message,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Handle incoming Forward Secrecy TLV messages
   */
  async handleForwardSecrecyTLV(tlv) {
    try {
      switch (tlv.type) {
        case FS_TLV_TYPES.FS_CAPABILITY:
          await this.handleCapabilityTLV(tlv);
          break;
          
        case FS_TLV_TYPES.KEY_ROTATION:
          await this.handleKeyRotationTLV(tlv);
          break;
          
        case FS_TLV_TYPES.KEY_ROTATION_ACK:
          await this.handleKeyRotationAckTLV(tlv);
          break;
          
        case FS_TLV_TYPES.EMERGENCY_ROTATION:
          await this.handleEmergencyRotationTLV(tlv);
          break;
          
        default:
          // Unknown Forward Secrecy TLV type
          console.warn(`Unknown Forward Secrecy TLV type: ${tlv.type}`);
      }
    } catch (error) {
      this.emit('error', {
        type: 'TLV_PROCESSING_FAILED',
        tlvType: tlv.type,
        error: error.message,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Handle capability negotiation TLV
   */
  async handleCapabilityTLV(tlv) {
    const peerCapabilities = this.parseCapabilityTLV(tlv);
    this.peerCapabilities = peerCapabilities;
    
    // Determine common capabilities
    const commonCapabilities = this.capabilities & peerCapabilities;
    
    this.emit('capabilityNegotiated', {
      ourCapabilities: this.capabilities,
      peerCapabilities,
      commonCapabilities,
      timestamp: Date.now()
    });
    
    // Configure Forward Secrecy Manager based on negotiated capabilities
    await this.configureForwardSecrecy(commonCapabilities);
  }

  /**
   * Handle key rotation TLV message
   */
  async handleKeyRotationTLV(tlv) {
    if (this.rotationInProgress) {
      console.warn('Key rotation already in progress, ignoring new rotation request');
      return;
    }
    
    this.rotationInProgress = true;
    
    try {
      const rotationData = this.parseKeyRotationTLV(tlv);
      
      // Validate rotation proof
      const isValid = await this.fsManager.validateRotationProof(rotationData);
      if (!isValid) {
        throw new Error('Invalid key rotation proof');
      }
      
      // Process the key rotation
      await this.fsManager.processIncomingRotation(rotationData);
      
      // Send acknowledgment
      const ackTLV = this.createKeyRotationAckTLV(rotationData);
      await this.sendTLVMessage(ackTLV);
      
      this.emit('keyRotationProcessed', {
        rotationData,
        timestamp: Date.now()
      });
      
    } catch (error) {
      // Send error response
      const errorTLV = this.createErrorTLV('KEY_ROTATION_FAILED', error.message);
      await this.sendTLVMessage(errorTLV);
      
      throw error;
    } finally {
      this.rotationInProgress = false;
    }
  }

  /**
   * Handle key rotation acknowledgment TLV
   */
  async handleKeyRotationAckTLV(tlv) {
    const ackData = this.parseKeyRotationAckTLV(tlv);
    
    // Notify Forward Secrecy Manager of successful rotation
    await this.fsManager.confirmRotationAcknowledged(ackData);
    
    this.emit('keyRotationAcknowledged', {
      ackData,
      timestamp: Date.now()
    });
  }

  /**
   * Handle rotation triggered by Forward Secrecy Manager
   */
  async handleRotationTriggered(rotationData) {
    if (this.rotationInProgress) {
      console.warn('Key rotation already in progress');
      return;
    }
    
    // Check if peer supports key rotation
    if (!(this.peerCapabilities & FS_CAPABILITIES.AUTOMATIC_ROTATION)) {
      console.warn('Peer does not support automatic key rotation');
      return;
    }
    
    this.rotationInProgress = true;
    
    try {
      const rotationTLV = this.createKeyRotationTLV(rotationData);
      await this.sendTLVMessage(rotationTLV);
      
      this.emit('keyRotationSent', {
        rotationData,
        timestamp: Date.now()
      });
      
    } catch (error) {
      this.rotationInProgress = false;
      throw error;
    }
  }

  /**
   * Handle emergency rotation
   */
  async handleEmergencyRotation(rotationData) {
    const emergencyTLV = this.createEmergencyRotationTLV(rotationData);
    await this.sendTLVMessage(emergencyTLV);
    
    this.emit('emergencyRotationSent', {
      rotationData,
      timestamp: Date.now()
    });
  }

  /**
   * Create capability TLV
   */
  createCapabilityTLV(capabilities) {
    const data = new Uint8Array(4);
    const view = new DataView(data.buffer);
    view.setUint32(0, capabilities, false); // Big-endian
    
    return {
      type: FS_TLV_TYPES.FS_CAPABILITY,
      data: data
    };
  }

  /**
   * Create key rotation TLV
   */
  createKeyRotationTLV(rotationData) {
    // TLV Structure:
    // - New DH public key (MPI format)
    // - Key generation number (4 bytes)
    // - Rotation proof hash (32 bytes)
    // - Timestamp (8 bytes)
    
    const publicKeyBytes = rotationData.newPublicKey;
    const totalLength = 4 + publicKeyBytes.length + 4 + 32 + 8;
    const data = new Uint8Array(totalLength);
    const view = new DataView(data.buffer);
    
    let offset = 0;
    
    // Public key length (MPI format)
    view.setUint32(offset, publicKeyBytes.length, false);
    offset += 4;
    
    // Public key data
    data.set(publicKeyBytes, offset);
    offset += publicKeyBytes.length;
    
    // Key generation number
    view.setUint32(offset, rotationData.generation, false);
    offset += 4;
    
    // Rotation proof hash
    data.set(rotationData.proofHash, offset);
    offset += 32;
    
    // Timestamp
    view.setBigUint64(offset, BigInt(rotationData.timestamp), false);
    
    return {
      type: FS_TLV_TYPES.KEY_ROTATION,
      data: data
    };
  }

  /**
   * Create key rotation acknowledgment TLV
   */
  createKeyRotationAckTLV(rotationData) {
    const data = new Uint8Array(37); // 1 + 4 + 32 bytes
    const view = new DataView(data.buffer);
    
    let offset = 0;
    
    // Acknowledgment status (1 = success)
    view.setUint8(offset, 1);
    offset += 1;
    
    // Generation number
    view.setUint32(offset, rotationData.generation, false);
    offset += 4;
    
    // Confirmation hash
    data.set(rotationData.confirmationHash, offset);
    
    return {
      type: FS_TLV_TYPES.KEY_ROTATION_ACK,
      data: data
    };
  }

  /**
   * Send TLV message through OTR session
   */
  async sendTLVMessage(tlv) {
    // Send empty data message with TLV
    return await this.otrSession.sendDataMessage('', [tlv]);
  }

  /**
   * Parse capability TLV
   */
  parseCapabilityTLV(tlv) {
    if (tlv.data.length < 4) {
      throw new Error('Invalid capability TLV length');
    }
    
    const view = new DataView(tlv.data.buffer);
    return view.getUint32(0, false); // Big-endian
  }

  /**
   * Configure Forward Secrecy based on negotiated capabilities
   */
  async configureForwardSecrecy(commonCapabilities) {
    const config = {
      automaticRotation: !!(commonCapabilities & FS_CAPABILITIES.AUTOMATIC_ROTATION),
      manualRotation: !!(commonCapabilities & FS_CAPABILITIES.MANUAL_ROTATION),
      emergencyRotation: !!(commonCapabilities & FS_CAPABILITIES.EMERGENCY_ROTATION),
      zeroKnowledgeProofs: !!(commonCapabilities & FS_CAPABILITIES.ZERO_KNOWLEDGE_PROOFS),
      secureDeletion: !!(commonCapabilities & FS_CAPABILITIES.SECURE_DELETION),
      auditTrails: !!(commonCapabilities & FS_CAPABILITIES.AUDIT_TRAILS)
    };
    
    await this.fsManager.updateConfiguration(config);
    
    this.emit('forwardSecrecyConfigured', {
      config,
      timestamp: Date.now()
    });
  }

  /**
   * Get integration status
   */
  getStatus() {
    return {
      capabilities: this.capabilities,
      peerCapabilities: this.peerCapabilities,
      rotationInProgress: this.rotationInProgress,
      otrSessionState: this.otrSession.getState(),
      timestamp: Date.now()
    };
  }
}
