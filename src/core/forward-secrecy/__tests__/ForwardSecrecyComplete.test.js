/**
 * Complete Forward Secrecy System Integration Tests
 * 
 * Comprehensive testing of the entire forward secrecy implementation:
 * - End-to-end system integration
 * - Enterprise policy compliance
 * - Advanced zero-knowledge verification
 * - Performance and scalability testing
 */

import { ForwardSecrecyManager } from '../ForwardSecrecyManager.js';
import { EnterprisePolicyManager } from '../EnterprisePolicyManager.js';
import { ZeroKnowledgeVerifier } from '../ZeroKnowledgeVerifier.js';
import { MemorySanitizer } from '../MemorySanitizer.js';

// Mock WebCrypto and performance APIs
global.crypto = {
  subtle: {
    digest: jest.fn(),
    importKey: jest.fn(),
    deriveBits: jest.fn()
  },
  getRandomValues: jest.fn()
};

global.performance = {
  now: jest.fn(() => Date.now()),
  memory: {
    usedJSHeapSize: 1024 * 1024
  }
};

describe('Complete Forward Secrecy System Tests', () => {
  let forwardSecrecyManager;
  let enterprisePolicyManager;
  let zeroKnowledgeVerifier;
  let memorySanitizer;
  
  beforeEach(async () => {
    // Setup crypto mocks
    crypto.getRandomValues.mockImplementation((array) => {
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
      return array;
    });
    
    crypto.subtle.digest.mockImplementation(async (algorithm, data) => {
      const hash = new Uint8Array(32);
      crypto.getRandomValues(hash);
      return hash.buffer;
    });
    
    crypto.subtle.importKey.mockResolvedValue({ type: 'secret' });
    crypto.subtle.deriveBits.mockImplementation(async (algorithm, key, length) => {
      const bits = new Uint8Array(length / 8);
      crypto.getRandomValues(bits);
      return bits.buffer;
    });
    
    // Initialize enterprise policy manager
    enterprisePolicyManager = new EnterprisePolicyManager({
      policyEnforcement: true,
      complianceStandards: ['FIPS-140-2', 'DoD-5220.22-M', 'SOX'],
      auditLevel: 'comprehensive'
    });
    
    // Initialize forward secrecy manager with enterprise integration
    forwardSecrecyManager = new ForwardSecrecyManager({
      autoRotation: true,
      rotationInterval: 3600000, // 1 hour
      messageCountThreshold: 1000,
      dataVolumeThreshold: 10 * 1024 * 1024,
      fipsCompliance: true,
      enterpriseIntegration: true
    });
    
    // Initialize components
    zeroKnowledgeVerifier = new ZeroKnowledgeVerifier({
      advancedProofs: true,
      enterpriseFeatures: true,
      batchVerification: true
    });
    
    memorySanitizer = new MemorySanitizer({
      aggressiveClearing: true,
      crossPlatformOptimization: true
    });
    
    // Initialize all components
    await enterprisePolicyManager.initialize();
    await forwardSecrecyManager.initialize();
    await zeroKnowledgeVerifier.initialize();
    await memorySanitizer.initialize();
  });
  
  afterEach(async () => {
    if (forwardSecrecyManager) await forwardSecrecyManager.shutdown();
    if (enterprisePolicyManager) await enterprisePolicyManager.shutdown();
    if (zeroKnowledgeVerifier) await zeroKnowledgeVerifier.shutdown();
    if (memorySanitizer) await memorySanitizer.shutdown();
    jest.clearAllMocks();
  });

  describe('Enterprise Integration', () => {
    test('should integrate with enterprise policy management', async () => {
      // Validate key rotation against enterprise policy
      const rotationOperation = {
        type: 'KEY_ROTATION',
        timeSinceLastRotation: 2 * 3600000, // 2 hours
        messageCount: 500,
        dataVolume: 5 * 1024 * 1024 // 5MB
      };
      
      const validation = await enterprisePolicyManager.validateOperation(rotationOperation);
      
      expect(validation.valid).toBe(true);
      expect(validation.violations).toHaveLength(0);
    });

    test('should enforce enterprise policies during operations', async () => {
      // Test policy violation detection
      const violatingOperation = {
        type: 'KEY_DELETION',
        erasurePasses: 2, // Below DoD minimum of 3
        verificationEnabled: false
      };
      
      const validation = await enterprisePolicyManager.validateOperation(violatingOperation);
      
      expect(validation.valid).toBe(false);
      expect(validation.violations.length).toBeGreaterThan(0);
      expect(validation.violations.some(v => v.violation.includes('Insufficient erasure passes'))).toBe(true);
    });

    test('should generate compliance reports', async () => {
      // Perform some operations to generate metrics
      await enterprisePolicyManager.validateOperation({
        type: 'KEY_ROTATION',
        timeSinceLastRotation: 3600000
      });
      
      const report = await enterprisePolicyManager.generateComplianceReport(
        Date.now() - 3600000,
        Date.now()
      );
      
      expect(report.reportId).toBeDefined();
      expect(report.complianceStandards).toContain('FIPS-140-2');
      expect(report.summary.totalChecks).toBeGreaterThan(0);
      expect(report.activePolicies).toHaveLength(5); // Default policies
    });
  });

  describe('Advanced Zero-Knowledge Verification', () => {
    test('should generate advanced deletion proofs', async () => {
      const deletionData = {
        keyGeneration: 1,
        deletionResult: {
          deletionTime: 25,
          erasurePasses: 7,
          verified: true
        },
        verificationResult: {
          confidence: 0.95,
          entropy: 7.8,
          patterns: { found: false },
          secure: true
        }
      };
      
      const proof = await zeroKnowledgeVerifier.generateAdvancedProof({
        type: 'DELETION_PROOF',
        data: deletionData,
        metadata: { standard: 'DoD-5220.22-M' }
      });
      
      expect(proof.type).toBe('DELETION_PROOF');
      expect(proof.deletionCommitment).toBeDefined();
      expect(proof.erasureProof).toBeDefined();
      expect(proof.verificationProof).toBeDefined();
      expect(proof.metadata.standard).toBe('DoD-5220.22-M');
    });

    test('should generate forward secrecy proofs', async () => {
      const secrecyData = {
        currentGeneration: 3,
        rotationHistory: [
          { oldGeneration: 0, newGeneration: 1, trigger: 'TIME_BASED', timestamp: Date.now() - 7200000 },
          { oldGeneration: 1, newGeneration: 2, trigger: 'MESSAGE_COUNT', timestamp: Date.now() - 3600000 },
          { oldGeneration: 2, newGeneration: 3, trigger: 'MANUAL', timestamp: Date.now() }
        ],
        secrecyValidation: { forwardSecrecyMaintained: true }
      };
      
      const proof = await zeroKnowledgeVerifier.generateAdvancedProof({
        type: 'FORWARD_SECRECY_PROOF',
        data: secrecyData,
        metadata: { auditRequired: true }
      });
      
      expect(proof.type).toBe('FORWARD_SECRECY_PROOF');
      expect(proof.currentGeneration).toBe(3);
      expect(proof.secrecyCommitment).toBeDefined();
      expect(proof.evolutionProof).toBeDefined();
      expect(proof.temporalProof).toBeDefined();
      expect(proof.metadata.rotationCount).toBe(3);
    });

    test('should generate batch proofs for multiple operations', async () => {
      const batchData = {
        operations: [
          {
            type: 'DELETION_PROOF',
            data: {
              keyGeneration: 1,
              deletionResult: { deletionTime: 20, erasurePasses: 7, verified: true },
              verificationResult: { confidence: 0.95, entropy: 7.8, secure: true }
            },
            metadata: {}
          },
          {
            type: 'KEY_ROTATION',
            data: {
              oldKeyGeneration: 1,
              newKeyGeneration: 2,
              rotationData: { rotationId: 'test-rotation' }
            },
            metadata: {}
          }
        ]
      };
      
      const batchProof = await zeroKnowledgeVerifier.generateAdvancedProof({
        type: 'BATCH_PROOF',
        data: batchData,
        metadata: { batchId: 'test-batch' }
      });
      
      expect(batchProof.type).toBe('BATCH_PROOF');
      expect(batchProof.batchSize).toBe(2);
      expect(batchProof.batchCommitment).toBeDefined();
      expect(batchProof.individualProofs).toHaveLength(2);
    });

    test('should generate enterprise proofs with policy compliance', async () => {
      const enterpriseData = {
        policyCompliance: {
          version: '1.0.0',
          policies: ['key-rotation-policy', 'secure-deletion-policy'],
          compliant: true
        },
        auditRequirements: {
          level: 'comprehensive',
          retention: 90 * 24 * 3600000,
          integrityRequired: true
        },
        complianceStandards: ['FIPS-140-2', 'DoD-5220.22-M']
      };
      
      const enterpriseProof = await zeroKnowledgeVerifier.generateAdvancedProof({
        type: 'ENTERPRISE_PROOF',
        data: enterpriseData,
        metadata: { organizationId: 'test-org' }
      });
      
      expect(enterpriseProof.type).toBe('ENTERPRISE_PROOF');
      expect(enterpriseProof.policyCommitment).toBeDefined();
      expect(enterpriseProof.auditProof).toBeDefined();
      expect(enterpriseProof.complianceProof).toBeDefined();
      expect(enterpriseProof.metadata.standards).toContain('FIPS-140-2');
    });
  });

  describe('End-to-End System Integration', () => {
    test('should perform complete secure key rotation workflow', async () => {
      // Set up event listeners
      const events = [];
      forwardSecrecyManager.on('keyRotated', (event) => events.push(event));
      forwardSecrecyManager.on('deletionCompleted', (event) => events.push(event));
      forwardSecrecyManager.on('verificationCompleted', (event) => events.push(event));
      
      // Perform key rotation
      const rotationResult = await forwardSecrecyManager.rotateKeysManually();
      
      // Verify all events were emitted
      expect(events.length).toBeGreaterThan(0);
      expect(events.some(e => e.trigger === 'MANUAL')).toBe(true);
      
      // Verify rotation result
      expect(rotationResult.success).toBe(true);
      expect(rotationResult.keyGeneration).toBe(1);
      expect(rotationResult.rotationProof).toBeDefined();
      expect(rotationResult.deletionVerified).toBe(true);
    });

    test('should maintain system performance under load', async () => {
      const operations = [];
      const startTime = performance.now();
      
      // Perform multiple concurrent operations
      for (let i = 0; i < 5; i++) {
        operations.push(forwardSecrecyManager.rotateKeysManually());
      }
      
      const results = await Promise.all(operations);
      const totalTime = performance.now() - startTime;
      
      // Verify all operations succeeded
      expect(results.every(r => r.success)).toBe(true);
      
      // Verify performance requirements
      const averageTime = totalTime / operations.length;
      expect(averageTime).toBeLessThan(200); // 200ms per operation under load
    });

    test('should handle system errors gracefully', async () => {
      // Mock a component failure
      const originalRotate = forwardSecrecyManager.keyRotationEngine.rotateKeys;
      forwardSecrecyManager.keyRotationEngine.rotateKeys = jest.fn()
        .mockRejectedValueOnce(new Error('Simulated failure'))
        .mockImplementation(originalRotate);
      
      // First rotation should fail
      await expect(forwardSecrecyManager.rotateKeysManually()).rejects.toThrow('Simulated failure');
      
      // System should recover for subsequent operations
      const result = await forwardSecrecyManager.rotateKeysManually();
      expect(result.success).toBe(true);
    });
  });

  describe('Compliance and Audit Integration', () => {
    test('should maintain comprehensive audit trails', async () => {
      // Perform operations that generate audit events
      await forwardSecrecyManager.rotateKeysManually();
      await forwardSecrecyManager.emergencyRotation('TEST_EMERGENCY');
      
      // Generate audit report
      const auditReport = await forwardSecrecyManager.getAuditReport(
        Date.now() - 3600000,
        Date.now()
      );
      
      expect(auditReport.summary.totalEvents).toBeGreaterThan(0);
      expect(auditReport.events.some(e => e.type === 'KEY_ROTATION_STARTED')).toBe(true);
      expect(auditReport.events.some(e => e.type === 'KEY_ROTATION_COMPLETED')).toBe(true);
      expect(auditReport.integrity.valid).toBe(true);
    });

    test('should validate FIPS 140-2 compliance', async () => {
      const operation = {
        type: 'KEY_GENERATION',
        algorithm: 'AES-256',
        keyManagement: 'secure'
      };
      
      const compliance = await enterprisePolicyManager.validateFIPS140_2(operation, {
        algorithm: 'AES-256',
        keyManagement: 'secure'
      });
      
      expect(compliance.compliant).toBe(true);
      expect(compliance.level).toBe('Level-2');
      expect(compliance.requirements.every(req => req.status === 'compliant')).toBe(true);
    });

    test('should validate DoD 5220.22-M compliance', async () => {
      const operation = {
        type: 'SECURE_DELETION'
      };
      
      const compliance = await enterprisePolicyManager.validateDoD5220_22M(operation, {
        passes: 7,
        patterns: ['random', 'zeros', 'ones'],
        verified: true
      });
      
      expect(compliance.compliant).toBe(true);
      expect(compliance.standard).toBe('DoD-5220.22-M');
      expect(compliance.requirements.every(req => req.status === 'compliant')).toBe(true);
    });
  });

  describe('Performance and Scalability', () => {
    test('should meet all timing requirements', async () => {
      const timingTests = [
        {
          operation: 'Key Rotation',
          test: () => forwardSecrecyManager.rotateKeysManually(),
          requirement: 100 // 100ms
        },
        {
          operation: 'Secure Deletion',
          test: async () => {
            const testKeys = { masterKey: new Uint8Array(32) };
            return await forwardSecrecyManager.secureDeletionManager.deleteKeys({
              keyGeneration: 1,
              keys: testKeys
            });
          },
          requirement: 50 // 50ms
        },
        {
          operation: 'Zero-Knowledge Verification',
          test: async () => {
            const proof = await zeroKnowledgeVerifier.generateRotationProof({
              oldKeyGeneration: 0,
              newKeyGeneration: 1,
              rotationData: { rotationId: 'test' }
            });
            return await zeroKnowledgeVerifier.verifyRotationProof(proof);
          },
          requirement: 100 // 100ms
        }
      ];
      
      for (const timingTest of timingTests) {
        const startTime = performance.now();
        const result = await timingTest.test();
        const operationTime = performance.now() - startTime;
        
        expect(operationTime).toBeLessThan(timingTest.requirement);
        expect(result.success || result.verified).toBe(true);
      }
    });

    test('should scale with increasing data sizes', async () => {
      const dataSizes = [32, 128, 512, 1024]; // bytes
      const results = [];
      
      for (const size of dataSizes) {
        const testKeys = {
          masterKey: new Uint8Array(size),
          encryptionKey: new Uint8Array(size)
        };
        
        const startTime = performance.now();
        const result = await forwardSecrecyManager.secureDeletionManager.deleteKeys({
          keyGeneration: 1,
          keys: testKeys
        });
        const operationTime = performance.now() - startTime;
        
        results.push({ size, time: operationTime, success: result.success });
      }
      
      // Verify all operations succeeded
      expect(results.every(r => r.success)).toBe(true);
      
      // Verify reasonable scaling (should be roughly linear)
      const timeRatio = results[3].time / results[0].time; // 1024 bytes vs 32 bytes
      expect(timeRatio).toBeLessThan(10); // Should not be more than 10x slower
    });
  });

  describe('System Status and Monitoring', () => {
    test('should provide comprehensive system status', async () => {
      // Perform some operations to populate metrics
      await forwardSecrecyManager.rotateKeysManually();
      
      const status = forwardSecrecyManager.getStatus();
      
      expect(status.initialized).toBe(true);
      expect(status.currentKeyGeneration).toBe(1);
      expect(status.performanceMetrics.averageRotationTime).toBeGreaterThan(0);
      expect(status.autoRotationEnabled).toBe(true);
    });

    test('should track performance metrics accurately', async () => {
      const initialStatus = forwardSecrecyManager.getStatus();
      
      // Perform multiple operations
      await forwardSecrecyManager.rotateKeysManually();
      await forwardSecrecyManager.rotateKeysManually();
      
      const finalStatus = forwardSecrecyManager.getStatus();
      
      expect(finalStatus.currentKeyGeneration).toBe(initialStatus.currentKeyGeneration + 2);
      expect(finalStatus.performanceMetrics.averageRotationTime).toBeGreaterThan(0);
    });

    test('should provide enterprise policy status', async () => {
      const policyStatus = enterprisePolicyManager.getStatus();
      
      expect(policyStatus.initialized).toBe(true);
      expect(policyStatus.activePolicies).toBe(5); // Default policies
      expect(policyStatus.policyEnforcement).toBe(true);
      expect(policyStatus.complianceStandards).toContain('FIPS-140-2');
    });
  });
});
