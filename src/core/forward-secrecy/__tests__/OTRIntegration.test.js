/**
 * Tests for OTR Forward Secrecy Integration
 * 
 * Tests the integration between Forward Secrecy and OTR protocol,
 * including TLV message handling, capability negotiation, and
 * key rotation protocol compliance.
 */

import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { OTRForwardSecrecyIntegration, FS_TLV_TYPES, FS_CAPABILITIES } from '../OTRIntegration.js';

// Mock OTR Session
class MockOTRSession {
  constructor() {
    this.state = 'ENCRYPTED';
    this.eventHandlers = {};
    this.sentMessages = [];
  }
  
  on(event, handler) {
    if (!this.eventHandlers[event]) {
      this.eventHandlers[event] = [];
    }
    this.eventHandlers[event].push(handler);
  }
  
  emit(event, data) {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].forEach(handler => handler(data));
    }
  }
  
  async sendDataMessage(message, tlvs) {
    this.sentMessages.push({ message, tlvs });
    return { success: true };
  }
  
  getState() {
    return this.state;
  }
}

// Mock Forward Secrecy Manager
class MockForwardSecrecyManager {
  constructor() {
    this.eventHandlers = {};
    this.rotationData = null;
  }
  
  on(event, handler) {
    if (!this.eventHandlers[event]) {
      this.eventHandlers[event] = [];
    }
    this.eventHandlers[event].push(handler);
  }
  
  emit(event, data) {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].forEach(handler => handler(data));
    }
  }
  
  async validateRotationProof(rotationData) {
    return true; // Mock validation success
  }
  
  async processIncomingRotation(rotationData) {
    this.rotationData = rotationData;
    return { success: true };
  }
  
  async confirmRotationAcknowledged(ackData) {
    return { success: true };
  }
  
  async updateConfiguration(config) {
    this.config = config;
    return { success: true };
  }
}

describe('OTRForwardSecrecyIntegration', () => {
  let otrSession;
  let fsManager;
  let integration;

  beforeEach(() => {
    otrSession = new MockOTRSession();
    fsManager = new MockForwardSecrecyManager();
    integration = new OTRForwardSecrecyIntegration(otrSession, fsManager);
  });

  afterEach(() => {
    // Clean up any timers or resources
  });

  describe('Initialization', () => {
    test('should initialize with correct capabilities', () => {
      expect(integration.capabilities).toBe(
        FS_CAPABILITIES.AUTOMATIC_ROTATION |
        FS_CAPABILITIES.MANUAL_ROTATION |
        FS_CAPABILITIES.EMERGENCY_ROTATION |
        FS_CAPABILITIES.ZERO_KNOWLEDGE_PROOFS |
        FS_CAPABILITIES.SECURE_DELETION |
        FS_CAPABILITIES.AUDIT_TRAILS
      );
    });

    test('should setup event handlers', () => {
      expect(otrSession.eventHandlers.encrypted).toBeDefined();
      expect(otrSession.eventHandlers.tlvReceived).toBeDefined();
      expect(fsManager.eventHandlers.rotationTriggered).toBeDefined();
      expect(fsManager.eventHandlers.emergencyRotation).toBeDefined();
    });
  });

  describe('Capability Negotiation', () => {
    test('should send capability TLV when OTR session is encrypted', async () => {
      // Trigger encrypted event
      otrSession.emit('encrypted');
      
      // Wait for async operations
      await new Promise(resolve => setTimeout(resolve, 0));
      
      expect(otrSession.sentMessages).toHaveLength(1);
      expect(otrSession.sentMessages[0].tlvs).toHaveLength(1);
      expect(otrSession.sentMessages[0].tlvs[0].type).toBe(FS_TLV_TYPES.FS_CAPABILITY);
    });

    test('should handle incoming capability TLV', async () => {
      const capabilityTLV = integration.createCapabilityTLV(
        FS_CAPABILITIES.AUTOMATIC_ROTATION | FS_CAPABILITIES.MANUAL_ROTATION
      );
      
      let negotiatedEvent = null;
      integration.on('capabilityNegotiated', (data) => {
        negotiatedEvent = data;
      });
      
      await integration.handleForwardSecrecyTLV(capabilityTLV);
      
      expect(negotiatedEvent).toBeTruthy();
      expect(negotiatedEvent.peerCapabilities).toBe(
        FS_CAPABILITIES.AUTOMATIC_ROTATION | FS_CAPABILITIES.MANUAL_ROTATION
      );
    });

    test('should configure Forward Secrecy based on common capabilities', async () => {
      const peerCapabilities = FS_CAPABILITIES.AUTOMATIC_ROTATION | FS_CAPABILITIES.SECURE_DELETION;
      const commonCapabilities = integration.capabilities & peerCapabilities;
      
      await integration.configureForwardSecrecy(commonCapabilities);
      
      expect(fsManager.config).toBeDefined();
      expect(fsManager.config.automaticRotation).toBe(true);
      expect(fsManager.config.secureDeletion).toBe(true);
      expect(fsManager.config.manualRotation).toBe(false); // Not in peer capabilities
    });
  });

  describe('Key Rotation Protocol', () => {
    test('should create valid key rotation TLV', () => {
      const rotationData = {
        newPublicKey: new Uint8Array(32).fill(0x42),
        generation: 5,
        proofHash: new Uint8Array(32).fill(0x33),
        timestamp: Date.now()
      };
      
      const tlv = integration.createKeyRotationTLV(rotationData);
      
      expect(tlv.type).toBe(FS_TLV_TYPES.KEY_ROTATION);
      expect(tlv.data).toBeInstanceOf(Uint8Array);
      expect(tlv.data.length).toBe(4 + 32 + 4 + 32 + 8); // Expected structure
    });

    test('should handle incoming key rotation TLV', async () => {
      const rotationData = {
        newPublicKey: new Uint8Array(32).fill(0x42),
        generation: 5,
        proofHash: new Uint8Array(32).fill(0x33),
        timestamp: Date.now()
      };
      
      const tlv = integration.createKeyRotationTLV(rotationData);
      
      let processedEvent = null;
      integration.on('keyRotationProcessed', (data) => {
        processedEvent = data;
      });
      
      await integration.handleForwardSecrecyTLV(tlv);
      
      expect(processedEvent).toBeTruthy();
      expect(fsManager.rotationData).toBeTruthy();
      expect(otrSession.sentMessages).toHaveLength(1); // ACK sent
      expect(otrSession.sentMessages[0].tlvs[0].type).toBe(FS_TLV_TYPES.KEY_ROTATION_ACK);
    });

    test('should create valid key rotation ACK TLV', () => {
      const rotationData = {
        generation: 5,
        confirmationHash: new Uint8Array(32).fill(0x55)
      };
      
      const tlv = integration.createKeyRotationAckTLV(rotationData);
      
      expect(tlv.type).toBe(FS_TLV_TYPES.KEY_ROTATION_ACK);
      expect(tlv.data).toBeInstanceOf(Uint8Array);
      expect(tlv.data.length).toBe(37); // 1 + 4 + 32 bytes
    });

    test('should handle rotation triggered by Forward Secrecy Manager', async () => {
      // Set peer capabilities to support automatic rotation
      integration.peerCapabilities = FS_CAPABILITIES.AUTOMATIC_ROTATION;
      
      const rotationData = {
        trigger: 'TIME',
        generation: 3,
        newPublicKey: new Uint8Array(32).fill(0x77),
        proofHash: new Uint8Array(32).fill(0x88),
        timestamp: Date.now()
      };
      
      let sentEvent = null;
      integration.on('keyRotationSent', (data) => {
        sentEvent = data;
      });
      
      await integration.handleRotationTriggered(rotationData);
      
      expect(sentEvent).toBeTruthy();
      expect(otrSession.sentMessages).toHaveLength(1);
      expect(otrSession.sentMessages[0].tlvs[0].type).toBe(FS_TLV_TYPES.KEY_ROTATION);
    });

    test('should prevent concurrent rotations', async () => {
      integration.rotationInProgress = true;
      
      const rotationData = {
        trigger: 'MANUAL',
        generation: 4
      };
      
      // Should not throw or send messages when rotation is in progress
      await integration.handleRotationTriggered(rotationData);
      
      expect(otrSession.sentMessages).toHaveLength(0);
    });
  });

  describe('Emergency Rotation', () => {
    test('should handle emergency rotation', async () => {
      const rotationData = {
        trigger: 'EMERGENCY',
        reason: 'SECURITY_BREACH',
        generation: 10,
        newPublicKey: new Uint8Array(32).fill(0x99),
        timestamp: Date.now()
      };
      
      let emergencyEvent = null;
      integration.on('emergencyRotationSent', (data) => {
        emergencyEvent = data;
      });
      
      await integration.handleEmergencyRotation(rotationData);
      
      expect(emergencyEvent).toBeTruthy();
      expect(otrSession.sentMessages).toHaveLength(1);
      expect(otrSession.sentMessages[0].tlvs[0].type).toBe(FS_TLV_TYPES.EMERGENCY_ROTATION);
    });
  });

  describe('TLV Message Parsing', () => {
    test('should parse capability TLV correctly', () => {
      const capabilities = FS_CAPABILITIES.AUTOMATIC_ROTATION | FS_CAPABILITIES.SECURE_DELETION;
      const tlv = integration.createCapabilityTLV(capabilities);
      
      const parsed = integration.parseCapabilityTLV(tlv);
      
      expect(parsed).toBe(capabilities);
    });

    test('should handle invalid capability TLV', () => {
      const invalidTLV = {
        type: FS_TLV_TYPES.FS_CAPABILITY,
        data: new Uint8Array(2) // Too short
      };
      
      expect(() => {
        integration.parseCapabilityTLV(invalidTLV);
      }).toThrow('Invalid capability TLV length');
    });
  });

  describe('Status and Monitoring', () => {
    test('should return correct status', () => {
      integration.peerCapabilities = FS_CAPABILITIES.AUTOMATIC_ROTATION;
      integration.rotationInProgress = true;
      
      const status = integration.getStatus();
      
      expect(status.capabilities).toBe(integration.capabilities);
      expect(status.peerCapabilities).toBe(FS_CAPABILITIES.AUTOMATIC_ROTATION);
      expect(status.rotationInProgress).toBe(true);
      expect(status.otrSessionState).toBe('ENCRYPTED');
      expect(status.timestamp).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    test('should emit error on TLV processing failure', async () => {
      // Mock validation failure
      fsManager.validateRotationProof = jest.fn().mockRejectedValue(new Error('Validation failed'));
      
      let errorEvent = null;
      integration.on('error', (data) => {
        errorEvent = data;
      });
      
      const rotationData = {
        newPublicKey: new Uint8Array(32),
        generation: 1,
        proofHash: new Uint8Array(32),
        timestamp: Date.now()
      };
      
      const tlv = integration.createKeyRotationTLV(rotationData);
      
      try {
        await integration.handleForwardSecrecyTLV(tlv);
      } catch (error) {
        // Expected to throw
      }
      
      expect(errorEvent).toBeTruthy();
      expect(errorEvent.type).toBe('TLV_PROCESSING_FAILED');
    });
  });
});
