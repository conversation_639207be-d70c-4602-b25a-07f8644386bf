/**
 * Forward Secrecy Manager Tests
 * 
 * Comprehensive test suite for ForwardSecrecyManager:
 * - Initialization and configuration
 * - Key rotation mechanisms
 * - Secure deletion operations
 * - Performance and reliability
 */

import { ForwardSecrecyManager } from '../ForwardSecrecyManager.js';
import { SecureRandom } from '../../crypto/SecureRandom.js';

// Mock WebCrypto for Node.js environment
global.crypto = {
  subtle: {
    digest: jest.fn(),
    importKey: jest.fn(),
    deriveBits: jest.fn(),
    generateKey: jest.fn(),
    encrypt: jest.fn(),
    decrypt: jest.fn()
  },
  getRandomValues: jest.fn()
};

global.performance = {
  now: jest.fn(() => Date.now()),
  memory: {
    usedJSHeapSize: 1024 * 1024
  }
};

describe('ForwardSecrecyManager', () => {
  let forwardSecrecyManager;
  let mockSecureRandom;
  
  beforeEach(async () => {
    // Setup crypto mocks
    crypto.getRandomValues.mockImplementation((array) => {
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
      return array;
    });
    
    crypto.subtle.digest.mockImplementation(async (algorithm, data) => {
      const hash = new Uint8Array(32);
      crypto.getRandomValues(hash);
      return hash.buffer;
    });
    
    crypto.subtle.importKey.mockResolvedValue({ type: 'secret' });
    crypto.subtle.deriveBits.mockImplementation(async (algorithm, key, length) => {
      const bits = new Uint8Array(length / 8);
      crypto.getRandomValues(bits);
      return bits.buffer;
    });
    
    crypto.subtle.generateKey.mockResolvedValue({
      privateKey: { type: 'private' },
      publicKey: { type: 'public' }
    });
    
    crypto.subtle.encrypt.mockImplementation(async (algorithm, key, data) => {
      const encrypted = new Uint8Array(data.byteLength + 16);
      crypto.getRandomValues(encrypted);
      return encrypted.buffer;
    });
    
    crypto.subtle.decrypt.mockImplementation(async (algorithm, key, data) => {
      const decrypted = new Uint8Array(data.byteLength - 16);
      crypto.getRandomValues(decrypted);
      return decrypted.buffer;
    });
    
    // Create mock secure random
    mockSecureRandom = {
      initialize: jest.fn().mockResolvedValue(true),
      getRandomBytes: jest.fn().mockImplementation(async (array) => {
        crypto.getRandomValues(array);
        return array;
      }),
      getStatus: jest.fn().mockReturnValue({ initialized: true }),
      shutdown: jest.fn()
    };
    
    // Initialize ForwardSecrecyManager
    forwardSecrecyManager = new ForwardSecrecyManager({
      autoRotation: true,
      rotationInterval: 3600000, // 1 hour
      messageCountThreshold: 1000,
      dataVolumeThreshold: 10 * 1024 * 1024,
      fipsCompliance: true,
      secureRandom: mockSecureRandom
    });
  });
  
  afterEach(async () => {
    if (forwardSecrecyManager) {
      await forwardSecrecyManager.shutdown();
    }
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should initialize with default configuration', async () => {
      const result = await forwardSecrecyManager.initialize();
      
      expect(result.success).toBe(true);
      expect(forwardSecrecyManager.state.initialized).toBe(true);
      expect(forwardSecrecyManager.state.currentKeyGeneration).toBe(0);
    });

    test('should initialize with custom configuration', async () => {
      const customManager = new ForwardSecrecyManager({
        autoRotation: false,
        rotationInterval: 7200000, // 2 hours
        messageCountThreshold: 500,
        dataVolumeThreshold: 5 * 1024 * 1024,
        fipsCompliance: false,
        secureRandom: mockSecureRandom
      });
      
      const result = await customManager.initialize();
      
      expect(result.success).toBe(true);
      expect(customManager.config.autoRotation).toBe(false);
      expect(customManager.config.rotationInterval).toBe(7200000);
      expect(customManager.config.messageCountThreshold).toBe(500);
      
      await customManager.shutdown();
    });

    test('should handle initialization failure gracefully', async () => {
      mockSecureRandom.initialize.mockRejectedValueOnce(new Error('Initialization failed'));
      
      await expect(forwardSecrecyManager.initialize()).rejects.toThrow('Initialization failed');
      expect(forwardSecrecyManager.state.initialized).toBe(false);
    });

    test('should validate configuration parameters', () => {
      expect(() => {
        new ForwardSecrecyManager({
          rotationInterval: -1000 // Invalid negative interval
        });
      }).toThrow();
      
      expect(() => {
        new ForwardSecrecyManager({
          messageCountThreshold: 0 // Invalid zero threshold
        });
      }).toThrow();
    });
  });

  describe('Key Rotation', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
    });

    test('should perform manual key rotation', async () => {
      const result = await forwardSecrecyManager.rotateKeysManually();
      
      expect(result.success).toBe(true);
      expect(result.keyGeneration).toBe(1);
      expect(result.rotationProof).toBeDefined();
      expect(result.deletionVerified).toBe(true);
      expect(forwardSecrecyManager.state.currentKeyGeneration).toBe(1);
    });

    test('should perform multiple key rotations', async () => {
      // First rotation
      const result1 = await forwardSecrecyManager.rotateKeysManually();
      expect(result1.keyGeneration).toBe(1);
      
      // Second rotation
      const result2 = await forwardSecrecyManager.rotateKeysManually();
      expect(result2.keyGeneration).toBe(2);
      
      // Third rotation
      const result3 = await forwardSecrecyManager.rotateKeysManually();
      expect(result3.keyGeneration).toBe(3);
      
      expect(forwardSecrecyManager.state.currentKeyGeneration).toBe(3);
    });

    test('should trigger automatic rotation based on time', async () => {
      // Set short rotation interval for testing
      forwardSecrecyManager.config.rotationInterval = 100; // 100ms
      
      const rotationPromise = new Promise((resolve) => {
        forwardSecrecyManager.once('keyRotated', resolve);
      });
      
      // Wait for automatic rotation
      const rotationEvent = await rotationPromise;
      
      expect(rotationEvent.trigger).toBe('TIME_BASED');
      expect(rotationEvent.keyGeneration).toBe(1);
    });

    test('should trigger automatic rotation based on message count', async () => {
      forwardSecrecyManager.config.messageCountThreshold = 5; // Low threshold for testing
      
      const rotationPromise = new Promise((resolve) => {
        forwardSecrecyManager.once('keyRotated', resolve);
      });
      
      // Simulate message processing
      for (let i = 0; i < 6; i++) {
        forwardSecrecyManager.metrics.messagesProcessed++;
        forwardSecrecyManager.checkRotationTriggers();
      }
      
      const rotationEvent = await rotationPromise;
      
      expect(rotationEvent.trigger).toBe('MESSAGE_COUNT');
      expect(rotationEvent.keyGeneration).toBe(1);
    });

    test('should trigger automatic rotation based on data volume', async () => {
      forwardSecrecyManager.config.dataVolumeThreshold = 1024; // 1KB for testing
      
      const rotationPromise = new Promise((resolve) => {
        forwardSecrecyManager.once('keyRotated', resolve);
      });
      
      // Simulate data processing
      forwardSecrecyManager.metrics.dataProcessed = 2048; // 2KB
      forwardSecrecyManager.checkRotationTriggers();
      
      const rotationEvent = await rotationPromise;
      
      expect(rotationEvent.trigger).toBe('DATA_VOLUME');
      expect(rotationEvent.keyGeneration).toBe(1);
    });

    test('should handle rotation failure gracefully', async () => {
      // Mock key generation failure
      forwardSecrecyManager.keyRotationEngine.rotateKeys = jest.fn()
        .mockRejectedValueOnce(new Error('Key generation failed'));
      
      await expect(forwardSecrecyManager.rotateKeysManually()).rejects.toThrow('Key generation failed');
      expect(forwardSecrecyManager.state.currentKeyGeneration).toBe(0);
    });

    test('should prevent concurrent rotations', async () => {
      // Start first rotation
      const rotation1 = forwardSecrecyManager.rotateKeysManually();
      
      // Try to start second rotation immediately
      const rotation2 = forwardSecrecyManager.rotateKeysManually();
      
      const results = await Promise.allSettled([rotation1, rotation2]);
      
      // One should succeed, one should be rejected
      const successes = results.filter(r => r.status === 'fulfilled');
      const failures = results.filter(r => r.status === 'rejected');
      
      expect(successes).toHaveLength(1);
      expect(failures).toHaveLength(1);
      expect(failures[0].reason.message).toContain('rotation in progress');
    });
  });

  describe('Emergency Rotation', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
    });

    test('should perform emergency rotation', async () => {
      const result = await forwardSecrecyManager.emergencyRotation('SECURITY_BREACH');
      
      expect(result.success).toBe(true);
      expect(result.keyGeneration).toBe(1);
      expect(result.trigger).toBe('EMERGENCY');
      expect(result.reason).toBe('SECURITY_BREACH');
      expect(result.emergencyProof).toBeDefined();
    });

    test('should handle different emergency reasons', async () => {
      const reasons = ['SECURITY_BREACH', 'KEY_COMPROMISE', 'PROTOCOL_VIOLATION', 'MANUAL_OVERRIDE'];
      
      for (let i = 0; i < reasons.length; i++) {
        const result = await forwardSecrecyManager.emergencyRotation(reasons[i]);
        
        expect(result.success).toBe(true);
        expect(result.reason).toBe(reasons[i]);
        expect(result.keyGeneration).toBe(i + 1);
      }
    });

    test('should prioritize emergency rotation over regular rotation', async () => {
      // Start regular rotation
      const regularRotation = forwardSecrecyManager.rotateKeysManually();
      
      // Immediately start emergency rotation
      const emergencyRotation = forwardSecrecyManager.emergencyRotation('SECURITY_BREACH');
      
      const results = await Promise.allSettled([regularRotation, emergencyRotation]);
      
      // Emergency rotation should succeed
      expect(results[1].status).toBe('fulfilled');
      expect(results[1].value.trigger).toBe('EMERGENCY');
    });
  });

  describe('Secure Deletion', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
      await forwardSecrecyManager.rotateKeysManually(); // Generate some keys to delete
    });

    test('should perform secure deletion after key rotation', async () => {
      const deletionPromise = new Promise((resolve) => {
        forwardSecrecyManager.once('deletionCompleted', resolve);
      });
      
      await forwardSecrecyManager.rotateKeysManually();
      
      const deletionEvent = await deletionPromise;
      
      expect(deletionEvent.keyGeneration).toBe(1); // Previous generation
      expect(deletionEvent.deletionTime).toBeGreaterThan(0);
      expect(deletionEvent.verified).toBe(true);
      expect(deletionEvent.passes).toBe(7); // DoD 5220.22-M standard
    });

    test('should verify secure deletion', async () => {
      await forwardSecrecyManager.rotateKeysManually();
      
      const verificationResult = await forwardSecrecyManager.verifySecureDeletion(1);
      
      expect(verificationResult.verified).toBe(true);
      expect(verificationResult.confidence).toBeGreaterThan(0.9);
      expect(verificationResult.entropy).toBeGreaterThan(7.0);
      expect(verificationResult.patterns.found).toBe(false);
    });

    test('should handle deletion verification failure', async () => {
      // Mock verification failure
      forwardSecrecyManager.secureDeletionManager.verifyDeletion = jest.fn()
        .mockResolvedValueOnce({
          verified: false,
          confidence: 0.5,
          entropy: 3.0,
          patterns: { found: true, count: 5 }
        });
      
      await forwardSecrecyManager.rotateKeysManually();
      
      const verificationResult = await forwardSecrecyManager.verifySecureDeletion(1);
      
      expect(verificationResult.verified).toBe(false);
      expect(verificationResult.confidence).toBeLessThan(0.9);
      expect(verificationResult.patterns.found).toBe(true);
    });
  });

  describe('Performance Monitoring', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
    });

    test('should track rotation performance metrics', async () => {
      const startTime = performance.now();
      
      await forwardSecrecyManager.rotateKeysManually();
      
      const status = forwardSecrecyManager.getStatus();
      
      expect(status.performanceMetrics.averageRotationTime).toBeGreaterThan(0);
      expect(status.performanceMetrics.totalRotations).toBe(1);
      expect(status.performanceMetrics.lastRotationTime).toBeGreaterThan(startTime);
    });

    test('should calculate average rotation time over multiple rotations', async () => {
      // Perform multiple rotations
      for (let i = 0; i < 5; i++) {
        await forwardSecrecyManager.rotateKeysManually();
      }
      
      const status = forwardSecrecyManager.getStatus();
      
      expect(status.performanceMetrics.totalRotations).toBe(5);
      expect(status.performanceMetrics.averageRotationTime).toBeGreaterThan(0);
      expect(status.performanceMetrics.rotationTimes).toHaveLength(5);
    });

    test('should meet performance requirements', async () => {
      const result = await forwardSecrecyManager.rotateKeysManually();
      
      // Should complete rotation in under 100ms (mocked environment)
      expect(result.rotationTime).toBeLessThan(100);
      
      const status = forwardSecrecyManager.getStatus();
      expect(status.performanceMetrics.averageRotationTime).toBeLessThan(100);
    });
  });

  describe('Status and Monitoring', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
    });

    test('should provide comprehensive status information', async () => {
      await forwardSecrecyManager.rotateKeysManually();
      
      const status = forwardSecrecyManager.getStatus();
      
      expect(status.initialized).toBe(true);
      expect(status.currentKeyGeneration).toBe(1);
      expect(status.autoRotationEnabled).toBe(true);
      expect(status.performanceMetrics).toBeDefined();
      expect(status.securityMetrics).toBeDefined();
      expect(status.uptime).toBeGreaterThan(0);
    });

    test('should track security metrics', async () => {
      await forwardSecrecyManager.rotateKeysManually();
      await forwardSecrecyManager.emergencyRotation('TEST');
      
      const status = forwardSecrecyManager.getStatus();
      
      expect(status.securityMetrics.totalRotations).toBe(2);
      expect(status.securityMetrics.emergencyRotations).toBe(1);
      expect(status.securityMetrics.securityEvents).toBeGreaterThan(0);
    });

    test('should provide audit report', async () => {
      await forwardSecrecyManager.rotateKeysManually();
      await forwardSecrecyManager.emergencyRotation('AUDIT_TEST');
      
      const auditReport = await forwardSecrecyManager.getAuditReport(
        Date.now() - 3600000,
        Date.now()
      );
      
      expect(auditReport.summary.totalEvents).toBeGreaterThan(0);
      expect(auditReport.events.some(e => e.type === 'KEY_ROTATION_STARTED')).toBe(true);
      expect(auditReport.events.some(e => e.type === 'KEY_ROTATION_COMPLETED')).toBe(true);
      expect(auditReport.events.some(e => e.type === 'EMERGENCY_ROTATION')).toBe(true);
      expect(auditReport.integrity.valid).toBe(true);
    });
  });

  describe('Error Handling and Recovery', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
    });

    test('should handle component failure gracefully', async () => {
      // Mock component failure
      forwardSecrecyManager.keyRotationEngine.rotateKeys = jest.fn()
        .mockRejectedValueOnce(new Error('Component failure'))
        .mockResolvedValueOnce({
          success: true,
          keyGeneration: 1,
          rotationTime: 50
        });
      
      // First rotation should fail
      await expect(forwardSecrecyManager.rotateKeysManually()).rejects.toThrow('Component failure');
      
      // System should recover for subsequent operations
      const result = await forwardSecrecyManager.rotateKeysManually();
      expect(result.success).toBe(true);
    });

    test('should maintain state consistency during failures', async () => {
      const initialGeneration = forwardSecrecyManager.state.currentKeyGeneration;
      
      // Mock rotation failure
      forwardSecrecyManager.keyRotationEngine.rotateKeys = jest.fn()
        .mockRejectedValueOnce(new Error('Rotation failed'));
      
      await expect(forwardSecrecyManager.rotateKeysManually()).rejects.toThrow('Rotation failed');
      
      // State should remain unchanged
      expect(forwardSecrecyManager.state.currentKeyGeneration).toBe(initialGeneration);
      expect(forwardSecrecyManager.state.rotationInProgress).toBe(false);
    });

    test('should handle shutdown gracefully', async () => {
      await forwardSecrecyManager.rotateKeysManually();
      
      const shutdownResult = await forwardSecrecyManager.shutdown();
      
      expect(shutdownResult.success).toBe(true);
      expect(forwardSecrecyManager.state.initialized).toBe(false);
      
      // Should not be able to perform operations after shutdown
      await expect(forwardSecrecyManager.rotateKeysManually()).rejects.toThrow('not initialized');
    });
  });

  describe('Configuration Validation', () => {
    test('should validate rotation interval', () => {
      expect(() => {
        new ForwardSecrecyManager({ rotationInterval: 0 });
      }).toThrow('Rotation interval must be positive');
      
      expect(() => {
        new ForwardSecrecyManager({ rotationInterval: -1000 });
      }).toThrow('Rotation interval must be positive');
    });

    test('should validate message count threshold', () => {
      expect(() => {
        new ForwardSecrecyManager({ messageCountThreshold: 0 });
      }).toThrow('Message count threshold must be positive');
      
      expect(() => {
        new ForwardSecrecyManager({ messageCountThreshold: -100 });
      }).toThrow('Message count threshold must be positive');
    });

    test('should validate data volume threshold', () => {
      expect(() => {
        new ForwardSecrecyManager({ dataVolumeThreshold: 0 });
      }).toThrow('Data volume threshold must be positive');
      
      expect(() => {
        new ForwardSecrecyManager({ dataVolumeThreshold: -1024 });
      }).toThrow('Data volume threshold must be positive');
    });

    test('should accept valid configuration', () => {
      expect(() => {
        new ForwardSecrecyManager({
          autoRotation: true,
          rotationInterval: 3600000,
          messageCountThreshold: 1000,
          dataVolumeThreshold: 10 * 1024 * 1024,
          fipsCompliance: true
        });
      }).not.toThrow();
    });
  });
});
