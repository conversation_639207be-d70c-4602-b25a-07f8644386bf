/**
 * Enhanced Secure Deletion Tests for WebOTR Forward Secrecy
 * 
 * Comprehensive testing of advanced secure deletion features:
 * - DoD 5220.22-M standard compliance
 * - Multiple-pass erasure with pattern verification
 * - Cross-platform memory sanitization
 * - Cryptographic proof generation and validation
 */

import { SecureDeletionManager } from '../SecureDeletionManager.js';
import { MemorySanitizer } from '../MemorySanitizer.js';
import { SecureRandom } from '../../crypto/SecureRandom.js';

// Mock WebCrypto API
global.crypto = {
  subtle: {
    digest: jest.fn()
  },
  getRandomValues: jest.fn()
};

global.performance = {
  now: jest.fn(() => Date.now())
};

describe('Enhanced Secure Deletion Tests', () => {
  let deletionManager;
  let memorySanitizer;
  let secureRandom;
  
  beforeEach(async () => {
    // Setup crypto mocks
    crypto.getRandomValues.mockImplementation((array) => {
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
      return array;
    });
    
    crypto.subtle.digest.mockImplementation(async (algorithm, data) => {
      const hash = new Uint8Array(32);
      crypto.getRandomValues(hash);
      return hash.buffer;
    });
    
    // Initialize components
    secureRandom = new SecureRandom();
    await secureRandom.initialize();
    
    deletionManager = new SecureDeletionManager({
      cryptographicErasure: true,
      overwritePasses: 7, // DoD standard
      enhancedErasure: true,
      memoryForensicsResistance: true,
      verificationEnabled: true
    });
    deletionManager.secureRandom = secureRandom;
    
    memorySanitizer = new MemorySanitizer({
      aggressiveClearing: true,
      clearingPasses: 3,
      antiForensics: true
    });
    
    await deletionManager.initialize();
    await memorySanitizer.initialize();
  });
  
  afterEach(async () => {
    if (deletionManager) {
      await deletionManager.shutdown();
    }
    if (memorySanitizer) {
      await memorySanitizer.shutdown();
    }
    if (secureRandom) {
      secureRandom.shutdown();
    }
    jest.clearAllMocks();
  });

  describe('DoD 5220.22-M Compliance', () => {
    test('should perform 7-pass erasure according to DoD standard', async () => {
      const testKeys = {
        masterKey: new Uint8Array(32),
        encryptionKey: new Uint8Array(32),
        macKey: new Uint8Array(32)
      };
      
      // Fill with test data
      testKeys.masterKey.fill(0xAA);
      testKeys.encryptionKey.fill(0x55);
      testKeys.macKey.fill(0xFF);
      
      const result = await deletionManager.deleteKeys({
        keyGeneration: 1,
        keys: testKeys
      });
      
      expect(result.success).toBe(true);
      expect(result.erasurePasses).toBe(7);
      expect(result.verified).toBe(true);
      expect(result.deletionTime).toBeLessThan(50);
    });

    test('should use proper erasure patterns', async () => {
      const testArray = new Uint8Array(256);
      testArray.fill(0xAA);
      
      const erasureSequence = await deletionManager.generateErasureSequence(256);
      
      expect(erasureSequence).toHaveLength(7);
      expect(erasureSequence.map(p => p.name)).toContain('random');
      expect(erasureSequence.map(p => p.name)).toContain('zeros');
      expect(erasureSequence.map(p => p.name)).toContain('ones');
      expect(erasureSequence.map(p => p.name)).toContain('alternating');
    });

    test('should verify each erasure pass', async () => {
      const testArray = new Uint8Array(128);
      crypto.getRandomValues(testArray);
      
      const erasureResult = await deletionManager.eraseTypedArray(testArray);
      
      expect(erasureResult.success).toBe(true);
      expect(erasureResult.erasureLog).toHaveLength(7);
      expect(erasureResult.erasureLog.every(log => log.verified)).toBe(true);
      expect(erasureResult.finalVerification.secure).toBe(true);
    });
  });

  describe('Enhanced Erasure Protocols', () => {
    test('should apply multiple erasure patterns', async () => {
      const testData = new Uint8Array(64);
      testData.fill(0x42);
      
      const patterns = await deletionManager.generateErasureSequence(64);
      
      for (const pattern of patterns) {
        const patternData = await pattern.generator();
        expect(patternData).toHaveLength(64);
        expect(patternData).toBeInstanceOf(Uint8Array);
      }
    });

    test('should enforce memory barriers between passes', async () => {
      const testArray = new Uint8Array(32);
      testArray.fill(0xFF);
      
      const barrierResult = await deletionManager.enforceMemoryBarrier();
      
      expect(typeof barrierResult).toBe('number');
    });

    test('should apply anti-forensics delays', async () => {
      const startTime = performance.now();
      await deletionManager.antiForensicsDelay();
      const endTime = performance.now();
      
      const delay = endTime - startTime;
      expect(delay).toBeGreaterThan(0);
      expect(delay).toBeLessThan(10); // Should be small delay
    });

    test('should perform platform-specific optimizations', async () => {
      const testArray = new Uint8Array(64);
      const patternData = new Uint8Array(64);
      crypto.getRandomValues(patternData);
      
      await deletionManager.platformSpecificErasure(testArray, patternData);
      
      // Should complete without error
      expect(testArray).toHaveLength(64);
    });
  });

  describe('Verification and Analysis', () => {
    test('should calculate entropy of erased data', async () => {
      const testArray = new Uint8Array(256);
      crypto.getRandomValues(testArray);
      
      const entropy = deletionManager.calculateEntropy(testArray);
      
      expect(entropy).toBeGreaterThan(7.0); // High entropy expected
      expect(entropy).toBeLessThanOrEqual(8.0); // Maximum entropy
    });

    test('should detect patterns in data', async () => {
      const testArray = new Uint8Array(64);
      testArray.fill(0xAA); // Create obvious pattern
      
      const patterns = deletionManager.detectPatterns(testArray);
      
      expect(patterns.found).toBe(true);
      expect(patterns.types).toContain('value-dominance');
      expect(patterns.confidence).toBeGreaterThan(0);
    });

    test('should analyze array statistics', async () => {
      const testArray = new Uint8Array(256);
      crypto.getRandomValues(testArray);
      
      const statistics = deletionManager.analyzeArrayStatistics(testArray);
      
      expect(statistics.mean).toBeGreaterThan(100);
      expect(statistics.mean).toBeLessThan(155);
      expect(statistics.uniformity).toBeDefined();
      expect(statistics.distribution).toHaveLength(256);
    });

    test('should calculate security confidence score', async () => {
      const mockStatistics = {
        mean: 127.5,
        deviation: 2.0,
        uniformity: { uniform: true }
      };
      const mockEntropy = 7.8;
      const mockPatterns = { found: false, confidence: 0 };
      
      const confidence = deletionManager.calculateSecurityConfidence(
        mockStatistics, mockEntropy, mockPatterns
      );
      
      expect(confidence).toBeGreaterThan(0.8);
      expect(confidence).toBeLessThanOrEqual(1.0);
    });
  });

  describe('Memory Sanitization Integration', () => {
    test('should clear typed arrays securely', async () => {
      const testArray = new Uint8Array(128);
      testArray.fill(0x42);
      
      const result = await memorySanitizer.clearMemory(testArray);
      
      expect(result.success).toBe(true);
      expect(result.clearingPasses).toBe(3);
      expect(result.clearingTime).toBeLessThan(50);
    });

    test('should detect platform capabilities', async () => {
      const capabilities = memorySanitizer.capabilities;
      
      expect(capabilities).toHaveProperty('gcAvailable');
      expect(capabilities).toHaveProperty('memoryInfoAvailable');
      expect(capabilities).toHaveProperty('bufferOpsAvailable');
    });

    test('should apply memory pressure for cache flushing', async () => {
      const startTime = performance.now();
      await memorySanitizer.applyMemoryPressure();
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeGreaterThan(0);
    });

    test('should select appropriate clearing strategy', async () => {
      const memoryInfo = {
        type: 'typed-array',
        size: 256,
        characteristics: ['byte-addressable']
      };
      
      const strategy = memorySanitizer.selectClearingStrategy(memoryInfo, {
        clearingPasses: 3
      });
      
      expect(strategy.type).toBe('typed-array-clearing');
      expect(strategy.passes).toBe(3);
      expect(strategy.patterns).toContain('random');
    });
  });

  describe('Cryptographic Proof Generation', () => {
    test('should generate secure deletion proof', async () => {
      const deletionResult = {
        keyGeneration: 1,
        deletionTime: 25.5,
        erasurePasses: 7,
        timestamp: Date.now()
      };
      
      const verificationResult = {
        confidence: 0.95,
        entropy: 7.8,
        patterns: { found: false }
      };
      
      const proof = await deletionManager.generateSecureDeletionProof(
        deletionResult, verificationResult
      );
      
      expect(proof.proofId).toBeDefined();
      expect(proof.proofHash).toHaveLength(64); // SHA-256 hex
      expect(proof.signature).toHaveLength(64);
      expect(proof.standard).toBe('DoD-5220.22-M-Enhanced');
    });

    test('should generate unique proof IDs', async () => {
      const id1 = await deletionManager.generateProofId();
      const id2 = await deletionManager.generateProofId();
      
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^proof-/);
      expect(id2).toMatch(/^proof-/);
    });

    test('should create verifiable proof signatures', async () => {
      const proofData = {
        keyGeneration: 1,
        deletionTime: 30,
        timestamp: Date.now()
      };
      
      const proofHash = new Uint8Array(32);
      crypto.getRandomValues(proofHash);
      
      const signature = await deletionManager.generateProofSignature(proofData, proofHash.buffer);
      
      expect(signature).toHaveLength(64);
      expect(signature).toMatch(/^[0-9a-f]+$/);
    });
  });

  describe('Performance Requirements', () => {
    test('should meet deletion time requirements', async () => {
      const testKeys = {
        masterKey: new Uint8Array(32),
        encryptionKey: new Uint8Array(32),
        macKey: new Uint8Array(32)
      };
      
      const startTime = performance.now();
      const result = await deletionManager.deleteKeys({
        keyGeneration: 1,
        keys: testKeys
      });
      const totalTime = performance.now() - startTime;
      
      expect(totalTime).toBeLessThan(50); // 50ms requirement
      expect(result.deletionTime).toBeLessThan(50);
    });

    test('should handle large key sets efficiently', async () => {
      const largeKeys = {
        masterKey: new Uint8Array(1024),
        encryptionKey: new Uint8Array(1024),
        macKey: new Uint8Array(1024),
        additionalKeys: Array.from({ length: 10 }, () => new Uint8Array(256))
      };
      
      const result = await deletionManager.deleteKeys({
        keyGeneration: 1,
        keys: largeKeys
      });
      
      expect(result.success).toBe(true);
      expect(result.deletionTime).toBeLessThan(100); // Reasonable for large data
    });

    test('should maintain performance under load', async () => {
      const deletionTimes = [];
      
      // Perform multiple deletions
      for (let i = 0; i < 5; i++) {
        const testKeys = {
          masterKey: new Uint8Array(64),
          encryptionKey: new Uint8Array(64)
        };
        
        const startTime = performance.now();
        await deletionManager.deleteKeys({
          keyGeneration: i,
          keys: testKeys
        });
        const deletionTime = performance.now() - startTime;
        
        deletionTimes.push(deletionTime);
      }
      
      const averageTime = deletionTimes.reduce((sum, time) => sum + time, 0) / deletionTimes.length;
      expect(averageTime).toBeLessThan(50);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle empty key sets', async () => {
      const emptyKeys = {};
      
      const result = await deletionManager.deleteKeys({
        keyGeneration: 1,
        keys: emptyKeys
      });
      
      expect(result.success).toBe(true);
    });

    test('should handle malformed key data', async () => {
      const malformedKeys = {
        masterKey: null,
        encryptionKey: undefined,
        macKey: 'not-a-typed-array'
      };
      
      const result = await deletionManager.deleteKeys({
        keyGeneration: 1,
        keys: malformedKeys
      });
      
      expect(result.success).toBe(true); // Should handle gracefully
    });

    test('should recover from verification failures', async () => {
      // Mock verification failure
      const originalVerify = deletionManager.verifyDeletion;
      deletionManager.verifyDeletion = jest.fn().mockResolvedValue({
        verified: false,
        details: 'Mock verification failure'
      });
      
      const testKeys = {
        masterKey: new Uint8Array(32)
      };
      
      const result = await deletionManager.deleteKeys({
        keyGeneration: 1,
        keys: testKeys
      });
      
      expect(result.success).toBe(true);
      expect(result.verified).toBe(false);
      
      // Restore original method
      deletionManager.verifyDeletion = originalVerify;
    });
  });

  describe('Status and Monitoring', () => {
    test('should provide comprehensive status information', async () => {
      // Perform some deletions to populate metrics
      await deletionManager.deleteKeys({
        keyGeneration: 1,
        keys: { masterKey: new Uint8Array(32) }
      });
      
      const status = deletionManager.getStatus();
      
      expect(status.initialized).toBe(true);
      expect(status.totalDeletions).toBe(1);
      expect(status.successRate).toBe(1.0);
      expect(status.averageDeletionTime).toBeGreaterThan(0);
    });

    test('should track deletion metrics accurately', async () => {
      const initialStatus = deletionManager.getStatus();
      const initialCount = initialStatus.totalDeletions;
      
      await deletionManager.deleteKeys({
        keyGeneration: 1,
        keys: { masterKey: new Uint8Array(32) }
      });
      
      const finalStatus = deletionManager.getStatus();
      
      expect(finalStatus.totalDeletions).toBe(initialCount + 1);
      expect(finalStatus.averageDeletionTime).toBeGreaterThan(0);
    });
  });
});
