/**
 * Integration Tests for WebOTR Forward Secrecy System
 * 
 * Comprehensive testing of the complete forward secrecy implementation:
 * - End-to-end key rotation workflows
 * - Secure deletion verification
 * - Zero-knowledge proof validation
 * - Performance and compliance testing
 */

import { ForwardSecrecyManager } from '../ForwardSecrecyManager.js';
import { KeyRotationEngine } from '../KeyRotationEngine.js';
import { SecureDeletionManager } from '../SecureDeletionManager.js';
import { ZeroKnowledgeVerifier } from '../ZeroKnowledgeVerifier.js';
import { AuditTrailSystem } from '../AuditTrailSystem.js';

// Mock WebCrypto API for testing environment
global.crypto = {
  subtle: {
    digest: jest.fn(),
    importKey: jest.fn(),
    deriveBits: jest.fn(),
    deriveKey: jest.fn()
  },
  getRandomValues: jest.fn()
};

// Mock performance API
global.performance = {
  now: jest.fn(() => Date.now())
};

describe('Forward Secrecy Integration Tests', () => {
  let forwardSecrecyManager;
  
  beforeEach(async () => {
    // Setup crypto mocks
    crypto.getRandomValues.mockImplementation((array) => {
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
      return array;
    });
    
    crypto.subtle.digest.mockImplementation(async (algorithm, data) => {
      // Simple mock hash
      const hash = new Uint8Array(32);
      crypto.getRandomValues(hash);
      return hash.buffer;
    });
    
    crypto.subtle.importKey.mockResolvedValue({ type: 'secret' });
    crypto.subtle.deriveBits.mockImplementation(async (algorithm, key, length) => {
      const bits = new Uint8Array(length / 8);
      crypto.getRandomValues(bits);
      return bits.buffer;
    });
    
    // Create manager with test configuration
    forwardSecrecyManager = new ForwardSecrecyManager({
      autoRotation: true,
      rotationInterval: 1000, // 1 second for testing
      messageCountThreshold: 10,
      dataVolumeThreshold: 1024,
      fipsCompliance: true
    });
  });
  
  afterEach(async () => {
    if (forwardSecrecyManager) {
      await forwardSecrecyManager.shutdown();
    }
    jest.clearAllMocks();
  });

  describe('System Initialization', () => {
    test('should initialize all components successfully', async () => {
      await forwardSecrecyManager.initialize();
      
      expect(forwardSecrecyManager.state.initialized).toBe(true);
      expect(forwardSecrecyManager.keyRotationEngine).toBeDefined();
      expect(forwardSecrecyManager.secureDeletionManager).toBeDefined();
      expect(forwardSecrecyManager.zeroKnowledgeVerifier).toBeDefined();
      expect(forwardSecrecyManager.auditTrailSystem).toBeDefined();
    });

    test('should emit initialization event', async () => {
      const initPromise = new Promise((resolve) => {
        forwardSecrecyManager.on('initialized', resolve);
      });
      
      await forwardSecrecyManager.initialize();
      const initEvent = await initPromise;
      
      expect(initEvent).toHaveProperty('timestamp');
      expect(initEvent).toHaveProperty('configuration');
    });

    test('should handle initialization failure gracefully', async () => {
      // Mock crypto failure
      crypto.subtle.digest.mockRejectedValue(new Error('Crypto not available'));
      
      await expect(forwardSecrecyManager.initialize()).rejects.toThrow();
    });
  });

  describe('Key Rotation Workflows', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
    });

    test('should perform manual key rotation', async () => {
      const rotationPromise = new Promise((resolve) => {
        forwardSecrecyManager.on('keyRotated', resolve);
      });
      
      const result = await forwardSecrecyManager.rotateKeysManually();
      const rotationEvent = await rotationPromise;
      
      expect(result.success).toBe(true);
      expect(result.keyGeneration).toBe(1);
      expect(result.rotationTime).toBeLessThan(100);
      expect(rotationEvent.trigger).toBe('MANUAL');
    });

    test('should perform automatic time-based rotation', async () => {
      // Fast-forward time to trigger rotation
      const originalNow = Date.now;
      Date.now = jest.fn(() => originalNow() + 2000); // 2 seconds later
      
      const rotationPromise = new Promise((resolve) => {
        forwardSecrecyManager.on('keyRotated', resolve);
      });
      
      await forwardSecrecyManager.checkRotationTriggers();
      const rotationEvent = await rotationPromise;
      
      expect(rotationEvent.trigger).toBe('TIME_BASED');
      
      // Restore Date.now
      Date.now = originalNow;
    });

    test('should perform message count triggered rotation', async () => {
      // Simulate message count threshold
      for (let i = 0; i < 11; i++) {
        forwardSecrecyManager.updateMessageStats(100);
      }
      
      const rotationPromise = new Promise((resolve) => {
        forwardSecrecyManager.on('keyRotated', resolve);
      });
      
      await forwardSecrecyManager.checkRotationTriggers();
      const rotationEvent = await rotationPromise;
      
      expect(rotationEvent.trigger).toBe('MESSAGE_COUNT');
    });

    test('should perform emergency rotation', async () => {
      const rotationPromise = new Promise((resolve) => {
        forwardSecrecyManager.on('keyRotated', resolve);
      });
      
      const result = await forwardSecrecyManager.emergencyRotation('SECURITY_BREACH');
      const rotationEvent = await rotationPromise;
      
      expect(result.success).toBe(true);
      expect(rotationEvent.trigger).toBe('EMERGENCY');
    });

    test('should prevent concurrent rotations', async () => {
      const rotation1 = forwardSecrecyManager.rotateKeysManually();
      const rotation2 = forwardSecrecyManager.rotateKeysManually();
      
      await expect(rotation1).resolves.toBeDefined();
      await expect(rotation2).rejects.toThrow('Key rotation already in progress');
    });
  });

  describe('Secure Deletion Verification', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
    });

    test('should securely delete old keys after rotation', async () => {
      const deletionPromise = new Promise((resolve) => {
        forwardSecrecyManager.on('deletionCompleted', resolve);
      });
      
      await forwardSecrecyManager.rotateKeysManually();
      const deletionEvent = await deletionPromise;
      
      expect(deletionEvent.verified).toBe(true);
      expect(deletionEvent.deletionTime).toBeLessThan(50);
    });

    test('should verify cryptographic erasure', async () => {
      const testKeys = {
        masterKey: new Uint8Array([1, 2, 3, 4]),
        encryptionKey: new Uint8Array([5, 6, 7, 8]),
        macKey: new Uint8Array([9, 10, 11, 12])
      };
      
      const deletionResult = await forwardSecrecyManager.secureDeletionManager.deleteKeys({
        keyGeneration: 0,
        keys: testKeys
      });
      
      expect(deletionResult.success).toBe(true);
      expect(deletionResult.verified).toBe(true);
      
      // Verify keys are zeroed
      expect(testKeys.masterKey.every(byte => byte === 0)).toBe(true);
      expect(testKeys.encryptionKey.every(byte => byte === 0)).toBe(true);
      expect(testKeys.macKey.every(byte => byte === 0)).toBe(true);
    });
  });

  describe('Zero-Knowledge Verification', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
    });

    test('should generate and verify rotation proofs', async () => {
      const verificationPromise = new Promise((resolve) => {
        forwardSecrecyManager.on('verificationCompleted', resolve);
      });
      
      const rotationResult = await forwardSecrecyManager.rotateKeysManually();
      const verificationEvent = await verificationPromise;
      
      expect(rotationResult.rotationProof).toBeDefined();
      expect(verificationEvent.verified).toBe(true);
      expect(verificationEvent.verificationTime).toBeLessThan(100);
    });

    test('should validate forward secrecy properties', async () => {
      await forwardSecrecyManager.rotateKeysManually();
      
      const secrecyValidation = await forwardSecrecyManager.verifyForwardSecrecy();
      
      expect(secrecyValidation.forwardSecrecyMaintained).toBe(true);
      expect(secrecyValidation.currentGeneration).toBe(1);
    });

    test('should detect invalid proofs', async () => {
      const invalidProof = {
        id: 'invalid-proof',
        type: 'KEY_ROTATION',
        oldKeyGeneration: 0,
        newKeyGeneration: 1,
        oldCommitment: { commitment: 'invalid' },
        newCommitment: { commitment: 'invalid' },
        transitionProof: { transitionHash: 'invalid' },
        integrityProof: { integrityHash: 'invalid' },
        timestamp: Date.now()
      };
      
      const verificationResult = await forwardSecrecyManager.zeroKnowledgeVerifier.verifyRotationProof(invalidProof);
      
      expect(verificationResult.verified).toBe(false);
      expect(verificationResult.error).toBeDefined();
    });
  });

  describe('Audit Trail Compliance', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
    });

    test('should log all security events', async () => {
      await forwardSecrecyManager.rotateKeysManually();
      
      const auditStatus = forwardSecrecyManager.auditTrailSystem.getStatus();
      
      expect(auditStatus.totalEvents).toBeGreaterThan(0);
      expect(auditStatus.eventTypes).toContain('KEY_ROTATION_STARTED');
      expect(auditStatus.eventTypes).toContain('KEY_ROTATION_COMPLETED');
    });

    test('should generate compliance reports', async () => {
      await forwardSecrecyManager.rotateKeysManually();
      
      const startTime = Date.now() - 10000;
      const endTime = Date.now();
      
      const report = await forwardSecrecyManager.getAuditReport(startTime, endTime);
      
      expect(report.reportId).toBeDefined();
      expect(report.summary.totalEvents).toBeGreaterThan(0);
      expect(report.integrity.valid).toBe(true);
      expect(report.signature).toBeDefined();
    });

    test('should verify audit trail integrity', async () => {
      await forwardSecrecyManager.rotateKeysManually();
      
      const integrity = await forwardSecrecyManager.auditTrailSystem.verifyIntegrity();
      
      expect(integrity.valid).toBe(true);
      expect(integrity.chainIntegrity).toBe(true);
      expect(integrity.failedEvents).toHaveLength(0);
    });
  });

  describe('Performance Requirements', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
    });

    test('should meet rotation time requirements', async () => {
      const startTime = performance.now();
      await forwardSecrecyManager.rotateKeysManually();
      const rotationTime = performance.now() - startTime;
      
      expect(rotationTime).toBeLessThan(100); // 100ms requirement
    });

    test('should meet deletion time requirements', async () => {
      const testKeys = {
        masterKey: new Uint8Array(32),
        encryptionKey: new Uint8Array(32),
        macKey: new Uint8Array(32)
      };
      
      const startTime = performance.now();
      const result = await forwardSecrecyManager.secureDeletionManager.deleteKeys({
        keyGeneration: 0,
        keys: testKeys
      });
      const deletionTime = performance.now() - startTime;
      
      expect(deletionTime).toBeLessThan(50); // 50ms requirement
      expect(result.deletionTime).toBeLessThan(50);
    });

    test('should meet verification time requirements', async () => {
      const rotationResult = await forwardSecrecyManager.rotateKeysManually();
      
      const startTime = performance.now();
      const verificationResult = await forwardSecrecyManager.zeroKnowledgeVerifier.verifyRotationProof(
        rotationResult.rotationProof
      );
      const verificationTime = performance.now() - startTime;
      
      expect(verificationTime).toBeLessThan(100); // 100ms requirement
      expect(verificationResult.verificationTime).toBeLessThan(100);
    });
  });

  describe('Error Handling and Recovery', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
    });

    test('should handle rotation failures gracefully', async () => {
      // Mock rotation failure
      forwardSecrecyManager.keyRotationEngine.rotateKeys = jest.fn().mockRejectedValue(
        new Error('Rotation failed')
      );
      
      const errorPromise = new Promise((resolve) => {
        forwardSecrecyManager.on('rotationFailed', resolve);
      });
      
      await expect(forwardSecrecyManager.rotateKeysManually()).rejects.toThrow('Rotation failed');
      const errorEvent = await errorPromise;
      
      expect(errorEvent.error).toBe('Rotation failed');
      expect(forwardSecrecyManager.state.rotationInProgress).toBe(false);
    });

    test('should handle deletion failures gracefully', async () => {
      // Mock deletion failure
      forwardSecrecyManager.secureDeletionManager.deleteKeys = jest.fn().mockRejectedValue(
        new Error('Deletion failed')
      );
      
      await expect(forwardSecrecyManager.rotateKeysManually()).rejects.toThrow();
    });

    test('should maintain system state consistency', async () => {
      const initialGeneration = forwardSecrecyManager.state.currentKeyGeneration;
      
      // Attempt failed rotation
      forwardSecrecyManager.keyRotationEngine.rotateKeys = jest.fn().mockRejectedValue(
        new Error('Rotation failed')
      );
      
      try {
        await forwardSecrecyManager.rotateKeysManually();
      } catch (error) {
        // Expected failure
      }
      
      // Verify state is unchanged
      expect(forwardSecrecyManager.state.currentKeyGeneration).toBe(initialGeneration);
      expect(forwardSecrecyManager.state.rotationInProgress).toBe(false);
    });
  });

  describe('System Status and Monitoring', () => {
    beforeEach(async () => {
      await forwardSecrecyManager.initialize();
    });

    test('should provide comprehensive status information', async () => {
      await forwardSecrecyManager.rotateKeysManually();
      
      const status = forwardSecrecyManager.getStatus();
      
      expect(status.initialized).toBe(true);
      expect(status.currentKeyGeneration).toBe(1);
      expect(status.lastRotationTime).toBeDefined();
      expect(status.performanceMetrics.averageRotationTime).toBeGreaterThan(0);
    });

    test('should track performance metrics', async () => {
      // Perform multiple rotations
      await forwardSecrecyManager.rotateKeysManually();
      await forwardSecrecyManager.rotateKeysManually();
      await forwardSecrecyManager.rotateKeysManually();
      
      const status = forwardSecrecyManager.getStatus();
      
      expect(status.performanceMetrics.averageRotationTime).toBeGreaterThan(0);
      expect(forwardSecrecyManager.performanceMetrics.rotationTimes).toHaveLength(3);
    });
  });
});
