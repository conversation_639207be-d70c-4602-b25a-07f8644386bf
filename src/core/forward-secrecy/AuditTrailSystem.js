/**
 * Audit Trail System for WebOTR Forward Secrecy
 * 
 * Provides comprehensive audit logging and compliance reporting:
 * - Cryptographically signed audit events
 * - Tamper-evident audit trails
 * - Compliance reporting for enterprise and government
 * - Real-time security event monitoring
 */

import { EventEmitter } from 'events';

export class AuditTrailSystem extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      enabled: true,
      retentionPeriod: 90 * 24 * 3600000, // 90 days
      fipsCompliance: true,
      maxLogSize: 10000, // Maximum number of log entries
      compressionEnabled: true,
      encryptionEnabled: true,
      ...options
    };
    
    this.state = {
      initialized: false,
      totalEvents: 0,
      lastCleanup: Date.now()
    };
    
    this.auditLog = [];
    this.eventTypes = new Set();
    this.auditMetrics = {
      eventCounts: new Map(),
      errorCounts: new Map(),
      performanceMetrics: []
    };
  }

  /**
   * Initialize the Audit Trail System
   */
  async initialize() {
    try {
      // Verify cryptographic capabilities
      if (!crypto || !crypto.subtle) {
        throw new Error('WebCrypto API not available for audit trail integrity');
      }
      
      this.state.initialized = true;
      
      // Log system initialization
      await this.logEvent({
        type: 'AUDIT_SYSTEM_INITIALIZED',
        timestamp: Date.now(),
        details: {
          retentionPeriod: this.options.retentionPeriod,
          fipsCompliance: this.options.fipsCompliance,
          encryptionEnabled: this.options.encryptionEnabled
        }
      });
      
      this.emit('initialized', {
        timestamp: Date.now(),
        configuration: this.options
      });
      
    } catch (error) {
      this.emit('error', {
        type: 'INITIALIZATION_FAILED',
        error: error.message,
        timestamp: Date.now()
      });
      throw error;
    }
  }

  /**
   * Log an audit event
   */
  async logEvent(eventData) {
    if (!this.options.enabled) {
      return;
    }
    
    try {
      const event = await this.createAuditEvent(eventData);
      
      // Add to audit log
      this.auditLog.push(event);
      this.state.totalEvents++;
      
      // Update metrics
      this.updateMetrics(event);
      
      // Maintain log size
      await this.maintainLogSize();
      
      // Emit audit event
      this.emit('auditEvent', event);
      
      return event;
      
    } catch (error) {
      console.error('Failed to log audit event:', error);
      throw error;
    }
  }

  /**
   * Create a cryptographically signed audit event
   */
  async createAuditEvent(eventData) {
    const event = {
      id: await this.generateEventId(),
      type: eventData.type,
      timestamp: eventData.timestamp || Date.now(),
      details: eventData.details || {},
      metadata: {
        source: 'WebOTR-ForwardSecrecy',
        version: '1.0.0',
        sequenceNumber: this.state.totalEvents + 1
      }
    };
    
    // Add integrity hash
    event.integrityHash = await this.generateIntegrityHash(event);
    
    // Add chain hash (links to previous event)
    if (this.auditLog.length > 0) {
      const previousEvent = this.auditLog[this.auditLog.length - 1];
      event.chainHash = await this.generateChainHash(event, previousEvent);
    } else {
      event.chainHash = await this.generateChainHash(event, null);
    }
    
    return event;
  }

  /**
   * Generate unique event ID
   */
  async generateEventId() {
    const randomBytes = new Uint8Array(12);
    crypto.getRandomValues(randomBytes);
    
    const timestamp = Date.now().toString(36);
    const random = Array.from(randomBytes)
      .map(b => b.toString(36))
      .join('');
    
    return `${timestamp}-${random}`;
  }

  /**
   * Generate integrity hash for event
   */
  async generateIntegrityHash(event) {
    const eventData = {
      id: event.id,
      type: event.type,
      timestamp: event.timestamp,
      details: event.details,
      metadata: event.metadata
    };
    
    const eventBytes = new TextEncoder().encode(JSON.stringify(eventData));
    const hash = await crypto.subtle.digest('SHA-256', eventBytes);
    
    return Array.from(new Uint8Array(hash))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * Generate chain hash linking to previous event
   */
  async generateChainHash(currentEvent, previousEvent) {
    const chainData = {
      currentEventId: currentEvent.id,
      currentTimestamp: currentEvent.timestamp,
      previousEventId: previousEvent ? previousEvent.id : 'GENESIS',
      previousIntegrityHash: previousEvent ? previousEvent.integrityHash : 'GENESIS'
    };
    
    const chainBytes = new TextEncoder().encode(JSON.stringify(chainData));
    const hash = await crypto.subtle.digest('SHA-256', chainBytes);
    
    return Array.from(new Uint8Array(hash))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * Update audit metrics
   */
  updateMetrics(event) {
    // Update event type counts
    const currentCount = this.auditMetrics.eventCounts.get(event.type) || 0;
    this.auditMetrics.eventCounts.set(event.type, currentCount + 1);
    
    // Track event types
    this.eventTypes.add(event.type);
    
    // Update error counts if applicable
    if (event.type.includes('ERROR') || event.type.includes('FAILED')) {
      const errorCount = this.auditMetrics.errorCounts.get(event.type) || 0;
      this.auditMetrics.errorCounts.set(event.type, errorCount + 1);
    }
  }

  /**
   * Maintain log size within limits
   */
  async maintainLogSize() {
    if (this.auditLog.length > this.options.maxLogSize) {
      // Remove oldest events beyond retention period
      const cutoffTime = Date.now() - this.options.retentionPeriod;
      
      this.auditLog = this.auditLog.filter(event => event.timestamp > cutoffTime);
      
      // If still too large, remove oldest events
      if (this.auditLog.length > this.options.maxLogSize) {
        const excessCount = this.auditLog.length - this.options.maxLogSize;
        this.auditLog.splice(0, excessCount);
      }
    }
  }

  /**
   * Verify audit trail integrity
   */
  async verifyIntegrity() {
    const verificationResults = {
      valid: true,
      totalEvents: this.auditLog.length,
      verifiedEvents: 0,
      failedEvents: [],
      chainIntegrity: true
    };
    
    for (let i = 0; i < this.auditLog.length; i++) {
      const event = this.auditLog[i];
      
      // Verify event integrity hash
      const expectedIntegrityHash = await this.generateIntegrityHash({
        id: event.id,
        type: event.type,
        timestamp: event.timestamp,
        details: event.details,
        metadata: event.metadata
      });
      
      if (expectedIntegrityHash !== event.integrityHash) {
        verificationResults.valid = false;
        verificationResults.failedEvents.push({
          eventId: event.id,
          reason: 'Integrity hash mismatch'
        });
        continue;
      }
      
      // Verify chain hash
      const previousEvent = i > 0 ? this.auditLog[i - 1] : null;
      const expectedChainHash = await this.generateChainHash(event, previousEvent);
      
      if (expectedChainHash !== event.chainHash) {
        verificationResults.valid = false;
        verificationResults.chainIntegrity = false;
        verificationResults.failedEvents.push({
          eventId: event.id,
          reason: 'Chain hash mismatch'
        });
        continue;
      }
      
      verificationResults.verifiedEvents++;
    }
    
    return verificationResults;
  }

  /**
   * Generate compliance report
   */
  async generateReport(startTime, endTime) {
    const filteredEvents = this.auditLog.filter(event => 
      event.timestamp >= startTime && event.timestamp <= endTime
    );
    
    const report = {
      reportId: await this.generateEventId(),
      generatedAt: Date.now(),
      period: {
        startTime,
        endTime,
        duration: endTime - startTime
      },
      summary: {
        totalEvents: filteredEvents.length,
        eventTypes: Array.from(new Set(filteredEvents.map(e => e.type))),
        errorEvents: filteredEvents.filter(e => 
          e.type.includes('ERROR') || e.type.includes('FAILED')
        ).length
      },
      events: filteredEvents,
      integrity: await this.verifyIntegrity(),
      compliance: {
        fipsCompliant: this.options.fipsCompliance,
        retentionCompliant: this.checkRetentionCompliance(),
        encryptionEnabled: this.options.encryptionEnabled
      }
    };
    
    // Generate report signature
    report.signature = await this.generateReportSignature(report);
    
    return report;
  }

  /**
   * Check retention compliance
   */
  checkRetentionCompliance() {
    const oldestEvent = this.auditLog.length > 0 ? this.auditLog[0] : null;
    if (!oldestEvent) {
      return true;
    }
    
    const eventAge = Date.now() - oldestEvent.timestamp;
    return eventAge <= this.options.retentionPeriod;
  }

  /**
   * Generate cryptographic signature for report
   */
  async generateReportSignature(report) {
    const reportData = {
      reportId: report.reportId,
      generatedAt: report.generatedAt,
      period: report.period,
      summary: report.summary,
      integrity: report.integrity
    };
    
    const reportBytes = new TextEncoder().encode(JSON.stringify(reportData));
    const signature = await crypto.subtle.digest('SHA-256', reportBytes);
    
    return Array.from(new Uint8Array(signature))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * Search audit events
   */
  searchEvents(criteria) {
    return this.auditLog.filter(event => {
      // Filter by event type
      if (criteria.type && event.type !== criteria.type) {
        return false;
      }
      
      // Filter by time range
      if (criteria.startTime && event.timestamp < criteria.startTime) {
        return false;
      }
      
      if (criteria.endTime && event.timestamp > criteria.endTime) {
        return false;
      }
      
      // Filter by details content
      if (criteria.detailsFilter) {
        const detailsString = JSON.stringify(event.details).toLowerCase();
        if (!detailsString.includes(criteria.detailsFilter.toLowerCase())) {
          return false;
        }
      }
      
      return true;
    });
  }

  /**
   * Get audit statistics
   */
  getStatistics() {
    const now = Date.now();
    const last24Hours = now - (24 * 3600000);
    const last7Days = now - (7 * 24 * 3600000);
    
    const recent24h = this.auditLog.filter(e => e.timestamp > last24Hours);
    const recent7d = this.auditLog.filter(e => e.timestamp > last7Days);
    
    return {
      total: {
        events: this.auditLog.length,
        eventTypes: this.eventTypes.size,
        errors: Array.from(this.auditMetrics.errorCounts.values())
          .reduce((sum, count) => sum + count, 0)
      },
      recent24h: {
        events: recent24h.length,
        eventTypes: new Set(recent24h.map(e => e.type)).size,
        errors: recent24h.filter(e => 
          e.type.includes('ERROR') || e.type.includes('FAILED')
        ).length
      },
      recent7d: {
        events: recent7d.length,
        eventTypes: new Set(recent7d.map(e => e.type)).size,
        errors: recent7d.filter(e => 
          e.type.includes('ERROR') || e.type.includes('FAILED')
        ).length
      },
      eventTypeCounts: Object.fromEntries(this.auditMetrics.eventCounts),
      errorTypeCounts: Object.fromEntries(this.auditMetrics.errorCounts)
    };
  }

  /**
   * Get audit trail status
   */
  getStatus() {
    return {
      initialized: this.state.initialized,
      enabled: this.options.enabled,
      totalEvents: this.state.totalEvents,
      logSize: this.auditLog.length,
      eventTypes: Array.from(this.eventTypes),
      retentionPeriod: this.options.retentionPeriod,
      fipsCompliance: this.options.fipsCompliance,
      encryptionEnabled: this.options.encryptionEnabled,
      lastCleanup: this.state.lastCleanup,
      statistics: this.getStatistics()
    };
  }

  /**
   * Export audit log
   */
  exportLog(format = 'json') {
    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(this.auditLog, null, 2);
      
      case 'csv':
        return this.exportToCsv();
      
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Export to CSV format
   */
  exportToCsv() {
    const headers = ['ID', 'Type', 'Timestamp', 'Details', 'Integrity Hash'];
    const rows = this.auditLog.map(event => [
      event.id,
      event.type,
      new Date(event.timestamp).toISOString(),
      JSON.stringify(event.details),
      event.integrityHash
    ]);

    return [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
  }

  /**
   * Get audit trail
   */
  getAuditTrail() {
    return {
      events: [...this.auditLog], // Return copy to prevent modification
      totalEvents: this.auditLog.length,
      initialized: this.state.initialized,
      statistics: this.getStatistics()
    };
  }

  /**
   * Shutdown the Audit Trail System
   */
  async shutdown() {
    // Log shutdown event
    if (this.state.initialized) {
      await this.logEvent({
        type: 'AUDIT_SYSTEM_SHUTDOWN',
        timestamp: Date.now(),
        details: {
          totalEventsLogged: this.state.totalEvents,
          finalLogSize: this.auditLog.length
        }
      });
    }
    
    this.state.initialized = false;
    
    this.emit('shutdown', {
      timestamp: Date.now(),
      finalStatistics: this.getStatistics()
    });
  }
}
