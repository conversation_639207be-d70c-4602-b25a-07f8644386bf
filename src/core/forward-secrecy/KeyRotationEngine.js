/**
 * Key Rotation Engine for WebOTR Forward Secrecy
 * 
 * Handles automatic and manual key rotation with:
 * - Time-based rotation triggers
 * - Message count and data volume triggers
 * - Emergency rotation capabilities
 * - Secure key generation and transition
 */

import { EventEmitter } from 'events';
import { SecureRandom } from '../crypto/SecureRandom.js';
import { KeyDerivation } from '../crypto/KeyDerivation.js';

export class KeyRotationEngine extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      autoRotation: true,
      rotationInterval: 3600000, // 1 hour
      messageCountThreshold: 1000,
      dataVolumeThreshold: 10 * 1024 * 1024, // 10MB
      maxRotationTime: 100, // 100ms
      keySize: 32, // 256 bits
      ...options
    };
    
    this.state = {
      initialized: false,
      currentKeys: null,
      keyGeneration: 0,
      rotationInProgress: false
    };
    
    this.secureRandom = null;
    this.keyDerivation = null;
  }

  /**
   * Initialize the Key Rotation Engine
   */
  async initialize() {
    try {
      this.secureRandom = new SecureRandom();
      this.keyDerivation = new KeyDerivation();
      
      await Promise.all([
        this.secureRandom.initialize(),
        this.keyDerivation.initialize()
      ]);
      
      // Generate initial keys
      this.state.currentKeys = await this.generateKeySet(0);
      this.state.keyGeneration = 0;
      this.state.initialized = true;
      
      this.emit('initialized', {
        keyGeneration: this.state.keyGeneration,
        timestamp: Date.now()
      });
      
    } catch (error) {
      this.emit('error', {
        type: 'INITIALIZATION_FAILED',
        error: error.message,
        timestamp: Date.now()
      });
      throw error;
    }
  }

  /**
   * Generate a new key set for a specific generation
   */
  async generateKeySet(generation) {
    const startTime = performance.now();
    
    try {
      // Generate master key material
      const masterKey = await this.secureRandom.generateBytes(this.options.keySize);
      
      // Derive specific keys using HKDF
      const encryptionKey = await this.keyDerivation.deriveKey(
        masterKey,
        `WebOTR-FS-Encryption-${generation}`,
        this.options.keySize
      );
      
      const macKey = await this.keyDerivation.deriveKey(
        masterKey,
        `WebOTR-FS-MAC-${generation}`,
        this.options.keySize
      );
      
      const nextKeyMaterial = await this.keyDerivation.deriveKey(
        masterKey,
        `WebOTR-FS-Next-${generation}`,
        this.options.keySize
      );
      
      // Generate key fingerprint
      const keyFingerprint = await this.generateKeyFingerprint({
        encryptionKey,
        macKey,
        generation
      });
      
      const generationTime = performance.now() - startTime;
      
      return {
        generation,
        masterKey,
        encryptionKey,
        macKey,
        nextKeyMaterial,
        keyFingerprint,
        createdAt: Date.now(),
        generationTime
      };
      
    } catch (error) {
      throw new Error(`Key generation failed: ${error.message}`);
    }
  }

  /**
   * Generate a cryptographic fingerprint for a key set
   */
  async generateKeyFingerprint(keyData) {
    const keyMaterial = new Uint8Array([
      ...keyData.encryptionKey,
      ...keyData.macKey,
      ...new TextEncoder().encode(keyData.generation.toString())
    ]);
    
    const hash = await crypto.subtle.digest('SHA-256', keyMaterial);
    return Array.from(new Uint8Array(hash))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * Perform key rotation
   */
  async rotateKeys(rotationRequest) {
    if (this.state.rotationInProgress) {
      throw new Error('Key rotation already in progress');
    }
    
    const startTime = performance.now();
    this.state.rotationInProgress = true;
    
    try {
      this.emit('rotationTriggered', {
        trigger: rotationRequest.trigger,
        currentGeneration: rotationRequest.currentGeneration,
        timestamp: Date.now()
      });
      
      // Generate new key set
      const newGeneration = rotationRequest.currentGeneration + 1;
      const newKeys = await this.generateKeySet(newGeneration);
      
      // Create rotation transition data
      const rotationData = await this.createRotationTransition(
        this.state.currentKeys,
        newKeys,
        rotationRequest
      );
      
      // Store old keys for secure deletion
      const oldKeys = this.state.currentKeys;
      
      // Update current keys
      this.state.currentKeys = newKeys;
      this.state.keyGeneration = newGeneration;
      
      const rotationTime = performance.now() - startTime;
      
      // Validate rotation time
      if (rotationTime > this.options.maxRotationTime) {
        console.warn(`Key rotation took ${rotationTime}ms, exceeding target of ${this.options.maxRotationTime}ms`);
      }
      
      const result = {
        success: true,
        oldKeys,
        newKeys,
        rotationData,
        rotationTime,
        trigger: rotationRequest.trigger,
        timestamp: Date.now()
      };
      
      this.emit('rotationCompleted', result);
      
      return result;
      
    } catch (error) {
      this.emit('rotationFailed', {
        trigger: rotationRequest.trigger,
        error: error.message,
        timestamp: Date.now()
      });
      throw error;
      
    } finally {
      this.state.rotationInProgress = false;
    }
  }

  /**
   * Create rotation transition data for protocol continuity
   */
  async createRotationTransition(oldKeys, newKeys, rotationRequest) {
    return {
      rotationId: await this.generateRotationId(),
      oldGeneration: oldKeys.generation,
      newGeneration: newKeys.generation,
      trigger: rotationRequest.trigger,
      transitionKey: await this.deriveTransitionKey(oldKeys, newKeys),
      rotationProof: await this.generateRotationProof(oldKeys, newKeys),
      timestamp: Date.now()
    };
  }

  /**
   * Generate unique rotation ID
   */
  async generateRotationId() {
    const randomBytes = await this.secureRandom.generateBytes(16);
    return Array.from(randomBytes)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * Derive transition key for protocol continuity
   */
  async deriveTransitionKey(oldKeys, newKeys) {
    const transitionMaterial = new Uint8Array([
      ...oldKeys.nextKeyMaterial,
      ...newKeys.masterKey.slice(0, 16)
    ]);
    
    return await this.keyDerivation.deriveKey(
      transitionMaterial,
      'WebOTR-FS-Transition',
      32
    );
  }

  /**
   * Generate cryptographic proof of key rotation
   */
  async generateRotationProof(oldKeys, newKeys) {
    // Create proof that new keys are properly derived from old keys
    const proofMaterial = new Uint8Array([
      ...oldKeys.keyFingerprint.slice(0, 16).split('').map(c => c.charCodeAt(0)),
      ...newKeys.keyFingerprint.slice(0, 16).split('').map(c => c.charCodeAt(0)),
      ...new TextEncoder().encode(Date.now().toString())
    ]);
    
    const proofHash = await crypto.subtle.digest('SHA-256', proofMaterial);
    
    return {
      oldKeyFingerprint: oldKeys.keyFingerprint,
      newKeyFingerprint: newKeys.keyFingerprint,
      proofHash: Array.from(new Uint8Array(proofHash))
        .map(b => b.toString(16).padStart(2, '0'))
        .join(''),
      timestamp: Date.now()
    };
  }

  /**
   * Get current key set
   */
  getCurrentKeys() {
    if (!this.state.initialized) {
      throw new Error('Key Rotation Engine not initialized');
    }
    
    return {
      generation: this.state.keyGeneration,
      encryptionKey: this.state.currentKeys.encryptionKey,
      macKey: this.state.currentKeys.macKey,
      keyFingerprint: this.state.currentKeys.keyFingerprint,
      createdAt: this.state.currentKeys.createdAt
    };
  }

  /**
   * Check if rotation is needed based on triggers
   */
  shouldRotate(messageCount, dataVolume, lastRotationTime) {
    const now = Date.now();
    const timeSinceRotation = now - lastRotationTime;
    
    return {
      timeTriggered: timeSinceRotation >= this.options.rotationInterval,
      messageCountTriggered: messageCount >= this.options.messageCountThreshold,
      dataVolumeTriggered: dataVolume >= this.options.dataVolumeThreshold,
      shouldRotate: timeSinceRotation >= this.options.rotationInterval ||
                   messageCount >= this.options.messageCountThreshold ||
                   dataVolume >= this.options.dataVolumeThreshold
    };
  }

  /**
   * Get rotation engine status
   */
  getStatus() {
    return {
      initialized: this.state.initialized,
      currentGeneration: this.state.keyGeneration,
      rotationInProgress: this.state.rotationInProgress,
      autoRotationEnabled: this.options.autoRotation,
      rotationInterval: this.options.rotationInterval,
      messageCountThreshold: this.options.messageCountThreshold,
      dataVolumeThreshold: this.options.dataVolumeThreshold,
      currentKeyFingerprint: this.state.currentKeys ? this.state.currentKeys.keyFingerprint : null,
      keyCreatedAt: this.state.currentKeys ? this.state.currentKeys.createdAt : null
    };
  }

  /**
   * Emergency key rotation
   */
  async emergencyRotation(reason) {
    return await this.rotateKeys({
      trigger: 'EMERGENCY',
      currentGeneration: this.state.keyGeneration,
      metadata: { reason, emergency: true }
    });
  }

  /**
   * Validate key integrity
   */
  async validateKeyIntegrity() {
    if (!this.state.currentKeys) {
      return { valid: false, error: 'No current keys' };
    }
    
    try {
      // Regenerate fingerprint and compare
      const expectedFingerprint = await this.generateKeyFingerprint({
        encryptionKey: this.state.currentKeys.encryptionKey,
        macKey: this.state.currentKeys.macKey,
        generation: this.state.currentKeys.generation
      });
      
      const valid = expectedFingerprint === this.state.currentKeys.keyFingerprint;
      
      return {
        valid,
        currentFingerprint: this.state.currentKeys.keyFingerprint,
        expectedFingerprint,
        generation: this.state.currentKeys.generation
      };
      
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Calculate key fingerprint
   */
  async calculateKeyFingerprint(keySet) {
    if (!keySet || !keySet.encryptionKey || !keySet.macKey) {
      throw new Error('Invalid key set for fingerprint calculation');
    }

    // Create fingerprint from key material
    const keyMaterial = new Uint8Array(keySet.encryptionKey.length + keySet.macKey.length);
    keyMaterial.set(keySet.encryptionKey, 0);
    keyMaterial.set(keySet.macKey, keySet.encryptionKey.length);

    // Hash the key material to create fingerprint
    const hashBuffer = await crypto.subtle.digest('SHA-256', keyMaterial);
    const hashArray = new Uint8Array(hashBuffer);

    // Convert to hex string
    return Array.from(hashArray)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * Shutdown the Key Rotation Engine
   */
  async shutdown() {
    // Clear current keys from memory
    if (this.state.currentKeys) {
      // Securely clear key material
      if (this.state.currentKeys.masterKey) {
        this.state.currentKeys.masterKey.fill(0);
      }
      if (this.state.currentKeys.encryptionKey) {
        this.state.currentKeys.encryptionKey.fill(0);
      }
      if (this.state.currentKeys.macKey) {
        this.state.currentKeys.macKey.fill(0);
      }
      if (this.state.currentKeys.nextKeyMaterial) {
        this.state.currentKeys.nextKeyMaterial.fill(0);
      }
      
      this.state.currentKeys = null;
    }
    
    this.state.initialized = false;
    this.state.keyGeneration = 0;
    
    this.emit('shutdown', {
      timestamp: Date.now()
    });
  }
}
