/**
 * Forward Secrecy Manager for WebOTR
 * 
 * Provides advanced forward secrecy mechanisms including:
 * - Automatic and manual key rotation
 * - Secure key deletion with cryptographic erasure
 * - Zero-knowledge verification protocols
 * - Compliance audit trails
 */

import { EventEmitter } from 'events';
import { KeyRotationEngine } from './KeyRotationEngine.js';
import { SecureDeletionManager } from './SecureDeletionManager.js';
import { ZeroKnowledgeVerifier } from './ZeroKnowledgeVerifier.js';
import { AuditTrailSystem } from './AuditTrailSystem.js';

export class ForwardSecrecyManager extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      // Rotation policies
      autoRotation: true,
      rotationInterval: 3600000, // 1 hour in milliseconds
      messageCountThreshold: 1000,
      dataVolumeThreshold: 10 * 1024 * 1024, // 10MB
      
      // Security settings
      secureMemory: true,
      cryptographicErasure: true,
      zeroKnowledgeProofs: true,
      auditTrails: true,
      
      // Performance settings
      rotationTimeout: 100, // 100ms max rotation time
      deletionTimeout: 50,  // 50ms max deletion time
      verificationTimeout: 100, // 100ms max verification time
      
      // Compliance settings
      fipsCompliance: true,
      auditRetention: 90 * 24 * 3600000, // 90 days
      
      ...options
    };
    
    this.state = {
      initialized: false,
      currentKeyGeneration: 0,
      lastRotationTime: null,
      messageCount: 0,
      dataVolume: 0,
      rotationInProgress: false,
      deletionInProgress: false
    };
    
    this.keyRotationEngine = null;
    this.secureDeletionManager = null;
    this.zeroKnowledgeVerifier = null;
    this.auditTrailSystem = null;
    
    this.rotationTimer = null;
    this.performanceMetrics = {
      rotationTimes: [],
      deletionTimes: [],
      verificationTimes: []
    };
  }

  /**
   * Initialize the Forward Secrecy Manager
   */
  async initialize() {
    try {
      // Initialize core components
      this.keyRotationEngine = new KeyRotationEngine({
        autoRotation: this.options.autoRotation,
        rotationInterval: this.options.rotationInterval,
        messageCountThreshold: this.options.messageCountThreshold,
        dataVolumeThreshold: this.options.dataVolumeThreshold
      });
      
      this.secureDeletionManager = new SecureDeletionManager({
        cryptographicErasure: this.options.cryptographicErasure,
        secureMemory: this.options.secureMemory,
        deletionTimeout: this.options.deletionTimeout
      });
      
      this.zeroKnowledgeVerifier = new ZeroKnowledgeVerifier({
        enabled: this.options.zeroKnowledgeProofs,
        verificationTimeout: this.options.verificationTimeout,
        fipsCompliance: this.options.fipsCompliance
      });
      
      this.auditTrailSystem = new AuditTrailSystem({
        enabled: this.options.auditTrails,
        retentionPeriod: this.options.auditRetention,
        fipsCompliance: this.options.fipsCompliance
      });
      
      // Initialize components
      await Promise.all([
        this.keyRotationEngine.initialize(),
        this.secureDeletionManager.initialize(),
        this.zeroKnowledgeVerifier.initialize(),
        this.auditTrailSystem.initialize()
      ]);
      
      // Set up event handlers
      this.setupEventHandlers();
      
      // Start automatic rotation if enabled
      if (this.options.autoRotation) {
        this.startAutomaticRotation();
      }
      
      this.state.initialized = true;
      this.state.lastRotationTime = Date.now();
      
      // Log initialization
      await this.auditTrailSystem.logEvent({
        type: 'FORWARD_SECRECY_INITIALIZED',
        timestamp: Date.now(),
        details: {
          autoRotation: this.options.autoRotation,
          rotationInterval: this.options.rotationInterval,
          fipsCompliance: this.options.fipsCompliance
        }
      });
      
      this.emit('initialized', {
        timestamp: Date.now(),
        configuration: this.options
      });
      
    } catch (error) {
      this.emit('error', {
        type: 'INITIALIZATION_FAILED',
        error: error.message,
        timestamp: Date.now()
      });
      throw error;
    }
  }

  /**
   * Set up event handlers for component coordination
   */
  setupEventHandlers() {
    // Key rotation events
    this.keyRotationEngine.on('rotationTriggered', this.handleRotationTriggered.bind(this));
    this.keyRotationEngine.on('rotationCompleted', this.handleRotationCompleted.bind(this));
    this.keyRotationEngine.on('rotationFailed', this.handleRotationFailed.bind(this));
    
    // Secure deletion events
    this.secureDeletionManager.on('deletionCompleted', this.handleDeletionCompleted.bind(this));
    this.secureDeletionManager.on('deletionFailed', this.handleDeletionFailed.bind(this));
    
    // Zero-knowledge verification events
    this.zeroKnowledgeVerifier.on('verificationCompleted', this.handleVerificationCompleted.bind(this));
    this.zeroKnowledgeVerifier.on('verificationFailed', this.handleVerificationFailed.bind(this));
    
    // Audit trail events
    this.auditTrailSystem.on('auditEvent', this.handleAuditEvent.bind(this));
  }

  /**
   * Start automatic key rotation
   */
  startAutomaticRotation() {
    if (this.rotationTimer) {
      clearInterval(this.rotationTimer);
    }
    
    this.rotationTimer = setInterval(() => {
      this.checkRotationTriggers();
    }, Math.min(this.options.rotationInterval / 10, 60000)); // Check every 1/10th of interval or 1 minute
  }

  /**
   * Stop automatic key rotation
   */
  stopAutomaticRotation() {
    if (this.rotationTimer) {
      clearInterval(this.rotationTimer);
      this.rotationTimer = null;
    }
  }

  /**
   * Check if key rotation should be triggered
   */
  async checkRotationTriggers() {
    if (this.state.rotationInProgress) {
      return;
    }
    
    const now = Date.now();
    const timeSinceLastRotation = now - this.state.lastRotationTime;
    
    // Check time-based trigger
    if (timeSinceLastRotation >= this.options.rotationInterval) {
      await this.rotateKeys('TIME_BASED');
      return;
    }
    
    // Check message count trigger
    if (this.state.messageCount >= this.options.messageCountThreshold) {
      await this.rotateKeys('MESSAGE_COUNT');
      return;
    }
    
    // Check data volume trigger
    if (this.state.dataVolume >= this.options.dataVolumeThreshold) {
      await this.rotateKeys('DATA_VOLUME');
      return;
    }
  }

  /**
   * Manually trigger key rotation
   */
  async rotateKeysManually() {
    return await this.rotateKeys('MANUAL');
  }

  /**
   * Emergency key rotation
   */
  async emergencyRotation(reason = 'SECURITY_EVENT') {
    return await this.rotateKeys('EMERGENCY', { reason });
  }

  /**
   * Perform key rotation
   */
  async rotateKeys(trigger, metadata = {}) {
    if (this.state.rotationInProgress) {
      throw new Error('Key rotation already in progress');
    }
    
    const startTime = performance.now();
    this.state.rotationInProgress = true;
    
    try {
      // Log rotation start
      await this.auditTrailSystem.logEvent({
        type: 'KEY_ROTATION_STARTED',
        timestamp: Date.now(),
        trigger,
        metadata,
        keyGeneration: this.state.currentKeyGeneration
      });
      
      // Perform rotation
      const rotationResult = await this.keyRotationEngine.rotateKeys({
        trigger,
        currentGeneration: this.state.currentKeyGeneration,
        metadata
      });
      
      // Generate zero-knowledge proof of rotation
      let rotationProof = null;
      if (this.options.zeroKnowledgeProofs) {
        rotationProof = await this.zeroKnowledgeVerifier.generateRotationProof({
          oldKeyGeneration: this.state.currentKeyGeneration,
          newKeyGeneration: this.state.currentKeyGeneration + 1,
          rotationData: rotationResult
        });
      }
      
      // Securely delete old keys
      const deletionResult = await this.secureDeletionManager.deleteKeys({
        keyGeneration: this.state.currentKeyGeneration,
        keys: rotationResult.oldKeys
      });
      
      // Update state
      this.state.currentKeyGeneration++;
      this.state.lastRotationTime = Date.now();
      this.state.messageCount = 0;
      this.state.dataVolume = 0;
      
      const rotationTime = performance.now() - startTime;
      this.performanceMetrics.rotationTimes.push(rotationTime);
      
      // Log successful rotation
      await this.auditTrailSystem.logEvent({
        type: 'KEY_ROTATION_COMPLETED',
        timestamp: Date.now(),
        trigger,
        keyGeneration: this.state.currentKeyGeneration,
        rotationTime,
        rotationProof: rotationProof ? rotationProof.hash : null,
        deletionVerified: deletionResult.verified
      });
      
      this.emit('keyRotated', {
        trigger,
        keyGeneration: this.state.currentKeyGeneration,
        rotationTime,
        rotationProof,
        deletionVerified: deletionResult.verified,
        timestamp: Date.now()
      });
      
      return {
        success: true,
        keyGeneration: this.state.currentKeyGeneration,
        rotationTime,
        rotationProof,
        deletionVerified: deletionResult.verified
      };
      
    } catch (error) {
      // Log rotation failure
      await this.auditTrailSystem.logEvent({
        type: 'KEY_ROTATION_FAILED',
        timestamp: Date.now(),
        trigger,
        error: error.message,
        keyGeneration: this.state.currentKeyGeneration
      });
      
      this.emit('rotationFailed', {
        trigger,
        error: error.message,
        timestamp: Date.now()
      });
      
      throw error;
      
    } finally {
      this.state.rotationInProgress = false;
    }
  }

  /**
   * Update message statistics for rotation triggers
   */
  updateMessageStats(messageSize) {
    this.state.messageCount++;
    this.state.dataVolume += messageSize;
  }

  /**
   * Get current forward secrecy status
   */
  getStatus() {
    return {
      initialized: this.state.initialized,
      currentKeyGeneration: this.state.currentKeyGeneration,
      lastRotationTime: this.state.lastRotationTime,
      messageCount: this.state.messageCount,
      dataVolume: this.state.dataVolume,
      rotationInProgress: this.state.rotationInProgress,
      autoRotationEnabled: this.options.autoRotation,
      nextRotationDue: this.state.lastRotationTime + this.options.rotationInterval,
      performanceMetrics: {
        averageRotationTime: this.performanceMetrics.rotationTimes.length > 0 
          ? this.performanceMetrics.rotationTimes.reduce((a, b) => a + b) / this.performanceMetrics.rotationTimes.length 
          : 0,
        averageDeletionTime: this.performanceMetrics.deletionTimes.length > 0
          ? this.performanceMetrics.deletionTimes.reduce((a, b) => a + b) / this.performanceMetrics.deletionTimes.length
          : 0
      }
    };
  }

  /**
   * Get compliance audit report
   */
  async getAuditReport(startTime, endTime) {
    return await this.auditTrailSystem.generateReport(startTime, endTime);
  }

  /**
   * Get audit trail
   */
  getAuditTrail() {
    return this.auditTrailSystem.getAuditTrail();
  }

  /**
   * Verify audit trail integrity
   */
  async verifyAuditTrailIntegrity() {
    return await this.auditTrailSystem.verifyIntegrity();
  }

  /**
   * Validate key integrity
   */
  async validateKeyIntegrity() {
    if (!this.state.initialized) {
      throw new Error('ForwardSecrecyManager not initialized');
    }

    try {
      // Use the KeyRotationEngine's built-in validation
      return await this.keyRotationEngine.validateKeyIntegrity();
    } catch (error) {
      return {
        valid: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get current keys
   */
  getCurrentKeys() {
    if (!this.keyRotationEngine || !this.keyRotationEngine.state.currentKeys) {
      throw new Error('No current keys available');
    }
    return this.keyRotationEngine.state.currentKeys;
  }

  /**
   * Increment message count for rotation triggers
   */
  incrementMessageCount() {
    this.state.messageCount++;
    this.checkRotationTriggers();
  }

  /**
   * Increment data volume for rotation triggers
   */
  incrementDataVolume(bytes) {
    this.state.dataVolume += bytes;
    this.checkRotationTriggers();
  }

  /**
   * Manual key rotation
   */
  async rotateKeysManually() {
    return await this.rotateKeys('MANUAL');
  }

  /**
   * Emergency rotation
   */
  async emergencyRotation(reason) {
    await this.auditTrailSystem.logEvent({
      type: 'EMERGENCY_ROTATION_TRIGGERED',
      reason,
      timestamp: Date.now()
    });

    return await this.rotateKeys('EMERGENCY', reason);
  }

  /**
   * Verify forward secrecy properties
   */
  async verifyForwardSecrecy() {
    return await this.zeroKnowledgeVerifier.verifyForwardSecrecy({
      currentKeyGeneration: this.state.currentKeyGeneration,
      lastRotationTime: this.state.lastRotationTime
    });
  }

  /**
   * Event handlers
   */
  async handleRotationTriggered(event) {
    this.emit('rotationTriggered', event);
  }

  async handleRotationCompleted(event) {
    this.emit('rotationCompleted', event);
  }

  async handleRotationFailed(event) {
    this.emit('rotationFailed', event);
  }

  async handleDeletionCompleted(event) {
    const deletionTime = event.deletionTime;
    this.performanceMetrics.deletionTimes.push(deletionTime);
    this.emit('deletionCompleted', event);
  }

  async handleDeletionFailed(event) {
    this.emit('deletionFailed', event);
  }

  async handleVerificationCompleted(event) {
    const verificationTime = event.verificationTime;
    this.performanceMetrics.verificationTimes.push(verificationTime);
    this.emit('verificationCompleted', event);
  }

  async handleVerificationFailed(event) {
    this.emit('verificationFailed', event);
  }

  async handleAuditEvent(event) {
    this.emit('auditEvent', event);
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown() {
    this.stopAutomaticRotation();
    
    if (this.keyRotationEngine) {
      await this.keyRotationEngine.shutdown();
    }
    
    if (this.secureDeletionManager) {
      await this.secureDeletionManager.shutdown();
    }
    
    if (this.zeroKnowledgeVerifier) {
      await this.zeroKnowledgeVerifier.shutdown();
    }
    
    if (this.auditTrailSystem) {
      await this.auditTrailSystem.shutdown();
    }
    
    this.emit('shutdown', {
      timestamp: Date.now()
    });
  }
}
