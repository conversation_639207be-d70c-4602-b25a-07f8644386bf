/**
 * Authenticated Key Exchange (AKE) implementation for WebOTR
 *
 * This module implements the AKE protocol for OTR, which allows two parties
 * to establish a secure communication channel with authentication.
 */
import { random } from '../crypto/random';
import { encrypt, decrypt } from '../crypto/aes';
import { hmacSha256 } from '../crypto/hmac';
import { sign, verify } from '../crypto/dsa';
import { generateDHKeyPair, dhExchange } from '../crypto/dh';
import { deriveKeys } from '../crypto/keys';
import { MESSAGE_TYPE, PROTOCOL_VERSION } from './state';

/**
 * Create a DH commit message
 * @param {Object} dhKeyPair - DH key pair
 * @param {number} protocolVersion - OTR protocol version
 * @param {number} instanceTag - Sender's instance tag
 * @param {number} receiverInstanceTag - Receiver's instance tag (0 if unknown)
 * @returns {Promise<Object>} DH commit message data
 */
export async function createDHCommit(dhKeyPair, protocolVersion, instanceTag, receiverInstanceTag = 0) {
  // Validate inputs
  if (!dhKeyPair || !dhKeyPair.publicKey) {
    throw new Error('Valid DH key pair is required for DH commit');
  }

  // Generate a random key for encrypting our DH public key
  const aesKey = await random(16); // 128-bit AES key

  // Create initialization vector
  const iv = await random(16);

  // Convert public key to bytes (handle different formats)
  let publicKeyBytes;
  if (typeof dhKeyPair.publicKey === 'string') {
    // If it's a base64 string, decode it
    try {
      publicKeyBytes = new Uint8Array(Buffer.from(dhKeyPair.publicKey, 'base64'));
    } catch {
      // If not base64, treat as regular string
      publicKeyBytes = new TextEncoder().encode(dhKeyPair.publicKey);
    }
  } else if (dhKeyPair.publicKey instanceof Uint8Array) {
    publicKeyBytes = dhKeyPair.publicKey;
  } else {
    // Convert object to string representation
    const keyStr = JSON.stringify(dhKeyPair.publicKey);
    publicKeyBytes = new TextEncoder().encode(keyStr);
  }

  // Encrypt our DH public key
  const encryptedPublicKey = await encrypt(publicKeyBytes, aesKey, iv);

  // Hash the public key
  const publicKeyHash = await hmacSha256(publicKeyBytes, aesKey);

  return {
    protocolVersion,
    messageType: MESSAGE_TYPE.DH_COMMIT,
    senderInstanceTag: instanceTag,
    receiverInstanceTag,
    encryptedPublicKey,
    publicKeyHash,
    aesKey,
    iv
  };
}

/**
 * Create a DH key message
 * @param {Object} dhKeyPair - DH key pair
 * @param {number} protocolVersion - OTR protocol version
 * @param {number} instanceTag - Sender's instance tag
 * @param {number} receiverInstanceTag - Receiver's instance tag
 * @returns {Promise<Object>} DH key message data
 */
export async function createDHKey(dhKeyPair, protocolVersion, instanceTag, receiverInstanceTag) {
  // Validate inputs
  if (!dhKeyPair || !dhKeyPair.publicKey) {
    throw new Error('Valid DH key pair is required for DH key message');
  }

  return {
    protocolVersion,
    messageType: MESSAGE_TYPE.DH_KEY,
    senderInstanceTag: instanceTag,
    receiverInstanceTag,
    publicKey: dhKeyPair.publicKey
  };
}

/**
 * Create a reveal signature message
 * @param {Object} dhKeyPair - DH key pair
 * @param {Object} dsaKeyPair - DSA key pair for signing
 * @param {Object} sharedSecret - Shared secret from DH exchange
 * @param {number} protocolVersion - OTR protocol version
 * @param {number} instanceTag - Sender's instance tag
 * @param {number} receiverInstanceTag - Receiver's instance tag
 * @param {Uint8Array} aesKey - AES key from DH commit
 * @param {Uint8Array} iv - IV from DH commit
 * @returns {Promise<Object>} Reveal signature message data
 */
export async function createRevealSignature(dhKeyPair, dsaKeyPair, sharedSecret, protocolVersion, instanceTag, receiverInstanceTag, aesKey, iv) {
  // Validate inputs
  if (!dhKeyPair || !dhKeyPair.publicKey) {
    throw new Error('Valid DH key pair is required for reveal signature');
  }
  if (!dsaKeyPair || !dsaKeyPair.privateKey) {
    throw new Error('Valid DSA key pair is required for reveal signature');
  }

  // Create the signature data
  const signatureData = {
    publicKey: dhKeyPair.publicKey,
    dhSharedSecret: sharedSecret,
    instanceTag
  };

  // Sign the data
  const signature = await sign(JSON.stringify(signatureData), dsaKeyPair.privateKey);

  // Encrypt the signature (ensure it's base64 encoded)
  const encryptedSignature = await encrypt(signature, aesKey, iv);

  // Ensure encrypted signature is base64 string for compatibility
  const encryptedSignatureBase64 = typeof encryptedSignature === 'string'
    ? encryptedSignature
    : btoa(String.fromCharCode(...new Uint8Array(encryptedSignature)));

  return {
    protocolVersion,
    messageType: MESSAGE_TYPE.REVEAL_SIGNATURE,
    senderInstanceTag: instanceTag,
    receiverInstanceTag,
    revealedKey: aesKey,
    encryptedSignature: encryptedSignatureBase64,
    macKey: await hmacSha256(encryptedSignatureBase64, aesKey)
  };
}

/**
 * Create a signature message
 * @param {Object} dhKeyPair - DH key pair
 * @param {Object} dsaKeyPair - DSA key pair for signing
 * @param {Object} sharedSecret - Shared secret from DH exchange
 * @param {number} protocolVersion - OTR protocol version
 * @param {number} instanceTag - Sender's instance tag
 * @param {number} receiverInstanceTag - Receiver's instance tag
 * @param {Uint8Array} sessionKey - Session key derived from shared secret
 * @returns {Promise<Object>} Signature message data
 */
export async function createSignature(dhKeyPair, dsaKeyPair, sharedSecret, protocolVersion, instanceTag, receiverInstanceTag, sessionKey) {
  // Validate inputs
  if (!dhKeyPair || !dhKeyPair.publicKey) {
    throw new Error('Valid DH key pair is required for signature');
  }
  if (!dsaKeyPair || !dsaKeyPair.privateKey) {
    throw new Error('Valid DSA key pair is required for signature');
  }

  // Create the signature data
  const signatureData = {
    publicKey: dhKeyPair.publicKey,
    dhSharedSecret: sharedSecret,
    instanceTag
  };

  // Sign the data
  const signature = await sign(JSON.stringify(signatureData), dsaKeyPair.privateKey);

  // Encrypt the signature with session key
  const encryptedSignature = await encrypt(signature, sessionKey);

  // Ensure encrypted signature is base64 string for compatibility
  const encryptedSignatureBase64 = typeof encryptedSignature === 'string'
    ? encryptedSignature
    : btoa(String.fromCharCode(...new Uint8Array(encryptedSignature)));

  return {
    protocolVersion,
    messageType: MESSAGE_TYPE.SIGNATURE,
    senderInstanceTag: instanceTag,
    receiverInstanceTag,
    encryptedSignature: encryptedSignatureBase64,
    macKey: await hmacSha256(encryptedSignatureBase64, sessionKey)
  };
}

/**
 * Start Authenticated Key Exchange
 * @param {OtrState} state - Current OTR state
 * @returns {Promise<Object>} Result with updated state and message to send
 */
export async function startAKE(state) {
  if (!state) {
    throw new Error('State is required for AKE');
  }

  // Generate DH key pair for this session
  const dhKeyPair = await generateDHKeyPair();

  // Store the key pair in state
  state.dhKeyPair = dhKeyPair;

  // Create DH commit message
  const dhCommit = await createDHCommit(
    dhKeyPair,
    PROTOCOL_VERSION.V3,
    state.instanceTag || 0,
    0 // Unknown receiver instance tag
  );

  // Update state
  if (state.startAKE) {
    state.startAKE();
  } else {
    state.state = state.STATE?.AWAITING_DHKEY || 1;
  }

  return {
    state,
    message: '?OTR:AKESTART',
    dhCommit
  };
}

/**
 * Process a DH commit message
 * @param {Object} message - DH commit message data
 * @param {Object} state - OTR state
 * @returns {Promise<Object>} Processing result
 */
export async function processDHCommit(message, state) {
  if (!state) {
    throw new Error('State is required for processing DH commit');
  }

  // Store the DH commit message for later
  state.dhCommitMessage = message;

  // Generate our DH key pair if we don't have one
  if (!state.dhKeyPair) {
    state.dhKeyPair = await generateDHKeyPair();
  }

  // Create a DH key message in response
  const dhKeyMessage = await createDHKey(
    state.dhKeyPair,
    state.protocolVersion || PROTOCOL_VERSION.V3,
    state.instanceTag || 0,
    message.senderInstanceTag
  );

  // Update state
  if (state.handleDHCommit) {
    state.handleDHCommit();
  }

  return {
    response: '?OTR:DHKEY',
    dhKeyMessage,
    state
  };
}

/**
 * Process a DH key message
 * @param {Object} message - DH key message data
 * @param {Object} state - OTR state
 * @returns {Promise<Object>} Processing result
 */
export async function processDHKey(message, state) {
  if (!state) {
    throw new Error('State is required for processing DH key');
  }

  // Store their DH public key
  state.theirPublicKey = message.publicKey;

  // Compute the shared secret
  const sharedSecret = await dhExchange(state.dhKeyPair.privateKey, message.publicKey);

  // Derive keys from the shared secret
  const keys = await deriveKeys(sharedSecret);

  // Create a reveal signature message
  const revealSignatureMessage = await createRevealSignature(
    state.dhKeyPair,
    state.dsaKeyPair,
    sharedSecret,
    state.protocolVersion || PROTOCOL_VERSION.V3,
    state.instanceTag || 0,
    message.senderInstanceTag,
    state.dhCommitMessage?.aesKey,
    state.dhCommitMessage?.iv
  );

  // Store the keys
  state.sendingAESKey = keys.sendingAESKey;
  state.sendingMACKey = keys.sendingMACKey;
  state.receivingAESKey = keys.receivingAESKey;
  state.receivingMACKey = keys.receivingMACKey;

  // Update state
  if (state.handleDHKey) {
    state.handleDHKey();
  }

  return {
    response: '?OTR:REVEALSIG',
    revealSignatureMessage,
    state
  };
}

/**
 * Process a reveal signature message
 * @param {Object} message - Reveal signature message data
 * @param {Object} state - OTR state
 * @returns {Promise<Object>} Processing result
 */
export async function processRevealSignature(message, state) {
  if (!state) {
    throw new Error('State is required for processing reveal signature');
  }

  try {
    // Decrypt the signature using the revealed key
    const decryptedSignature = await decrypt(
      message.encryptedSignature,
      message.revealedKey
    );

    // Verify the signature
    const signatureData = {
      publicKey: state.theirPublicKey,
      dhSharedSecret: state.sharedSecret,
      instanceTag: message.senderInstanceTag
    };

    const isValid = await verify(
      JSON.stringify(signatureData),
      decryptedSignature,
      state.theirDSAPublicKey
    );

    if (!isValid) {
      throw new Error('Invalid signature in reveal signature message');
    }

    // Compute our shared secret
    const sharedSecret = await dhExchange(state.dhKeyPair.privateKey, state.theirPublicKey);

    // Derive session keys
    const keys = await deriveKeys(sharedSecret);

    // Create signature message
    const signatureMessage = await createSignature(
      state.dhKeyPair,
      state.dsaKeyPair,
      sharedSecret,
      state.protocolVersion || PROTOCOL_VERSION.V3,
      state.instanceTag || 0,
      message.senderInstanceTag,
      keys.sendingAESKey
    );

    // Store the keys
    state.sendingAESKey = keys.sendingAESKey;
    state.sendingMACKey = keys.sendingMACKey;
    state.receivingAESKey = keys.receivingAESKey;
    state.receivingMACKey = keys.receivingMACKey;

    // Update state
    if (state.handleRevealSignature) {
      state.handleRevealSignature();
    }

    return {
      response: '?OTR:SIG',
      signatureMessage,
      state
    };
  } catch (error) {
    throw new Error(`Failed to process reveal signature: ${error.message}`);
  }
}

/**
 * Process a signature message
 * @param {Object} message - Signature message data
 * @param {Object} state - OTR state
 * @returns {Promise<Object>} Processing result
 */
export async function processSignature(message, state) {
  if (!state) {
    throw new Error('State is required for processing signature');
  }

  try {
    // Decrypt the signature using our receiving key
    const decryptedSignature = await decrypt(
      message.encryptedSignature,
      state.receivingAESKey
    );

    // Verify the signature
    const signatureData = {
      publicKey: state.theirPublicKey,
      dhSharedSecret: state.sharedSecret,
      instanceTag: message.senderInstanceTag
    };

    const isValid = await verify(
      JSON.stringify(signatureData),
      decryptedSignature,
      state.theirDSAPublicKey
    );

    if (!isValid) {
      throw new Error('Invalid signature in signature message');
    }

    // AKE is complete - transition to encrypted state
    if (state.goEncrypted) {
      state.goEncrypted();
    } else if (state.setState) {
      state.setState(state.STATE?.ENCRYPTED || 4);
    } else {
      state.state = state.STATE?.ENCRYPTED || 4;
    }

    // Update state
    if (state.handleSignature) {
      state.handleSignature();
    }

    return {
      response: null, // No response needed - AKE is complete
      state
    };
  } catch (error) {
    throw new Error(`Failed to process signature: ${error.message}`);
  }
<<<<<<< HEAD
=======
  
  // Update state
  state.handleSignature();
  
  return {
    response: null,
    state
  };
}

/**
 * Start the AKE process
 * @param {Object} state - OTR state
 * @returns {Promise<Object>} AKE initialization result
 */
export async function startAKE(state) {
  // Generate our DH key pair
  state.dhKeyPair = await generateDHKeyPair();
  
  // Create a DH commit message
  const dhCommitMessage = await createDHCommit(
    state.dhKeyPair,
    state.protocolVersion,
    state.instanceTag,
    state.theirInstanceTag
  );
  
  // Store the DH commit message for later
  state.dhCommitMessage = dhCommitMessage;
  
  // Update state
  state.startAKE();
  
  return {
    message: dhCommitMessage,
    state
  };
>>>>>>> origin/feature/browser-extension
}