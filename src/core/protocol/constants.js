/**
 * OTR protocol constants
 */

/**
 * OTR protocol versions
 */
export const PROTOCOL_VERSION = {
  V1: 1,
  V2: 2,
  V3: 3,
  V4: 4,
  CURRENT: 3 // Default to version 3
};

/**
 * OTR message types
 */
export const MESSAGE_TYPE = {
  QUERY: 1,
  DH_COMMIT: 2,
  DH_KEY: 3,
  REVE<PERSON>_SIGNATURE: 4,
  SIGNATURE: 5,
  DATA: 6,
  ERROR: 7,
  WHITESPACE_TAG: 8
};

/**
 * OTR message prefixes
 */
export const MESSAGE_PREFIX = {
  QUERY: '?OTR?',
  WHITESPACE_TAG: ' \t  \t\t\t\t \t \t \t    \t\t  \t',
  ERROR: '?OTR ERROR:',
  DATA: '?OTR:'
}; 