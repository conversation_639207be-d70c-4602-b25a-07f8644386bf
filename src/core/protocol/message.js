/**
 * OTR message formatting and parsing
 */
import { MESSAGE_TYPE, PROTOCOL_VERSION } from './state';
import { encrypt, decrypt } from '../crypto/aes';
import { hmacSha256, verifyHmacSha256 } from '../crypto/hmac';

// OTR protocol message identifiers
const MESSAGE_PREFIX = {
  QUERY: '?OTR',
  DH_COMMIT: '?OTR:AAMC',
  DH_KEY: '?OTR:AAMD',
  REVEAL_SIGNATURE: '?OTR:AAME',
  SIGNATURE: '?OTR:AAMF',
  DATA: '?OTR:AAMG',
  ERROR: '?OTR ERROR:',
  ENCODED: '?OTR:',
  WHITESPACE_TAG: '\x20\x09\x20\x20\x09\x09\x09\x09\x20\x09\x20\x09\x20\x09\x20\x20'
};

// OTR protocol message versions
const VERSION_TAGS = {
  V2: '?OTRv2?',
  V3: '?OTRv3?',
  V2_V3: '?OTRv23?'
};

/**
 * Parse an OTR message
 * @param {string} message - Message to parse
 * @returns {Object|null} Parsed message or null if not an OTR message
 */
export function parseMessage(message) {
  // Handle null/undefined inputs
  if (message === null || message === undefined) {
    throw new Error('Message cannot be null or undefined');
  }

  // Handle non-string inputs
  if (typeof message !== 'string') {
    throw new Error('Message must be a string');
  }

  // Handle empty string - return as plaintext
  if (message === '') {
    return {
      type: MESSAGE_TYPE.PLAINTEXT,
      content: '',
      encrypted: false,
      timestamp: Date.now()
    };
  }

  // Check for OTR error message (most specific first)
  if (message.startsWith(MESSAGE_PREFIX.ERROR)) {
    const errorContent = message.substring(MESSAGE_PREFIX.ERROR.length).trim();
    return {
      type: MESSAGE_TYPE.ERROR,
      content: errorContent,
      error: errorContent, // For backward compatibility
      encrypted: false,
      timestamp: Date.now()
    };
  }

  // Check for OTR DH commit message (more specific first)
  if (message.startsWith(MESSAGE_PREFIX.DH_COMMIT)) {
    return {
      type: MESSAGE_TYPE.DH_COMMIT,
      content: message.substring(MESSAGE_PREFIX.DH_COMMIT.length),
      encrypted: false,
      timestamp: Date.now()
    };
  }

  // Check for OTR query message (must not be a more specific OTR message)
  if (message.startsWith(MESSAGE_PREFIX.QUERY) && !message.startsWith(MESSAGE_PREFIX.QUERY + ':')) {
    return {
      type: MESSAGE_TYPE.QUERY,
      content: message,
      encrypted: false,
      versions: extractVersions(message),
      timestamp: Date.now()
    };
  }

  // Check for OTR DH key message
  if (message.startsWith(MESSAGE_PREFIX.DH_KEY)) {
    return {
      type: MESSAGE_TYPE.DH_KEY,
      content: message.substring(MESSAGE_PREFIX.DH_KEY.length),
      encrypted: false,
      timestamp: Date.now()
    };
  }

  // Check for OTR reveal signature message
  if (message.startsWith(MESSAGE_PREFIX.REVEAL_SIGNATURE)) {
    return {
      type: MESSAGE_TYPE.REVEAL_SIGNATURE,
      content: message.substring(MESSAGE_PREFIX.REVEAL_SIGNATURE.length),
      encrypted: false,
      timestamp: Date.now()
    };
  }

  // Check for OTR signature message
  if (message.startsWith(MESSAGE_PREFIX.SIGNATURE)) {
    return {
      type: MESSAGE_TYPE.SIGNATURE,
      content: message.substring(MESSAGE_PREFIX.SIGNATURE.length),
      encrypted: false,
      timestamp: Date.now()
    };
  }

  // Check for OTR data message
  if (message.startsWith(MESSAGE_PREFIX.DATA)) {
    return {
      type: MESSAGE_TYPE.DATA,
      content: message.substring(MESSAGE_PREFIX.DATA.length),
      encrypted: true,
      timestamp: Date.now()
    };
  }



  // Check for OTR whitespace tag
  if (message.includes(MESSAGE_PREFIX.WHITESPACE_TAG)) {
    return {
      type: MESSAGE_TYPE.WHITESPACE_TAG,
      content: message,
      encrypted: false,
      versions: extractVersionsFromTag(message),
      timestamp: Date.now()
    };
  }

  // Default to plaintext message
  return {
    type: MESSAGE_TYPE.PLAINTEXT,
    content: message,
    encrypted: false,
    timestamp: Date.now()
  };
}

/**
 * Parse an OTR query message
 * @param {string} message - Query message to parse
 * @returns {Object} Parsed query message
 */
function parseQueryMessage(message) {
  let versions = [];
  
  if (message.includes(VERSION_TAGS.V2)) {
    versions.push(PROTOCOL_VERSION.V2);
  }
  
  if (message.includes(VERSION_TAGS.V3)) {
    versions.push(PROTOCOL_VERSION.V3);
  }
  
  if (message.includes(VERSION_TAGS.V2_V3)) {
    versions.push(PROTOCOL_VERSION.V2);
    versions.push(PROTOCOL_VERSION.V3);
  }
  
  return {
    type: MESSAGE_TYPE.QUERY,
    data: {
      versions
    }
  };
}

/**
 * Parse a DH commit message
 * @param {string} message - DH commit message to parse
 * @returns {Object} Parsed DH commit message
 */
function parseDHCommit(message) {
  // This is a placeholder for the actual implementation
  // In a real implementation, we would extract the encrypted DH key and hash
  return {
    encryptedKey: null,
    hash: null
  };
}

/**
 * Parse a DH key message
 * @param {string} message - DH key message to parse
 * @returns {Object} Parsed DH key message
 */
function parseDHKey(message) {
  // This is a placeholder for the actual implementation
  // In a real implementation, we would extract the DH public key
  return {
    publicKey: null
  };
}

/**
 * Parse a reveal signature message
 * @param {string} message - Reveal signature message to parse
 * @returns {Object} Parsed reveal signature message
 */
function parseRevealSignature(message) {
  // This is a placeholder for the actual implementation
  // In a real implementation, we would extract the revealed key, encrypted signature, and MAC
  return {
    revealedKey: null,
    encryptedSignature: null,
    mac: null
  };
}

/**
 * Parse a signature message
 * @param {string} message - Signature message to parse
 * @returns {Object} Parsed signature message
 */
function parseSignature(message) {
  // This is a placeholder for the actual implementation
  // In a real implementation, we would extract the encrypted signature and MAC
  return {
    encryptedSignature: null,
    mac: null
  };
}

/**
 * Parse a data message
 * @param {string} message - Data message to parse
 * @returns {Object} Parsed data message
 */
function parseData(message) {
  // This is a placeholder for the actual implementation
  // In a real implementation, we would extract the sender's instance tag,
  // receiver's instance tag, flags, sender's keyid, recipient's keyid,
  // next DH public key, counter, encrypted message, authenticator, and old MAC keys
  return {
    senderInstanceTag: null,
    receiverInstanceTag: null,
    flags: null,
    senderKeyId: null,
    recipientKeyId: null,
    nextDHPublicKey: null,
    counter: null,
    encryptedMessage: null,
    authenticator: null,
    oldMACKeys: null
  };
}

/**
 * Parse an error message
 * @param {string} message - Error message to parse
 * @returns {Object} Parsed error message
 */
function parseError(message) {
  // Extract the error message
  const errorMessage = message.substring(MESSAGE_PREFIX.ERROR.length).trim();
  return {
    error: errorMessage
  };
}

/**
 * Create a generic OTR message
 * @param {string|Object} content - Message content
 * @param {string} type - Message type
 * @param {boolean} encrypted - Whether the message is encrypted
 * @returns {Object} Created message object
 */
export function createMessage(content, type, encrypted = false) {
  if (!type || typeof type !== 'string') {
    throw new Error('Message type is required and must be a string');
  }

  // Validate type against known message types
  const validTypes = Object.values(MESSAGE_TYPE);
  if (!validTypes.includes(type)) {
    throw new Error(`Invalid message type: ${type}. Must be one of: ${validTypes.join(', ')}`);
  }

  return {
    content,
    type,
    encrypted,
    timestamp: Date.now()
  };
}

/**
 * Create an OTR query message
 * @param {Array<number>} versions - OTR protocol versions to include
 * @returns {string} OTR query message
 */
export function createQueryMessage(versions) {
  return `${MESSAGE_PREFIX.QUERY}v${versions.join('')}?`;
}

/**
 * Create an OTR data message
 * @param {string} content - Message content
 * @returns {string} OTR data message
 */
export function createDataMessage(content) {
  return `${MESSAGE_PREFIX.DATA}${content}`;
}

/**
 * Create an OTR error message
 * @param {string} error - Error message
 * @returns {string} OTR error message
 */
export function createErrorMessage(error) {
  return `${MESSAGE_PREFIX.ERROR} ${error}`;
}

/**
 * Encrypt a message using OTR
 * @param {string} message - Message to encrypt
 * @param {Uint8Array} aesKey - AES key
 * @param {Uint8Array} macKey - MAC key
 * @param {number} counter - Message counter
 * @returns {Promise<Object>} Encrypted message and MAC
 */
export async function encryptMessage(message, aesKey, macKey, counter = 0) {
  // Validate inputs
  if (message === null || message === undefined) {
    throw new Error('Message cannot be null or undefined');
  }

  if (aesKey === null || aesKey === undefined) {
    throw new Error('AES key cannot be null or undefined');
  }

  if (macKey === null || macKey === undefined) {
    throw new Error('MAC key cannot be null or undefined');
  }

  // Convert message to string if needed
  const messageStr = typeof message === 'string' ? message : String(message);

  // Convert message to bytes
  const encoder = new TextEncoder();
  const messageBytes = encoder.encode(messageStr);

  // Create initialization vector from counter
  const iv = new Uint8Array(16);
  iv[0] = (counter >> 24) & 0xFF;
  iv[1] = (counter >> 16) & 0xFF;
  iv[2] = (counter >> 8) & 0xFF;
  iv[3] = counter & 0xFF;

  // Encrypt message
  const encryptedBase64 = await encrypt(messageBytes, aesKey, iv);

  // Convert base64 back to Uint8Array for MAC computation
  const encryptedWithIV = Uint8Array.from(atob(encryptedBase64), c => c.charCodeAt(0));

  // Extract just the encrypted data (without IV) for MAC computation
  const encryptedMessage = encryptedWithIV.slice(16); // Skip the 16-byte IV

  // Calculate MAC over the encrypted bytes (without IV)
  const mac = await hmacSha256(encryptedMessage, macKey);

  return {
    encryptedMessage,
    mac
  };
}

/**
 * Decrypt a message using OTR
 * @param {Uint8Array} encryptedMessage - Encrypted message
 * @param {Uint8Array} aesKey - AES key
 * @param {Uint8Array} macKey - MAC key
 * @param {Uint8Array} mac - Message authentication code
 * @param {number} counter - Message counter
 * @returns {Promise<string>} Decrypted message
 */
export async function decryptMessage(encryptedMessage, aesKey, macKey, mac, counter = 0) {
  // Validate inputs
  if (encryptedMessage === null || encryptedMessage === undefined) {
    throw new Error('Encrypted message cannot be null or undefined');
  }

  if (aesKey === null || aesKey === undefined) {
    throw new Error('AES key cannot be null or undefined');
  }

  if (macKey === null || macKey === undefined) {
    throw new Error('MAC key cannot be null or undefined');
  }

  if (mac === null || mac === undefined) {
    throw new Error('MAC cannot be null or undefined');
  }

  // Verify MAC
  const isValid = await verifyHmacSha256(encryptedMessage, mac, macKey);
  if (!isValid) {
    throw new Error('Invalid MAC');
  }

  // Create initialization vector from counter
  const iv = new Uint8Array(16);
  iv[0] = (counter >> 24) & 0xFF;
  iv[1] = (counter >> 16) & 0xFF;
  iv[2] = (counter >> 8) & 0xFF;
  iv[3] = counter & 0xFF;

  // Reconstruct the full encrypted data with IV for decryption
  const fullEncryptedData = new Uint8Array(16 + encryptedMessage.length);
  fullEncryptedData.set(iv); // Set IV at the beginning
  fullEncryptedData.set(encryptedMessage, 16); // Set encrypted data after IV

  // Convert to base64 for decryption
  const encryptedBase64 = btoa(String.fromCharCode(...fullEncryptedData));

  // Decrypt message
  const decryptedBytes = await decrypt(encryptedBase64, aesKey);

  // Convert bytes to string
  const decoder = new TextDecoder();
  return decoder.decode(decryptedBytes);
}

/**
 * Extract OTR versions from a query message
 * @param {string} message - Query message
 * @returns {Array<number>} Supported OTR versions
 * @private
 */
function extractVersions(message) {
  const match = message.match(/\?OTR\?v([1-9]+)\?/);
  if (!match || !match[1]) {
    return [];
  }
  
  return match[1].split('').map(v => parseInt(v, 10));
}

/**
 * Extract OTR versions from a whitespace tag
 * @param {string} message - Message with whitespace tag
 * @returns {Array<number>} Supported OTR versions
 * @private
 */
function extractVersionsFromTag(message) {
  // This is a simplified implementation
  // In practice, you would check specific whitespace patterns
  return [1, 2, 3].filter(() => Math.random() > 0.5);
}