/**
 * Message Fragmentation Implementation
 * 
 * Based on libOTR's fragment.c implementation patterns.
 * Handles OTR message fragmentation and reassembly for large messages.
 */

const crypto = require('crypto');

/**
 * Fragment Header Constants (based on libOTR)
 */
const FRAGMENT_CONSTANTS = {
  // Fragment header format: ?OTR|instance_tag|fragment_num|total_fragments|fragment_data,
  HEADER_PREFIX: '?OTR|',
  HEADER_SUFFIX: ',',
  SEPARATOR: '|',
  
  // Fragment limits
  MIN_FRAGMENT_SIZE: 50,     // Minimum useful fragment size
  MAX_FRAGMENT_SIZE: 65535,  // Maximum fragment size
  MAX_FRAGMENTS: 65535,      // Maximum number of fragments
  
  // Header sizes for different protocol versions
  V2_HEADER_SIZE: 19,        // OTR v2 fragment header size
  V3_HEADER_SIZE: 37,        // OTR v3 fragment header size (includes instance tags)
  
  // Fragment timeout (milliseconds)
  FRAGMENT_TIMEOUT: 60000    // 60 seconds
};

/**
 * Fragment Information Structure
 */
class FragmentInfo {
  constructor() {
    this.instanceTag = 0;
    this.fragmentNum = 0;
    this.totalFragments = 0;
    this.data = '';
    this.timestamp = Date.now();
  }
}

/**
 * Fragment Buffer for reassembly
 */
class FragmentBuffer {
  constructor(instanceTag, totalFragments) {
    this.instanceTag = instanceTag;
    this.totalFragments = totalFragments;
    this.fragments = new Map();
    this.createdTime = Date.now();
    this.lastUpdated = Date.now();
  }

  /**
   * Add a fragment to the buffer
   */
  addFragment(fragmentNum, data) {
    this.fragments.set(fragmentNum, data);
    this.lastUpdated = Date.now();
  }

  /**
   * Check if all fragments are received
   */
  isComplete() {
    if (this.fragments.size !== this.totalFragments) {
      return false;
    }
    
    // Check that we have all fragment numbers from 1 to totalFragments
    for (let i = 1; i <= this.totalFragments; i++) {
      if (!this.fragments.has(i)) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Reassemble the complete message
   */
  reassemble() {
    if (!this.isComplete()) {
      throw new Error('Cannot reassemble incomplete fragment buffer');
    }
    
    let completeMessage = '';
    for (let i = 1; i <= this.totalFragments; i++) {
      completeMessage += this.fragments.get(i);
    }
    
    return completeMessage;
  }

  /**
   * Check if buffer has expired
   */
  isExpired() {
    return (Date.now() - this.createdTime) > FRAGMENT_CONSTANTS.FRAGMENT_TIMEOUT;
  }
}

/**
 * Message Fragmentation Handler
 * Based on libOTR's otrl_proto_fragment_create and related functions
 */
class MessageFragmentation {
  constructor() {
    // Fragment buffers for reassembly (keyed by instance tag)
    this.fragmentBuffers = new Map();
    
    // Cleanup timer
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredFragments();
    }, 30000); // Clean up every 30 seconds
  }

  /**
   * Fragment a message for transmission (based on otrl_proto_fragment_create)
   */
  fragmentMessage(message, maxSize = 1400, protocolVersion = 3, instanceTag = null) {
    try {
      // Validate inputs
      if (!message || typeof message !== 'string') {
        throw new Error('Message must be a non-empty string');
      }
      
      if (maxSize < FRAGMENT_CONSTANTS.MIN_FRAGMENT_SIZE) {
        throw new Error(`Max size too small: ${maxSize} < ${FRAGMENT_CONSTANTS.MIN_FRAGMENT_SIZE}`);
      }
      
      // Calculate header size based on protocol version
      const headerSize = protocolVersion === 3 ? 
        FRAGMENT_CONSTANTS.V3_HEADER_SIZE : 
        FRAGMENT_CONSTANTS.V2_HEADER_SIZE;
      
      // Calculate available space for fragment data
      const availableSpace = maxSize - headerSize;
      
      if (availableSpace <= 0) {
        throw new Error('Max size too small to accommodate fragment header');
      }
      
      // Check if fragmentation is needed
      if (message.length <= maxSize) {
        return [message]; // No fragmentation needed
      }
      
      // Calculate number of fragments needed
      const totalFragments = Math.ceil(message.length / availableSpace);
      
      if (totalFragments > FRAGMENT_CONSTANTS.MAX_FRAGMENTS) {
        throw new Error(`Message too large: requires ${totalFragments} fragments (max: ${FRAGMENT_CONSTANTS.MAX_FRAGMENTS})`);
      }
      
      // Generate instance tag if not provided (for v3)
      if (protocolVersion === 3 && !instanceTag) {
        instanceTag = this.generateInstanceTag();
      }
      
      // Create fragments
      const fragments = [];
      for (let i = 0; i < totalFragments; i++) {
        const fragmentNum = i + 1;
        const start = i * availableSpace;
        const end = Math.min(start + availableSpace, message.length);
        const fragmentData = message.substring(start, end);
        
        const fragment = this.createFragment(
          instanceTag,
          fragmentNum,
          totalFragments,
          fragmentData,
          protocolVersion
        );
        
        fragments.push(fragment);
      }
      
      return fragments;
      
    } catch (error) {
      throw new Error(`Message fragmentation failed: ${error.message}`);
    }
  }

  /**
   * Create a single fragment (based on libOTR fragment format)
   */
  createFragment(instanceTag, fragmentNum, totalFragments, data, protocolVersion = 3) {
    if (protocolVersion === 3) {
      // OTR v3 format: ?OTR|instance_tag|fragment_num|total_fragments|fragment_data,
      return `${FRAGMENT_CONSTANTS.HEADER_PREFIX}${instanceTag.toString(16).padStart(8, '0')}${FRAGMENT_CONSTANTS.SEPARATOR}${fragmentNum.toString(16).padStart(4, '0')}${FRAGMENT_CONSTANTS.SEPARATOR}${totalFragments.toString(16).padStart(4, '0')}${FRAGMENT_CONSTANTS.SEPARATOR}${data}${FRAGMENT_CONSTANTS.HEADER_SUFFIX}`;
    } else {
      // OTR v2 format: ?OTR,fragment_num,total_fragments,fragment_data,
      return `${FRAGMENT_CONSTANTS.HEADER_PREFIX}${fragmentNum}${FRAGMENT_CONSTANTS.SEPARATOR}${totalFragments}${FRAGMENT_CONSTANTS.SEPARATOR}${data}${FRAGMENT_CONSTANTS.HEADER_SUFFIX}`;
    }
  }

  /**
   * Process incoming fragment and attempt reassembly
   */
  processFragment(fragmentMessage) {
    try {
      // Parse fragment
      const fragmentInfo = this.parseFragment(fragmentMessage);
      
      if (!fragmentInfo) {
        throw new Error('Invalid fragment format');
      }
      
      // Get or create fragment buffer
      const bufferKey = `${fragmentInfo.instanceTag}_${fragmentInfo.totalFragments}`;
      let buffer = this.fragmentBuffers.get(bufferKey);
      
      if (!buffer) {
        buffer = new FragmentBuffer(fragmentInfo.instanceTag, fragmentInfo.totalFragments);
        this.fragmentBuffers.set(bufferKey, buffer);
      }
      
      // Add fragment to buffer
      buffer.addFragment(fragmentInfo.fragmentNum, fragmentInfo.data);
      
      // Check if reassembly is possible
      if (buffer.isComplete()) {
        const completeMessage = buffer.reassemble();
        
        // Clean up the buffer
        this.fragmentBuffers.delete(bufferKey);
        
        return {
          complete: true,
          message: completeMessage,
          instanceTag: fragmentInfo.instanceTag
        };
      }
      
      // Reassembly not yet complete
      return {
        complete: false,
        fragmentsReceived: buffer.fragments.size,
        totalFragments: buffer.totalFragments,
        instanceTag: fragmentInfo.instanceTag
      };
      
    } catch (error) {
      throw new Error(`Fragment processing failed: ${error.message}`);
    }
  }

  /**
   * Parse fragment message (based on libOTR fragment parsing)
   */
  parseFragment(fragmentMessage) {
    if (!fragmentMessage || !fragmentMessage.startsWith(FRAGMENT_CONSTANTS.HEADER_PREFIX)) {
      return null;
    }
    
    // Remove prefix and suffix
    let content = fragmentMessage.substring(FRAGMENT_CONSTANTS.HEADER_PREFIX.length);
    if (content.endsWith(FRAGMENT_CONSTANTS.HEADER_SUFFIX)) {
      content = content.substring(0, content.length - FRAGMENT_CONSTANTS.HEADER_SUFFIX.length);
    }
    
    const parts = content.split(FRAGMENT_CONSTANTS.SEPARATOR);
    
    const fragmentInfo = new FragmentInfo();
    
    if (parts.length === 4) {
      // OTR v3 format: instance_tag|fragment_num|total_fragments|fragment_data
      fragmentInfo.instanceTag = parseInt(parts[0], 16);
      fragmentInfo.fragmentNum = parseInt(parts[1], 16);
      fragmentInfo.totalFragments = parseInt(parts[2], 16);
      fragmentInfo.data = parts[3];
    } else if (parts.length === 3) {
      // OTR v2 format: fragment_num|total_fragments|fragment_data
      fragmentInfo.instanceTag = 0; // No instance tag in v2
      fragmentInfo.fragmentNum = parseInt(parts[0]);
      fragmentInfo.totalFragments = parseInt(parts[1]);
      fragmentInfo.data = parts[2];
    } else {
      return null; // Invalid format
    }
    
    // Validate parsed values
    if (isNaN(fragmentInfo.fragmentNum) || 
        isNaN(fragmentInfo.totalFragments) ||
        fragmentInfo.fragmentNum < 1 ||
        fragmentInfo.totalFragments < 1 ||
        fragmentInfo.fragmentNum > fragmentInfo.totalFragments) {
      return null;
    }
    
    return fragmentInfo;
  }

  /**
   * Check if a message is a fragment
   */
  isFragment(message) {
    return message && message.startsWith(FRAGMENT_CONSTANTS.HEADER_PREFIX) &&
           message.includes(FRAGMENT_CONSTANTS.SEPARATOR);
  }

  /**
   * Generate a random instance tag
   */
  generateInstanceTag() {
    // Generate random 32-bit value >= 0x00000100
    return Math.floor(Math.random() * 0xFFFFFF00) + 0x00000100;
  }

  /**
   * Clean up expired fragment buffers
   */
  cleanupExpiredFragments() {
    const now = Date.now();
    const expiredKeys = [];
    
    for (const [key, buffer] of this.fragmentBuffers) {
      if (buffer.isExpired()) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => {
      this.fragmentBuffers.delete(key);
    });
    
    if (expiredKeys.length > 0) {
      console.log(`Cleaned up ${expiredKeys.length} expired fragment buffers`);
    }
  }

  /**
   * Get fragmentation statistics
   */
  getStatistics() {
    const stats = {
      activeBuffers: this.fragmentBuffers.size,
      buffers: []
    };
    
    for (const [key, buffer] of this.fragmentBuffers) {
      stats.buffers.push({
        key,
        instanceTag: buffer.instanceTag,
        totalFragments: buffer.totalFragments,
        receivedFragments: buffer.fragments.size,
        complete: buffer.isComplete(),
        age: Date.now() - buffer.createdTime,
        expired: buffer.isExpired()
      });
    }
    
    return stats;
  }

  /**
   * Clear all fragment buffers
   */
  clearAllBuffers() {
    this.fragmentBuffers.clear();
  }

  /**
   * Cleanup resources
   */
  destroy() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.clearAllBuffers();
  }
}

module.exports = {
  MessageFragmentation,
  FragmentInfo,
  FragmentBuffer,
  FRAGMENT_CONSTANTS
};
