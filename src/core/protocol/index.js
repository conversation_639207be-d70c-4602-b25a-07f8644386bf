/**
 * OTR protocol module
 * Comprehensive exports for all OTR protocol functionality
 */

// Import core state and protocol components
import { OtrState, STATE, MESSAGE_TYPE, PROTOCOL_VERSION } from './state';
import { SM<PERSON><PERSON>and<PERSON>, SMP_MESSAGE_TYPE, SMP_RESULT } from './smp';
import { MESSAGE_PREFIX } from './constants';

// Import message handling functions
import {
  parseMessage,
  createQueryMessage,
  createDataMessage,
  createErrorMessage,
  encryptMessage,
  decryptMessage
} from './message';

// Import AKE functions
import {
  createDHCommit,
  createDHKey,
  createRevealSignature,
  createSignature,
  processDHCommit,
  processDHKey,
  processRevealSignature,
  processSignature,
  startAKE
} from './ake';

// Export all functionality
export {
  // Core state and protocol
  OtrState,
  STATE,
  MESSAGE_TYPE,
  PROTOCOL_VERSION,
  MESSAGE_PREFIX,

  // SMP functionality
  SMPHandler,
  SMP_MESSAGE_TYPE,
  SMP_RESULT,

  // Message handling
  parseMessage,
  createQueryMessage,
  createDataMessage,
  createErrorMessage,
  encryptMessage,
  decryptMessage,

  // AKE functionality
  createDHCommit,
  createDHKey,
  createRevealSignature,
  createSignature,
  processDHCommit,
  processDHKey,
  processRevealSignature,
  processSignature,
  startAKE
};
