/**
 * OTR protocol module
 * Comprehensive exports for all OTR protocol functionality
 */

// Import core state and protocol components
import { OtrState, STATE, MESSAGE_TYPE, PROTOCOL_VERSION } from './state';
import { SM<PERSON>Hand<PERSON>, SMP_STATE, SMP_RESULT, SMP_MESSAGE_TYPE, SMPState } from './smp';
import { MESSAGE_PREFIX } from './constants';

// Import message handling functions
import {
  parseMessage,
  createQueryMessage,
  createDataMessage,
  createErrorMessage,
  encryptMessage,
  decryptMessage
} from './message';

// Import AKE functions
import {
  createDHCommit,
  createDHKey,
  createRevealSignature,
  createSignature,
  processDHCommit,
  processDHKey,
  processRevealSignature,
  processSignature,
  startAKE
} from './ake';

// Export all functionality
export {
  // Core state and protocol
  OtrState,
  STATE,
  MESSAGE_TYPE,
  PROTOCOL_VERSION,
  MESSAGE_PREFIX,

  // SMP functionality
  SMPHandler,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as SMP,
  SMP_STATE,
  SMP_RESULT,
  SMP_MESSAGE_TYPE,
  SMPState,

  // Message handling
  parseMessage,
  createQueryMessage,
  createDataMessage,
  createErrorMessage,
  encryptMessage,
  decryptMessage,

  // AKE functionality
  createDHCommit,
  createDHKey,
  createRevealSignature,
  createSignature,
  processDHCommit,
  processDHKey,
  processRevealSignature,
  processSignature,
  startAKE
};
