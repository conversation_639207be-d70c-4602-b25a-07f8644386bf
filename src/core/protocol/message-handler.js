/**
 * Message Handler Implementation
 * 
 * Based on libOTR's message.c implementation patterns.
 * Handles OTR message processing, encryption, and protocol state management.
 */

const crypto = require('crypto');
// Note: OTRMessageBridge will be imported when needed
// const { OTRMessageBridge } = require('../../tests/reference/libotr-compat/message-bridge');

/**
 * Message States (based on libOTR's OtrlMessageState)
 */
const MESSAGE_STATES = {
  PLAINTEXT: 0,
  ENCRYPTED: 1,
  FINISHED: 2
};

/**
 * Message Types (based on libOTR's OtrlMessageType)
 */
const MESSAGE_TYPES = {
  NOTOTR: 0,
  TAGGEDPLAINTEXT: 1,
  QUERY: 2,
  DH_COMMIT: 3,
  DH_KEY: 4,
  REVEAL_SIGNATURE: 5,
  SIGNATURE: 6,
  V1_KEYEXCH: 7,
  DATA: 8,
  ERROR: 9,
  UNKNOWN: 10
};

/**
 * Error Codes (based on libOTR's OtrlErrorCode)
 */
const ERROR_CODES = {
  NONE: 0,
  ENCRYPTION_ERROR: 1,
  MSG_NOT_IN_PRIVATE: 2,
  MSG_UNREADABLE: 3,
  MSG_MALFORMED: 4
};

/**
 * Message Events (based on libOTR's OtrlMessageEvent)
 */
const MESSAGE_EVENTS = {
  NONE: 0,
  ENCRYPTION_REQUIRED: 1,
  ENCRYPTION_ERROR: 2,
  CONNECTION_ENDED: 3,
  SETUP_ERROR: 4,
  MSG_REFLECTED: 5,
  MSG_RESENT: 6,
  RCVDMSG_NOT_IN_PRIVATE: 7,
  RCVDMSG_UNREADABLE: 8,
  RCVDMSG_MALFORMED: 9,
  RCVDMSG_GENERAL_ERR: 10,
  RCVDMSG_UNENCRYPTED: 11,
  RCVDMSG_UNRECOGNIZED: 12,
  RCVDMSG_FOR_OTHER_INSTANCE: 13
};

/**
 * OTR Constants
 */
const OTR_CONSTANTS = {
  ERROR_PREFIX: '?OTR Error: ',
  MESSAGE_TAG_BASE: '\x20\x09\x20\x20\x09\x09\x09\x09\x20\x09\x20\x09\x20\x09\x20\x20',
  MESSAGE_TAG_V1: '\x20\x09\x20\x09\x20\x20\x09\x20',
  MESSAGE_TAG_V2: '\x20\x20\x09\x09\x20\x20\x09\x20',
  MESSAGE_TAG_V3: '\x20\x20\x09\x09\x20\x20\x09\x09',
  HEARTBEAT_INTERVAL: 60,
  RESEND_INTERVAL: 60,
  MAX_AKE_WAIT_TIME: 60
};

/**
 * Fragment Policies (based on libOTR's OtrlFragmentPolicy)
 */
const FRAGMENT_POLICIES = {
  SEND_SKIP: 0,
  SEND_ALL: 1,
  SEND_ALL_BUT_FIRST: 2,
  SEND_ALL_BUT_LAST: 3
};

/**
 * Message Handler - Implements OTR message processing
 * Based on libOTR's otrl_message_sending and otrl_message_receiving functions
 */
class MessageHandler {
  constructor(context, ops = {}) {
    this.context = context;
    this.ops = ops; // Message operations (callbacks)
    // this.messageBridge = new OTRMessageBridge(); // Will be initialized when needed
    
    // Message processing state
    this.lastMessage = null;
    this.lastSent = null;
    this.mayRetransmit = 0;
    
    // Fragment handling
    this.fragmentBuffer = new Map();
    this.maxMessageSize = 1400; // Default max message size
  }

  /**
   * Handle outgoing message (based on otrl_message_sending)
   * Processes messages about to be sent to the network
   */
  async sendMessage(originalMessage, policy = {}, tlvs = null) {
    try {
      // Validate inputs
      if (!originalMessage) {
        throw new Error('Message is required');
      }

      // Check if this is an OTR query message
      if (this.isOTRQuery(originalMessage)) {
        return await this.handleOTRQuery(originalMessage, policy);
      }

      // Handle based on current message state
      switch (this.context.messageState) {
        case MESSAGE_STATES.PLAINTEXT:
          return await this.handlePlaintextSending(originalMessage, policy);
          
        case MESSAGE_STATES.ENCRYPTED:
          return await this.handleEncryptedSending(originalMessage, tlvs);
          
        case MESSAGE_STATES.FINISHED:
          return await this.handleFinishedSending(originalMessage);
          
        default:
          throw new Error(`Unknown message state: ${this.context.messageState}`);
      }
      
    } catch (error) {
      throw new Error(`Message sending failed: ${error.message}`);
    }
  }

  /**
   * Handle incoming message (based on otrl_message_receiving)
   * Processes messages received from the network
   */
  async receiveMessage(message, sender) {
    try {
      // Determine message type
      const messageType = this.getMessageType(message);
      
      // Handle based on message type
      switch (messageType) {
        case MESSAGE_TYPES.NOTOTR:
          return await this.handlePlaintextReceiving(message, sender);
          
        case MESSAGE_TYPES.QUERY:
          return await this.handleQueryReceiving(message, sender);
          
        case MESSAGE_TYPES.DH_COMMIT:
        case MESSAGE_TYPES.DH_KEY:
        case MESSAGE_TYPES.REVEAL_SIGNATURE:
        case MESSAGE_TYPES.SIGNATURE:
          return await this.handleAKEMessage(message, messageType, sender);
          
        case MESSAGE_TYPES.DATA:
          return await this.handleDataMessage(message, sender);
          
        case MESSAGE_TYPES.ERROR:
          return await this.handleErrorMessage(message, sender);
          
        case MESSAGE_TYPES.TAGGEDPLAINTEXT:
          return await this.handleTaggedPlaintext(message, sender);
          
        default:
          return await this.handleUnknownMessage(message, sender);
      }
      
    } catch (error) {
      throw new Error(`Message receiving failed: ${error.message}`);
    }
  }

  /**
   * Handle plaintext message sending
   */
  async handlePlaintextSending(message, policy) {
    // Check if encryption is required
    if (policy.requireEncryption) {
      // Store message for potential retransmission
      this.lastMessage = message;
      this.lastSent = Date.now();
      this.mayRetransmit = 2;
      
      // Emit encryption required event
      if (this.ops.handleMessageEvent) {
        this.ops.handleMessageEvent(MESSAGE_EVENTS.ENCRYPTION_REQUIRED, this.context, null);
      }
      
      // Return OTR query to initiate encryption
      return this.createOTRQuery(policy);
    }
    
    // Check if we should add whitespace tags
    if (policy.sendWhitespaceTag && this.context.otrOffer !== 'REJECTED') {
      return this.addWhitespaceTags(message, policy);
    }
    
    // Send as plaintext
    return { message, encrypted: false };
  }

  /**
   * Handle encrypted message sending
   */
  async handleEncryptedSending(message, tlvs) {
    try {
      // Convert message if needed
      let processedMessage = message;
      if (this.ops.convertMessage) {
        processedMessage = await this.ops.convertMessage(message, 'SENDING');
      }
      
      // Create encrypted data message
      const encryptedMessage = await this.createDataMessage(processedMessage, tlvs);
      
      // Update last sent time
      this.lastSent = Date.now();
      
      return { message: encryptedMessage, encrypted: true };
      
    } catch (error) {
      // Emit encryption error event
      if (this.ops.handleMessageEvent) {
        this.ops.handleMessageEvent(MESSAGE_EVENTS.ENCRYPTION_ERROR, this.context, null);
      }
      
      // Return error message
      return {
        message: this.createErrorMessage(ERROR_CODES.ENCRYPTION_ERROR),
        encrypted: false,
        error: true
      };
    }
  }

  /**
   * Handle finished state message sending
   */
  async handleFinishedSending(message) {
    // Emit connection ended event
    if (this.ops.handleMessageEvent) {
      this.ops.handleMessageEvent(MESSAGE_EVENTS.CONNECTION_ENDED, this.context, null);
    }
    
    // Return empty message
    return { message: '', encrypted: false };
  }

  /**
   * Create OTR data message (encrypted)
   */
  async createDataMessage(plaintext, tlvs = null) {
    // This is a placeholder implementation
    // In a real implementation, this would:
    // 1. Increment message counter
    // 2. Encrypt with AES-CTR using session keys
    // 3. Compute MAC
    // 4. Format as OTR data message
    
    const messageData = {
      flags: 0x00,
      senderKeyId: this.context.ourKeyId || 1,
      recipientKeyId: this.context.theirKeyId || 1,
      dhY: Buffer.alloc(192), // Placeholder DH public key
      ctr: this.getNextCounter(),
      encryptedMessage: this.encryptMessage(plaintext),
      mac: this.computeMAC(plaintext),
      oldMacKeys: Buffer.alloc(0)
    };
    
    // For now, return a placeholder OTR data message
    // In a full implementation, this would use the message bridge
    return '?OTR:' + Buffer.from(JSON.stringify(messageData)).toString('base64') + '.';
  }

  /**
   * Get message type from message content
   */
  getMessageType(message) {
    if (!message || typeof message !== 'string') {
      return MESSAGE_TYPES.NOTOTR;
    }
    
    // Check for OTR messages
    if (message.startsWith('?OTR:')) {
      // For now, assume it's a DATA message if it's a valid OTR message
      // In a full implementation, we would parse the message to determine the exact type
      return MESSAGE_TYPES.DATA;
    }
    
    // Check for OTR query
    if (message.includes('?OTR')) {
      return MESSAGE_TYPES.QUERY;
    }
    
    // Check for OTR error
    if (message.startsWith(OTR_CONSTANTS.ERROR_PREFIX)) {
      return MESSAGE_TYPES.ERROR;
    }
    
    // Check for whitespace tags
    if (this.hasWhitespaceTags(message)) {
      return MESSAGE_TYPES.TAGGEDPLAINTEXT;
    }
    
    return MESSAGE_TYPES.NOTOTR;
  }

  /**
   * Check if message is an OTR query
   */
  isOTRQuery(message) {
    return message.includes('?OTR') && !message.startsWith('?OTR:');
  }

  /**
   * Check if message has whitespace tags
   */
  hasWhitespaceTags(message) {
    return message.includes(OTR_CONSTANTS.MESSAGE_TAG_BASE) ||
           message.includes(OTR_CONSTANTS.MESSAGE_TAG_V1) ||
           message.includes(OTR_CONSTANTS.MESSAGE_TAG_V2) ||
           message.includes(OTR_CONSTANTS.MESSAGE_TAG_V3);
  }

  /**
   * Add whitespace tags to message
   */
  addWhitespaceTags(message, policy) {
    let taggedMessage = message + OTR_CONSTANTS.MESSAGE_TAG_BASE;
    
    if (policy.allowV1) {
      taggedMessage += OTR_CONSTANTS.MESSAGE_TAG_V1;
    }
    if (policy.allowV2) {
      taggedMessage += OTR_CONSTANTS.MESSAGE_TAG_V2;
    }
    if (policy.allowV3) {
      taggedMessage += OTR_CONSTANTS.MESSAGE_TAG_V3;
    }
    
    this.context.otrOffer = 'SENT';
    return { message: taggedMessage, encrypted: false };
  }

  /**
   * Create OTR query message
   */
  createOTRQuery(policy) {
    let query = '?OTR';
    
    // Add version information
    if (policy.allowV3) query += 'v3';
    if (policy.allowV2) query += 'v2';
    if (policy.allowV1) query += 'v1';
    
    query += '?';
    
    this.context.otrOffer = 'SENT';
    return { message: query, encrypted: false };
  }

  /**
   * Create error message
   */
  createErrorMessage(errorCode) {
    const errorMessages = {
      [ERROR_CODES.ENCRYPTION_ERROR]: 'Encryption error occurred',
      [ERROR_CODES.MSG_NOT_IN_PRIVATE]: 'Message not in private',
      [ERROR_CODES.MSG_UNREADABLE]: 'Message unreadable',
      [ERROR_CODES.MSG_MALFORMED]: 'Message malformed'
    };
    
    const errorText = errorMessages[errorCode] || 'Unknown error';
    return OTR_CONSTANTS.ERROR_PREFIX + errorText;
  }

  // Placeholder methods (to be implemented)
  async handleOTRQuery(message, policy) {
    return { message: 'OTR query handling not yet implemented', encrypted: false };
  }

  async handlePlaintextReceiving(message, sender) {
    return { message, encrypted: false, internal: false };
  }

  async handleQueryReceiving(message, sender) {
    return { message: null, encrypted: false, internal: true };
  }

  async handleAKEMessage(message, messageType, sender) {
    return { message: null, encrypted: false, internal: true };
  }

  async handleDataMessage(message, sender) {
    return { message: 'Decrypted message placeholder', encrypted: true, internal: false };
  }

  async handleErrorMessage(message, sender) {
    return { message: null, encrypted: false, internal: true };
  }

  async handleTaggedPlaintext(message, sender) {
    // Remove whitespace tags and return plaintext
    let cleanMessage = message;
    cleanMessage = cleanMessage.replace(OTR_CONSTANTS.MESSAGE_TAG_BASE, '');
    cleanMessage = cleanMessage.replace(OTR_CONSTANTS.MESSAGE_TAG_V1, '');
    cleanMessage = cleanMessage.replace(OTR_CONSTANTS.MESSAGE_TAG_V2, '');
    cleanMessage = cleanMessage.replace(OTR_CONSTANTS.MESSAGE_TAG_V3, '');
    
    return { message: cleanMessage, encrypted: false, internal: false };
  }

  async handleUnknownMessage(message, sender) {
    if (this.ops.handleMessageEvent) {
      this.ops.handleMessageEvent(MESSAGE_EVENTS.RCVDMSG_UNRECOGNIZED, this.context, message);
    }
    return { message: null, encrypted: false, internal: true };
  }

  // Utility methods (placeholders)
  getNextCounter() {
    return Buffer.alloc(8, 0);
  }

  encryptMessage(plaintext) {
    // Placeholder encryption
    return Buffer.from(plaintext, 'utf8');
  }

  computeMAC(data) {
    // Placeholder MAC computation
    return Buffer.alloc(20, 0);
  }
}

module.exports = {
  MessageHandler,
  MESSAGE_STATES,
  MESSAGE_TYPES,
  ERROR_CODES,
  MESSAGE_EVENTS,
  FRAGMENT_POLICIES,
  OTR_CONSTANTS
};
