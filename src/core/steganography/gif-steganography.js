/**
 * GIF Steganography Implementation
 * Advanced steganography support for animated GIF files
 */

import { SteganographyOTR, STEGO_MESSAGE_TYPE } from './index.js';

/**
 * GIF-specific steganography constants
 */
export const GIF_STEGO_CONSTANTS = {
  SIGNATURE: 'GIF87a',
  SIGNATURE_89: 'GIF89a',
  FRAME_SEPARATOR: 0x21,
  IMAGE_SEPARATOR: 0x2C,
  TRAILER: 0x3B,
  EXTENSION_INTRODUCER: 0x21,
  GRAPHIC_CONTROL_LABEL: 0xF9,
  COMMENT_LABEL: 0xFE,
  APPLICATION_EXTENSION_LABEL: 0xFF
};

/**
 * GIF Frame structure for steganography
 */
class GIFFrame {
  constructor(imageData, delay = 100, disposal = 0) {
    this.imageData = imageData;
    this.delay = delay; // Frame delay in centiseconds
    this.disposal = disposal; // Disposal method
    this.localColorTable = null;
    this.transparent = false;
    this.transparentIndex = 0;
  }
}

/**
 * GIF Steganography Engine
 * Extends base steganography with GIF-specific functionality
 */
export class GIFSteganography extends SteganographyOTR {
  constructor(options = {}) {
    super(options);
    
    // GIF-specific options
    this.gifOptions = {
      frameDistribution: options.frameDistribution || 'even', // 'even', 'first', 'last', 'random'
      preserveAnimation: options.preserveAnimation !== false,
      maxFramesUsed: options.maxFramesUsed || -1, // -1 for all frames
      frameSelectionSeed: options.frameSelectionSeed || null,
      ...options
    };
  }

  /**
   * Hide message in GIF animation
   * @param {Object} gifData - GIF data with frames
   * @param {string} message - Message to hide
   * @param {Object} options - Steganography options
   * @returns {Promise<Object>} Stego GIF data
   */
  async hideMessageInGIF(gifData, message, options = {}) {
    try {
      if (!gifData || !gifData.frames || gifData.frames.length === 0) {
        throw new Error('Invalid GIF data: no frames found');
      }

      // Prepare message with metadata
      const messageData = this._prepareMessageData(message, {
        type: STEGO_MESSAGE_TYPE.OTR_MESSAGE,
        gifFrameCount: gifData.frames.length,
        ...options
      });

      // Calculate message distribution across frames
      const frameDistribution = this._calculateFrameDistribution(
        gifData.frames.length,
        messageData.length,
        this.gifOptions.frameDistribution
      );

      // Create stego frames
      const stegoFrames = await this._hideInFrames(
        gifData.frames,
        messageData,
        frameDistribution
      );

      // Preserve GIF metadata
      const stegoGIF = {
        ...gifData,
        frames: stegoFrames,
        _stegoMetadata: {
          originalFrameCount: gifData.frames.length,
          messageLength: messageData.length,
          frameDistribution: frameDistribution,
          timestamp: Date.now()
        }
      };

      return stegoGIF;

    } catch (error) {
      throw new Error(`GIF steganography encoding failed: ${error.message}`);
    }
  }

  /**
   * Reveal message from GIF animation
   * @param {Object} stegoGIF - Stego GIF data
   * @param {Object} options - Extraction options
   * @returns {Promise<string>} Hidden message
   */
  async revealMessageFromGIF(stegoGIF, options = {}) {
    try {
      if (!stegoGIF || !stegoGIF.frames || stegoGIF.frames.length === 0) {
        throw new Error('Invalid stego GIF: no frames found');
      }

      // Detect message distribution pattern
      const frameDistribution = this._detectFrameDistribution(stegoGIF);

      // Extract message data from frames
      const extractedData = await this._extractFromFrames(
        stegoGIF.frames,
        frameDistribution
      );

      if (!extractedData) {
        return null;
      }

      // Parse and validate message
      const message = this._parseMessageData(extractedData);
      return message;

    } catch (error) {
      console.debug('GIF steganography decoding failed:', error.message);
      return null;
    }
  }

  /**
   * Detect if GIF contains hidden message
   * @param {Object} gifData - GIF data to analyze
   * @returns {Promise<boolean>} True if hidden message detected
   */
  async detectMessageInGIF(gifData) {
    try {
      if (!gifData || !gifData.frames || gifData.frames.length === 0) {
        return false;
      }

      // Check for steganography signatures in frames
      for (let i = 0; i < Math.min(gifData.frames.length, 3); i++) {
        const frame = gifData.frames[i];
        if (await this.detectMessage(frame.imageData)) {
          return true;
        }
      }

      return false;

    } catch (error) {
      return false;
    }
  }

  /**
   * Calculate GIF capacity for message hiding
   * @param {Object} gifData - GIF data
   * @returns {Promise<number>} Capacity in bytes
   */
  async calculateGIFCapacity(gifData) {
    if (!gifData || !gifData.frames || gifData.frames.length === 0) {
      return 0;
    }

    let totalCapacity = 0;

    // Calculate capacity for each frame
    for (const frame of gifData.frames) {
      const frameCapacity = await this.calculateCapacity(frame.imageData);
      totalCapacity += frameCapacity;
    }

    // Account for distribution overhead
    const distributionOverhead = gifData.frames.length * 4; // 4 bytes per frame for metadata
    return Math.max(0, totalCapacity - distributionOverhead);
  }

  /**
   * Generate animated cover GIF
   * @param {number} width - GIF width
   * @param {number} height - GIF height
   * @param {number} frameCount - Number of frames
   * @param {string} style - Animation style
   * @returns {Promise<Object>} Generated GIF data
   */
  async generateAnimatedCover(width, height, frameCount = 10, style = 'noise') {
    const frames = [];

    for (let i = 0; i < frameCount; i++) {
      const frameImageData = await this._generateAnimatedFrame(
        width, height, i, frameCount, style
      );
      
      const frame = new GIFFrame(frameImageData, 100); // 100ms delay
      frames.push(frame);
    }

    return {
      width,
      height,
      frames,
      globalColorTable: null,
      backgroundColorIndex: 0,
      pixelAspectRatio: 0,
      loopCount: 0, // Infinite loop
      _generated: true,
      _style: style
    };
  }

  // Private methods

  /**
   * Calculate frame distribution for message hiding
   * @param {number} frameCount - Total number of frames
   * @param {number} messageLength - Message length in bytes
   * @param {string} strategy - Distribution strategy
   * @returns {Array} Frame distribution plan
   * @private
   */
  _calculateFrameDistribution(frameCount, messageLength, strategy) {
    const distribution = [];

    switch (strategy) {
      case 'first':
        // Use only first frame
        distribution.push({
          frameIndex: 0,
          startByte: 0,
          endByte: messageLength
        });
        break;

      case 'last':
        // Use only last frame
        distribution.push({
          frameIndex: frameCount - 1,
          startByte: 0,
          endByte: messageLength
        });
        break;

      case 'random':
        // Randomly distribute across frames
        const selectedFrames = this._selectRandomFrames(frameCount, messageLength);
        let currentByte = 0;
        
        for (const frameIndex of selectedFrames) {
          const chunkSize = Math.min(
            Math.ceil(messageLength / selectedFrames.length),
            messageLength - currentByte
          );
          
          distribution.push({
            frameIndex,
            startByte: currentByte,
            endByte: currentByte + chunkSize
          });
          
          currentByte += chunkSize;
          if (currentByte >= messageLength) break;
        }
        break;

      case 'even':
      default:
        // Evenly distribute across all frames
        const bytesPerFrame = Math.ceil(messageLength / frameCount);
        
        for (let i = 0; i < frameCount; i++) {
          const startByte = i * bytesPerFrame;
          const endByte = Math.min(startByte + bytesPerFrame, messageLength);
          
          if (startByte < messageLength) {
            distribution.push({
              frameIndex: i,
              startByte,
              endByte
            });
          }
        }
        break;
    }

    return distribution;
  }

  /**
   * Hide message data in GIF frames
   * @param {Array} frames - GIF frames
   * @param {string} messageData - Message data to hide
   * @param {Array} distribution - Frame distribution plan
   * @returns {Promise<Array>} Stego frames
   * @private
   */
  async _hideInFrames(frames, messageData, distribution) {
    const stegoFrames = [...frames]; // Copy frames

    for (const chunk of distribution) {
      const frame = frames[chunk.frameIndex];
      const chunkData = messageData.substring(chunk.startByte, chunk.endByte);

      if (chunkData.length > 0) {
        // Hide chunk in this frame
        const stegoImageData = await this.hideMessage(frame.imageData, chunkData);
        
        // Create new frame with stego data
        stegoFrames[chunk.frameIndex] = new GIFFrame(
          stegoImageData,
          frame.delay,
          frame.disposal
        );
      }
    }

    return stegoFrames;
  }

  /**
   * Extract message data from GIF frames
   * @param {Array} frames - Stego GIF frames
   * @param {Array} distribution - Frame distribution plan
   * @returns {Promise<string>} Extracted message data
   * @private
   */
  async _extractFromFrames(frames, distribution) {
    const messageChunks = [];

    for (const chunk of distribution) {
      if (chunk.frameIndex < frames.length) {
        const frame = frames[chunk.frameIndex];
        const extractedChunk = await this.revealMessage(frame.imageData);
        
        if (extractedChunk) {
          messageChunks.push({
            startByte: chunk.startByte,
            data: extractedChunk
          });
        }
      }
    }

    // Sort chunks by start byte and concatenate
    messageChunks.sort((a, b) => a.startByte - b.startByte);
    return messageChunks.map(chunk => chunk.data).join('');
  }

  /**
   * Detect frame distribution pattern in stego GIF
   * @param {Object} stegoGIF - Stego GIF data
   * @returns {Array} Detected distribution pattern
   * @private
   */
  _detectFrameDistribution(stegoGIF) {
    // Try to detect distribution from metadata
    if (stegoGIF._stegoMetadata && stegoGIF._stegoMetadata.frameDistribution) {
      return stegoGIF._stegoMetadata.frameDistribution;
    }

    // Fallback: assume even distribution across all frames
    const frameCount = stegoGIF.frames.length;
    const distribution = [];

    for (let i = 0; i < frameCount; i++) {
      distribution.push({
        frameIndex: i,
        startByte: i * 100, // Estimate
        endByte: (i + 1) * 100
      });
    }

    return distribution;
  }

  /**
   * Select random frames for message distribution
   * @param {number} frameCount - Total frame count
   * @param {number} messageLength - Message length
   * @returns {Array} Selected frame indices
   * @private
   */
  _selectRandomFrames(frameCount, messageLength) {
    const framesNeeded = Math.min(frameCount, Math.ceil(messageLength / 100));
    const selectedFrames = [];

    // Use seed for reproducible randomness if provided
    let random = this.gifOptions.frameSelectionSeed ? 
      this._seededRandom(this.gifOptions.frameSelectionSeed) : Math.random;

    while (selectedFrames.length < framesNeeded) {
      const frameIndex = Math.floor(random() * frameCount);
      if (!selectedFrames.includes(frameIndex)) {
        selectedFrames.push(frameIndex);
      }
    }

    return selectedFrames.sort((a, b) => a - b);
  }

  /**
   * Generate animated frame for cover GIF
   * @param {number} width - Frame width
   * @param {number} height - Frame height
   * @param {number} frameIndex - Current frame index
   * @param {number} totalFrames - Total frame count
   * @param {string} style - Animation style
   * @returns {Promise<ImageData>} Generated frame
   * @private
   */
  async _generateAnimatedFrame(width, height, frameIndex, totalFrames, style) {
    const data = new Uint8ClampedArray(width * height * 4);
    const progress = frameIndex / totalFrames;

    switch (style) {
      case 'wave':
        // Animated wave pattern
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const i = (y * width + x) * 4;
            const wave = Math.sin((x * 0.02) + (progress * Math.PI * 2)) * 127 + 128;
            data[i] = wave;
            data[i + 1] = wave * 0.8;
            data[i + 2] = wave * 0.6;
            data[i + 3] = 255;
          }
        }
        break;

      case 'spiral':
        // Animated spiral pattern
        const centerX = width / 2;
        const centerY = height / 2;
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const i = (y * width + x) * 4;
            const dx = x - centerX;
            const dy = y - centerY;
            const angle = Math.atan2(dy, dx) + (progress * Math.PI * 2);
            const distance = Math.sqrt(dx * dx + dy * dy);
            const spiral = Math.sin(angle + distance * 0.1) * 127 + 128;
            data[i] = spiral;
            data[i + 1] = spiral * 0.7;
            data[i + 2] = spiral * 0.9;
            data[i + 3] = 255;
          }
        }
        break;

      case 'noise':
      default:
        // Animated noise pattern
        for (let i = 0; i < data.length; i += 4) {
          const noise = (Math.sin(i * 0.001 + progress * Math.PI * 2) + 1) * 127.5;
          data[i] = noise + Math.random() * 50 - 25;
          data[i + 1] = noise + Math.random() * 50 - 25;
          data[i + 2] = noise + Math.random() * 50 - 25;
          data[i + 3] = 255;
        }
        break;
    }

    return { data, width, height };
  }

  /**
   * Seeded random number generator
   * @param {number} seed - Random seed
   * @returns {Function} Random function
   * @private
   */
  _seededRandom(seed) {
    let state = seed;
    return function() {
      state = (state * 1664525 + 1013904223) % 4294967296;
      return state / 4294967296;
    };
  }
}

/**
 * GIF Steganography utilities
 */
export const GIFStegoUtils = {
  /**
   * Convert ImageData frames to GIF data structure
   * @param {Array} imageDataFrames - Array of ImageData objects
   * @param {Object} options - Conversion options
   * @returns {Object} GIF data structure
   */
  imageDataToGIF(imageDataFrames, options = {}) {
    const frames = imageDataFrames.map((imageData, index) => 
      new GIFFrame(imageData, options.delay || 100)
    );

    return {
      width: imageDataFrames[0].width,
      height: imageDataFrames[0].height,
      frames,
      globalColorTable: null,
      backgroundColorIndex: 0,
      pixelAspectRatio: 0,
      loopCount: options.loopCount || 0
    };
  },

  /**
   * Extract ImageData frames from GIF data structure
   * @param {Object} gifData - GIF data structure
   * @returns {Array} Array of ImageData objects
   */
  gifToImageData(gifData) {
    return gifData.frames.map(frame => frame.imageData);
  },

  /**
   * Validate GIF data structure
   * @param {Object} gifData - GIF data to validate
   * @returns {boolean} True if valid
   */
  validateGIFData(gifData) {
    if (!gifData || typeof gifData !== 'object') {
      return false;
    }

    if (!Array.isArray(gifData.frames) || gifData.frames.length === 0) {
      return false;
    }

    // Validate each frame
    for (const frame of gifData.frames) {
      if (!frame.imageData || !frame.imageData.data || !frame.imageData.width || !frame.imageData.height) {
        return false;
      }
    }

    return true;
  }
};
