/**
 * WebOTR Steganography Module
 * Implements OTR message hiding in images using steganography
 */

import { encode, decode } from '@masknet/stego-js';

// Steganography constants
export const STEGO_CONSTANTS = {
  MAGIC_HEADER: 'WOTR', // WebOTR magic header
  VERSION: 1,
  MAX_MESSAGE_SIZE: 1024 * 1024, // 1MB max message size
  MIN_IMAGE_SIZE: 512 * 512, // Minimum image size for steganography
  QUALITY_THRESHOLD: 0.95 // Minimum image quality after embedding
};

// Message types for steganography
export const STEGO_MESSAGE_TYPE = {
  OTR_MESSAGE: 1,
  AKE_MESSAGE: 2,
  SMP_MESSAGE: 3,
  HEARTBEAT: 4
};

// Error types
export const STEGO_ERROR = {
  INVALID_IMAGE: 'Invalid image format or size',
  MESSAGE_TOO_LARGE: 'Message too large for image capacity',
  EXTRACTION_FAILED: 'Failed to extract message from image',
  INVALID_FORMAT: 'Invalid steganography message format',
  QUALITY_TOO_LOW: 'Image quality degradation too high'
};

/**
 * Main steganography class for WebOTR
 */
export class SteganographyOTR {
  constructor(options = {}) {
    this.options = {
      quality: options.quality || 0.95,
      password: options.password || null,
      compression: options.compression !== false,
      ...options
    };
  }

  /**
   * Hide an OTR message in an image
   * @param {ImageData|HTMLImageElement|string} coverImage - Cover image
   * @param {string} otrMessage - OTR message to hide
   * @param {Object} options - Hiding options
   * @returns {Promise<ImageData>} Stego image with hidden message
   */
  async hideMessage(coverImage, otrMessage, options = {}) {
    try {
      // Validate inputs
      this._validateImage(coverImage);
      this._validateMessage(otrMessage);

      // Create steganography message format
      const stegoMessage = this._createStegoMessage(otrMessage, options);
      
      // Convert message to binary
      const messageData = this._serializeMessage(stegoMessage);
      
      // Check capacity
      await this._checkCapacity(coverImage, messageData);
      
      // Embed message using stego-js
      const stegoImage = await encode(coverImage, messageData, {
        password: this.options.password,
        ...options
      });
      
      // Validate quality
      await this._validateQuality(coverImage, stegoImage);
      
      return stegoImage;
      
    } catch (error) {
      throw new Error(`Steganography hiding failed: ${error.message}`);
    }
  }

  /**
   * Extract an OTR message from a stego image
   * @param {ImageData|HTMLImageElement|string} stegoImage - Stego image
   * @param {Object} options - Extraction options
   * @returns {Promise<string|null>} Extracted OTR message or null
   */
  async revealMessage(stegoImage, options = {}) {
    try {
      // Validate image
      this._validateImage(stegoImage);
      
      // Extract data using stego-js
      const extractedData = await decode(stegoImage, {
        password: this.options.password,
        ...options
      });
      
      if (!extractedData) {
        return null;
      }
      
      // Deserialize and validate message
      const stegoMessage = this._deserializeMessage(extractedData);
      
      if (!stegoMessage || !this._validateStegoMessage(stegoMessage)) {
        return null;
      }
      
      return stegoMessage.payload;
      
    } catch (error) {
      // Don't throw on extraction failure - just return null
      console.debug('Steganography extraction failed:', error.message);
      return null;
    }
  }

  /**
   * Check if an image contains hidden data
   * @param {ImageData|HTMLImageElement|string} image - Image to check
   * @returns {Promise<boolean>} True if image contains hidden data
   */
  async detectMessage(image) {
    try {
      const message = await this.revealMessage(image);
      return message !== null;
    } catch (error) {
      return false;
    }
  }

  /**
   * Calculate hiding capacity of an image
   * @param {ImageData|HTMLImageElement|string} image - Image to analyze
   * @returns {Promise<number>} Capacity in bytes
   */
  async calculateCapacity(image) {
    try {
      this._validateImage(image);
      
      // Get image dimensions
      const { width, height } = await this._getImageDimensions(image);
      
      // Estimate capacity (conservative estimate)
      // Stego-js uses frequency domain, so capacity varies
      const pixels = width * height;
      const estimatedCapacity = Math.floor(pixels / 8); // ~1 bit per 8 pixels
      
      return Math.min(estimatedCapacity, STEGO_CONSTANTS.MAX_MESSAGE_SIZE);
      
    } catch (error) {
      throw new Error(`Capacity calculation failed: ${error.message}`);
    }
  }

  /**
   * Generate a suitable cover image for steganography
   * @param {number} width - Image width
   * @param {number} height - Image height
   * @param {string} style - Image style ('noise', 'gradient', 'pattern')
   * @returns {Promise<ImageData>} Generated cover image
   */
  async generateCover(width = 1024, height = 1024, style = 'noise') {
    try {
      // For testing, create a simple mock image
      if (typeof document === 'undefined') {
        // Node.js environment - return mock data
        return {
          data: new Uint8ClampedArray(width * height * 4),
          width: width,
          height: height
        };
      }
      
      // Browser environment
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      
      // Generate image based on style
      const imageData = ctx.createImageData(width, height);
      const data = imageData.data;
      
      switch (style) {
        case 'noise':
          this._generateNoise(data);
          break;
        case 'gradient':
          this._generateGradient(data, width, height);
          break;
        case 'pattern':
          this._generatePattern(data, width, height);
          break;
        default:
          this._generateNoise(data);
      }
      
      return imageData;
      
    } catch (error) {
      throw new Error(`Cover generation failed: ${error.message}`);
    }
  }

  // Private methods

  /**
   * Validate image input
   * @param {*} image - Image to validate
   * @private
   */
  _validateImage(image) {
    if (!image) {
      throw new Error(STEGO_ERROR.INVALID_IMAGE);
    }
  }

  /**
   * Validate message input
   * @param {string} message - Message to validate
   * @private
   */
  _validateMessage(message) {
    if (!message || typeof message !== 'string') {
      throw new Error('Invalid message: must be a non-empty string');
    }
    
    if (message.length > STEGO_CONSTANTS.MAX_MESSAGE_SIZE) {
      throw new Error(STEGO_ERROR.MESSAGE_TOO_LARGE);
    }
  }

  /**
   * Create steganography message format
   * @param {string} otrMessage - OTR message
   * @param {Object} options - Message options
   * @returns {Object} Formatted stego message
   * @private
   */
  _createStegoMessage(otrMessage, options = {}) {
    return {
      header: STEGO_CONSTANTS.MAGIC_HEADER,
      version: STEGO_CONSTANTS.VERSION,
      type: options.type || STEGO_MESSAGE_TYPE.OTR_MESSAGE,
      timestamp: Date.now(),
      length: otrMessage.length,
      checksum: this._calculateChecksum(otrMessage),
      payload: otrMessage
    };
  }

  /**
   * Serialize message for embedding
   * @param {Object} stegoMessage - Stego message object
   * @returns {string} Serialized message
   * @private
   */
  _serializeMessage(stegoMessage) {
    try {
      const serialized = JSON.stringify(stegoMessage);
      
      if (this.options.compression) {
        // Simple compression could be added here
        return serialized;
      }
      
      return serialized;
    } catch (error) {
      throw new Error(`Message serialization failed: ${error.message}`);
    }
  }

  /**
   * Deserialize extracted message
   * @param {string} data - Extracted data
   * @returns {Object|null} Deserialized stego message
   * @private
   */
  _deserializeMessage(data) {
    try {
      if (typeof data !== 'string') {
        return null;
      }
      
      const stegoMessage = JSON.parse(data);
      return stegoMessage;
    } catch (error) {
      return null;
    }
  }

  /**
   * Validate steganography message format
   * @param {Object} stegoMessage - Message to validate
   * @returns {boolean} True if valid
   * @private
   */
  _validateStegoMessage(stegoMessage) {
    if (!stegoMessage || typeof stegoMessage !== 'object') {
      return false;
    }
    
    // Check required fields
    const required = ['header', 'version', 'type', 'payload'];
    for (const field of required) {
      if (!(field in stegoMessage)) {
        return false;
      }
    }
    
    // Validate header
    if (stegoMessage.header !== STEGO_CONSTANTS.MAGIC_HEADER) {
      return false;
    }
    
    // Validate version
    if (stegoMessage.version !== STEGO_CONSTANTS.VERSION) {
      return false;
    }
    
    // Validate checksum if present
    if (stegoMessage.checksum) {
      const calculatedChecksum = this._calculateChecksum(stegoMessage.payload);
      if (calculatedChecksum !== stegoMessage.checksum) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Calculate simple checksum for message integrity
   * @param {string} message - Message to checksum
   * @returns {number} Checksum value
   * @private
   */
  _calculateChecksum(message) {
    let checksum = 0;
    for (let i = 0; i < message.length; i++) {
      checksum = ((checksum << 5) - checksum + message.charCodeAt(i)) & 0xffffffff;
    }
    return checksum;
  }

  /**
   * Check if message fits in image capacity
   * @param {*} image - Cover image
   * @param {string} messageData - Serialized message
   * @returns {Promise<void>}
   * @private
   */
  async _checkCapacity(image, messageData) {
    const capacity = await this.calculateCapacity(image);
    const messageSize = new Blob([messageData]).size;
    
    if (messageSize > capacity) {
      throw new Error(STEGO_ERROR.MESSAGE_TOO_LARGE);
    }
  }

  /**
   * Validate image quality after embedding
   * @param {*} originalImage - Original image
   * @param {*} stegoImage - Stego image
   * @returns {Promise<void>}
   * @private
   */
  async _validateQuality(originalImage, stegoImage) {
    // Quality validation could be implemented here
    // For now, we trust stego-js to maintain quality
    return true;
  }

  /**
   * Get image dimensions
   * @param {*} image - Image to analyze
   * @returns {Promise<Object>} Width and height
   * @private
   */
  async _getImageDimensions(image) {
    // This would need to be implemented based on image type
    // For now, return default dimensions
    return { width: 1024, height: 1024 };
  }

  /**
   * Generate noise pattern
   * @param {Uint8ClampedArray} data - Image data array
   * @private
   */
  _generateNoise(data) {
    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.random() * 255;     // Red
      data[i + 1] = Math.random() * 255; // Green
      data[i + 2] = Math.random() * 255; // Blue
      data[i + 3] = 255;                 // Alpha
    }
  }

  /**
   * Generate gradient pattern
   * @param {Uint8ClampedArray} data - Image data array
   * @param {number} width - Image width
   * @param {number} height - Image height
   * @private
   */
  _generateGradient(data, width, height) {
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const i = (y * width + x) * 4;
        const intensity = (x + y) / (width + height) * 255;
        data[i] = intensity;     // Red
        data[i + 1] = intensity; // Green
        data[i + 2] = intensity; // Blue
        data[i + 3] = 255;       // Alpha
      }
    }
  }

  /**
   * Generate pattern
   * @param {Uint8ClampedArray} data - Image data array
   * @param {number} width - Image width
   * @param {number} height - Image height
   * @private
   */
  _generatePattern(data, width, height) {
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const i = (y * width + x) * 4;
        const pattern = ((x ^ y) % 256);
        data[i] = pattern;       // Red
        data[i + 1] = pattern;   // Green
        data[i + 2] = pattern;   // Blue
        data[i + 3] = 255;       // Alpha
      }
    }
  }
}
