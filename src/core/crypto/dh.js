/**
 * Di<PERSON><PERSON><PERSON><PERSON><PERSON> key exchange for WebOTR
 */
import { random } from './random';
import { BigInteger } from 'jsbn';

// OTR uses the 1536-bit MODP Group from RFC 3526
const MODP_GROUP = {
  // Prime modulus (1536-bit MODP Group prime from RFC 3526)
  P: new Uint8Array([
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xC9, 0x0F, 0xDA, 0xA2, 
    0x21, 0x68, 0xC2, 0x34, 0xC4, 0xC6, 0x62, 0x8B, 0x80, 0xDC, 0x1C, 0xD1,
    0x29, 0x02, 0x4E, 0x08, 0x8A, 0x67, 0xCC, 0x74, 0x02, 0x0B, 0xBE, 0xA6, 
    0x3B, 0x13, 0x9B, 0x22, 0x51, 0x4A, 0x08, 0x79, 0x8E, 0x34, 0x04, 0xDD,
    0xEF, 0x95, 0x19, 0xB3, 0xCD, 0x3A, 0x43, 0x1B, 0x30, 0x2B, 0x0A, 0x6D, 
    0xF2, 0x5F, 0x14, 0x37, 0x4F, 0xE1, 0x35, 0x6D, 0x6D, 0x51, 0xC2, 0x45,
    0xE4, 0x85, 0xB5, 0x76, 0x62, 0x5E, 0x7E, 0xC6, 0xF4, 0x4C, 0x42, 0xE9, 
    0xA6, 0x37, 0xED, 0x6B, 0x0B, 0xFF, 0x5C, 0xB6, 0xF4, 0x06, 0xB7, 0xED,
    0xEE, 0x38, 0x6B, 0xFB, 0x5A, 0x89, 0x9F, 0xA5, 0xAE, 0x9F, 0x24, 0x11, 
    0x7C, 0x4B, 0x1F, 0xE6, 0x49, 0x28, 0x66, 0x51, 0xEC, 0xE4, 0x5B, 0x3D,
    0xC2, 0x00, 0x7C, 0xB8, 0xA1, 0x63, 0xBF, 0x05, 0x98, 0xDA, 0x48, 0x36, 
    0x1C, 0x55, 0xD3, 0x9A, 0x69, 0x16, 0x3F, 0xA8, 0xFD, 0x24, 0xCF, 0x5F,
    0x83, 0x65, 0x5D, 0x23, 0xDC, 0xA3, 0xAD, 0x96, 0x1C, 0x62, 0xF3, 0x56, 
    0x20, 0x85, 0x52, 0xBB, 0x9E, 0xD5, 0x29, 0x07, 0x70, 0x96, 0x96, 0x6D,
    0x67, 0x0C, 0x35, 0x4E, 0x4A, 0xBC, 0x98, 0x04, 0xF1, 0x74, 0x6C, 0x08, 
    0xCA, 0x23, 0x73, 0x27, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
  ]),
  
  // Generator
  G: new Uint8Array([0x02]) // Generator is 2
};

// Convert byte array to BigInteger
function bytesToBigInt(bytes) {
  let hex = Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join('');
  return new BigInteger(hex, 16);
}

// Convert BigInteger to byte array
function bigIntToBytes(bigInt, length) {
  const hex = bigInt.toString(16).padStart(length * 2, '0');
  const bytes = new Uint8Array(length);
  for (let i = 0; i < length; i++) {
    bytes[i] = parseInt(hex.substring(i * 2, i * 2 + 2), 16);
  }
  return bytes;
}



/**
 * Generate a Diffie-Hellman key pair
 * @returns {Promise<Object>} Key pair with public and private components
 */
export async function generateDHKeyPair() {
  // Use Web Crypto API if available
  if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
    try {
      // Generate DH key pair
      const keyPair = await window.crypto.subtle.generateKey(
        {
          name: 'DH',
          prime: MODP_GROUP.P,
          generator: MODP_GROUP.G
        },
        true,
        ['deriveKey', 'deriveBits']
      );
      
      // Export keys
      const privateKey = await window.crypto.subtle.exportKey('pkcs8', keyPair.privateKey);
      const publicKey = await window.crypto.subtle.exportKey('spki', keyPair.publicKey);
      
      return {
        privateKey: new Uint8Array(privateKey),
        publicKey: new Uint8Array(publicKey)
      };
    } catch (error) {
      // Fall back to pure JS implementation if Web Crypto fails
      console.warn('Web Crypto API failed for DH, falling back to pure JS implementation', error);
      return generateDHKeyPairFallback();
    }
  } else {
    // Use pure JS implementation if Web Crypto is not available
    return generateDHKeyPairFallback();
  }
}

/**
 * Perform Diffie-Hellman key exchange
 * @param {Uint8Array} privateKey - Our private key
 * @param {Uint8Array} publicKey - Their public key
 * @returns {Promise<Uint8Array>} Shared secret
 */
export async function dhExchange(privateKey, publicKey) {
  // Use Web Crypto API if available
  if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
    try {
      // Import keys
      const privateKeyObj = await window.crypto.subtle.importKey(
        'pkcs8',
        privateKey,
        {
          name: 'DH',
          prime: MODP_GROUP.P,
          generator: MODP_GROUP.G
        },
        false,
        ['deriveBits']
      );
      
      const publicKeyObj = await window.crypto.subtle.importKey(
        'spki',
        publicKey,
        {
          name: 'DH',
          prime: MODP_GROUP.P,
          generator: MODP_GROUP.G
        },
        false,
        []
      );
      
      // Derive shared secret
      const sharedSecret = await window.crypto.subtle.deriveBits(
        {
          name: 'DH',
          public: publicKeyObj
        },
        privateKeyObj,
        256
      );
      
      return new Uint8Array(sharedSecret);
    } catch (error) {
      // Fall back to pure JS implementation if Web Crypto fails
      console.warn('Web Crypto API failed for DH exchange, falling back to pure JS implementation', error);
      return dhExchangeFallback(privateKey, publicKey);
    }
  } else {
    // Use pure JS implementation if Web Crypto is not available
    return dhExchangeFallback(privateKey, publicKey);
  }
}

/**
 * Pure JS implementation of DH key pair generation
 * @returns {Object} Key pair with public and private components
 */
function generateDHKeyPairFallback() {
  // Generate a random private key (a)
  const privateKeyBytes = random(192); // 1536 bits = 192 bytes
  const a = bytesToBigInt(privateKeyBytes);
  
  // Calculate public key g^a mod p
  const g = bytesToBigInt(MODP_GROUP.G);
  const p = bytesToBigInt(MODP_GROUP.P);
  const publicKey = g.modPow(a, p);
  
  // Convert to byte arrays
  const publicKeyBytes = bigIntToBytes(publicKey, 192);
  
  return {
    privateKey: privateKeyBytes,
    publicKey: publicKeyBytes
  };
}

/**
 * Pure JS implementation of DH key exchange
 * @param {Uint8Array} privateKey - Our private key
 * @param {Uint8Array} publicKey - Their public key
 * @returns {Uint8Array} Shared secret
 */
function dhExchangeFallback(privateKey, publicKey) {
  // Convert byte arrays to BigInteger
  const a = bytesToBigInt(privateKey);
  const B = bytesToBigInt(publicKey);
  const p = bytesToBigInt(MODP_GROUP.P);
  
  // Calculate shared secret B^a mod p
  const sharedSecret = B.modPow(a, p);
  
  // Convert to byte array (use 32 bytes for 256-bit AES key)
  return bigIntToBytes(sharedSecret, 32);
} 