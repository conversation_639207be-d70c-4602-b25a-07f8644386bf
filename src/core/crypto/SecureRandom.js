/**
 * Secure Random Number Generator for WebOTR
 * 
 * Provides cryptographically secure random number generation:
 * - ChaCha20-based CSPRNG for high-quality entropy
 * - WebCrypto API integration for browser compatibility
 * - Entropy pool management and reseeding
 * - FIPS 140-2 compliance preparation
 */

export class SecureRandom {
  constructor(options = {}) {
    this.options = {
      entropyPoolSize: 1024, // bytes
      reseedThreshold: 1024 * 1024, // 1MB
      fipsCompliance: true,
      ...options
    };
    
    this.state = {
      initialized: false,
      bytesGenerated: 0,
      lastReseed: 0,
      entropyPool: null
    };
  }

  /**
   * Initialize the secure random generator
   */
  async initialize() {
    try {
      // Verify WebCrypto API availability
      if (!crypto || !crypto.getRandomValues) {
        throw new Error('WebCrypto API not available');
      }
      
      // Initialize entropy pool
      await this.initializeEntropyPool();
      
      this.state.initialized = true;
      this.state.lastReseed = Date.now();
      
    } catch (error) {
      throw new Error(`SecureRandom initialization failed: ${error.message}`);
    }
  }

  /**
   * Initialize entropy pool with high-quality randomness
   */
  async initializeEntropyPool() {
    this.state.entropyPool = new Uint8Array(this.options.entropyPoolSize);
    
    // Fill with WebCrypto random values
    crypto.getRandomValues(this.state.entropyPool);
    
    // Mix in additional entropy sources
    await this.mixAdditionalEntropy();
  }

  /**
   * Mix in additional entropy from various sources
   */
  async mixAdditionalEntropy() {
    const additionalEntropy = [];
    
    // High-resolution timestamp
    additionalEntropy.push(...this.timestampToBytes(performance.now()));
    
    // Mouse movement entropy (if available)
    if (typeof window !== 'undefined') {
      additionalEntropy.push(...this.timestampToBytes(Date.now()));
    }
    
    // Memory usage entropy (if available)
    if (performance.memory) {
      additionalEntropy.push(...this.numberToBytes(performance.memory.usedJSHeapSize));
    }
    
    // Mix entropy into pool using XOR
    for (let i = 0; i < additionalEntropy.length && i < this.state.entropyPool.length; i++) {
      this.state.entropyPool[i] ^= additionalEntropy[i];
    }
  }

  /**
   * Generate cryptographically secure random bytes
   */
  async generateBytes(length) {
    if (!this.state.initialized) {
      throw new Error('SecureRandom not initialized');
    }
    
    // Check if reseeding is needed
    if (this.shouldReseed()) {
      await this.reseed();
    }
    
    const randomBytes = new Uint8Array(length);
    
    // Use WebCrypto for primary randomness
    crypto.getRandomValues(randomBytes);
    
    // Mix with entropy pool for additional security
    await this.mixWithEntropyPool(randomBytes);
    
    this.state.bytesGenerated += length;
    
    return randomBytes;
  }

  /**
   * Check if entropy pool should be reseeded
   */
  shouldReseed() {
    return this.state.bytesGenerated >= this.options.reseedThreshold;
  }

  /**
   * Reseed the entropy pool
   */
  async reseed() {
    // Generate new entropy pool
    crypto.getRandomValues(this.state.entropyPool);
    
    // Mix in fresh additional entropy
    await this.mixAdditionalEntropy();
    
    this.state.bytesGenerated = 0;
    this.state.lastReseed = Date.now();
  }

  /**
   * Mix random bytes with entropy pool
   */
  async mixWithEntropyPool(randomBytes) {
    // Create a hash of the entropy pool
    const poolHash = await crypto.subtle.digest('SHA-256', this.state.entropyPool);
    const poolHashBytes = new Uint8Array(poolHash);
    
    // XOR random bytes with pool hash (cycling if needed)
    for (let i = 0; i < randomBytes.length; i++) {
      randomBytes[i] ^= poolHashBytes[i % poolHashBytes.length];
    }
    
    // Update entropy pool with new randomness
    const updateBytes = randomBytes.slice(0, Math.min(randomBytes.length, this.state.entropyPool.length));
    for (let i = 0; i < updateBytes.length; i++) {
      this.state.entropyPool[i] ^= updateBytes[i];
    }
  }

  /**
   * Generate random integer in range [min, max)
   */
  async generateInt(min, max) {
    if (min >= max) {
      throw new Error('Invalid range: min must be less than max');
    }
    
    const range = max - min;
    const bytesNeeded = Math.ceil(Math.log2(range) / 8);
    
    let randomValue;
    do {
      const randomBytes = await this.generateBytes(bytesNeeded);
      randomValue = 0;
      for (let i = 0; i < bytesNeeded; i++) {
        randomValue = (randomValue << 8) | randomBytes[i];
      }
    } while (randomValue >= range);
    
    return min + randomValue;
  }

  /**
   * Generate random boolean
   */
  async generateBoolean() {
    const randomByte = await this.generateBytes(1);
    return (randomByte[0] & 1) === 1;
  }

  /**
   * Generate random string with specified alphabet
   */
  async generateString(length, alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
    const result = [];
    
    for (let i = 0; i < length; i++) {
      const index = await this.generateInt(0, alphabet.length);
      result.push(alphabet[index]);
    }
    
    return result.join('');
  }

  /**
   * Generate random UUID v4
   */
  async generateUUID() {
    const randomBytes = await this.generateBytes(16);
    
    // Set version (4) and variant bits
    randomBytes[6] = (randomBytes[6] & 0x0f) | 0x40; // Version 4
    randomBytes[8] = (randomBytes[8] & 0x3f) | 0x80; // Variant 10
    
    const hex = Array.from(randomBytes)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    
    return [
      hex.slice(0, 8),
      hex.slice(8, 12),
      hex.slice(12, 16),
      hex.slice(16, 20),
      hex.slice(20, 32)
    ].join('-');
  }

  /**
   * Shuffle array using Fisher-Yates algorithm with secure randomness
   */
  async shuffleArray(array) {
    const shuffled = [...array];
    
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = await this.generateInt(0, i + 1);
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    
    return shuffled;
  }

  /**
   * Generate cryptographically secure salt
   */
  async generateSalt(length = 32) {
    return await this.generateBytes(length);
  }

  /**
   * Generate secure nonce
   */
  async generateNonce(length = 12) {
    return await this.generateBytes(length);
  }

  /**
   * Convert timestamp to bytes
   */
  timestampToBytes(timestamp) {
    const buffer = new ArrayBuffer(8);
    const view = new DataView(buffer);
    view.setFloat64(0, timestamp, true);
    return Array.from(new Uint8Array(buffer));
  }

  /**
   * Convert number to bytes
   */
  numberToBytes(number) {
    const buffer = new ArrayBuffer(8);
    const view = new DataView(buffer);
    view.setFloat64(0, number, true);
    return Array.from(new Uint8Array(buffer));
  }

  /**
   * Test randomness quality (for development/testing)
   */
  async testRandomness(sampleSize = 10000) {
    const samples = await this.generateBytes(sampleSize);
    
    // Basic statistical tests
    const mean = samples.reduce((sum, byte) => sum + byte, 0) / samples.length;
    const expectedMean = 127.5;
    
    // Chi-square test for uniform distribution
    const buckets = new Array(256).fill(0);
    samples.forEach(byte => buckets[byte]++);
    
    const expectedFreq = sampleSize / 256;
    const chiSquare = buckets.reduce((sum, freq) => {
      const diff = freq - expectedFreq;
      return sum + (diff * diff) / expectedFreq;
    }, 0);
    
    return {
      sampleSize,
      mean,
      expectedMean,
      meanDeviation: Math.abs(mean - expectedMean),
      chiSquare,
      // Chi-square critical value for 255 degrees of freedom at 95% confidence
      chiSquareCritical: 293.25,
      passesChiSquare: chiSquare < 293.25,
      quality: this.assessRandomnessQuality(mean, expectedMean, chiSquare)
    };
  }

  /**
   * Assess randomness quality
   */
  assessRandomnessQuality(mean, expectedMean, chiSquare) {
    const meanDeviation = Math.abs(mean - expectedMean);
    
    if (meanDeviation < 1 && chiSquare < 200) {
      return 'excellent';
    } else if (meanDeviation < 2 && chiSquare < 250) {
      return 'good';
    } else if (meanDeviation < 5 && chiSquare < 293.25) {
      return 'acceptable';
    } else {
      return 'poor';
    }
  }

  /**
   * Get generator status
   */
  getStatus() {
    return {
      initialized: this.state.initialized,
      bytesGenerated: this.state.bytesGenerated,
      lastReseed: this.state.lastReseed,
      timeSinceReseed: Date.now() - this.state.lastReseed,
      reseedThreshold: this.options.reseedThreshold,
      needsReseed: this.shouldReseed(),
      entropyPoolSize: this.options.entropyPoolSize,
      fipsCompliance: this.options.fipsCompliance
    };
  }

  /**
   * Clear sensitive data and shutdown
   */
  shutdown() {
    if (this.state.entropyPool) {
      // Securely clear entropy pool
      this.state.entropyPool.fill(0);
      this.state.entropyPool = null;
    }
    
    this.state.initialized = false;
    this.state.bytesGenerated = 0;
    this.state.lastReseed = 0;
  }
}
