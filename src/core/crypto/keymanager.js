/**
 * KeyManager - Key management implementation for WebOTR
 * 
 * This module provides key management functionality, including:
 * - Key generation
 * - Key rotation
 * - Key expiry
 * - Key persistence
 */
import { generateKeys } from './keys';
import KeyStore from './keystore';
import { generateInstanceTag } from './keys';

// Default expiry time: 30 days
const DEFAULT_EXPIRY_TIME = 30 * 24 * 60 * 60 * 1000;

class KeyManager {
  constructor() {
    this.keyStore = new KeyStore();
    this.initialized = false;
    this.activeKeyId = null;
    this.instanceTag = null;
    this.rotationTimer = null;
    this.expiryTime = DEFAULT_EXPIRY_TIME;
    this.autoRotate = true;
  }

  /**
   * Initialize the key manager
   * @param {Object} options - Configuration options
   * @param {string} options.passphrase - Optional passphrase for key encryption
   * @param {number} options.expiryTime - Time in ms before keys expire
   * @param {boolean} options.autoRotate - Whether to auto-rotate keys
   * @returns {Promise<void>}
   */
  async initialize(options = {}) {
    if (this.initialized) return;

    const { 
      passphrase = '', 
      expiryTime = DEFAULT_EXPIRY_TIME,
      autoRotate = true
    } = options;

    // Initialize key store
    await this.keyStore.initialize(passphrase);
    
    // Set options
    this.expiryTime = expiryTime;
    this.autoRotate = autoRotate;
    
    // Create or get instance tag
    await this._initializeInstanceTag();
    
    // Find active key or generate new one
    await this._initializeActiveKey();
    
    // Setup rotation timer if auto-rotation is enabled
    if (this.autoRotate) {
      this._setupRotationTimer();
    }
    
    this.initialized = true;
  }

  /**
   * Get the currently active key pair
   * @returns {Promise<Object>} The active key pair
   */
  async getActiveKeyPair() {
    if (!this.initialized) {
      throw new Error('KeyManager not initialized');
    }
    
    if (!this.activeKeyId) {
      throw new Error('No active key available');
    }
    
    return this.keyStore.getKeyPair(this.activeKeyId);
  }

  /**
   * Get all key pairs
   * @returns {Promise<Array>} Array of key pair metadata
   */
  async listKeyPairs() {
    if (!this.initialized) {
      throw new Error('KeyManager not initialized');
    }
    
    return this.keyStore.listKeyPairs();
  }

  /**
   * Get the instance tag
   * @returns {number} Instance tag
   */
  getInstanceTag() {
    return this.instanceTag;
  }

  /**
   * Rotate the active key
   * @returns {Promise<string>} ID of the new active key
   */
  async rotateKey() {
    if (!this.initialized) {
      throw new Error('KeyManager not initialized');
    }
    
    try {
      // Generate new key pair
      const keyPair = await generateKeys();
      const timestamp = Date.now();
      const keyId = `key-${timestamp}`;
      
      // Store new key pair
      await this.keyStore.storeKeyPair(keyId, keyPair, {
        active: true,
        created: timestamp,
        expires: timestamp + this.expiryTime
      });
      
      // Mark old key as inactive
      if (this.activeKeyId) {
        await this.keyStore.updateMetadata(this.activeKeyId, {
          active: false
        });
      }
      
      // Update active key
      this.activeKeyId = keyId;
      
      // Reset rotation timer
      if (this.autoRotate) {
        this._resetRotationTimer();
      }
      
      return keyId;
    } catch (error) {
      throw new Error(`Failed to rotate key: ${error.message}`);
    }
  }

  /**
   * Set the expiry time for keys
   * @param {number} expiryTime - Time in ms before keys expire
   */
  setExpiryTime(expiryTime) {
    this.expiryTime = expiryTime;
    
    // Update expiry time for active key
    if (this.initialized && this.activeKeyId) {
      const timestamp = Date.now();
      this.keyStore.updateMetadata(this.activeKeyId, {
        expires: timestamp + this.expiryTime
      });
      
      // Reset rotation timer
      if (this.autoRotate) {
        this._resetRotationTimer();
      }
    }
  }

  /**
   * Set whether to auto-rotate keys
   * @param {boolean} autoRotate - Whether to auto-rotate keys
   */
  setAutoRotate(autoRotate) {
    this.autoRotate = autoRotate;
    
    if (autoRotate) {
      this._setupRotationTimer();
    } else {
      this._clearRotationTimer();
    }
  }

  /**
   * Clean up expired keys
   * @returns {Promise<number>} Number of keys deleted
   */
  async cleanupExpiredKeys() {
    if (!this.initialized) {
      throw new Error('KeyManager not initialized');
    }
    
    const keys = await this.keyStore.listKeyPairs();
    const now = Date.now();
    let deletedCount = 0;
    
    for (const key of keys) {
      // Don't delete active key even if expired
      if (key.id === this.activeKeyId) {
        continue;
      }
      
      // Delete expired keys
      if (key.expires && key.expires < now) {
        await this.keyStore.deleteKeyPair(key.id);
        deletedCount++;
      }
    }
    
    return deletedCount;
  }

  /**
   * Close the key manager
   * @returns {Promise<void>}
   */
  async close() {
    if (this.initialized) {
      this._clearRotationTimer();
      await this.keyStore.close();
      this.initialized = false;
      this.activeKeyId = null;
    }
  }

  // Private methods

  /**
   * Initialize the instance tag
   * @returns {Promise<void>}
   * @private
   */
  async _initializeInstanceTag() {
    try {
      const tagData = await this.keyStore._getItem('META_STORE', 'instance-tag');
      if (tagData && tagData.tag) {
        this.instanceTag = tagData.tag;
      } else {
        this.instanceTag = generateInstanceTag();
        await this.keyStore._storeItem('META_STORE', {
          id: 'instance-tag',
          tag: this.instanceTag,
          created: Date.now()
        });
      }
    } catch (error) {
      // If there's an error, generate a new tag
      this.instanceTag = generateInstanceTag();
    }
  }

  /**
   * Initialize the active key
   * @returns {Promise<void>}
   * @private
   */
  async _initializeActiveKey() {
    try {
      const keys = await this.keyStore.listKeyPairs();
      const now = Date.now();
      
      // Find active, non-expired key
      const activeKey = keys.find(key => 
        key.active && (!key.expires || key.expires > now)
      );
      
      if (activeKey) {
        this.activeKeyId = activeKey.id;
        return;
      }
      
      // No active key, generate a new one
      await this.rotateKey();
    } catch (error) {
      // If there's an error, generate a new key
      await this.rotateKey();
    }
  }

  /**
   * Setup rotation timer
   * @private
   */
  _setupRotationTimer() {
    this._clearRotationTimer();
    
    // Set timer for key rotation
    this.rotationTimer = setTimeout(async () => {
      try {
        await this.rotateKey();
        // Clean up expired keys
        await this.cleanupExpiredKeys();
      } catch (error) {
        console.error('Key rotation failed:', error);
      }
    }, this.expiryTime);
  }

  /**
   * Reset rotation timer
   * @private
   */
  _resetRotationTimer() {
    this._clearRotationTimer();
    this._setupRotationTimer();
  }

  /**
   * Clear rotation timer
   * @private
   */
  _clearRotationTimer() {
    if (this.rotationTimer) {
      clearTimeout(this.rotationTimer);
      this.rotationTimer = null;
    }
  }
}

export default KeyManager; 