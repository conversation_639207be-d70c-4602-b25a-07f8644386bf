/**
 * AES encryption/decryption for WebOTR
 */
import CryptoJS from 'crypto-js';

/**
 * Convert Uint8Array to WordArray
 * @param {Uint8Array} arr - Uint8Array to convert
 * @returns {CryptoJS.lib.WordArray} WordArray
 */
function uint8ArrayToWordArray(arr) {
  const words = [];
  for (let i = 0; i < arr.length; i += 4) {
    words.push(
      (arr[i] << 24) |
      ((arr[i+1] || 0) << 16) |
      ((arr[i+2] || 0) << 8) |
      (arr[i+3] || 0)
    );
  }
  return CryptoJS.lib.WordArray.create(words, arr.length);
}

/**
 * Convert WordArray to Uint8Array
 * @param {CryptoJS.lib.WordArray} wordArray - WordArray to convert
 * @returns {Uint8Array} Uint8Array
 */
function wordArrayToUint8Array(wordArray) {
  const words = wordArray.words;
  const sigBytes = wordArray.sigBytes;
  const u8 = new Uint8Array(sigBytes);
  
  for (let i = 0; i < sigBytes; i++) {
    const byte = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
    u8[i] = byte;
  }
  
  return u8;
}

/**
 * Convert string to Uint8Array
 * @param {string} str - String to convert
 * @returns {Uint8Array} Uint8Array representation
 */
function stringToUint8Array(str) {
  const encoder = new TextEncoder();
  return encoder.encode(str);
}

/**
 * Convert Uint8Array to string
 * @param {Uint8Array} arr - Uint8Array to convert
 * @returns {string} String representation
 */
function uint8ArrayToString(arr) {
  const decoder = new TextDecoder();
  return decoder.decode(arr);
}

/**
 * Normalize input to Uint8Array
 * @param {string|Uint8Array} input - Input to normalize
 * @returns {Uint8Array} Normalized Uint8Array
 */
function normalizeInput(input) {
  if (typeof input === 'string') {
    return stringToUint8Array(input);
  }
  if (input instanceof Uint8Array) {
    return input;
  }
  throw new Error('Input must be string or Uint8Array');
}

/**
 * Normalize key to Uint8Array
 * @param {string|Uint8Array} key - Key to normalize
 * @returns {Uint8Array} Normalized key
 */
function normalizeKey(key) {
  if (typeof key === 'string') {
    // Pad or truncate key to 32 bytes (256 bits)
    const keyBytes = stringToUint8Array(key);
    const normalizedKey = new Uint8Array(32);
    for (let i = 0; i < 32; i++) {
      normalizedKey[i] = keyBytes[i % keyBytes.length] || 0;
    }
    return normalizedKey;
  }
  if (key instanceof Uint8Array) {
    return key;
  }
  throw new Error('Key must be string or Uint8Array');
}

/**
 * Generate random IV
 * @returns {Uint8Array} Random 16-byte IV
 */
function generateIV() {
  const iv = new Uint8Array(16);
  if (typeof window !== 'undefined' && window.crypto) {
    window.crypto.getRandomValues(iv);
  } else {
    // Fallback for Node.js environment
    for (let i = 0; i < iv.length; i++) {
      iv[i] = Math.floor(Math.random() * 256);
    }
  }
  return iv;
}

/**
 * Encrypt data using AES-CTR
 * @param {string|Uint8Array} data - Data to encrypt
 * @param {string|Uint8Array} key - Encryption key
 * @param {Uint8Array} [iv] - Initialization vector (generated if not provided)
 * @returns {Promise<string>} Base64-encoded encrypted data with IV prepended
 */
export async function encrypt(data, key, iv) {
  // Normalize inputs
  const normalizedData = normalizeInput(data);
  const normalizedKey = normalizeKey(key);
  const normalizedIV = iv || generateIV();

  // Use Web Crypto API if available
  if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
    try {
      const cryptoKey = await window.crypto.subtle.importKey(
        'raw',
        normalizedKey,
        { name: 'AES-CTR' },
        false,
        ['encrypt']
      );

      const encrypted = await window.crypto.subtle.encrypt(
        { name: 'AES-CTR', counter: normalizedIV, length: 128 },
        cryptoKey,
        normalizedData
      );

      // Prepend IV to encrypted data and encode as base64
      const result = new Uint8Array(normalizedIV.length + encrypted.byteLength);
      result.set(normalizedIV);
      result.set(new Uint8Array(encrypted), normalizedIV.length);

      return btoa(String.fromCharCode(...result));
    } catch (error) {
      // Fall back to pure JS implementation if Web Crypto fails
      console.warn('Web Crypto API failed, falling back to pure JS implementation', error);
      return encryptFallback(normalizedData, normalizedKey, normalizedIV);
    }
  } else {
    // Use pure JS implementation if Web Crypto is not available
    return encryptFallback(normalizedData, normalizedKey, normalizedIV);
  }
}

/**
 * Decrypt data using AES-CTR
 * @param {string} data - Base64-encoded encrypted data with IV prepended
 * @param {string|Uint8Array} key - Decryption key
 * @returns {Promise<string>} Decrypted plaintext
 */
export async function decrypt(data, key) {
  if (typeof data !== 'string') {
    throw new Error('Encrypted data must be a base64 string');
  }

  // Normalize key
  const normalizedKey = normalizeKey(key);

  // Decode base64 data
  const encryptedBytes = new Uint8Array(
    atob(data).split('').map(char => char.charCodeAt(0))
  );

  // Extract IV and encrypted data
  const iv = encryptedBytes.slice(0, 16);
  const encryptedData = encryptedBytes.slice(16);

  // Use Web Crypto API if available
  if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
    try {
      const cryptoKey = await window.crypto.subtle.importKey(
        'raw',
        normalizedKey,
        { name: 'AES-CTR' },
        false,
        ['decrypt']
      );

      const decrypted = await window.crypto.subtle.decrypt(
        { name: 'AES-CTR', counter: iv, length: 128 },
        cryptoKey,
        encryptedData
      );

      return new Uint8Array(decrypted);
    } catch (error) {
      // Fall back to pure JS implementation if Web Crypto fails
      console.warn('Web Crypto API failed, falling back to pure JS implementation', error);
      return decryptFallback(encryptedData, normalizedKey, iv);
    }
  } else {
    // Use pure JS implementation if Web Crypto is not available
    return decryptFallback(encryptedData, normalizedKey, iv);
  }
}

/**
 * Pure JS implementation of AES-CTR encryption
 * @param {Uint8Array} data - Data to encrypt
 * @param {Uint8Array} key - Encryption key
 * @param {Uint8Array} iv - Initialization vector
 * @returns {string} Base64-encoded encrypted data with IV prepended
 */
function encryptFallback(data, key, iv) {
  // Convert Uint8Array to WordArray
  const dataWords = uint8ArrayToWordArray(data);
  const keyWords = uint8ArrayToWordArray(key);
  const ivWords = uint8ArrayToWordArray(iv);

  // Encrypt using CryptoJS
  const encrypted = CryptoJS.AES.encrypt(
    dataWords,
    keyWords,
    {
      iv: ivWords,
      mode: CryptoJS.mode.CTR,
      padding: CryptoJS.pad.NoPadding
    }
  );

  // Convert result back to Uint8Array and prepend IV
  const encryptedBytes = wordArrayToUint8Array(encrypted.ciphertext);
  const result = new Uint8Array(iv.length + encryptedBytes.length);
  result.set(iv);
  result.set(encryptedBytes, iv.length);

  return btoa(String.fromCharCode(...result));
}

/**
 * Pure JS implementation of AES-CTR decryption
 * @param {Uint8Array} data - Data to decrypt
 * @param {Uint8Array} key - Decryption key
 * @param {Uint8Array} iv - Initialization vector
 * @returns {string} Decrypted plaintext
 */
function decryptFallback(data, key, iv) {
  // Convert Uint8Array to WordArray
  const dataWords = uint8ArrayToWordArray(data);
  const keyWords = uint8ArrayToWordArray(key);
  const ivWords = uint8ArrayToWordArray(iv);

  // Create CipherParams object
  const cipherParams = CryptoJS.lib.CipherParams.create({
    ciphertext: dataWords
  });

  // Decrypt using CryptoJS
  const decrypted = CryptoJS.AES.decrypt(
    cipherParams,
    keyWords,
    {
      iv: ivWords,
      mode: CryptoJS.mode.CTR,
      padding: CryptoJS.pad.NoPadding
    }
  );

  // Convert result back to Uint8Array
  return wordArrayToUint8Array(decrypted);
}