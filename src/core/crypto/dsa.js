/**
 * DSA signature generation and verification for WebOTR
 */
import { random } from './random';
import CryptoJS from 'crypto-js';

/**
 * Generate a DSA key pair
 * @returns {Promise<Object>} Key pair with public and private components
 */
export async function generateDSAKeyPair() {
  // Use Web Crypto API if available
  if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
    try {
      // Generate DSA key pair
      const keyPair = await window.crypto.subtle.generateKey(
        {
          name: 'ECDSA',
          namedCurve: 'P-256'
        },
        true,
        ['sign', 'verify']
      );
      
      // Export keys
      const privateKey = await window.crypto.subtle.exportKey('pkcs8', keyPair.privateKey);
      const publicKey = await window.crypto.subtle.exportKey('spki', keyPair.publicKey);
      
      return {
        privateKey: new Uint8Array(privateKey),
        publicKey: new Uint8Array(publicKey)
      };
    } catch (error) {
      // Fall back to pure JS implementation if Web Crypto fails
      console.warn('Web Crypto API failed for DSA, falling back to pure JS implementation', error);
      return generateDSAKeyPairFallback();
    }
  } else {
    // Use pure JS implementation if Web Crypto is not available
    return generateDSAKeyPairFallback();
  }
}

/**
 * Sign data using DSA
 * @param {Uint8Array} data - Data to sign
 * @param {Uint8Array} privateKey - Private key
 * @returns {Promise<Uint8Array>} Signature
 */
export async function sign(data, privateKey) {
  // Use Web Crypto API if available
  if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
    try {
      // Import private key
      const privateKeyObj = await window.crypto.subtle.importKey(
        'pkcs8',
        privateKey,
        {
          name: 'ECDSA',
          namedCurve: 'P-256'
        },
        false,
        ['sign']
      );
      
      // Sign data
      const signature = await window.crypto.subtle.sign(
        {
          name: 'ECDSA',
          hash: { name: 'SHA-256' }
        },
        privateKeyObj,
        data
      );
      
      return new Uint8Array(signature);
    } catch (error) {
      // Fall back to pure JS implementation if Web Crypto fails
      console.warn('Web Crypto API failed for DSA signing, falling back to pure JS implementation', error);
      return signFallback(data, privateKey);
    }
  } else {
    // Use pure JS implementation if Web Crypto is not available
    return signFallback(data, privateKey);
  }
}

/**
 * Verify a DSA signature
 * @param {Uint8Array} data - Signed data
 * @param {Uint8Array} signature - Signature to verify
 * @param {Uint8Array} publicKey - Public key
 * @returns {Promise<boolean>} True if signature is valid
 */
export async function verify(data, signature, publicKey) {
  // Use Web Crypto API if available
  if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
    try {
      // Import public key
      const publicKeyObj = await window.crypto.subtle.importKey(
        'spki',
        publicKey,
        {
          name: 'ECDSA',
          namedCurve: 'P-256'
        },
        false,
        ['verify']
      );
      
      // Verify signature
      return await window.crypto.subtle.verify(
        {
          name: 'ECDSA',
          hash: { name: 'SHA-256' }
        },
        publicKeyObj,
        signature,
        data
      );
    } catch (error) {
      // Fall back to pure JS implementation if Web Crypto fails
      console.warn('Web Crypto API failed for DSA verification, falling back to pure JS implementation', error);
      return verifyFallback(data, signature, publicKey);
    }
  } else {
    // Use pure JS implementation if Web Crypto is not available
    return verifyFallback(data, signature, publicKey);
  }
}

/**
 * Pure JS implementation of DSA key pair generation
 * @returns {Object} Key pair with public and private components
 */
function generateDSAKeyPairFallback() {
  // For the fallback implementation, we'll use a simplified approach
  // In a production environment, this should be replaced with a proper DSA implementation
  
  // Generate a random private key (32 bytes)
  const privateKey = random(32);
  
  // For the public key, we'll use a hash of the private key
  // This is NOT a proper DSA implementation, just a placeholder
  const privateKeyWords = uint8ArrayToWordArray(privateKey);
  const publicKeyWords = CryptoJS.SHA256(privateKeyWords);
  const publicKey = wordArrayToUint8Array(publicKeyWords);
  
  return {
    privateKey,
    publicKey
  };
}

/**
 * Pure JS implementation of DSA signing
 * @param {Uint8Array} data - Data to sign
 * @param {Uint8Array} privateKey - Private key
 * @returns {Uint8Array} Signature
 */
function signFallback(data, privateKey) {
  // For the fallback implementation, we'll use HMAC-SHA256 as a simplified signature
  // In a production environment, this should be replaced with a proper DSA implementation
  
  // Convert data and private key to WordArray
  const dataWords = uint8ArrayToWordArray(data);
  const keyWords = uint8ArrayToWordArray(privateKey);
  
  // Create HMAC
  const hmac = CryptoJS.HmacSHA256(dataWords, keyWords);
  
  // Convert back to Uint8Array
  return wordArrayToUint8Array(hmac);
}

/**
 * Pure JS implementation of DSA verification
 * @param {Uint8Array} data - Signed data
 * @param {Uint8Array} signature - Signature to verify
 * @param {Uint8Array} publicKey - Public key
 * @returns {boolean} True if signature is valid
 */
function verifyFallback(data, signature, publicKey) {
  // For the fallback implementation, we'll verify the HMAC-SHA256
  // In a production environment, this should be replaced with a proper DSA implementation
  
  // Since our simplified implementation uses the hash of the private key as the public key,
  // we can't actually verify the signature with just the public key.
  // In a real DSA implementation, this would be possible.
  
  // For now, we'll just compare the signature with a hash of the data and public key
  // This is NOT secure and is just a placeholder
  
  // Convert data and public key to WordArray
  const dataWords = uint8ArrayToWordArray(data);
  const keyWords = uint8ArrayToWordArray(publicKey);
  
  // Create HMAC using the public key (this is not how DSA works!)
  const expectedHmac = CryptoJS.HmacSHA256(dataWords, keyWords);
  const expectedSignature = wordArrayToUint8Array(expectedHmac);
  
  // Compare signatures
  if (signature.length !== expectedSignature.length) {
    return false;
  }
  
  // Constant-time comparison to prevent timing attacks
  let result = 0;
  for (let i = 0; i < signature.length; i++) {
    result |= signature[i] ^ expectedSignature[i];
  }
  
  return result === 0;
}

/**
 * Convert Uint8Array to WordArray
 * @param {Uint8Array} arr - Uint8Array to convert
 * @returns {CryptoJS.lib.WordArray} WordArray
 */
function uint8ArrayToWordArray(arr) {
  const words = [];
  for (let i = 0; i < arr.length; i += 4) {
    words.push(
      (arr[i] << 24) |
      ((arr[i+1] || 0) << 16) |
      ((arr[i+2] || 0) << 8) |
      (arr[i+3] || 0)
    );
  }
  return CryptoJS.lib.WordArray.create(words, arr.length);
}

/**
 * Convert WordArray to Uint8Array
 * @param {CryptoJS.lib.WordArray} wordArray - WordArray to convert
 * @returns {Uint8Array} Uint8Array
 */
function wordArrayToUint8Array(wordArray) {
  const words = wordArray.words;
  const sigBytes = wordArray.sigBytes;
  const u8 = new Uint8Array(sigBytes);
  
  for (let i = 0; i < sigBytes; i++) {
    const byte = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
    u8[i] = byte;
  }
  
  return u8;
} 