/**
 * Cryptographic functions for the Socialist Millionaire Protocol
 */
import { BigInteger } from 'jsbn';
import CryptoJS from 'crypto-js';

// OTR uses the 1536-bit MODP Group from RFC 3526
export const MODP_GROUP = {
  P: new BigInteger('FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD129024E088A67CC74020BBEA63B139B22514A08798E3404DDEF9519B3CD3A431B302B0A6DF25F14374FE1356D6D51C245E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7EDEE386BFB5A899FA5AE9F24117C4B1FE649286651ECE45B3DC2007CB8A163BF0598DA48361C55D39A69163FA8FD24CF5F83655D23DCA3AD961C62F356208552BB9ED529077096966D670C354E4ABC9804F1746C08CA18217C32905E462E36CE3BE39E772C180E86039B2783A2EC07A28FB5C55DF06F4C52C9DE2BCBF6955817183995497CEA956AE515D2261898FA051015728E5A8AACAA68FFFFFFFFFFFFFFFF', 16),
  G: new BigInteger('2'),
  Q: new BigInteger('7FFFFFFFFFFFFFFF44B09D00097AB5C52B46DE7AA6EE8A2F5C9D05C4B4E7832C5068DFA6C1A71484FE656DF1A57B86C63D6D8B79E1E414DD27D8E1AC1F10B0D6A1536E301A1EDE2C9FB4D4B5FA9FDA9D5B6DA27D2D2DB8A3F12DF5AD5E44B8F54D8E9C84B59F9A6E7F950A0E1F9C58E0B1F7B1B02414ED1E1F17DA3B31ED95AF47DC3', 16)
};

/**
 * Generate a random exponent for SMP
 * @returns {BigInteger} Random exponent
 */
export function generateRandomExponent() {
  const array = new Uint8Array(40); // 320 bits
  crypto.getRandomValues(array);
  
  let hex = '';
  for (let i = 0; i < array.length; i++) {
    hex += array[i].toString(16).padStart(2, '0');
  }
  
  return new BigInteger(hex, 16).mod(MODP_GROUP.Q);
}

/**
 * Compute g1 = g^x1 for SMP
 * @param {BigInteger} x - Exponent
 * @returns {BigInteger} g^x mod p
 */
export function computeG1(x) {
  return MODP_GROUP.G.modPow(x, MODP_GROUP.P);
}

/**
 * Compute g2 = (g2a or g2b)^x2 for SMP
 * @param {BigInteger} g2Base - Base value (g2a or g2b)
 * @param {BigInteger} x2 - Exponent
 * @returns {BigInteger} g2Base^x2 mod p
 */
export function computeG2(g2Base, x2) {
  return g2Base.modPow(x2, MODP_GROUP.P);
}

/**
 * Compute g3 = (g3a or g3b)^x3 for SMP
 * @param {BigInteger} g3Base - Base value (g3a or g3b)
 * @param {BigInteger} x3 - Exponent
 * @returns {BigInteger} g3Base^x3 mod p
 */
export function computeG3(g3Base, x3) {
  return g3Base.modPow(x3, MODP_GROUP.P);
}

/**
 * Create a zero-knowledge proof
 * @param {BigInteger} x - Value to prove knowledge of
 * @returns {Object} Zero-knowledge proof
 */
export function createZKP(x) {
  // Generate random r
  const r = generateRandomExponent();
  
  // Compute c = g^r
  const c = MODP_GROUP.G.modPow(r, MODP_GROUP.P);
  
  // Compute d = g^x
  const d = MODP_GROUP.G.modPow(x, MODP_GROUP.P);
  
  return { c, d, r };
}

/**
 * Verify a zero-knowledge proof
 * @param {Object} zkp - Zero-knowledge proof
 * @param {BigInteger} publicValue - Public value (g^x)
 * @returns {boolean} True if proof is valid
 */
export function verifyZKP(zkp, publicValue) {
  // Check that c = g^r
  const c = MODP_GROUP.G.modPow(zkp.r, MODP_GROUP.P);
  
  // Check that d = publicValue
  return c.equals(zkp.c) && publicValue.equals(zkp.d);
}

/**
 * Hash a value for use in SMP
 * @param {string} value - Value to hash
 * @returns {BigInteger} Hashed value
 */
export function hashForSMP(value) {
  // Create SHA-256 hash
  const hash = CryptoJS.SHA256(value);
  
  // Convert to BigInteger
  const hex = hash.toString(CryptoJS.enc.Hex);
  return new BigInteger(hex, 16).mod(MODP_GROUP.Q);
} 