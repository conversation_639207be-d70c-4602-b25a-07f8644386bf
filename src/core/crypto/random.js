/**
 * Secure random number generation for WebOTR
 */

/**
 * Generate cryptographically secure random values
 * @param {Uint8Array} array - Array to fill with random values
 * @returns {Uint8Array} Array filled with random values
 */
export function getRandomValues(array) {
  // Browser environment
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    return window.crypto.getRandomValues(array);
  }
  // Web Worker or newer Node.js with crypto.getRandomValues
  else if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    return crypto.getRandomValues(array);
  }
  // Node.js environment
  else if (typeof require !== 'undefined') {
    try {
      const nodeCrypto = require('crypto');
      const randomBytes = nodeCrypto.randomBytes(array.length);
      array.set(randomBytes);
      return array;
    } catch (error) {
      throw new Error('Node.js crypto module not available: ' + error.message);
    }
  }
  else {
    throw new Error('No secure random number generator available');
  }
}

/**
 * Generate a random byte array of specified length
 * @param {number} length - Length of the array to generate
 * @returns {Uint8Array} Random byte array
 */
export function random(length) {
  const array = new Uint8Array(length);
  return getRandomValues(array);
}

/**
 * Generate a random integer between min and max (inclusive)
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {number} Random integer
 */
export function randomInt(min, max) {
  const range = max - min + 1;
  const bytesNeeded = Math.ceil(Math.log2(range) / 8);
  const maxValue = Math.pow(256, bytesNeeded);
  const maxRange = maxValue - (maxValue % range);
  
  let randomValue;
  do {
    const randomBytes = random(bytesNeeded);
    randomValue = 0;
    for (let i = 0; i < bytesNeeded; i++) {
      randomValue = (randomValue << 8) + randomBytes[i];
    }
  } while (randomValue >= maxRange);
  
  return min + (randomValue % range);
} 