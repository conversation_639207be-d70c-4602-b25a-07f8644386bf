/**
 * Core cryptographic functions for WebOTR
 */
// Only import what we need from the basic crypto operations
import { 
  generateDHKeyPair, 
  generateDS<PERSON><PERSON>eyPair, 
  verifySignature, 
  createSignature,
  encrypt,
  decrypt,
  hmacSha256,
  random
} from './basic';

// Import SMP-related functions
import {
  generateRandomExponent,
  computeG1,
  computeG2,
  computeG3,
  createZK<PERSON>,
  verifyZK<PERSON>,
  hashForSMP,
  MODP_GROUP
} from './smp';

/**
 * Generate OTR keys
 * @returns {Promise<Object>} Generated keys
 */
export async function generateKeys() {
  // Generate DSA keys for authentication
  const dsaKeys = await generateDSAKeyPair();
  
  // Generate DH keys for key exchange
  const dhKeys = await generateDHKeyPair();
  
  return {
    dsa: dsaKeys,
    dh: dhKeys
  };
}

/**
 * Derive keys from shared secret using HKDF
 * @param {Uint8Array} sharedSecret - Shared secret from DH exchange
 * @param {Uint8Array} salt - Optional salt (defaults to zero bytes)
 * @param {Uint8Array} info - Optional context info
 * @returns {Promise<Object>} Derived keys
 */
export async function deriveKeys(sharedSecret, salt = null, info = null) {
  // Use proper HKDF for key derivation
  if (!salt) {
    salt = new Uint8Array(32); // Zero salt if not provided
  }

  if (!info) {
    info = new TextEncoder().encode('WebOTR-v1-key-derivation');
  }

  // HKDF Extract: PRK = HMAC-SHA256(salt, IKM)
  const prk = await hmacSha256(salt, sharedSecret);

  // HKDF Expand: derive multiple keys
  const sendingAESKey = await hmacSha256(prk, new Uint8Array([...info, 0x01]));
  const receivingAESKey = await hmacSha256(prk, new Uint8Array([...info, 0x02]));
  const sendingMACKey = await hmacSha256(prk, new Uint8Array([...info, 0x03]));
  const receivingMACKey = await hmacSha256(prk, new Uint8Array([...info, 0x04]));

  return {
    sendingAESKey: sendingAESKey.slice(0, 32), // Use 256 bits for AES key
    receivingAESKey: receivingAESKey.slice(0, 32),
    sendingMACKey: sendingMACKey, // Use all 256 bits for MAC key
    receivingMACKey: receivingMACKey,
    // Legacy compatibility
    aesKey: sendingAESKey.slice(0, 16), // 128 bits for backward compatibility
    macKey: sendingMACKey
  };
}

/**
 * Generate an instance tag
 * @returns {number} Instance tag
 */
export function generateInstanceTag() {
  // Generate a random 32-bit number with highest bit not 0
  // (as per OTRv3 spec section 5.1)
  const bytes = random(4);
  let tag = bytes[0] | (bytes[1] << 8) | (bytes[2] << 16) | (bytes[3] << 24);

  // Ensure highest 8 bits are non-zero by setting at least one of them
  if ((tag & 0xFF000000) === 0) {
    tag |= 0x01000000;
  }

  // Ensure it's a positive number (JavaScript quirk)
  return tag >>> 0;
}

/**
 * Securely clear sensitive data from memory
 * @param {Uint8Array|Array} data - Data to clear
 */
export function secureClear(data) {
  if (data && data.fill) {
    data.fill(0);
  }
}

/**
 * Constant-time comparison to prevent timing attacks
 * @param {Uint8Array} a - First array
 * @param {Uint8Array} b - Second array
 * @returns {boolean} True if arrays are equal
 */
export function constantTimeEqual(a, b) {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a[i] ^ b[i];
  }

  return result === 0;
}

// Export all the required functions
export {
  // From basic crypto
  verifySignature,
  createSignature,
  encrypt,
  decrypt,
  hmacSha256,
  random,

  // SMP functions
  generateRandomExponent,
  computeG1,
  computeG2,
  computeG3,
  createZKP,
  verifyZKP,
  hashForSMP,
  MODP_GROUP
};