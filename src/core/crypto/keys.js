/**
 * Key generation and management for WebOTR
 */
import { random } from './random';
import { hmacSha256 } from './hmac';
import { generateDHKeyPair } from './dh';
import CryptoJS from 'crypto-js';

/**
 * Generate a new OTR key pair
 * @returns {Promise<Object>} Key pair with public and private components
 */
export async function generateKeys() {
  // Generate DH key pair for encryption
  const dhKeyPair = await generateDHKeyPair();
  
  // Generate DSA key pair for signing (to be implemented)
  // For now, we'll use a placeholder
  const dsaKeyPair = {
    privateKey: random(32),
    publicKey: random(32)
  };
  
  return {
    dh: dhKeyPair,
    dsa: dsaKeyPair
  };
}

/**
 * Derive session keys from shared secret
 * @param {Uint8Array} secret - Shared secret from DH exchange
 * @returns {Promise<Object>} Derived keys for AES and MAC
 */
export async function deriveKeys(secret) {
  // OTR uses a key derivation function based on SHA-256
  // We'll derive multiple keys from the shared secret
  
  // Convert to WordArray for CryptoJS
  const secretWords = uint8ArrayToWordArray(secret);
  
  // Derive secure session ID (half of the secret)
  const secbytes = secret.length;
  const sessionId = secret.slice(0, secbytes / 2);
  
  // Derive AES keys
  const sendingAESKey = await deriveAESKey(secret, 0x00);
  const receivingAESKey = await deriveAESKey(secret, 0x01);
  
  // Derive MAC keys
  const sendingMACKey = await deriveMACKey(sendingAESKey);
  const receivingMACKey = await deriveMACKey(receivingAESKey);
  
  return {
    sessionId,
    sendingAESKey,
    receivingAESKey,
    sendingMACKey,
    receivingMACKey
  };
}

/**
 * Derive an AES key from the shared secret
 * @param {Uint8Array} secret - Shared secret
 * @param {number} type - Key type (0x00 for sending, 0x01 for receiving)
 * @returns {Promise<Uint8Array>} AES key
 */
async function deriveAESKey(secret, type) {
  // Create a buffer with the secret and type
  const buffer = new Uint8Array(secret.length + 1);
  buffer.set(secret);
  buffer[secret.length] = type;
  
  // Hash the buffer to create the AES key
  const hash = await hmacSha256(buffer, new Uint8Array([0x00]));
  
  // Return the first 16 bytes (128 bits) for AES-128
  return hash.slice(0, 16);
}

/**
 * Derive a MAC key from an AES key
 * @param {Uint8Array} aesKey - AES key
 * @returns {Promise<Uint8Array>} MAC key
 */
async function deriveMACKey(aesKey) {
  // In OTR, the MAC key is derived from the AES key
  return await hmacSha256(aesKey, new Uint8Array([0x00]));
}

/**
 * Generate a random instance tag
 * @returns {number} Random instance tag
 */
export function generateInstanceTag() {
  // Instance tags must be 4-byte values > 0x00000100
  const bytes = random(4);
  
  // Ensure the value is > 0x00000100
  bytes[0] = Math.max(bytes[0], 1);
  if (bytes[0] === 1) {
    bytes[1] = Math.max(bytes[1], 1);
  }
  
  // Convert to a 32-bit integer
  return (bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3];
}

/**
 * Convert Uint8Array to WordArray
 * @param {Uint8Array} arr - Uint8Array to convert
 * @returns {CryptoJS.lib.WordArray} WordArray
 */
function uint8ArrayToWordArray(arr) {
  const words = [];
  for (let i = 0; i < arr.length; i += 4) {
    words.push(
      (arr[i] << 24) |
      ((arr[i+1] || 0) << 16) |
      ((arr[i+2] || 0) << 8) |
      (arr[i+3] || 0)
    );
  }
  return CryptoJS.lib.WordArray.create(words, arr.length);
}

/**
 * Convert WordArray to Uint8Array
 * @param {CryptoJS.lib.WordArray} wordArray - WordArray to convert
 * @returns {Uint8Array} Uint8Array
 */
function wordArrayToUint8Array(wordArray) {
  const words = wordArray.words;
  const sigBytes = wordArray.sigBytes;
  const u8 = new Uint8Array(sigBytes);
  
  for (let i = 0; i < sigBytes; i++) {
    const byte = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
    u8[i] = byte;
  }
  
  return u8;
} 