/**
 * Di<PERSON><PERSON>-<PERSON><PERSON> Key Exchange Implementation
 * 
 * Based on libOTR's dh.c implementation patterns.
 * Implements RFC 3526 1536-bit MODP Group for OTR v3.
 */

const crypto = require('crypto');

/**
 * RFC 3526 1536-bit MODP Group constants (from libOTR dh.c)
 */
const DH_CONSTANTS = {
  // Group ID for 1536-bit MODP group
  GROUP_ID: 5,
  
  // 1536-bit prime modulus (RFC 3526)
  MODULUS: "0x" +
    "FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD1" +
    "29024E088A67CC74020BBEA63B139B22514A08798E3404DD" +
    "EF9519B3CD3A431B302B0A6DF25F14374FE1356D6D51C245" +
    "E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7ED" +
    "EE386BFB5A899FA5AE9F24117C4B1FE649286651ECE45B3D" +
    "C2007CB8A163BF0598DA48361C55D39A69163FA8FD24CF5F" +
    "83655D23DCA3AD961C62F356208552BB9ED529077096966D" +
    "670C354E4ABC9804F1746C08CA237327FFFFFFFFFFFFFFFF",
  
  // Generator
  GENERATOR: "0x02",
  
  // Modulus length in bits and bytes
  MOD_LEN_BITS: 1536,
  MOD_LEN_BYTES: 192
};

/**
 * DH Keypair structure (based on libOTR's DH_keypair)
 */
class DHKeyPair {
  constructor() {
    this.groupId = DH_CONSTANTS.GROUP_ID;
    this.privateKey = null;  // Private key (random value)
    this.publicKey = null;   // Public key (g^privateKey mod p)
  }

  /**
   * Initialize keypair (based on otrl_dh_keypair_init)
   */
  init() {
    this.privateKey = null;
    this.publicKey = null;
  }

  /**
   * Copy keypair (based on otrl_dh_keypair_copy)
   */
  copy(source) {
    this.groupId = source.groupId;
    this.privateKey = source.privateKey ? Buffer.from(source.privateKey) : null;
    this.publicKey = source.publicKey ? Buffer.from(source.publicKey) : null;
  }

  /**
   * Free keypair (based on otrl_dh_keypair_free)
   */
  free() {
    if (this.privateKey) {
      this.privateKey.fill(0);
      this.privateKey = null;
    }
    if (this.publicKey) {
      this.publicKey.fill(0);
      this.publicKey = null;
    }
  }
}

/**
 * DH Session Keys structure (based on libOTR's DH_sesskeys)
 */
class DHSessionKeys {
  constructor() {
    this.sendCtr = Buffer.alloc(16);      // Send counter
    this.rcvCtr = Buffer.alloc(16);       // Receive counter
    this.sendEncKey = null;               // Send encryption key
    this.rcvEncKey = null;                // Receive encryption key
    this.sendMacKey = Buffer.alloc(20);   // Send MAC key
    this.rcvMacKey = Buffer.alloc(20);    // Receive MAC key
    this.extraKey = Buffer.alloc(32);     // Extra key material
  }

  /**
   * Free session keys (based on otrl_dh_session_free)
   */
  free() {
    this.sendCtr.fill(0);
    this.rcvCtr.fill(0);
    this.sendMacKey.fill(0);
    this.rcvMacKey.fill(0);
    this.extraKey.fill(0);
    
    if (this.sendEncKey) {
      this.sendEncKey.fill(0);
      this.sendEncKey = null;
    }
    if (this.rcvEncKey) {
      this.rcvEncKey.fill(0);
      this.rcvEncKey = null;
    }
  }

  /**
   * Blank session keys (based on otrl_dh_session_blank)
   */
  blank() {
    this.sendCtr.fill(0);
    this.rcvCtr.fill(0);
    this.sendMacKey.fill(0);
    this.rcvMacKey.fill(0);
    this.extraKey.fill(0);
  }
}

/**
 * DH Key Exchange Implementation
 */
class DHKeyExchange {
  constructor() {
    this.modulus = this.hexToBigInt(DH_CONSTANTS.MODULUS);
    this.generator = this.hexToBigInt(DH_CONSTANTS.GENERATOR);
  }

  /**
   * Generate DH keypair (based on otrl_dh_gen_keypair)
   */
  async generateKeyPair() {
    const keyPair = new DHKeyPair();
    
    try {
      // Generate random private key (1 < privateKey < modulus-1)
      let privateKey;
      do {
        privateKey = this.generateRandomBigInt(DH_CONSTANTS.MOD_LEN_BYTES);
      } while (privateKey <= 1n || privateKey >= this.modulus - 1n);
      
      // Compute public key: g^privateKey mod p
      const publicKey = this.modPow(this.generator, privateKey, this.modulus);
      
      // Validate public key is in valid range (2 <= publicKey <= modulus-2)
      if (publicKey <= 2n || publicKey >= this.modulus - 2n) {
        throw new Error('Generated public key is out of valid range');
      }
      
      // Store keys as buffers
      keyPair.privateKey = this.bigIntToBuffer(privateKey, DH_CONSTANTS.MOD_LEN_BYTES);
      keyPair.publicKey = this.bigIntToBuffer(publicKey, DH_CONSTANTS.MOD_LEN_BYTES);
      
      return keyPair;
      
    } catch (error) {
      keyPair.free();
      throw new Error(`DH keypair generation failed: ${error.message}`);
    }
  }

  /**
   * Compute shared secret (based on otrl_dh_session)
   */
  async computeSharedSecret(ourPrivateKey, theirPublicKey) {
    try {
      // Convert to BigInt
      const privateKey = this.bufferToBigInt(ourPrivateKey);
      const publicKey = this.bufferToBigInt(theirPublicKey);
      
      // Validate their public key
      if (!this.validatePublicKey(publicKey)) {
        throw new Error('Invalid public key received');
      }
      
      // Compute shared secret: theirPublicKey^ourPrivateKey mod p
      const sharedSecret = this.modPow(publicKey, privateKey, this.modulus);
      
      // Validate shared secret
      if (sharedSecret <= 1n || sharedSecret >= this.modulus - 1n) {
        throw new Error('Invalid shared secret computed');
      }
      
      return this.bigIntToBuffer(sharedSecret, DH_CONSTANTS.MOD_LEN_BYTES);
      
    } catch (error) {
      throw new Error(`Shared secret computation failed: ${error.message}`);
    }
  }

  /**
   * Validate DH public key
   */
  validatePublicKey(publicKey) {
    // Check range: 2 <= publicKey <= modulus-2
    if (publicKey <= 2n || publicKey >= this.modulus - 2n) {
      return false;
    }
    
    // Additional validation could include subgroup order check
    // For now, we'll use the basic range check
    return true;
  }

  /**
   * Serialize public key for transmission
   */
  serializePublicKey(publicKey) {
    if (Buffer.isBuffer(publicKey)) {
      return publicKey;
    }
    
    // Convert BigInt to buffer if needed
    if (typeof publicKey === 'bigint') {
      return this.bigIntToBuffer(publicKey, DH_CONSTANTS.MOD_LEN_BYTES);
    }
    
    throw new Error('Invalid public key format');
  }

  /**
   * Deserialize public key from transmission
   */
  deserializePublicKey(data) {
    if (!Buffer.isBuffer(data)) {
      throw new Error('Public key data must be a Buffer');
    }
    
    if (data.length !== DH_CONSTANTS.MOD_LEN_BYTES) {
      throw new Error(`Public key must be ${DH_CONSTANTS.MOD_LEN_BYTES} bytes`);
    }
    
    return data;
  }

  /**
   * Convert hex string to BigInt
   */
  hexToBigInt(hexString) {
    const cleanHex = hexString.replace(/^0x/, '');
    return BigInt('0x' + cleanHex);
  }

  /**
   * Convert BigInt to buffer with specified length
   */
  bigIntToBuffer(bigInt, length) {
    const hex = bigInt.toString(16).padStart(length * 2, '0');
    return Buffer.from(hex, 'hex');
  }

  /**
   * Convert buffer to BigInt
   */
  bufferToBigInt(buffer) {
    return BigInt('0x' + buffer.toString('hex'));
  }

  /**
   * Generate random BigInt of specified byte length
   */
  generateRandomBigInt(byteLength) {
    const randomBytes = crypto.randomBytes(byteLength);
    return this.bufferToBigInt(randomBytes);
  }

  /**
   * Modular exponentiation: base^exponent mod modulus
   * Uses JavaScript's built-in BigInt modular exponentiation
   */
  modPow(base, exponent, modulus) {
    return this.fastModPow(base, exponent, modulus);
  }

  /**
   * Fast modular exponentiation using binary method
   */
  fastModPow(base, exponent, modulus) {
    if (modulus === 1n) return 0n;
    
    let result = 1n;
    base = base % modulus;
    
    while (exponent > 0n) {
      if (exponent % 2n === 1n) {
        result = (result * base) % modulus;
      }
      exponent = exponent >> 1n;
      base = (base * base) % modulus;
    }
    
    return result;
  }

  /**
   * Increment counter (based on otrl_dh_incctr)
   */
  incrementCounter(counter) {
    // Increment the top half of the counter block
    for (let i = 15; i >= 8; i--) {
      counter[i]++;
      if (counter[i] !== 0) break;
    }
  }

  /**
   * Compare counters (based on otrl_dh_cmpctr)
   */
  compareCounters(ctr1, ctr2) {
    // Compare as unsigned 64-bit values (top 8 bytes)
    for (let i = 8; i < 16; i++) {
      if (ctr1[i] < ctr2[i]) return -1;
      if (ctr1[i] > ctr2[i]) return 1;
    }
    return 0;
  }
}

module.exports = {
  DHKeyExchange,
  DHKeyPair,
  DHSessionKeys,
  DH_CONSTANTS
};
