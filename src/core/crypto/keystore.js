/**
 * KeyStore - Secure key storage implementation for WebOTR
 * 
 * This module provides secure storage for cryptographic keys using
 * browser's IndexedDB and the Web Crypto API for additional encryption.
 */
import { random } from './random';
import { hmacSha256 } from './hmac';

// Default database name
const DB_NAME = 'webOTR_keystore';
const DB_VERSION = 1;
const KEY_STORE = 'keys';
const META_STORE = 'metadata';

class KeyStore {
  constructor(dbName = DB_NAME) {
    this.dbName = dbName;
    this.db = null;
    this.initialized = false;
    this._encryptionKey = null;
  }

  /**
   * Initialize the key store
   * @param {string} passphrase - Optional passphrase for additional encryption
   * @returns {Promise<void>}
   */
  async initialize(passphrase = '') {
    if (this.initialized) return;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, DB_VERSION);

      request.onerror = (event) => {
        reject(new Error(`Failed to open key database: ${event.target.error}`));
      };

      request.onsuccess = (event) => {
        this.db = event.target.result;
        this.initialized = true;
        
        // Derive encryption key from passphrase if provided
        if (passphrase) {
          this._deriveEncryptionKey(passphrase)
            .then(() => resolve())
            .catch(reject);
        } else {
          resolve();
        }
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Create key store
        if (!db.objectStoreNames.contains(KEY_STORE)) {
          db.createObjectStore(KEY_STORE, { keyPath: 'id' });
        }
        
        // Create metadata store
        if (!db.objectStoreNames.contains(META_STORE)) {
          db.createObjectStore(META_STORE, { keyPath: 'id' });
        }
      };
    });
  }

  /**
   * Store a key pair
   * @param {string} id - Unique identifier for the key pair
   * @param {Object} keyPair - The key pair to store
   * @param {Object} metadata - Optional metadata about the key pair
   * @returns {Promise<void>}
   */
  async storeKeyPair(id, keyPair, metadata = {}) {
    if (!this.initialized) {
      throw new Error('KeyStore not initialized');
    }

    // Encrypt the key pair if encryption is enabled
    const encryptedData = await this._encryptData(keyPair);
    
    const timestamp = Date.now();
    const keyData = {
      id,
      data: encryptedData,
      created: timestamp,
      updated: timestamp
    };

    const metaData = {
      id,
      ...metadata,
      created: timestamp,
      updated: timestamp
    };

    return Promise.all([
      this._storeItem(KEY_STORE, keyData),
      this._storeItem(META_STORE, metaData)
    ]);
  }

  /**
   * Retrieve a key pair
   * @param {string} id - Unique identifier for the key pair
   * @returns {Promise<Object>} The key pair
   */
  async getKeyPair(id) {
    if (!this.initialized) {
      throw new Error('KeyStore not initialized');
    }

    const keyData = await this._getItem(KEY_STORE, id);
    if (!keyData) {
      throw new Error(`Key pair with id ${id} not found`);
    }

    return this._decryptData(keyData.data);
  }

  /**
   * Delete a key pair
   * @param {string} id - Unique identifier for the key pair
   * @returns {Promise<void>}
   */
  async deleteKeyPair(id) {
    if (!this.initialized) {
      throw new Error('KeyStore not initialized');
    }

    return Promise.all([
      this._deleteItem(KEY_STORE, id),
      this._deleteItem(META_STORE, id)
    ]);
  }

  /**
   * List all stored key pairs
   * @returns {Promise<Array>} Array of key metadata
   */
  async listKeyPairs() {
    if (!this.initialized) {
      throw new Error('KeyStore not initialized');
    }

    return this._getAllItems(META_STORE);
  }

  /**
   * Update metadata for a key pair
   * @param {string} id - Unique identifier for the key pair
   * @param {Object} metadata - New metadata
   * @returns {Promise<void>}
   */
  async updateMetadata(id, metadata) {
    if (!this.initialized) {
      throw new Error('KeyStore not initialized');
    }

    const existingMeta = await this._getItem(META_STORE, id);
    if (!existingMeta) {
      throw new Error(`Key pair with id ${id} not found`);
    }

    const updatedMeta = {
      ...existingMeta,
      ...metadata,
      updated: Date.now()
    };

    return this._storeItem(META_STORE, updatedMeta);
  }

  /**
   * Close the key store
   * @returns {Promise<void>}
   */
  async close() {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.initialized = false;
      this._encryptionKey = null;
    }
  }

  // Private methods

  /**
   * Derive encryption key from passphrase
   * @param {string} passphrase - Passphrase for encryption
   * @returns {Promise<void>}
   * @private
   */
  async _deriveEncryptionKey(passphrase) {
    // Convert passphrase to Uint8Array
    const encoder = new TextEncoder();
    const passphraseData = encoder.encode(passphrase);
    
    // Add salt
    const salt = await this._getSalt();
    
    // Derive key using PBKDF2
    const keyMaterial = await window.crypto.subtle.importKey(
      'raw',
      passphraseData,
      { name: 'PBKDF2' },
      false,
      ['deriveKey']
    );
    
    this._encryptionKey = await window.crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt,
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Get or create salt for key derivation
   * @returns {Promise<Uint8Array>}
   * @private
   */
  async _getSalt() {
    try {
      const saltData = await this._getItem(META_STORE, 'encryption-salt');
      if (saltData && saltData.salt) {
        return new Uint8Array(saltData.salt);
      }
    } catch (e) {
      // If salt doesn't exist, we'll create a new one
    }
    
    // Create new salt
    const salt = random(16);
    await this._storeItem(META_STORE, {
      id: 'encryption-salt',
      salt: Array.from(salt),
      created: Date.now()
    });
    
    return salt;
  }

  /**
   * Encrypt data
   * @param {Object} data - Data to encrypt
   * @returns {Promise<Object>} Encrypted data
   * @private
   */
  async _encryptData(data) {
    // If no encryption key, just return the data
    if (!this._encryptionKey) {
      return data;
    }
    
    // Convert data to string
    const dataString = JSON.stringify(data);
    const encoder = new TextEncoder();
    const dataBytes = encoder.encode(dataString);
    
    // Generate IV
    const iv = window.crypto.getRandomValues(new Uint8Array(12));
    
    // Encrypt
    const ciphertext = await window.crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv
      },
      this._encryptionKey,
      dataBytes
    );
    
    // Return encrypted data with IV
    return {
      ciphertext: Array.from(new Uint8Array(ciphertext)),
      iv: Array.from(iv),
      encrypted: true
    };
  }

  /**
   * Decrypt data
   * @param {Object} encryptedData - Encrypted data
   * @returns {Promise<Object>} Decrypted data
   * @private
   */
  async _decryptData(encryptedData) {
    // If not encrypted, just return the data
    if (!encryptedData.encrypted) {
      return encryptedData;
    }
    
    // If no encryption key, can't decrypt
    if (!this._encryptionKey) {
      throw new Error('Encryption key not available');
    }
    
    // Convert back to Uint8Array
    const ciphertext = new Uint8Array(encryptedData.ciphertext);
    const iv = new Uint8Array(encryptedData.iv);
    
    // Decrypt
    const decryptedBytes = await window.crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv
      },
      this._encryptionKey,
      ciphertext
    );
    
    // Convert back to object
    const decoder = new TextDecoder();
    const decryptedString = decoder.decode(decryptedBytes);
    return JSON.parse(decryptedString);
  }

  /**
   * Store item in object store
   * @param {string} storeName - Name of the object store
   * @param {Object} item - Item to store
   * @returns {Promise<void>}
   * @private
   */
  _storeItem(storeName, item) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(item);
      
      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error(`Failed to store item in ${storeName}`));
    });
  }

  /**
   * Get item from object store
   * @param {string} storeName - Name of the object store
   * @param {string} id - Item ID
   * @returns {Promise<Object>} The item
   * @private
   */
  _getItem(storeName, id) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(id);
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(new Error(`Failed to get item from ${storeName}`));
    });
  }

  /**
   * Delete item from object store
   * @param {string} storeName - Name of the object store
   * @param {string} id - Item ID
   * @returns {Promise<void>}
   * @private
   */
  _deleteItem(storeName, id) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(id);
      
      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error(`Failed to delete item from ${storeName}`));
    });
  }

  /**
   * Get all items from object store
   * @param {string} storeName - Name of the object store
   * @returns {Promise<Array>} Array of items
   * @private
   */
  _getAllItems(storeName) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(new Error(`Failed to get items from ${storeName}`));
    });
  }
}

export default KeyStore; 