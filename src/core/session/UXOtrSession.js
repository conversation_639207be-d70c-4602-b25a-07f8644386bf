import { EventEmitter } from 'events';
import { OtrSession } from './OtrSession.js';

/**
 * UXOtrSession - Enhanced OTR session with UX integration
 * 
 * Extends the base OTR session with user experience features:
 * - Real-time status updates
 * - Progress tracking
 * - Error handling with user-friendly messages
 * - Verification state management
 * - Message encryption/decryption with metadata
 */
export class UXOtrSession extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      autoConnect: false,
      progressUpdates: true,
      verificationRequired: true,
      ...options
    };
    
    // Core OTR session
    this.otrSession = new OtrSession();
    
    // UX state
    this.state = 'disconnected';
    this.isVerified = false;
    this.connectionProgress = 0;
    this.lastError = null;
    this.fingerprint = null;
    this.contactFingerprint = null;
    this.verificationMethod = null;
    this.messageHistory = [];
    
    // Performance metrics
    this.metrics = {
      connectionStartTime: null,
      connectionDuration: null,
      verificationStartTime: null,
      verificationDuration: null,
      messagesSent: 0,
      messagesReceived: 0
    };
    
    this.setupOtrEventHandlers();
  }

  setupOtrEventHandlers() {
    // Core OTR events
    this.otrSession.on('stateChange', this.handleOtrStateChange.bind(this));
    this.otrSession.on('messageReceived', this.handleOtrMessageReceived.bind(this));
    this.otrSession.on('error', this.handleOtrError.bind(this));
    this.otrSession.on('fingerprintReceived', this.handleFingerprintReceived.bind(this));
    this.otrSession.on('verificationRequired', this.handleVerificationRequired.bind(this));
    
    // Connection progress simulation for demo
    this.otrSession.on('connecting', this.handleConnectionProgress.bind(this));
  }

  async startOTR() {
    try {
      this.metrics.connectionStartTime = Date.now();
      this.lastError = null;
      this.connectionProgress = 0;
      
      this.setState('connecting');
      this.emit('connectionStarted');
      
      // Simulate connection progress
      await this.simulateConnectionProgress();
      
      // Start actual OTR session
      if (this.otrSession.startOTR) {
        await this.otrSession.startOTR();
      } else {
        // Fallback simulation for demo
        await this.simulateOtrConnection();
      }
      
      this.metrics.connectionDuration = Date.now() - this.metrics.connectionStartTime;
      this.emit('connectionCompleted', { duration: this.metrics.connectionDuration });
      
    } catch (error) {
      this.handleConnectionError(error);
    }
  }

  async endOTR() {
    try {
      if (this.otrSession.endOTR) {
        await this.otrSession.endOTR();
      }
      
      this.setState('disconnected');
      this.isVerified = false;
      this.verificationMethod = null;
      this.connectionProgress = 0;
      
      this.emit('sessionEnded');
      
    } catch (error) {
      this.handleOtrError(error);
    }
  }

  async sendMessage(content) {
    try {
      if (this.state !== 'connected') {
        throw new Error('OTR session not connected');
      }
      
      const message = {
        id: Date.now(),
        content,
        timestamp: new Date(),
        encrypted: true,
        verified: this.isVerified,
        type: 'sent'
      };
      
      // Send through OTR session
      if (this.otrSession.sendMessage) {
        await this.otrSession.sendMessage(content);
      }
      
      this.messageHistory.push(message);
      this.metrics.messagesSent++;
      
      this.emit('messageSent', message);
      return message;
      
    } catch (error) {
      this.handleMessageError(error, content);
      throw error;
    }
  }

  async verifyIdentity(method, data) {
    try {
      this.metrics.verificationStartTime = Date.now();
      this.emit('verificationStarted', { method });
      
      let verified = false;
      
      switch (method) {
        case 'qr-code':
          verified = await this.verifyQRCode(data);
          break;
        case 'question-answer':
          verified = await this.verifySMP(data);
          break;
        case 'manual-fingerprint':
          verified = await this.verifyManualFingerprint(data);
          break;
        default:
          throw new Error(`Unknown verification method: ${method}`);
      }
      
      this.isVerified = verified;
      this.verificationMethod = method;
      this.metrics.verificationDuration = Date.now() - this.metrics.verificationStartTime;
      
      this.emit('verificationCompleted', {
        method,
        verified,
        duration: this.metrics.verificationDuration
      });
      
      if (verified) {
        this.setState('verified');
      }
      
      return verified;
      
    } catch (error) {
      this.emit('verificationFailed', { method, error: error.message });
      throw error;
    }
  }

  // Verification method implementations
  async verifyQRCode(data) {
    // Simulate QR code verification
    await this.delay(1500);
    
    if (data.fingerprint && this.contactFingerprint) {
      return data.fingerprint === this.contactFingerprint;
    }
    
    // For demo, simulate 90% success rate
    return Math.random() > 0.1;
  }

  async verifySMP(data) {
    // Simulate SMP verification
    await this.delay(2000);
    
    if (data.question && data.answer) {
      // In real implementation, this would use the SMP protocol
      // For demo, simulate verification based on answer length and content
      return data.answer.length >= 3 && !data.answer.toLowerCase().includes('wrong');
    }
    
    return Math.random() > 0.2; // 80% success rate
  }

  async verifyManualFingerprint(data) {
    // Manual verification is based on user confirmation
    await this.delay(500);
    return data.confirmed === true;
  }

  // Event handlers
  handleOtrStateChange(newState) {
    this.setState(newState);
  }

  handleOtrMessageReceived(message) {
    const processedMessage = {
      id: Date.now(),
      content: message.content || message,
      timestamp: new Date(),
      encrypted: message.encrypted || this.state === 'connected',
      verified: message.verified || this.isVerified,
      type: 'received'
    };
    
    this.messageHistory.push(processedMessage);
    this.metrics.messagesReceived++;
    
    this.emit('messageReceived', processedMessage);
  }

  handleOtrError(error) {
    this.lastError = {
      message: error.message || 'Unknown OTR error',
      timestamp: new Date(),
      code: error.code || 'UNKNOWN_ERROR'
    };
    
    this.emit('error', this.lastError);
  }

  handleFingerprintReceived(fingerprint) {
    if (!this.fingerprint) {
      this.fingerprint = fingerprint;
      this.emit('fingerprintGenerated', fingerprint);
    } else {
      this.contactFingerprint = fingerprint;
      this.emit('contactFingerprintReceived', fingerprint);
    }
  }

  handleVerificationRequired() {
    this.emit('verificationRequired');
  }

  handleConnectionProgress() {
    // Simulate connection progress updates
    const progressInterval = setInterval(() => {
      this.connectionProgress += 10;
      this.emit('connectionProgress', this.connectionProgress);
      
      if (this.connectionProgress >= 100) {
        clearInterval(progressInterval);
      }
    }, 200);
  }

  handleConnectionError(error) {
    this.setState('error');
    this.lastError = {
      message: error.message || 'Connection failed',
      timestamp: new Date(),
      code: error.code || 'CONNECTION_ERROR'
    };
    
    this.emit('connectionFailed', this.lastError);
  }

  handleMessageError(error, content) {
    this.emit('messageFailed', {
      content,
      error: error.message,
      timestamp: new Date()
    });
  }

  // State management
  setState(newState) {
    const oldState = this.state;
    this.state = newState;
    
    this.emit('stateChange', {
      from: oldState,
      to: newState,
      timestamp: new Date()
    });
  }

  // Utility methods
  async simulateConnectionProgress() {
    const steps = [
      { progress: 10, message: 'Initializing OTR protocol...' },
      { progress: 25, message: 'Generating keys...' },
      { progress: 50, message: 'Establishing secure channel...' },
      { progress: 75, message: 'Verifying connection...' },
      { progress: 100, message: 'Connection established' }
    ];
    
    for (const step of steps) {
      await this.delay(300);
      this.connectionProgress = step.progress;
      this.emit('connectionProgress', {
        progress: step.progress,
        message: step.message
      });
    }
  }

  async simulateOtrConnection() {
    await this.delay(1000);
    this.setState('connected');
    
    // Generate mock fingerprints
    this.fingerprint = this.generateMockFingerprint();
    this.contactFingerprint = this.generateMockFingerprint();
    
    this.emit('fingerprintGenerated', this.fingerprint);
    this.emit('contactFingerprintReceived', this.contactFingerprint);
    
    // Emit verification required after connection
    setTimeout(() => {
      if (!this.isVerified && this.options.verificationRequired) {
        this.emit('verificationRequired');
      }
    }, 1000);
  }

  generateMockFingerprint() {
    const chars = '0123456789ABCDEF';
    let fingerprint = '';
    for (let i = 0; i < 40; i++) {
      fingerprint += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return fingerprint;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Getters
  getState() {
    return this.state;
  }

  getVerificationStatus() {
    return {
      isVerified: this.isVerified,
      method: this.verificationMethod,
      fingerprint: this.fingerprint,
      contactFingerprint: this.contactFingerprint
    };
  }

  getMetrics() {
    return { ...this.metrics };
  }

  getMessageHistory() {
    return [...this.messageHistory];
  }

  // Cleanup
  disconnect() {
    if (this.otrSession && this.otrSession.disconnect) {
      this.otrSession.disconnect();
    }
    
    this.removeAllListeners();
    this.state = 'disconnected';
    this.isVerified = false;
    this.messageHistory = [];
  }
}

export default UXOtrSession;
