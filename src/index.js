/**
 * WebOTR Main Entry Point
 * 
 * This is the main entry point for the WebOTR application.
 * It exports the core functionality and UI components.
 */

// Core OTR functionality
export { OTRSession } from './core/session';
export { OTRProtocol } from './core/protocol';
export { CryptoEngine } from './core/crypto';
export { ForwardSecrecy } from './core/forward-secrecy';

// Platform integrations
export { PlatformManager } from './platforms';

// UI Components
export { 
  OtrToggle,
  OtrStatus,
  VerificationDialog,
  Notifications
} from './ui';

// Extension integration (for web builds)
if (typeof window !== 'undefined' && window.chrome && window.chrome.runtime) {
  // Browser extension environment
  import('./extension-integration').then(module => {
    module.initializeExtensionIntegration();
  }).catch(err => {
    console.warn('Extension integration not available:', err);
  });
}

// Default export for convenience
export default {
  version: '0.1.0',
  name: 'WebOTR'
};
