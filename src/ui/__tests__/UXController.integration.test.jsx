import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import UXController from '../UXController.jsx';

// Mock the OTR session
jest.mock('../../core/session/OtrSession.js', () => {
  return {
    OtrSession: jest.fn().mockImplementation(() => ({
      on: jest.fn(),
      startOTR: jest.fn(),
      endOTR: jest.fn(),
      sendMessage: jest.fn(),
      disconnect: jest.fn()
    }))
  };
});

// Mock the verification components
jest.mock('../components/verification/VerificationDialog.jsx', () => {
  return function MockVerificationDialog({ isOpen, onComplete, onClose }) {
    if (!isOpen) return null;
    return (
      <div data-testid="verification-dialog">
        <button onClick={() => onComplete({ method: 'qr-code', verified: true })}>
          Complete Verification
        </button>
        <button onClick={onClose}>Close Dialog</button>
      </div>
    );
  };
});

describe('UXController Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the main interface correctly', () => {
    render(<UXController />);
    
    expect(screen.getByText('WebOTR Secure Messaging')).toBeInTheDocument();
    expect(screen.getByText('End-to-end encrypted conversations')).toBeInTheDocument();
    expect(screen.getByText('Start Secure Chat')).toBeInTheDocument();
  });

  it('shows initial disconnected status', () => {
    render(<UXController />);
    
    expect(screen.getByText('Not Connected')).toBeInTheDocument();
    expect(screen.getByText('No OTR session active')).toBeInTheDocument();
  });

  it('displays empty message state initially', () => {
    render(<UXController />);
    
    expect(screen.getByText('No messages yet. Start a conversation!')).toBeInTheDocument();
    expect(screen.getByText('0 messages')).toBeInTheDocument();
  });

  it('starts OTR session when button is clicked', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    expect(startButton).toBeDisabled();
    expect(screen.getByText('Connecting...')).toBeInTheDocument();
  });

  it('shows connection progress during OTR setup', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    expect(screen.getByText('Establishing secure connection...')).toBeInTheDocument();
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('enables message input when connected', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    // Start OTR session
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    // Wait for connection to complete (simulated)
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Type a secure message...')).not.toBeDisabled();
    }, { timeout: 3000 });
  });

  it('sends messages when connected', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    // Start OTR session and wait for connection
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Type a secure message...')).not.toBeDisabled();
    }, { timeout: 3000 });
    
    // Type and send a message
    const messageInput = screen.getByPlaceholderText('Type a secure message...');
    await user.type(messageInput, 'Hello, secure world!');
    
    const sendButton = screen.getByText('Send');
    await user.click(sendButton);
    
    // Message should appear in the chat
    expect(screen.getByText('Hello, secure world!')).toBeInTheDocument();
    expect(screen.getByText('1 message')).toBeInTheDocument();
  });

  it('shows verification warning when connected but unverified', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    // Start OTR session
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    // Wait for connection and verification warning
    await waitFor(() => {
      expect(screen.getByText(/Connection established but identity not verified/)).toBeInTheDocument();
    }, { timeout: 3000 });
    
    expect(screen.getByText('Verify Now')).toBeInTheDocument();
  });

  it('opens verification dialog when verify button is clicked', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    // Start OTR session and wait for connection
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    await waitFor(() => {
      expect(screen.getByText('Verify Identity')).toBeInTheDocument();
    }, { timeout: 3000 });
    
    const verifyButton = screen.getByText('Verify Identity');
    await user.click(verifyButton);
    
    expect(screen.getByTestId('verification-dialog')).toBeInTheDocument();
  });

  it('completes verification process', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    // Start OTR session
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    // Wait for connection and open verification
    await waitFor(() => {
      expect(screen.getByText('Verify Identity')).toBeInTheDocument();
    }, { timeout: 3000 });
    
    const verifyButton = screen.getByText('Verify Identity');
    await user.click(verifyButton);
    
    // Complete verification
    const completeButton = screen.getByText('Complete Verification');
    await user.click(completeButton);
    
    // Should show success message
    await waitFor(() => {
      expect(screen.getByText(/Identity verified using qr-code/)).toBeInTheDocument();
    });
  });

  it('shows verified status after successful verification', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    // Start OTR session and complete verification
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    await waitFor(() => {
      expect(screen.getByText('Verify Identity')).toBeInTheDocument();
    }, { timeout: 3000 });
    
    const verifyButton = screen.getByText('Verify Identity');
    await user.click(verifyButton);
    
    const completeButton = screen.getByText('Complete Verification');
    await user.click(completeButton);
    
    // Status should change to verified
    await waitFor(() => {
      expect(screen.getByText('Verified')).toBeInTheDocument();
    });
  });

  it('shows encrypted and verified badges on messages after verification', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    // Start OTR session, verify, and send message
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    await waitFor(() => {
      expect(screen.getByText('Verify Identity')).toBeInTheDocument();
    }, { timeout: 3000 });
    
    const verifyButton = screen.getByText('Verify Identity');
    await user.click(verifyButton);
    
    const completeButton = screen.getByText('Complete Verification');
    await user.click(completeButton);
    
    // Wait for verification to complete
    await waitFor(() => {
      expect(screen.getByText('Verified')).toBeInTheDocument();
    });
    
    // Send a message
    const messageInput = screen.getByPlaceholderText('Type a secure message...');
    await user.type(messageInput, 'Verified message');
    
    const sendButton = screen.getByText('Send');
    await user.click(sendButton);
    
    // Message should have both encrypted and verified badges
    expect(screen.getByText('Encrypted')).toBeInTheDocument();
    expect(screen.getByText('Verified')).toBeInTheDocument();
  });

  it('ends OTR session when end button is clicked', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    // Start OTR session
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    await waitFor(() => {
      expect(screen.getByText('End Session')).not.toBeDisabled();
    }, { timeout: 3000 });
    
    // End the session
    const endButton = screen.getByText('End Session');
    await user.click(endButton);
    
    // Should show disconnected status
    expect(screen.getByText('Not Connected')).toBeInTheDocument();
    expect(screen.getByText(/OTR session ended/)).toBeInTheDocument();
  });

  it('handles keyboard shortcuts correctly', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    // Start OTR session
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Type a secure message...')).not.toBeDisabled();
    }, { timeout: 3000 });
    
    // Type message and press Enter
    const messageInput = screen.getByPlaceholderText('Type a secure message...');
    await user.type(messageInput, 'Message sent with Enter');
    await user.keyboard('{Enter}');
    
    // Message should be sent
    expect(screen.getByText('Message sent with Enter')).toBeInTheDocument();
  });

  it('shows appropriate alerts for different scenarios', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    // Start OTR session
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    // Should show info alert about starting session
    expect(screen.getByText('Starting secure OTR session...')).toBeInTheDocument();
    
    // Wait for connection warning
    await waitFor(() => {
      expect(screen.getByText(/Connection established but identity not verified/)).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('maintains message history during session', async () => {
    const user = userEvent.setup();
    render(<UXController />);
    
    // Start OTR session
    const startButton = screen.getByText('Start Secure Chat');
    await user.click(startButton);
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Type a secure message...')).not.toBeDisabled();
    }, { timeout: 3000 });
    
    // Send multiple messages
    const messageInput = screen.getByPlaceholderText('Type a secure message...');
    
    await user.type(messageInput, 'First message');
    await user.keyboard('{Enter}');
    
    await user.type(messageInput, 'Second message');
    await user.keyboard('{Enter}');
    
    // Both messages should be visible
    expect(screen.getByText('First message')).toBeInTheDocument();
    expect(screen.getByText('Second message')).toBeInTheDocument();
    expect(screen.getByText('2 messages')).toBeInTheDocument();
  });

  it('is accessible with screen readers', () => {
    render(<UXController />);
    
    // Check for proper ARIA labels and roles
    expect(screen.getByRole('main')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Start Secure Chat/ })).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
    
    // Status should have proper accessibility attributes
    expect(screen.getByRole('status')).toBeInTheDocument();
  });
});
