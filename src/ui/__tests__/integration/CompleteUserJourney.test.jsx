import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import UXController from '../../UXController.jsx';
import { UXOtrSession } from '../../../core/session/UXOtrSession.js';

// Mock the enhanced OTR session
jest.mock('../../../core/session/UXOtrSession.js');

describe('Complete User Journey Integration Tests', () => {
  let mockSession;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create mock session with all required methods
    mockSession = {
      on: jest.fn(),
      startOTR: jest.fn(),
      endOTR: jest.fn(),
      sendMessage: jest.fn(),
      verifyIdentity: jest.fn(),
      disconnect: jest.fn(),
      getState: jest.fn(() => 'disconnected'),
      getVerificationStatus: jest.fn(() => ({ isVerified: false })),
      getMetrics: jest.fn(() => ({})),
      getMessageHistory: jest.fn(() => [])
    };
    
    UXOtrSession.mockImplementation(() => mockSession);
  });

  describe('First-Time User Journey', () => {
    it('completes the full first-time user experience', async () => {
      const user = userEvent.setup();
      
      // Step 1: Initial load
      render(<UXController />);
      
      // Verify initial state
      expect(screen.getByText('WebOTR Secure Messaging')).toBeInTheDocument();
      expect(screen.getByText('Not Connected')).toBeInTheDocument();
      expect(screen.getByText('Start Secure Chat')).toBeInTheDocument();
      expect(screen.getByText('No messages yet. Start a conversation!')).toBeInTheDocument();
      
      // Step 2: Start OTR session
      const startButton = screen.getByText('Start Secure Chat');
      await user.click(startButton);
      
      // Verify session start was called
      expect(mockSession.startOTR).toHaveBeenCalled();
      expect(screen.getByText('Starting secure OTR session...')).toBeInTheDocument();
      
      // Step 3: Simulate connection progress
      const stateChangeHandler = mockSession.on.mock.calls.find(call => call[0] === 'stateChange')[1];
      const connectionProgressHandler = mockSession.on.mock.calls.find(call => call[0] === 'connectionProgress')[1];
      const connectionCompletedHandler = mockSession.on.mock.calls.find(call => call[0] === 'connectionCompleted')[1];
      
      // Simulate connection progress
      connectionProgressHandler({ progress: 50, message: 'Establishing secure channel...' });
      expect(screen.getByText('Establishing secure connection...')).toBeInTheDocument();
      
      // Simulate connection completion
      stateChangeHandler({ to: 'connected' });
      connectionCompletedHandler({ duration: 2000 });
      
      await waitFor(() => {
        expect(screen.getByText('Connected')).toBeInTheDocument();
      });
      
      // Step 4: Verify warning about unverified identity
      await waitFor(() => {
        expect(screen.getByText(/Connection established but identity not verified/)).toBeInTheDocument();
      });
      
      // Step 5: Start verification process
      const verifyButton = screen.getByText('Verify Now');
      await user.click(verifyButton);
      
      // Verification dialog should open
      expect(screen.getByText('Verify Identity')).toBeInTheDocument();
      expect(screen.getByText('Choose Verification Method')).toBeInTheDocument();
      
      // Step 6: Select QR Code verification
      const qrOption = screen.getByText('QR Code').closest('button');
      await user.click(qrOption);
      
      // Step 7: Complete verification
      const verificationCompletedHandler = mockSession.on.mock.calls.find(call => call[0] === 'verificationCompleted')[1];
      verificationCompletedHandler({ method: 'qr-code', verified: true });
      
      await waitFor(() => {
        expect(screen.getByText(/Identity verified using qr-code/)).toBeInTheDocument();
      });
      
      // Step 8: Send first message
      const messageInput = screen.getByPlaceholderText('Type a secure message...');
      await user.type(messageInput, 'Hello, secure world!');
      
      const sendButton = screen.getByText('Send');
      await user.click(sendButton);
      
      // Verify message was sent
      expect(mockSession.sendMessage).toHaveBeenCalledWith('Hello, secure world!');
      
      // Step 9: Simulate message sent confirmation
      const messageSentHandler = mockSession.on.mock.calls.find(call => call[0] === 'messageSent')[1];
      messageSentHandler({
        id: 1,
        content: 'Hello, secure world!',
        encrypted: true,
        verified: true,
        type: 'sent',
        timestamp: new Date()
      });
      
      await waitFor(() => {
        expect(screen.getByText('Hello, secure world!')).toBeInTheDocument();
        expect(screen.getByText('Encrypted')).toBeInTheDocument();
        expect(screen.getByText('Verified')).toBeInTheDocument();
      });
    });
  });

  describe('Error Recovery Journey', () => {
    it('handles connection failures gracefully', async () => {
      const user = userEvent.setup();
      render(<UXController />);
      
      // Start connection
      const startButton = screen.getByText('Start Secure Chat');
      await user.click(startButton);
      
      // Simulate connection failure
      const connectionFailedHandler = mockSession.on.mock.calls.find(call => call[0] === 'connectionFailed')[1];
      connectionFailedHandler({ message: 'Network error' });
      
      await waitFor(() => {
        expect(screen.getByText(/Connection failed/)).toBeInTheDocument();
        expect(screen.getByText('Retry')).toBeInTheDocument();
      });
      
      // Test retry functionality
      const retryButton = screen.getByText('Retry');
      await user.click(retryButton);
      
      expect(mockSession.startOTR).toHaveBeenCalledTimes(2);
    });

    it('handles verification failures with recovery options', async () => {
      const user = userEvent.setup();
      render(<UXController />);
      
      // Set up connected state
      const stateChangeHandler = mockSession.on.mock.calls.find(call => call[0] === 'stateChange')[1];
      stateChangeHandler({ to: 'connected' });
      
      await waitFor(() => {
        expect(screen.getByText('Verify Identity')).toBeInTheDocument();
      });
      
      // Start verification
      const verifyButton = screen.getByText('Verify Identity');
      await user.click(verifyButton);
      
      // Simulate verification failure
      const verificationFailedHandler = mockSession.on.mock.calls.find(call => call[0] === 'verificationFailed')[1];
      verificationFailedHandler({ method: 'qr-code', error: 'QR code scan failed' });
      
      await waitFor(() => {
        expect(screen.getByText(/Verification failed using qr-code/)).toBeInTheDocument();
      });
    });
  });

  describe('Multi-Method Verification Journey', () => {
    it('allows trying different verification methods', async () => {
      const user = userEvent.setup();
      render(<UXController />);
      
      // Set up connected state
      const stateChangeHandler = mockSession.on.mock.calls.find(call => call[0] === 'stateChange')[1];
      stateChangeHandler({ to: 'connected' });
      
      await waitFor(() => {
        expect(screen.getByText('Verify Identity')).toBeInTheDocument();
      });
      
      // Open verification dialog
      const verifyButton = screen.getByText('Verify Identity');
      await user.click(verifyButton);
      
      // Try QR Code first
      const qrOption = screen.getByText('QR Code').closest('button');
      await user.click(qrOption);
      expect(qrOption).toHaveClass('selected');
      
      // Switch to Question & Answer
      const qaOption = screen.getByText('Question & Answer').closest('button');
      await user.click(qaOption);
      expect(qaOption).toHaveClass('selected');
      expect(qrOption).not.toHaveClass('selected');
      
      // Switch to Manual Verification
      const manualOption = screen.getByText('Manual Verification').closest('button');
      await user.click(manualOption);
      expect(manualOption).toHaveClass('selected');
      expect(qaOption).not.toHaveClass('selected');
    });
  });

  describe('Message Exchange Journey', () => {
    it('handles bidirectional message exchange', async () => {
      const user = userEvent.setup();
      render(<UXController />);
      
      // Set up verified connection
      const stateChangeHandler = mockSession.on.mock.calls.find(call => call[0] === 'stateChange')[1];
      const messageSentHandler = mockSession.on.mock.calls.find(call => call[0] === 'messageSent')[1];
      const messageReceivedHandler = mockSession.on.mock.calls.find(call => call[0] === 'messageReceived')[1];
      
      stateChangeHandler({ to: 'connected' });
      
      // Send outgoing message
      const messageInput = screen.getByPlaceholderText('Type a secure message...');
      await user.type(messageInput, 'Hello from me');
      
      const sendButton = screen.getByText('Send');
      await user.click(sendButton);
      
      // Confirm message sent
      messageSentHandler({
        id: 1,
        content: 'Hello from me',
        encrypted: true,
        verified: true,
        type: 'sent',
        timestamp: new Date()
      });
      
      // Receive incoming message
      messageReceivedHandler({
        id: 2,
        content: 'Hello back to you',
        encrypted: true,
        verified: true,
        type: 'received',
        timestamp: new Date()
      });
      
      await waitFor(() => {
        expect(screen.getByText('Hello from me')).toBeInTheDocument();
        expect(screen.getByText('Hello back to you')).toBeInTheDocument();
        expect(screen.getByText('2 messages')).toBeInTheDocument();
      });
    });
  });

  describe('Session Management Journey', () => {
    it('handles complete session lifecycle', async () => {
      const user = userEvent.setup();
      render(<UXController />);
      
      // Start session
      const startButton = screen.getByText('Start Secure Chat');
      await user.click(startButton);
      
      // Complete connection
      const stateChangeHandler = mockSession.on.mock.calls.find(call => call[0] === 'stateChange')[1];
      stateChangeHandler({ to: 'connected' });
      
      await waitFor(() => {
        expect(screen.getByText('End Session')).not.toBeDisabled();
      });
      
      // End session
      const endButton = screen.getByText('End Session');
      await user.click(endButton);
      
      expect(mockSession.endOTR).toHaveBeenCalled();
      
      // Simulate session ended
      stateChangeHandler({ to: 'disconnected' });
      
      await waitFor(() => {
        expect(screen.getByText('Not Connected')).toBeInTheDocument();
        expect(screen.getByText(/OTR session ended/)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility Journey', () => {
    it('supports complete keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<UXController />);
      
      // Tab through interface
      await user.tab();
      expect(screen.getByText('Start Secure Chat')).toHaveFocus();
      
      // Start session with keyboard
      await user.keyboard('{Enter}');
      expect(mockSession.startOTR).toHaveBeenCalled();
      
      // Set up connected state
      const stateChangeHandler = mockSession.on.mock.calls.find(call => call[0] === 'stateChange')[1];
      stateChangeHandler({ to: 'connected' });
      
      // Tab to message input
      await user.tab();
      await user.tab();
      await user.tab();
      
      const messageInput = screen.getByPlaceholderText('Type a secure message...');
      expect(messageInput).toHaveFocus();
      
      // Type and send message with keyboard
      await user.type(messageInput, 'Keyboard message');
      await user.keyboard('{Enter}');
      
      expect(mockSession.sendMessage).toHaveBeenCalledWith('Keyboard message');
    });

    it('provides proper screen reader announcements', async () => {
      const user = userEvent.setup();
      render(<UXController />);
      
      // Check for proper ARIA labels
      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('status')).toBeInTheDocument();
      expect(screen.getByLabelText(/OTR session/)).toBeInTheDocument();
      
      // Start session and check status updates
      const startButton = screen.getByText('Start Secure Chat');
      await user.click(startButton);
      
      const stateChangeHandler = mockSession.on.mock.calls.find(call => call[0] === 'stateChange')[1];
      stateChangeHandler({ to: 'connected' });
      
      await waitFor(() => {
        expect(screen.getByLabelText(/OTR session connected/)).toBeInTheDocument();
      });
    });
  });

  describe('Performance Journey', () => {
    it('loads components efficiently', async () => {
      const startTime = performance.now();
      
      render(<UXController />);
      
      // Check that initial render is fast
      const renderTime = performance.now() - startTime;
      expect(renderTime).toBeLessThan(100); // Should render in under 100ms
      
      // Check that all essential elements are present
      expect(screen.getByText('WebOTR Secure Messaging')).toBeInTheDocument();
      expect(screen.getByText('Start Secure Chat')).toBeInTheDocument();
      expect(screen.getByRole('status')).toBeInTheDocument();
    });
  });
});
