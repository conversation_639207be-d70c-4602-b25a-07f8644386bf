# User Journey Flows for WebOTR UX

## Overview

This document outlines the key user journeys for WebOTR's user experience, focusing on intuitive verification workflows and clear status communication.

## Journey 1: First-Time OTR Setup

### User Goal
Start a secure conversation with a contact for the first time.

### Journey Flow

```mermaid
graph TD
    A[User opens chat] --> B[Sees 'Start Secure Chat' button]
    B --> C[Clicks button]
    C --> D[Connection progress shown]
    D --> E[OTR session established]
    E --> F[Warning: Identity not verified]
    F --> G[User clicks 'Verify Now']
    G --> H[Verification method selection]
    H --> I[Complete verification]
    I --> J[Secure verified chat]
    
    F --> K[User ignores warning]
    K --> L[Continues with unverified chat]
```

### Key UX Elements
- **Clear call-to-action**: "Start Secure Chat" button
- **Progress feedback**: Connection progress indicator
- **Security awareness**: Warning about unverified identity
- **Easy verification**: One-click access to verification

### Success Metrics
- 90% of users successfully start OTR session
- 70% of users complete verification on first attempt
- Average time to secure chat: < 30 seconds

## Journey 2: Identity Verification

### User Goal
Verify the identity of their chat partner to ensure secure communication.

### Journey Flow

```mermaid
graph TD
    A[User clicks 'Verify Identity'] --> B[Method selection screen]
    B --> C{Choose method}
    
    C --> D[QR Code Method]
    D --> E[Show/Scan QR code]
    E --> F[QR verification complete]
    
    C --> G[Question-Answer Method]
    G --> H[Enter shared secret]
    H --> I[SMP verification process]
    I --> J[SMP verification complete]
    
    C --> K[Manual Fingerprint]
    K --> L[Compare fingerprints]
    L --> M[Manual confirmation]
    
    F --> N[Identity verified]
    J --> N
    M --> N
    N --> O[Secure verified chat]
    
    E --> P[QR scan failed]
    I --> Q[SMP failed]
    L --> R[Fingerprints don't match]
    P --> S[Error recovery]
    Q --> S
    R --> S
    S --> B
```

### Verification Methods

#### 1. QR Code Verification
**Best for**: In-person meetings, video calls
- **Step 1**: User A generates QR code containing fingerprint
- **Step 2**: User B scans QR code with camera
- **Step 3**: Automatic verification and confirmation

#### 2. Question-Answer (SMP)
**Best for**: Remote verification with shared knowledge
- **Step 1**: User A creates question and answer
- **Step 2**: User B receives question and provides answer
- **Step 3**: Cryptographic verification without revealing answer

#### 3. Manual Fingerprint Comparison
**Best for**: Technical users, phone/email verification
- **Step 1**: Both users view their fingerprints
- **Step 2**: Users compare fingerprints via secure channel
- **Step 3**: Manual confirmation of match

### UX Considerations
- **Progressive disclosure**: Start with simplest method (QR code)
- **Clear instructions**: Step-by-step guidance for each method
- **Error recovery**: Clear paths when verification fails
- **Accessibility**: All methods work with screen readers

## Journey 3: Ongoing Secure Communication

### User Goal
Maintain awareness of security status during conversation.

### Journey Flow

```mermaid
graph TD
    A[User in verified chat] --> B[Sends message]
    B --> C[Message shows encrypted badge]
    C --> D[Recipient receives message]
    D --> E[Message shows verified badge]
    
    A --> F[Security status changes]
    F --> G[Alert notification]
    G --> H[User investigates]
    H --> I[Takes corrective action]
    
    A --> J[User clicks status indicator]
    J --> K[Security details panel]
    K --> L[Options to re-verify or end session]
```

### Status Indicators

#### Connection States
- **🔴 Disconnected**: No OTR session
- **🟡 Connecting**: Establishing session
- **🟢 Connected**: Session active, encrypted
- **✅ Verified**: Identity confirmed
- **⚠️ Unverified**: Connected but not verified
- **❌ Error**: Connection or security issue

#### Message Badges
- **🔒 Encrypted**: Message sent through OTR
- **🔓 Unencrypted**: Plain text message
- **✓ Verified**: From verified contact
- **? Unverified**: From unverified contact

### Security Alerts

#### Warning Scenarios
- Connection established but identity not verified
- Verification status changed or expired
- Potential man-in-the-middle attack detected
- OTR session ended unexpectedly

#### Alert Design
- **Clear language**: No technical jargon
- **Actionable**: Specific steps to resolve
- **Appropriate urgency**: Color coding by severity
- **Dismissible**: User can acknowledge and continue

## Journey 4: Error Recovery

### User Goal
Resolve security issues or connection problems.

### Journey Flow

```mermaid
graph TD
    A[Error occurs] --> B[Alert displayed]
    B --> C[User reads error message]
    C --> D{Error type}
    
    D --> E[Connection error]
    E --> F[Retry connection]
    F --> G[Success] --> H[Resume chat]
    F --> I[Still failing] --> J[Contact support]
    
    D --> K[Verification failed]
    K --> L[Try different method]
    L --> M[Success] --> H
    L --> N[Still failing] --> O[Manual verification]
    
    D --> P[Security warning]
    P --> Q[End session]
    Q --> R[Start new session]
    P --> S[Investigate further]
    S --> T[Advanced security options]
```

### Error Types and Recovery

#### Connection Errors
- **Network issues**: Retry with exponential backoff
- **Protocol errors**: Reset session and restart
- **Timeout**: Increase timeout and retry

#### Verification Errors
- **QR scan failed**: Improve lighting, try again
- **SMP failed**: Check answer, try different question
- **Fingerprint mismatch**: Verify through different channel

#### Security Warnings
- **MITM detected**: End session, verify through secure channel
- **Key changed**: Re-verify identity
- **Session compromised**: Start fresh session

### Recovery UX Principles
- **Clear explanation**: What went wrong and why
- **Multiple options**: Different ways to resolve
- **Escalation path**: When to seek help
- **Prevention tips**: How to avoid future issues

## Accessibility Considerations

### Screen Reader Support
- All status indicators have descriptive ARIA labels
- Verification steps announced clearly
- Error messages read with appropriate urgency
- Progress indicators provide percentage updates

### Keyboard Navigation
- All interactive elements accessible via keyboard
- Logical tab order through verification flows
- Escape key closes dialogs and cancels operations
- Enter/Space activates buttons and confirmations

### Visual Accessibility
- High contrast ratios (4.5:1 minimum)
- Color not the only indicator of status
- Large touch targets (44px minimum)
- Clear visual hierarchy and spacing

### Cognitive Accessibility
- Simple, clear language throughout
- Consistent interaction patterns
- Progress indicators for multi-step processes
- Confirmation before destructive actions

## Testing Strategy

### Usability Testing
- Task completion rates for each journey
- Time to complete verification
- Error recovery success rates
- User satisfaction scores

### Accessibility Testing
- Screen reader compatibility
- Keyboard navigation testing
- Color contrast validation
- Cognitive load assessment

### Security Testing
- Verification method effectiveness
- Error handling robustness
- Alert appropriateness and timing
- Recovery path validation
