# WebOTR Design System

## Overview

The WebOTR Design System provides a comprehensive set of design tokens, components, and patterns for creating consistent, accessible, and user-friendly OTR messaging experiences.

## Design Principles

### 1. Security First
- Clear visual hierarchy for security states
- Unmistakable indicators for encrypted/unencrypted states
- Prominent warnings for security issues

### 2. Accessibility by Default
- WCAG 2.1 AA compliance
- High contrast ratios (4.5:1 minimum)
- Keyboard navigation support
- Screen reader compatibility

### 3. Progressive Disclosure
- Simple interfaces that reveal complexity when needed
- Step-by-step verification processes
- Clear entry points for advanced features

### 4. Platform Agnostic
- Consistent experience across different messaging platforms
- Adaptable styling for platform integration
- Responsive design for various screen sizes

## Color Palette

### Primary Colors
- **Primary Blue**: `#2563eb` - Main brand color, call-to-action buttons
- **Primary Dark**: `#1d4ed8` - Hover states, active elements
- **Primary Light**: `#3b82f6` - Backgrounds, subtle highlights

### Security States
- **Secure Green**: `#10b981` - Verified, encrypted, trusted states
- **Warning Orange**: `#f59e0b` - Caution, unverified states
- **Danger Red**: `#ef4444` - Errors, security threats, unencrypted

### Neutral Colors
- **Text Primary**: `#1e293b` - Main text content
- **Text Secondary**: `#64748b` - Supporting text, labels
- **Text Muted**: `#94a3b8` - Placeholder text, disabled states
- **Background**: `#ffffff` - Main background
- **Surface**: `#f8fafc` - Card backgrounds, panels
- **Border**: `#e2e8f0` - Dividers, input borders

## Typography

### Font Stack
```css
font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
```

### Type Scale
- **Heading 1**: 2.25rem (36px) - Page titles
- **Heading 2**: 1.875rem (30px) - Section headers
- **Heading 3**: 1.5rem (24px) - Subsection headers
- **Heading 4**: 1.25rem (20px) - Component titles
- **Body Large**: 1.125rem (18px) - Important body text
- **Body**: 1rem (16px) - Default body text
- **Body Small**: 0.875rem (14px) - Supporting text
- **Caption**: 0.75rem (12px) - Labels, captions

## Spacing Scale

Based on 0.25rem (4px) increments:
- **xs**: 0.25rem (4px)
- **sm**: 0.5rem (8px)
- **md**: 1rem (16px)
- **lg**: 1.5rem (24px)
- **xl**: 2rem (32px)
- **2xl**: 3rem (48px)
- **3xl**: 4rem (64px)

## Component Categories

### 1. Status Indicators
- Session status badges
- Verification level indicators
- Security warning icons
- Progress indicators

### 2. Verification Components
- QR code scanner/generator
- Question-answer dialogs
- Fingerprint comparison
- Trust level selectors

### 3. Authentication Dialogs
- Modal overlays
- Step-by-step wizards
- Error recovery flows
- Success confirmations

### 4. Navigation Elements
- Tab navigation
- Breadcrumbs
- Action buttons
- Menu systems

## Accessibility Guidelines

### Color Contrast
- Text on background: minimum 4.5:1 ratio
- Large text (18px+): minimum 3:1 ratio
- Interactive elements: minimum 3:1 ratio

### Focus Management
- Visible focus indicators on all interactive elements
- Logical tab order
- Focus trapping in modals
- Skip links for keyboard navigation

### Screen Reader Support
- Semantic HTML structure
- ARIA labels and descriptions
- Live regions for dynamic content
- Alternative text for images and icons

### Keyboard Navigation
- All functionality accessible via keyboard
- Standard keyboard shortcuts
- Escape key closes modals
- Enter/Space activates buttons

## Implementation Notes

### CSS Custom Properties
All design tokens are implemented as CSS custom properties for easy theming and maintenance.

### Component Library
Components are built using React with TypeScript for type safety and better developer experience.

### Testing
All components include accessibility tests using axe-core and manual testing with screen readers.

## Usage Examples

```jsx
// Status indicator
<StatusBadge status="verified" size="md" />

// Verification dialog
<VerificationDialog 
  type="qr-code" 
  onComplete={handleVerification}
  onCancel={handleCancel}
/>

// Security warning
<SecurityAlert 
  level="warning" 
  message="Connection is not encrypted"
  action="Start OTR"
/>
```
