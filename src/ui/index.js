/**
 * UI Components for WebOTR
 * 
 * This file exports UI components used by the browser extensions.
 */

import { OtrToggle, createToggleButton } from './components/toggle';
import { OtrStatus, createStatusIndicator } from './components/status';
import { VerificationDialog, createVerificationDialog } from './dialogs/verification';
import { Notifications, createNotification } from './notifications';

export {
  OtrToggle,
  OtrStatus,
  VerificationDialog,
  Notifications,
  createToggleButton,
  createStatusIndicator,
  createVerificationDialog,
  createNotification
};

/**
 * Initialize UI components
 * @param {Object} platform - Platform adapter
 */
export function initUI(platform) {
  // Implementation of UI initialization
} 