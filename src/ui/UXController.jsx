import React, { useState, useEffect, useCallback } from 'react';
import { UXOtrSession } from '../core/session/UXOtrSession.js';
import VerificationDialog from './components/verification/VerificationDialog.jsx';
import { StatusIndicator, StatusBadge, SecurityAlert, ProgressIndicator } from './components/status/StatusIndicator.jsx';
import './UXController.css';

/**
 * UXController - Enhanced UI controller for OTR interactions
 * 
 * Provides comprehensive user experience with:
 * - Intuitive verification workflows
 * - Clear status indicators
 * - User-friendly authentication dialogs
 * - Error handling and notifications
 */
const UXController = () => {
  // Core state
  const [session, setSession] = useState(null);
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [status, setStatus] = useState('disconnected');
  
  // UX state
  const [showVerificationDialog, setShowVerificationDialog] = useState(false);
  const [verificationMethod, setVerificationMethod] = useState('qr-code');
  const [contactName, setContactName] = useState('Contact');
  const [fingerprint, setFingerprint] = useState('');
  const [isVerified, setIsVerified] = useState(false);
  const [alerts, setAlerts] = useState([]);
  const [connectionProgress, setConnectionProgress] = useState(0);
  const [isConnecting, setIsConnecting] = useState(false);

  // Initialize enhanced OTR session
  useEffect(() => {
    const uxOtrSession = new UXOtrSession({
      autoConnect: false,
      progressUpdates: true,
      verificationRequired: true
    });
    setSession(uxOtrSession);

    // Set up enhanced event listeners
    uxOtrSession.on('stateChange', handleStateChange);
    uxOtrSession.on('messageReceived', handleMessageReceived);
    uxOtrSession.on('messageSent', handleMessageSent);
    uxOtrSession.on('fingerprintGenerated', handleFingerprintGenerated);
    uxOtrSession.on('contactFingerprintReceived', handleContactFingerprintReceived);
    uxOtrSession.on('verificationRequired', handleVerificationRequired);
    uxOtrSession.on('verificationCompleted', handleVerificationCompleted);
    uxOtrSession.on('verificationFailed', handleVerificationFailed);
    uxOtrSession.on('connectionProgress', handleConnectionProgress);
    uxOtrSession.on('connectionStarted', handleConnectionStarted);
    uxOtrSession.on('connectionCompleted', handleConnectionCompleted);
    uxOtrSession.on('connectionFailed', handleConnectionFailed);
    uxOtrSession.on('error', handleError);

    return () => {
      if (uxOtrSession) {
        uxOtrSession.disconnect();
      }
    };
  }, []);

  // Enhanced event handlers
  const handleStateChange = useCallback((stateData) => {
    const newState = stateData.to || stateData;
    setStatus(newState);

    // Show appropriate alerts based on state changes
    if (newState === 'connected' && !isVerified) {
      addAlert({
        level: 'warning',
        message: 'Connection established but identity not verified. Consider verifying your contact.',
        action: 'Verify Now',
        onAction: () => setShowVerificationDialog(true)
      });
    } else if (newState === 'verified') {
      addAlert({
        level: 'success',
        message: 'Identity verified! Your conversation is now fully secure.'
      });
    } else if (newState === 'error') {
      setIsConnecting(false);
      setConnectionProgress(0);
    }
  }, [isVerified]);

  const handleMessageReceived = useCallback((message) => {
    setMessages(prev => [...prev, message]);
  }, []);

  const handleMessageSent = useCallback((message) => {
    setMessages(prev => [...prev, message]);
  }, []);

  const handleFingerprintGenerated = useCallback((fp) => {
    setFingerprint(fp);
  }, []);

  const handleContactFingerprintReceived = useCallback((fp) => {
    // Store contact fingerprint for verification
    setContactName('Contact'); // In real app, this would come from contact info
  }, []);

  const handleVerificationRequired = useCallback(() => {
    setShowVerificationDialog(true);
  }, []);

  const handleVerificationCompleted = useCallback((result) => {
    setIsVerified(result.verified);
    if (result.verified) {
      addAlert({
        level: 'success',
        message: `Identity verified using ${result.method}. Your conversation is now secure.`
      });
    }
  }, []);

  const handleVerificationFailed = useCallback((result) => {
    addAlert({
      level: 'error',
      message: `Verification failed using ${result.method}. ${result.error || 'Please try again.'}`
    });
  }, []);

  const handleConnectionProgress = useCallback((progressData) => {
    setConnectionProgress(progressData.progress || progressData);
  }, []);

  const handleConnectionStarted = useCallback(() => {
    setIsConnecting(true);
    setConnectionProgress(0);
  }, []);

  const handleConnectionCompleted = useCallback((result) => {
    setIsConnecting(false);
    setConnectionProgress(100);
    addAlert({
      level: 'info',
      message: `Secure connection established in ${Math.round(result.duration / 1000)}s`
    });
  }, []);

  const handleConnectionFailed = useCallback((error) => {
    setIsConnecting(false);
    setConnectionProgress(0);
    addAlert({
      level: 'error',
      message: error.message || 'Connection failed. Please try again.',
      action: 'Retry',
      onAction: () => handleStartOTR()
    });
  }, []);

  const handleError = useCallback((error) => {
    addAlert({
      level: 'error',
      message: error.message || 'An error occurred with the OTR connection.',
      action: 'Retry',
      onAction: () => session?.startOTR?.()
    });
  }, [session]);

  // UI actions
  const addAlert = (alert) => {
    const alertWithId = {
      ...alert,
      id: Date.now(),
      onDismiss: () => removeAlert(alertWithId.id)
    };
    setAlerts(prev => [...prev, alertWithId]);
    
    // Auto-dismiss info alerts after 5 seconds
    if (alert.level === 'info' || alert.level === 'success') {
      setTimeout(() => removeAlert(alertWithId.id), 5000);
    }
  };

  const removeAlert = (alertId) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || !session || status !== 'connected') return;

    try {
      await session.sendMessage(inputMessage);
      setInputMessage('');
    } catch (error) {
      addAlert({
        level: 'error',
        message: 'Failed to send message. Please check your connection.',
        action: 'Retry',
        onAction: () => handleSendMessage()
      });
    }
  };

  const handleStartOTR = async () => {
    if (!session) return;

    try {
      addAlert({
        level: 'info',
        message: 'Starting secure OTR session...'
      });

      await session.startOTR();
    } catch (error) {
      addAlert({
        level: 'error',
        message: 'Failed to start OTR session. Please try again.',
        action: 'Retry',
        onAction: () => handleStartOTR()
      });
    }
  };

  const handleEndOTR = async () => {
    if (!session) return;

    try {
      await session.endOTR();
      setIsVerified(false);
      addAlert({
        level: 'info',
        message: 'OTR session ended. Messages are no longer encrypted.'
      });
    } catch (error) {
      addAlert({
        level: 'error',
        message: 'Failed to end OTR session properly.'
      });
    }
  };

  const handleVerificationComplete = async (result) => {
    setShowVerificationDialog(false);

    try {
      if (session && result.verified) {
        await session.verifyIdentity(result.method, result.data);
      }
    } catch (error) {
      addAlert({
        level: 'error',
        message: 'Verification process failed. Please try again.'
      });
    }
  };

  const handleStatusClick = (currentStatus) => {
    if (currentStatus === 'unverified' || currentStatus === 'connected') {
      setShowVerificationDialog(true);
    }
  };

  return (
    <div className="ux-controller">
      <div className="header">
        <div className="title-section">
          <h1>WebOTR Secure Messaging</h1>
          <p className="subtitle">End-to-end encrypted conversations</p>
        </div>
        
        <div className="status-section">
          <StatusIndicator
            status={status === 'connected' ? (isVerified ? 'verified' : 'unverified') : status}
            size="lg"
            showDetails={true}
            onClick={handleStatusClick}
          />
        </div>
      </div>

      {/* Connection Progress */}
      {isConnecting && (
        <ProgressIndicator
          progress={connectionProgress}
          status="Establishing secure connection..."
          showPercentage={true}
        />
      )}

      {/* Alerts */}
      <div className="alerts-container">
        {alerts.map(alert => (
          <SecurityAlert
            key={alert.id}
            level={alert.level}
            message={alert.message}
            action={alert.action}
            onAction={alert.onAction}
            onDismiss={alert.onDismiss}
          />
        ))}
      </div>
      
      <div className="controls">
        <button 
          className="button primary"
          onClick={handleStartOTR} 
          disabled={status === 'connected' || isConnecting}
        >
          {isConnecting ? 'Connecting...' : 'Start Secure Chat'}
        </button>
        
        <button 
          className="button secondary"
          onClick={handleEndOTR} 
          disabled={status !== 'connected'}
        >
          End Session
        </button>
        
        <button 
          className="button secondary"
          onClick={() => setShowVerificationDialog(true)}
          disabled={status !== 'connected'}
        >
          Verify Identity
        </button>
      </div>
      
      <div className="messages">
        <div className="messages-header">
          <h3>Messages</h3>
          <div className="message-count">
            {messages.length} message{messages.length !== 1 ? 's' : ''}
          </div>
        </div>
        
        <div className="messages-list">
          {messages.length === 0 ? (
            <div className="empty-state">
              <div className="empty-icon">💬</div>
              <p>No messages yet. Start a conversation!</p>
            </div>
          ) : (
            messages.map(message => (
              <div key={message.id} className={`message ${message.type}`}>
                <div className="message-header">
                  <div className="message-badges">
                    <StatusBadge 
                      status={message.encrypted ? 'encrypted' : 'unencrypted'} 
                      size="xs"
                    />
                    {message.verified && (
                      <StatusBadge status="verified" size="xs" />
                    )}
                  </div>
                  <div className="message-time">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
                <div className="message-content">{message.content}</div>
              </div>
            ))
          )}
        </div>
      </div>
      
      <div className="input-area">
        <div className="input-wrapper">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder={status === 'connected' ? 'Type a secure message...' : 'Start OTR to send encrypted messages'}
            disabled={status !== 'connected'}
            className="message-input"
          />
          <button 
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || status !== 'connected'}
            className="send-button"
          >
            Send
          </button>
        </div>
        
        {status !== 'connected' && (
          <div className="input-hint">
            <span className="hint-icon">🔒</span>
            Start an OTR session to send encrypted messages
          </div>
        )}
      </div>

      {/* Verification Dialog */}
      <VerificationDialog
        isOpen={showVerificationDialog}
        onClose={() => setShowVerificationDialog(false)}
        onComplete={handleVerificationComplete}
        verificationMethod={verificationMethod}
        contactName={contactName}
        fingerprint={fingerprint}
      />
    </div>
  );
};

export default UXController;
