<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebOTR UX Components Demo</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 2rem;
            background: #f8fafc;
            line-height: 1.6;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .demo-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        
        .demo-header p {
            font-size: 1.125rem;
            color: #64748b;
            margin: 0;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .demo-section h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 0.5rem;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 1.5rem;
        }
        
        .demo-item {
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .demo-item h3 {
            margin: 0 0 1rem 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: #374151;
        }
        
        .demo-item p {
            margin: 0 0 1rem 0;
            color: #64748b;
            font-size: 0.875rem;
        }
        
        /* Status Indicator Styles (inline for demo) */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
            border-radius: 8px;
            margin: 0.5rem;
        }
        
        .status-indicator.success { color: #10b981; }
        .status-indicator.warning { color: #f59e0b; }
        .status-indicator.danger { color: #ef4444; }
        .status-indicator.verified { color: #059669; }
        
        .status-icon { font-size: 1.25rem; }
        .status-content { display: flex; flex-direction: column; gap: 0.25rem; }
        .status-label { font-weight: 600; font-size: 0.875rem; }
        .status-description { font-size: 0.75rem; color: #64748b; }
        
        /* Status Badge Styles */
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            margin: 0.25rem;
        }
        
        .status-badge.success { background: #d1fae5; color: #065f46; }
        .status-badge.warning { background: #fef3c7; color: #92400e; }
        .status-badge.danger { background: #fee2e2; color: #991b1b; }
        .status-badge.verified { background: #d1fae5; color: #047857; }
        
        /* Security Alert Styles */
        .security-alert {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid;
            margin: 1rem 0;
        }
        
        .security-alert.info { background: #eff6ff; border-color: #bfdbfe; color: #1e40af; }
        .security-alert.warning { background: #fffbeb; border-color: #fed7aa; color: #92400e; }
        .security-alert.danger { background: #fef2f2; border-color: #fecaca; color: #991b1b; }
        .security-alert.success { background: #f0fdf4; border-color: #bbf7d0; color: #166534; }
        
        .alert-icon { font-size: 1.25rem; flex-shrink: 0; }
        .alert-content { flex: 1; }
        .alert-message { font-weight: 500; line-height: 1.4; }
        
        /* Button Styles */
        .button {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            border: none;
            margin: 0.25rem;
            transition: all 0.2s ease;
        }
        
        .button.primary { background: #2563eb; color: white; }
        .button.secondary { background: white; color: #374151; border: 1px solid #d1d5db; }
        .button:hover { transform: translateY(-1px); }
        
        /* Verification Dialog Preview */
        .dialog-preview {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1.5rem;
            background: white;
            max-width: 400px;
            margin: 1rem auto;
        }
        
        .dialog-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .method-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .method-option {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .method-option:hover { border-color: #cbd5e1; background: #f8fafc; }
        .method-option.selected { border-color: #2563eb; background: #eff6ff; }
        
        .method-icon { font-size: 2rem; }
        .method-title { font-weight: 600; color: #1e293b; }
        .method-description { font-size: 0.875rem; color: #64748b; }
        
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>WebOTR UX Components</h1>
            <p>Interactive demonstration of user experience components for secure messaging</p>
        </div>

        <!-- Status Indicators Section -->
        <div class="demo-section">
            <h2>Status Indicators</h2>
            <p>Clear visual indicators for OTR session status and security levels.</p>
            
            <div class="demo-grid">
                <div class="demo-item">
                    <h3>Session Status</h3>
                    <p>Shows the current state of the OTR connection.</p>
                    
                    <div class="status-indicator danger">
                        <div class="status-icon">🔴</div>
                        <div class="status-content">
                            <div class="status-label">Not Connected</div>
                            <div class="status-description">No OTR session active</div>
                        </div>
                    </div>
                    
                    <div class="status-indicator warning">
                        <div class="status-icon">🟡</div>
                        <div class="status-content">
                            <div class="status-label">Connecting</div>
                            <div class="status-description">Establishing OTR session</div>
                        </div>
                    </div>
                    
                    <div class="status-indicator success">
                        <div class="status-icon">🟢</div>
                        <div class="status-content">
                            <div class="status-label">Connected</div>
                            <div class="status-description">Messages encrypted</div>
                        </div>
                    </div>
                    
                    <div class="status-indicator verified">
                        <div class="status-icon">✅</div>
                        <div class="status-content">
                            <div class="status-label">Verified</div>
                            <div class="status-description">Identity confirmed</div>
                        </div>
                    </div>
                </div>
                
                <div class="demo-item">
                    <h3>Message Badges</h3>
                    <p>Compact indicators for individual messages.</p>
                    
                    <div>
                        <span class="status-badge success">
                            <span>🔒</span>
                            <span>Encrypted</span>
                        </span>
                        
                        <span class="status-badge danger">
                            <span>🔓</span>
                            <span>Unencrypted</span>
                        </span>
                        
                        <span class="status-badge verified">
                            <span>✓</span>
                            <span>Verified</span>
                        </span>
                        
                        <span class="status-badge warning">
                            <span>?</span>
                            <span>Unverified</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Alerts Section -->
        <div class="demo-section">
            <h2>Security Alerts</h2>
            <p>Contextual notifications for security events and user guidance.</p>
            
            <div class="security-alert info">
                <div class="alert-icon">ℹ️</div>
                <div class="alert-content">
                    <div class="alert-message">Starting secure OTR session...</div>
                </div>
            </div>
            
            <div class="security-alert warning">
                <div class="alert-icon">⚠️</div>
                <div class="alert-content">
                    <div class="alert-message">Connection established but identity not verified. Consider verifying your contact.</div>
                </div>
            </div>
            
            <div class="security-alert danger">
                <div class="alert-icon">❌</div>
                <div class="alert-content">
                    <div class="alert-message">Potential security issue detected. Please verify your contact's identity.</div>
                </div>
            </div>
            
            <div class="security-alert success">
                <div class="alert-icon">✅</div>
                <div class="alert-content">
                    <div class="alert-message">Identity verified using QR code. Your conversation is now secure.</div>
                </div>
            </div>
        </div>

        <!-- Verification Dialog Section -->
        <div class="demo-section">
            <h2>Verification Dialog</h2>
            <p>Step-by-step identity verification with multiple methods.</p>
            
            <div class="dialog-preview">
                <div class="dialog-header">
                    <h3>Verify Identity</h3>
                    <span>✕</span>
                </div>
                
                <div>
                    <h4>Choose Verification Method</h4>
                    <div class="method-options">
                        <div class="method-option selected">
                            <div class="method-icon">📱</div>
                            <div>
                                <div class="method-title">QR Code</div>
                                <div class="method-description">Scan or show QR code</div>
                            </div>
                        </div>
                        
                        <div class="method-option">
                            <div class="method-icon">❓</div>
                            <div>
                                <div class="method-title">Question & Answer</div>
                                <div class="method-description">Share a secret question</div>
                            </div>
                        </div>
                        
                        <div class="method-option">
                            <div class="method-icon">🔍</div>
                            <div>
                                <div class="method-title">Manual Verification</div>
                                <div class="method-description">Compare fingerprints manually</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Implementation Section -->
        <div class="demo-section">
            <h2>Implementation</h2>
            <p>React components with TypeScript support and accessibility features.</p>
            
            <div class="demo-grid">
                <div class="demo-item">
                    <h3>Usage Example</h3>
                    <div class="code-block">
&lt;StatusIndicator
  status="verified"
  size="lg"
  showDetails={true}
  onClick={handleStatusClick}
/&gt;

&lt;VerificationDialog
  isOpen={showDialog}
  onComplete={handleVerification}
  verificationMethod="qr-code"
/&gt;</div>
                </div>
                
                <div class="demo-item">
                    <h3>Accessibility Features</h3>
                    <ul>
                        <li>WCAG 2.1 AA compliance</li>
                        <li>Screen reader support</li>
                        <li>Keyboard navigation</li>
                        <li>High contrast ratios</li>
                        <li>Focus management</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Controls Section -->
        <div class="demo-section">
            <h2>Interactive Demo</h2>
            <p>Try the components in action.</p>
            
            <div style="text-align: center; padding: 2rem;">
                <button class="button primary" onclick="alert('Starting OTR session...')">
                    Start Secure Chat
                </button>
                <button class="button secondary" onclick="alert('Opening verification dialog...')">
                    Verify Identity
                </button>
                <button class="button secondary" onclick="alert('Ending OTR session...')">
                    End Session
                </button>
            </div>
        </div>
    </div>

    <script>
        // Add interactivity to method options
        document.querySelectorAll('.method-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.method-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
    </script>
</body>
</html>
