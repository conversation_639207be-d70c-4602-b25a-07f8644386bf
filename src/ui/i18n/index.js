/**
 * Internationalization (i18n) system for WebOTR UX
 * 
 * Provides multi-language support for all user-facing text
 * with fallback to English and context-aware translations
 */

// Default language
const DEFAULT_LANGUAGE = 'en';

// Available languages
export const SUPPORTED_LANGUAGES = {
  en: 'English',
  es: 'Español',
  fr: 'Français',
  de: 'Deutsch',
  it: 'Italiano',
  pt: 'Português',
  ru: 'Русский',
  zh: '中文',
  ja: '日本語',
  ko: '한국어'
};

// Translation strings
const translations = {
  en: {
    // Common
    common: {
      ok: 'OK',
      cancel: 'Cancel',
      close: 'Close',
      back: 'Back',
      next: 'Next',
      continue: 'Continue',
      retry: 'Retry',
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      warning: 'Warning',
      info: 'Information'
    },
    
    // Main interface
    main: {
      title: 'WebOTR Secure Messaging',
      subtitle: 'End-to-end encrypted conversations',
      startSecureChat: 'Start Secure Chat',
      endSession: 'End Session',
      verifyIdentity: 'Verify Identity',
      connecting: 'Connecting...',
      messages: 'Messages',
      messageCount: '{count} message{count, plural, one {} other {s}}',
      noMessages: 'No messages yet. Start a conversation!',
      typeMessage: 'Type a secure message...',
      typeMessageDisabled: 'Start OTR to send encrypted messages',
      send: 'Send',
      startOtrHint: 'Start an OTR session to send encrypted messages'
    },
    
    // Status indicators
    status: {
      disconnected: 'Not Connected',
      disconnectedDesc: 'No OTR session active',
      connecting: 'Connecting',
      connectingDesc: 'Establishing OTR session',
      connected: 'Connected',
      connectedDesc: 'OTR session active, messages encrypted',
      verified: 'Verified',
      verifiedDesc: 'Identity verified, secure communication',
      unverified: 'Unverified',
      unverifiedDesc: 'Connected but identity not verified',
      error: 'Error',
      errorDesc: 'Connection error or security issue',
      encrypted: 'Encrypted',
      unencrypted: 'Unencrypted'
    },
    
    // Verification
    verification: {
      title: 'Verify Identity',
      chooseMethod: 'Choose Verification Method',
      qrCode: 'QR Code',
      qrCodeDesc: 'Scan or show QR code',
      questionAnswer: 'Question & Answer',
      questionAnswerDesc: 'Share a secret question',
      manualVerification: 'Manual Verification',
      manualVerificationDesc: 'Compare fingerprints manually',
      step: 'Step {step}',
      scanCode: 'Scan Code',
      verify: 'Verify',
      question: 'Question',
      answer: 'Answer',
      compare: 'Compare',
      confirm: 'Confirm',
      verificationComplete: 'Verification Complete',
      verificationFailed: 'Verification Failed',
      verifyingIdentity: 'Verifying identity...'
    },
    
    // QR Code verification
    qrCode: {
      showToContact: 'Show this QR code to {contactName}',
      scanContact: 'Scan {contactName}\'s QR code',
      startCamera: 'Start Camera',
      stopScanning: 'Stop Scanning',
      positionCode: 'Position QR code within the frame',
      cameraFailed: 'Camera access failed: {error}',
      noCameraDetected: 'No camera detected on this device',
      enterManually: 'Enter Code Manually',
      pasteCode: 'Paste the QR code data or fingerprint here...',
      verifyCode: 'Verify Code',
      scannedCode: 'I\'ve Scanned Their Code',
      qrVerified: 'QR Code Verified!',
      qrVerificationComplete: 'The QR code has been successfully verified.',
      completeVerification: 'Complete Verification'
    },
    
    // Question-Answer verification
    questionAnswer: {
      createQuestion: 'Create a Verification Question',
      questionDescription: 'Think of a question that only you and {contactName} would know the answer to. This could be about a shared experience, memory, or secret.',
      questionLabel: 'Question for {contactName}',
      enterQuestion: 'Enter your question here...',
      showExamples: 'Show Example Questions',
      hideExamples: 'Hide Example Questions',
      exampleQuestions: 'Here are some example questions:',
      privacyNote: 'Privacy Note: Your answer will be verified cryptographically without being revealed to {contactName} or anyone else.',
      provideAnswer: 'Provide Your Answer',
      answerQuestion: 'Answer {contactName}\'s Question',
      yourAnswer: 'Your Answer',
      enterAnswer: 'Enter your answer...',
      answerTips: 'Tips for a good answer:',
      answerTip1: 'Be specific and exact',
      answerTip2: 'Use the same spelling and capitalization',
      answerTip3: 'Avoid extra spaces or punctuation',
      answerTip4: 'Consider case sensitivity',
      verifyAnswer: 'Verify Answer',
      verifying: 'Verifying...',
      verificationSuccessful: 'Verification Successful!',
      answersMatch: 'Both answers match. Your identity has been verified through shared knowledge.',
      verificationFailed: 'Verification Failed',
      answersNoMatch: 'The answers don\'t match. This could mean:',
      differentAnswers: 'Different answers were provided',
      spellingDifferences: 'Spelling or capitalization differences',
      misunderstood: 'One person misunderstood the question',
      securityIssue: 'Potential security issue',
      tryDifferentAnswer: 'Try Different Answer',
      tryDifferentQuestion: 'Try Different Question'
    },
    
    // Manual fingerprint verification
    manualFingerprint: {
      compareFingerprints: 'Compare Fingerprints',
      comparisonInstructions: 'Compare your fingerprint with {contactName}\'s fingerprint through a secure channel (phone call, in person, encrypted message, etc.).',
      yourFingerprint: 'Your Fingerprint',
      contactFingerprint: '{contactName}\'s Fingerprint',
      copy: 'Copy',
      copied: 'Copied',
      askToShare: 'Ask {contactName} to share their fingerprint with you',
      secureChannels: 'Secure Verification Channels',
      phoneCall: 'Phone Call',
      phoneCallDesc: 'Read fingerprints to each other',
      inPerson: 'In Person',
      inPersonDesc: 'Compare fingerprints face-to-face',
      encryptedEmail: 'Encrypted Email',
      encryptedEmailDesc: 'Exchange via secure email',
      secureMessaging: 'Secure Messaging',
      secureMessagingDesc: 'Use another encrypted app',
      verificationMethod: 'How did you verify the fingerprints? (Optional)',
      selectMethod: 'Select verification method...',
      additionalNotes: 'Additional Notes (Optional)',
      addNotes: 'Add any additional verification details...',
      showAdvanced: 'Show Advanced Options',
      hideAdvanced: 'Hide Advanced Options',
      confirmVerification: 'Confirm Verification',
      fingerprintsMatch: 'Do the fingerprints match exactly?',
      compareCarefully: 'Compare every character carefully. The fingerprints must match completely for secure verification.',
      fingerprintsNoMatch: 'Fingerprints Don\'t Match',
      fingerprintsDoMatch: 'Fingerprints Match',
      verificationSuccessful: 'Verification Successful!',
      manualVerificationComplete: 'You have successfully verified {contactName}\'s identity through manual fingerprint comparison.',
      verificationFailed: 'Verification Failed',
      fingerprintMismatch: 'The fingerprints don\'t match. This could indicate a security issue or an error in the verification process.',
      tryAgain: 'Try Again',
      cancelVerification: 'Cancel Verification',
      important: 'Important: Only confirm if you are absolutely certain the fingerprints match and you verified them through a secure channel.'
    },
    
    // Alerts and notifications
    alerts: {
      startingSession: 'Starting secure OTR session...',
      connectionEstablished: 'Connection established but identity not verified. Consider verifying your contact.',
      verifyNow: 'Verify Now',
      identityVerified: 'Identity verified! Your conversation is now fully secure.',
      sessionEnded: 'OTR session ended. Messages are no longer encrypted.',
      connectionFailed: 'Connection failed. Please try again.',
      verificationCompleted: 'Identity verified using {method}. Your conversation is now secure.',
      verificationFailed: 'Verification failed using {method}. Please try again.',
      messageFailed: 'Failed to send message. Please check your connection.',
      establishingConnection: 'Establishing secure connection...',
      connectionEstablishedIn: 'Secure connection established in {duration}s'
    },
    
    // Progress messages
    progress: {
      initializingProtocol: 'Initializing OTR protocol...',
      generatingKeys: 'Generating keys...',
      establishingChannel: 'Establishing secure channel...',
      verifyingConnection: 'Verifying connection...',
      connectionEstablished: 'Connection established'
    },
    
    // Accessibility
    a11y: {
      sessionDisconnected: 'OTR session disconnected',
      sessionConnecting: 'OTR session connecting',
      sessionConnected: 'OTR session connected and encrypted',
      sessionVerified: 'OTR session verified and secure',
      sessionError: 'OTR session error',
      messageEncrypted: 'Message is encrypted',
      messageUnencrypted: 'Message is unencrypted',
      messageVerified: 'Message is verified',
      dismissAlert: 'Dismiss alert',
      closeDialog: 'Close dialog'
    }
  }
  
  // Additional languages would be added here
  // es: { ... },
  // fr: { ... },
  // etc.
};

// Current language state
let currentLanguage = DEFAULT_LANGUAGE;

// Detect browser language
export function detectBrowserLanguage() {
  const browserLang = navigator.language || navigator.userLanguage;
  const langCode = browserLang.split('-')[0];
  return SUPPORTED_LANGUAGES[langCode] ? langCode : DEFAULT_LANGUAGE;
}

// Set current language
export function setLanguage(language) {
  if (SUPPORTED_LANGUAGES[language]) {
    currentLanguage = language;
    // Trigger re-render in React components
    window.dispatchEvent(new CustomEvent('languageChanged', { detail: language }));
    return true;
  }
  return false;
}

// Get current language
export function getCurrentLanguage() {
  return currentLanguage;
}

// Get translation with interpolation support
export function t(key, params = {}) {
  const keys = key.split('.');
  let translation = translations[currentLanguage];
  
  // Navigate through nested keys
  for (const k of keys) {
    if (translation && typeof translation === 'object') {
      translation = translation[k];
    } else {
      translation = undefined;
      break;
    }
  }
  
  // Fallback to English if translation not found
  if (translation === undefined && currentLanguage !== DEFAULT_LANGUAGE) {
    let fallback = translations[DEFAULT_LANGUAGE];
    for (const k of keys) {
      if (fallback && typeof fallback === 'object') {
        fallback = fallback[k];
      } else {
        fallback = undefined;
        break;
      }
    }
    translation = fallback;
  }
  
  // Return key if no translation found
  if (translation === undefined) {
    console.warn(`Translation not found for key: ${key}`);
    return key;
  }
  
  // Handle interpolation
  if (typeof translation === 'string' && Object.keys(params).length > 0) {
    return interpolate(translation, params);
  }
  
  return translation;
}

// Simple interpolation function
function interpolate(template, params) {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return params[key] !== undefined ? params[key] : match;
  });
}

// React hook for translations
export function useTranslation() {
  const [, forceUpdate] = React.useReducer(x => x + 1, 0);
  
  React.useEffect(() => {
    const handleLanguageChange = () => forceUpdate();
    window.addEventListener('languageChanged', handleLanguageChange);
    return () => window.removeEventListener('languageChanged', handleLanguageChange);
  }, []);
  
  return {
    t,
    currentLanguage,
    setLanguage,
    supportedLanguages: SUPPORTED_LANGUAGES
  };
}

// Initialize with browser language
if (typeof window !== 'undefined') {
  const browserLang = detectBrowserLanguage();
  setLanguage(browserLang);
}

export default {
  t,
  setLanguage,
  getCurrentLanguage,
  detectBrowserLanguage,
  useTranslation,
  SUPPORTED_LANGUAGES
};
