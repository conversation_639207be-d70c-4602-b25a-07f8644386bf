<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Import Test</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .log-container {
      background-color: #f5f5f5;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      margin-top: 20px;
      font-family: monospace;
      height: 300px;
      overflow-y: auto;
    }
    
    .success {
      color: #2ecc71;
    }
    
    .error {
      color: #e74c3c;
    }
    
    .info {
      color: #3498db;
    }
  </style>
</head>
<body>
  <h1>QR Code Verification Test</h1>
  <p>This page tests the QR code verification functionality.</p>
  
  <div style="margin-bottom: 20px;">
    <button id="test-button">Test QR Code Generation</button>
    <button id="test-scanner-button" style="margin-left: 10px;">Test QR Scanner</button>
    <button id="test-manual-button" style="margin-left: 10px;">Test Manual Entry</button>
  </div>
  <div id="qr-container" style="margin-top: 20px;"></div>
  
  <h2>Console Log</h2>
  <div id="log" class="log-container"></div>
  
  <script type="module">
    // Log function
    function log(message, type = 'info') {
      console.log(message);
      
      const logElement = document.getElementById('log');
      const entry = document.createElement('div');
      entry.className = type;
      entry.textContent = message;
      logElement.appendChild(entry);
      logElement.scrollTop = logElement.scrollHeight;
    }
    
    // Override console.log
    const originalLog = console.log;
    console.log = function(...args) {
      originalLog.apply(console, args);
      log(args.join(' '));
    };
    
    // Override console.error
    const originalError = console.error;
    console.error = function(...args) {
      originalError.apply(console, args);
      log(args.join(' '), 'error');
    };
    
    log('Testing imports...');
    
    // Try importing the modules
    try {
      const { QRCodeVerifier } = await import('/src/ui/components/verification/QRCodeVerifier.js');
      log('Successfully imported QRCodeVerifier.js', 'success');
      
      // Create QR verifier instance
      const qrVerifier = new QRCodeVerifier({
        localFingerprint: '5A2F8E7B1D94C65B304218B209A587E2C17F33',
        expectedFingerprint: '7E1DC849A3B6F592D0783E5C9A1FB432D6502C91',
        onVerificationComplete: (result) => {
          if (result.success) {
            log('Verification complete: SUCCESS', 'success');
          } else {
            log('Verification complete: FAILURE', 'error');
          }
          log(`Scanned fingerprint: ${result.scannedFingerprint}`);
          log(`Expected fingerprint: ${result.expectedFingerprint}`);
        }
      });
      
      // Test QR scanner
      document.getElementById('test-scanner-button').addEventListener('click', () => {
        try {
          log('Starting QR code scanner...');
          qrVerifier.startScanner().catch(error => {
            log(`Error starting scanner: ${error.message}`, 'error');
          });
        } catch (error) {
          log(`Error starting scanner: ${error.message}`, 'error');
        }
      });
      
      // Test manual entry
      document.getElementById('test-manual-button').addEventListener('click', () => {
        try {
          log('Opening manual fingerprint entry form...');
          qrVerifier.showManualEntry();
        } catch (error) {
          log(`Error showing manual entry: ${error.message}`, 'error');
        }
      });
      
      // Test QR code generation
      document.getElementById('test-button').addEventListener('click', async () => {
        try {
          log('Creating QR code...');
          
          log('Generating QR code...');
          const qrCodeDataUrl = await qrVerifier.generateQRCode();
          
          log('QR code generated successfully!', 'success');
          
          // Display QR code
          const qrContainer = document.getElementById('qr-container');
          
          // Create QR code data for testing
          const qrData = {
            type: 'otr-verification',
            version: '1.0',
            fingerprint: '5A2F8E7B1D94C65B304218B209A587E2C17F33',
            timestamp: new Date().toISOString(),
            instanceID: `${navigator.platform}-TEST`.toUpperCase()
          };
          
          // Convert to JSON string and format for display
          const qrString = JSON.stringify(qrData);
          const formattedJson = JSON.stringify(qrData, null, 2);
          
          qrContainer.innerHTML = `
            <div style="display: flex; gap: 20px; flex-wrap: wrap;">
              <div>
                <h3>QR Code Image</h3>
                <img src="${qrCodeDataUrl}" alt="QR Code" style="border: 1px solid #ddd;">
              </div>
              <div>
                <h3>QR Code Data</h3>
                <pre style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto;">${formattedJson}</pre>
                <button id="copy-data-button" style="margin-top: 10px;">Copy Data</button>
                <button id="test-verification-button" style="margin-top: 10px; margin-left: 10px;">Test Verification</button>
              </div>
            </div>
          `;
          
          // Add event listener for copy button
          document.getElementById('copy-data-button').addEventListener('click', () => {
            navigator.clipboard.writeText(qrString).then(() => {
              log('QR code data copied to clipboard', 'success');
            }).catch(err => {
              log('Failed to copy: ' + err, 'error');
            });
          });
          
          // Add event listener for test verification button
          document.getElementById('test-verification-button').addEventListener('click', () => {
            try {
              log('Testing verification with QR code data...');
              
              // Process the QR code data
              const result = qrVerifier.compareFingerprints(
                qrData.fingerprint,
                qrVerifier.options.expectedFingerprint
              );
              
              if (result) {
                log('Verification successful! Fingerprints match.', 'success');
              } else {
                log('Verification failed! Fingerprints do not match.', 'error');
              }
              
              // Show the manual entry form
              log('Opening manual entry form...');
              qrVerifier.showManualEntry();
            } catch (error) {
              log(`Error during verification test: ${error.message}`, 'error');
            }
          });
        } catch (error) {
          log(`Error generating QR code: ${error.message}`, 'error');
        }
      });
    } catch (error) {
      log(`Failed to import QRCodeVerifier.js: ${error.message}`, 'error');
    }
  </script>
</body>
</html>
