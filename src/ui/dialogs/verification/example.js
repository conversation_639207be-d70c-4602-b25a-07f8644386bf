/**
 * Example usage of the Verification Dialog
 * 
 * This file demonstrates how to use the Verification Dialog component
 * in an application to verify conversation partners using SMP.
 */

import { createVerificationDialog } from '/src/ui/dialogs/verification/index.js';
import { SMPHandler } from '/src/core/protocol/smp.js';

/**
 * Initialize the verification dialog example
 */
function initVerificationExample() {
  // Get container element
  const container = document.getElementById('verification-container');
  if (!container) {
    console.error('Verification container element not found');
    return;
  }
  
  // Create a button to open the dialog
  const openButton = document.createElement('button');
  openButton.textContent = 'Verify Conversation Partner';
  openButton.className = 'open-verification-button';
  container.appendChild(openButton);
  
  // Create status indicator
  const statusIndicator = document.createElement('div');
  statusIndicator.className = 'verification-status';
  statusIndicator.textContent = 'Not verified';
  container.appendChild(statusIndicator);
  
  // Create SMP handler
  const smpHandler = new SMPHandler();
  
  // Sample fingerprints (in a real app, these would come from the OTR session)
  const localFingerprint = '5A2F8E7B1D94C65B304218B209A587E2C17F33';
  const partnerFingerprint = '7E1DC849A3B6F592D0783E5C9A1FB432D6502C91';
  
  // Create verification dialog
  const verificationDialog = createVerificationDialog(container, {
    defaultMethod: 'qr-code', // Set QR code as the default method
    smpHandler: smpHandler,
    localFingerprint: localFingerprint,
    partnerInfo: {
      name: 'Bob',
      fingerprint: partnerFingerprint
    },
    onVerificationComplete: (result) => {
      console.log('Verification complete:', result);
      
      // Update status indicator
      if (result.success) {
        statusIndicator.textContent = 'Verified ✅';
        statusIndicator.className = 'verification-status verified';
      } else {
        statusIndicator.textContent = 'Verification failed ❌';
        statusIndicator.className = 'verification-status failed';
      }
    }
  });
  
  // Open dialog when button is clicked
  openButton.addEventListener('click', () => {
    verificationDialog.open();
  });
  
  // Listen for verification events
  container.addEventListener('verification:open', (event) => {
    console.log('Verification dialog opened');
  });
  
  container.addEventListener('verification:close', (event) => {
    console.log('Verification dialog closed');
  });
  
  container.addEventListener('verification:methodChange', (event) => {
    console.log('Verification method changed:', event.detail.method);
  });
  
  container.addEventListener('verification:submit', (event) => {
    console.log('Verification submitted');
    
    // In a real application, this is where you would handle the SMP response
    // from the other party. For this example, we'll simulate a successful
    // verification after a delay.
    setTimeout(() => {
      // Simulate SMP response
      const smpResponse = {
        result: Math.random() > 0.3 ? 1 : 2, // 70% chance of success
        initiator: true,
        question: event.detail.dialog.state.question
      };
      
      // Handle SMP response
      smpHandler.handleVerificationResult(smpResponse);
    }, 3000);
  });
  
  // Additional event listeners for QR code verification
  container.addEventListener('qr:scanStart', () => {
    console.log('QR code scanning started');
  });
  
  container.addEventListener('qr:scanSuccess', (event) => {
    console.log('QR code successfully scanned:', event.detail);
  });
  
  container.addEventListener('qr:scanError', (event) => {
    console.error('QR code scanning error:', event.detail.error);
  });
  
  container.addEventListener('qr:cameraError', (event) => {
    console.error('Camera error during QR scanning:', event.detail.error);
  });
  
  return {
    container,
    verificationDialog,
    smpHandler
  };
}

/**
 * Add styles for the example
 */
function addExampleStyles() {
  const style = document.createElement('style');
  style.textContent = `
    .open-verification-button {
      padding: 10px 20px;
      background-color: #0078d4;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      margin-bottom: 20px;
    }
    
    .open-verification-button:hover {
      background-color: #006cbe;
    }
    
    .verification-status {
      padding: 10px;
      border-radius: 4px;
      background-color: #f0f0f0;
      margin-top: 20px;
      font-size: 16px;
    }
    
    .verification-status.verified {
      background-color: #dff6dd;
      color: #107c10;
    }
    
    .verification-status.failed {
      background-color: #fde7e9;
      color: #d83b01;
    }
  `;
  document.head.appendChild(style);
}

/**
 * Initialize the example when the DOM is ready
 */
function initExample() {
  // Add styles
  addExampleStyles();
  
  // Create container if it doesn't exist
  let container = document.getElementById('verification-container');
  if (!container) {
    container = document.createElement('div');
    container.id = 'verification-container';
    container.style.padding = '20px';
    document.body.appendChild(container);
  }
  
  // Initialize verification example
  const example = initVerificationExample();
  
  // Log success
  console.log('Verification example initialized');
  
  return example;
}

// Export functions
export { initExample, initVerificationExample };

// Initialize example if this script is loaded directly
if (typeof window !== 'undefined' && window.document) {
  window.addEventListener('DOMContentLoaded', initExample);
}
