# SMP Authentication Feature

This directory contains the implementation of the Socialist Millionaire Protocol (SMP) authentication feature for the WebOTteR project. SMP allows two parties to verify they both know the same secret without revealing the secret to each other, providing a secure way to authenticate conversation partners.

## Components

### Core Components

- **VerificationDialog**: The main dialog component that provides the UI for SMP authentication.
- **QRCodeVerifier**: A component for QR code-based verification.
- **EmojiSelector**: A component for selecting emojis to include in questions and answers.

### Files

- `index.js`: The main implementation of the verification dialog.
- `styles.css`: Styles for the verification dialog.
- `example.js`: Example usage of the verification dialog.
- `demo.html`: A standalone demo page for testing the verification dialog.
- `index.html`: Redirect page to the demo.
- `serve-demo.sh`: Script to serve the demo locally.
- `../components/verification/QRCodeVerifier.js`: QR code generation and scanning component.
- `../components/verification/EmojiSelector.js`: Emoji selector component.
- `../components/verification/emoji-selector.css`: Styles for the emoji selector.

## Usage

### Basic Usage

```javascript
import { createVerificationDialog } from './ui/dialogs/verification';
import { SMPHandler } from './core/protocol/smp';

// Create SMP handler
const smpHandler = new SMPHandler();

// Create verification dialog
const verificationDialog = createVerificationDialog(containerElement, {
  defaultMethod: 'secret-qa',
  smpHandler: smpHandler,
  localFingerprint: 'YOUR_LOCAL_FINGERPRINT',
  partnerInfo: {
    name: 'Partner Name',
    fingerprint: 'PARTNER_FINGERPRINT'
  },
  onVerificationComplete: (result) => {
    console.log('Verification complete:', result);
    // Update UI based on result.success
  }
});

// Open the dialog
verificationDialog.open();
```

### Verification Methods

The dialog supports three verification methods:

1. **Secret Question & Answer (SECRET_QA)**
   - User enters a question and the expected answer
   - Emoji selector available for both question and answer fields
   - The question is sent to the partner
   - The partner must provide the correct answer

2. **QR Code (QR_CODE)**
   - Generates a QR code containing the user's OTR fingerprint
   - User can scan their partner's QR code using device camera
   - Fingerprints are compared for verification

3. **Fingerprint (FINGERPRINT)**
   - Displays both user's and partner's fingerprints
   - User compares fingerprints via a different communication channel
   - User confirms if fingerprints match

### Events

The verification dialog dispatches the following events:

- `verification:open`: When the dialog is opened
- `verification:close`: When the dialog is closed
- `verification:methodChange`: When the verification method is changed
- `verification:submit`: When verification is submitted

### Customization

The verification dialog can be customized with the following options:

- `defaultMethod`: The default verification method to show
- `smpHandler`: An instance of SMPHandler for SMP operations
- `localFingerprint`: The user's OTR fingerprint
- `partnerInfo`: Information about the conversation partner
  - `name`: The partner's name
  - `fingerprint`: The partner's OTR fingerprint
- `onVerificationComplete`: Callback function called when verification completes

## Demo

To run the demo:

### Method 1: Using the serve-demo.sh script

1. Make the script executable (if not already): `chmod +x serve-demo.sh`
2. Run the script: `./serve-demo.sh`
3. Open your browser and navigate to http://localhost:8000/demo.html
4. Click "Open Verification Dialog" to test the verification dialog
5. Try different verification methods
6. Use "Toggle Success/Failure" to simulate different verification results

### Method 2: Opening the HTML file directly

1. Open `demo.html` in a web browser
2. Click "Open Verification Dialog" to test the verification dialog
3. Try different verification methods
4. Use "Toggle Success/Failure" to simulate different verification results

**Note**: Some browsers may restrict JavaScript module imports when opening HTML files directly. If you encounter issues, use Method 1 instead.

## Implementation Details

### SMP Protocol

The SMP implementation follows the OTRv3 specification and provides:

- End-to-end encrypted verification
- Zero-knowledge proofs for secure authentication
- Protection against man-in-the-middle attacks
- Multiple verification methods for different use cases

### UI Components

The UI components are designed to be:

- Accessible: ARIA attributes, keyboard navigation, screen reader support
- Responsive: Works on desktop and mobile devices
- Themeable: Supports light and dark modes
- User-friendly: Clear instructions and error messages

## Integration with OTR

To integrate with an OTR session:

1. Create an SMPHandler instance
2. Register it with the OTR session
3. Create a verification dialog with the SMPHandler
4. Handle verification results to update the UI

## Security Considerations

- SMP verification should be performed over an already encrypted OTR channel
- Users should be encouraged to use verification methods appropriate for their threat model
- QR code verification is most secure when done in person
- Fingerprint verification requires a separate secure channel
- Question-based verification is only as secure as the chosen question and answer

## Accessibility

The verification dialog includes the following accessibility features:

- ARIA attributes for screen readers
- Keyboard navigation
- High contrast mode support
- Reduced motion support
- Clear error messages
- Responsive design for different screen sizes

## Future Improvements

- Add support for more verification methods
- Improve QR code scanning with better error handling
- Add internationalization support
- Add more customization options
- Improve mobile experience
