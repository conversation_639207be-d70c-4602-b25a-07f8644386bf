/**
 * Verification Dialog Styles
 */

/* Dialog container */
.verification-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Dialog content */
.verification-dialog-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Dialog header */
.verification-dialog-header {
  padding: 16px 20px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.verification-dialog-header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 0;
  margin: 0;
}

.close-button:hover {
  color: #000;
}

/* Dialog body */
.verification-dialog-body {
  padding: 20px;
  flex-grow: 1;
  overflow-y: auto;
}

/* Dialog footer */
.verification-dialog-footer {
  padding: 16px 20px;
  background-color: #f5f5f5;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

/* Buttons */
.verification-dialog button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
}

.cancel-button, .try-different-method-button {
  background-color: #f0f0f0;
  border: 1px solid #d0d0d0;
  color: #333;
}

.cancel-button:hover, .try-different-method-button:hover {
  background-color: #e0e0e0;
  border-color: #c0c0c0;
}

.verify-button, .done-button, .scan-button, .fingerprint-match-button, .try-again-button {
  background-color: #0078d4;
  border: 1px solid #0078d4;
  color: white;
}

.verify-button:hover, .done-button:hover, .scan-button:hover, .fingerprint-match-button:hover, .try-again-button:hover {
  background-color: #006cbe;
  border-color: #006cbe;
}

.fingerprint-mismatch-button {
  background-color: #e74c3c;
  border: 1px solid #e74c3c;
  color: white;
}

.fingerprint-mismatch-button:hover {
  background-color: #c0392b;
  border-color: #c0392b;
}

/* Verification methods */
.verification-methods {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.method-button {
  flex: 1;
  padding: 10px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s;
}

.method-button:hover {
  border-bottom-color: #0078d4;
}

.method-button.active {
  border-bottom-color: #0078d4;
  font-weight: bold;
}

/* Method content */
.method-content {
  padding: 10px 0;
}

.method-content h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
}

/* Inputs */
.input-with-emoji {
  position: relative;
  margin-bottom: 16px;
}

.question-input, .answer-input {
  width: 100%;
  padding: 10px 40px 10px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.emoji-toggle {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  margin: 0;
}

.emoji-container {
  position: relative;
  margin-top: 8px;
}

/* QR Code styles */
.qr-code-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 220px;
  background-color: #f9f9f9;
  border: 1px dashed #ccc;
  border-radius: 4px;
  margin-bottom: 16px;
  overflow: hidden;
}

.qr-code-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.qr-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 14px;
  color: #666;
}

.qr-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 14px;
  color: #e74c3c;
}

.scan-button {
  display: block;
  width: 100%;
  padding: 12px;
  margin-top: 16px;
  font-size: 16px;
}

/* QR scanner container */
.qr-scanner-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.qr-scanner-container video {
  max-width: 100%;
  max-height: 70vh;
  border: 2px solid white;
  border-radius: 8px;
}

.qr-scanner-container canvas {
  display: none;
}

/* Fingerprint display */
.fingerprint-display {
  font-family: monospace;
  font-size: 16px;
  padding: 12px;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin-bottom: 16px;
  word-break: break-all;
  line-height: 1.5;
}

.fingerprint-buttons {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.fingerprint-buttons button {
  flex: 1;
  padding: 12px;
}

/* Loading state */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 120, 212, 0.2);
  border-top-color: #0078d4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Success state */
.success-message {
  text-align: center;
  padding: 30px 0;
}

.success-message p:first-child {
  font-size: 18px;
  font-weight: bold;
  color: #2ecc71;
  margin-bottom: 16px;
}

/* Failure state */
.failure-message {
  padding: 20px 0;
}

.failure-message p:first-child {
  font-size: 18px;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 16px;
}

.failure-message ul {
  margin-top: 16px;
  padding-left: 20px;
}

.failure-message li {
  margin-bottom: 8px;
}

/* Responsive styles */
@media (max-width: 480px) {
  .verification-dialog-content {
    width: 95%;
    max-height: 95vh;
  }
  
  .verification-methods {
    flex-direction: column;
    gap: 4px;
  }
  
  .method-button {
    padding: 12px;
    border-bottom: none;
    border-left: 3px solid transparent;
    text-align: left;
  }
  
  .method-button.active, .method-button:hover {
    border-bottom: none;
    border-left-color: #0078d4;
  }
  
  .verification-dialog-footer {
    flex-direction: column;
    gap: 8px;
  }
  
  .verification-dialog-footer button {
    width: 100%;
  }
  
  .fingerprint-buttons {
    flex-direction: column;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .verification-dialog-content {
    background-color: #2d2d2d;
    color: #f0f0f0;
  }
  
  .verification-dialog-header {
    background-color: #222;
    border-bottom-color: #444;
  }
  
  .verification-dialog-header h2 {
    color: #f0f0f0;
  }
  
  .close-button {
    color: #aaa;
  }
  
  .close-button:hover {
    color: #fff;
  }
  
  .verification-dialog-footer {
    background-color: #222;
    border-top-color: #444;
  }
  
  .method-content h3 {
    color: #f0f0f0;
  }
  
  .question-input, .answer-input {
    background-color: #333;
    border-color: #444;
    color: #f0f0f0;
  }
  
  .qr-code-placeholder {
    background-color: #333;
    border-color: #444;
  }
  
  .fingerprint-display {
    background-color: #333;
    color: #f0f0f0;
  }
  
  .cancel-button, .try-different-method-button {
    background-color: #444;
    border-color: #555;
    color: #f0f0f0;
  }
  
  .cancel-button:hover, .try-different-method-button:hover {
    background-color: #555;
    border-color: #666;
  }
  
  .verification-methods {
    border-bottom-color: #444;
  }
}
