#!/bin/bash

# This script serves the SMP Authentication Demo locally

# Get the project root directory (3 levels up from this script)
SCRIPT_DIR="$(dirname "$0")"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../" && pwd)"

# Check if npx is installed 
if command -v npx &>/dev/null; then
    echo "Starting server from project root with npx serve..."
    cd "$PROJECT_ROOT"
    npx serve -l 8000
elif command -v python3 &>/dev/null; then
    echo "Starting server from project root with Python 3..."
    cd "$PROJECT_ROOT"
    python3 -m http.server 8000
elif command -v python &>/dev/null; then
    echo "Starting server from project root with Python..."
    cd "$PROJECT_ROOT"
    python -m SimpleHTTPServer 8000
else
    echo "Error: Could not find Python or npx to start a local server."
    echo "Please install Python 3 or Node.js to run this demo."
    exit 1
fi

echo "Server started at http://localhost:8000"
echo "Open your browser and navigate to http://localhost:8000/src/ui/dialogs/verification/demo.html"
echo "Press Ctrl+C to stop the server"
