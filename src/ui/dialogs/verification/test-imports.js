// Test script to check if imports are working correctly
console.log('Testing imports...');

// Try importing the modules
import('/src/ui/dialogs/verification/index.js')
  .then(() => {
    console.log('Successfully imported index.js');
  })
  .catch(error => {
    console.error('Failed to import index.js:', error);
  });

import('/src/ui/components/verification/QRCodeVerifier.js')
  .then(() => {
    console.log('Successfully imported QRCodeVerifier.js');
  })
  .catch(error => {
    console.error('Failed to import QRCodeVerifier.js:', error);
  });

import('/src/core/protocol/smp.js')
  .then(() => {
    console.log('Successfully imported smp.js');
  })
  .catch(error => {
    console.error('Failed to import smp.js:', error);
  });
