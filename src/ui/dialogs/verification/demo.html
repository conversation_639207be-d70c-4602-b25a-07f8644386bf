<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SMP Authentication Demo</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      text-align: center;
      margin-bottom: 40px;
    }
    
    h1 {
      color: #0078d4;
    }
    
    .demo-section {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 30px;
    }
    
    .demo-controls {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 20px;
    }
    
    .demo-controls button {
      padding: 10px 20px;
      background-color: #0078d4;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
    }
    
    .demo-controls button:hover {
      background-color: #006cbe;
    }
    
    .demo-controls button.secondary {
      background-color: #f0f0f0;
      color: #333;
      border: 1px solid #d0d0d0;
    }
    
    .demo-controls button.secondary:hover {
      background-color: #e0e0e0;
    }
    
    .demo-output {
      background-color: #f0f0f0;
      border: 1px solid #d0d0d0;
      border-radius: 4px;
      padding: 10px;
      font-family: monospace;
      height: 200px;
      overflow-y: auto;
    }
    
    .demo-output p {
      margin: 5px 0;
    }
    
    .demo-output .success {
      color: #107c10;
    }
    
    .demo-output .error {
      color: #d83b01;
    }
    
    .demo-output .info {
      color: #0078d4;
    }
    
    .verification-container {
      margin-top: 20px;
    }
    
    footer {
      text-align: center;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #e0e0e0;
      font-size: 14px;
      color: #666;
    }
    
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #1e1e1e;
        color: #f0f0f0;
      }
      
      .demo-section {
        background-color: #2d2d2d;
      }
      
      .demo-output {
        background-color: #252525;
        border-color: #444;
        color: #f0f0f0;
      }
      
      .demo-controls button.secondary {
        background-color: #3d3d3d;
        color: #f0f0f0;
        border-color: #555;
      }
      
      .demo-controls button.secondary:hover {
        background-color: #4d4d4d;
      }
    }
  </style>
</head>
<body>
  <header>
    <h1>SMP Authentication Demo</h1>
    <p>This demo showcases the Socialist Millionaire Protocol (SMP) authentication feature for secure identity verification.</p>
  </header>
  
  <main>
    <section class="demo-section">
      <h2>Verification Dialog Demo</h2>
      <p>Click the button below to open the verification dialog and test the different verification methods.</p>
      
      <div class="demo-controls">
        <button id="open-dialog-button">Open Verification Dialog</button>
        <button id="toggle-success-button" class="secondary">Toggle Success/Failure</button>
        <button id="clear-output-button" class="secondary">Clear Output</button>
      </div>
      
      <div class="demo-output" id="demo-output">
        <p class="info">Demo initialized. Click "Open Verification Dialog" to begin.</p>
      </div>
      
      <div id="verification-container" class="verification-container"></div>
    </section>
    
    <section class="demo-section">
      <h2>About SMP Authentication</h2>
      <p>The Socialist Millionaire Protocol (SMP) allows two parties to verify they both know the same secret without revealing the secret to each other. This is used in OTR messaging to authenticate conversation partners.</p>
      
      <h3>Verification Methods</h3>
      <ul>
        <li><strong>Secret Question & Answer</strong>: Ask a question only your partner would know the answer to.</li>
        <li><strong>QR Code</strong>: Scan a QR code containing your partner's OTR fingerprint. This method is most secure when done in person.</li>
        <li><strong>Fingerprint</strong>: Manually compare OTR fingerprints via a different communication channel.</li>
      </ul>
      
      <h3>QR Code Verification Instructions</h3>
      <p>To test the QR code verification feature:</p>
      <ol>
        <li>Open the Verification Dialog using the button above</li>
        <li>Click on the "QR Code" tab at the top of the dialog</li>
        <li>You'll see a QR code displayed - this represents your fingerprint</li>
        <li>Click the "Scan Partner's Code" button to activate your camera</li>
        <li>You can either:
          <ul>
            <li>Point your camera at another device showing a QR code</li>
            <li>Use the "Enter Manually" button to input a fingerprint directly</li>
          </ul>
        </li>
        <li>If verification succeeds, you'll see a success message</li>
      </ol>
      <p><strong>Note:</strong> For demo purposes, the verification has a 70% chance of success by default. You can toggle this with the "Toggle Success/Failure" button.</p>
    </section>
  </main>
  
  <footer>
    <p>WebOTR SMP Authentication Demo | Part of the WebOTteR Project</p>
  </footer>
  
  <script type="module">
    // Use a path relative to the server root
    import { initExample } from '/src/ui/dialogs/verification/example.js';
    
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', () => {
      // Initialize the example
      const example = initExample();
      
      if (!example) {
        console.error('Failed to initialize example');
        document.getElementById('demo-output').innerHTML = '<p class="error">Error: Failed to initialize example. Check console for details.</p>';
        return;
      }
      
    
    // Get elements
    const openDialogButton = document.getElementById('open-dialog-button');
    const toggleSuccessButton = document.getElementById('toggle-success-button');
    const clearOutputButton = document.getElementById('clear-output-button');
    const outputElement = document.getElementById('demo-output');
    
    // Success probability (70% by default)
    let successProbability = 0.7;
    
    // Log function
    function log(message, type = 'info') {
      const p = document.createElement('p');
      p.textContent = message;
      p.className = type;
      outputElement.appendChild(p);
      outputElement.scrollTop = outputElement.scrollHeight;
    }
    
    // Open dialog button
    if (openDialogButton && example && example.verificationDialog) {
      openDialogButton.addEventListener('click', () => {
        example.verificationDialog.open();
        log('Verification dialog opened');
      });
    }
    
    // Toggle success button
    if (toggleSuccessButton) {
      toggleSuccessButton.addEventListener('click', () => {
        successProbability = successProbability === 0.7 ? 0.0 : 0.7;
        log(`Verification will now ${successProbability > 0 ? 'likely succeed' : 'always fail'}`);
      });
    }
    
    // Clear output button
    if (clearOutputButton) {
      clearOutputButton.addEventListener('click', () => {
        outputElement.innerHTML = '';
        log('Output cleared');
      });
    }
    
    // Listen for verification events
    const container = document.getElementById('verification-container');
    if (container) {
      container.addEventListener('verification:open', () => {
        log('Event: verification dialog opened');
      });
      
      container.addEventListener('verification:close', () => {
        log('Event: verification dialog closed');
      });
      
      container.addEventListener('verification:methodChange', (event) => {
        log(`Event: verification method changed to ${event.detail.method}`);
      });
      
      container.addEventListener('verification:submit', (event) => {
        log('Event: verification submitted');
        
        // Override the example's setTimeout to use our success probability
        setTimeout(() => {
          const success = Math.random() < successProbability;
          const result = success ? 1 : 2; // 1 = SUCCESS, 2 = FAILURE
          
          log(`Simulating ${success ? 'successful' : 'failed'} verification`, success ? 'success' : 'error');
          
          // Handle SMP response
          if (example && example.smpHandler) {
            example.smpHandler.handleVerificationResult({
              result: result,
              initiator: true,
              question: event.detail.dialog.state.question
            });
          }
        }, 3000);
      });
    }
    
    // Log initialization
    log('Demo initialized successfully', 'success');
    });
  </script>
</body>
</html>
