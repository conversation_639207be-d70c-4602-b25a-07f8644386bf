import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import QRCodeVerifier from './QRCodeVerifier.jsx';
import QuestionAnswerVerifier from './QuestionAnswerVerifier.jsx';
import ManualFingerprintVerifier from './ManualFingerprintVerifier.jsx';
import './VerificationDialog.css';

/**
 * VerificationDialog - Main component for OTR verification workflows
 * 
 * Supports multiple verification methods:
 * - QR Code scanning/generation
 * - Question-Answer (SMP)
 * - Manual fingerprint comparison
 */
const VerificationDialog = ({
  isOpen,
  onClose,
  onComplete,
  verificationMethod = 'qr-code',
  contactName,
  fingerprint,
  className = ''
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [verificationData, setVerificationData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Reset state when dialog opens
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(1);
      setVerificationData(null);
      setError(null);
    }
  }, [isOpen]);

  const handleMethodChange = (method) => {
    setVerificationMethod(method);
    setCurrentStep(1);
    setError(null);
  };

  const handleStepComplete = (data) => {
    setVerificationData(data);
    if (currentStep < getTotalSteps()) {
      setCurrentStep(currentStep + 1);
    } else {
      handleVerificationComplete(data);
    }
  };

  const handleVerificationComplete = (data) => {
    setIsLoading(true);
    // Simulate verification process
    setTimeout(() => {
      setIsLoading(false);
      onComplete({
        method: verificationMethod,
        data,
        verified: true,
        timestamp: new Date().toISOString()
      });
    }, 1500);
  };

  const getTotalSteps = () => {
    switch (verificationMethod) {
      case 'qr-code': return 2;
      case 'question-answer': return 3;
      case 'manual-fingerprint': return 2;
      default: return 1;
    }
  };

  const renderStepIndicator = () => (
    <div className="verification-steps">
      {Array.from({ length: getTotalSteps() }, (_, index) => (
        <div
          key={index}
          className={`step ${index + 1 <= currentStep ? 'active' : ''} ${
            index + 1 < currentStep ? 'completed' : ''
          }`}
        >
          <div className="step-number">{index + 1}</div>
          <div className="step-label">{getStepLabel(index + 1)}</div>
        </div>
      ))}
    </div>
  );

  const getStepLabel = (step) => {
    switch (verificationMethod) {
      case 'qr-code':
        return step === 1 ? 'Scan Code' : 'Verify';
      case 'question-answer':
        return step === 1 ? 'Question' : step === 2 ? 'Answer' : 'Verify';
      case 'manual-fingerprint':
        return step === 1 ? 'Compare' : 'Confirm';
      default:
        return `Step ${step}`;
    }
  };

  const renderMethodSelector = () => (
    <div className="method-selector">
      <h3>Choose Verification Method</h3>
      <div className="method-options">
        <button
          className={`method-option ${verificationMethod === 'qr-code' ? 'selected' : ''}`}
          onClick={() => handleMethodChange('qr-code')}
        >
          <div className="method-icon">📱</div>
          <div className="method-info">
            <div className="method-title">QR Code</div>
            <div className="method-description">Scan or show QR code</div>
          </div>
        </button>
        
        <button
          className={`method-option ${verificationMethod === 'question-answer' ? 'selected' : ''}`}
          onClick={() => handleMethodChange('question-answer')}
        >
          <div className="method-icon">❓</div>
          <div className="method-info">
            <div className="method-title">Question & Answer</div>
            <div className="method-description">Share a secret question</div>
          </div>
        </button>
        
        <button
          className={`method-option ${verificationMethod === 'manual-fingerprint' ? 'selected' : ''}`}
          onClick={() => handleMethodChange('manual-fingerprint')}
        >
          <div className="method-icon">🔍</div>
          <div className="method-info">
            <div className="method-title">Manual Verification</div>
            <div className="method-description">Compare fingerprints manually</div>
          </div>
        </button>
      </div>
    </div>
  );

  const renderCurrentStep = () => {
    switch (verificationMethod) {
      case 'qr-code':
        return <QRCodeVerifier
          step={currentStep}
          onComplete={handleStepComplete}
          fingerprint={fingerprint}
          contactName={contactName}
          mode="both"
        />;
      case 'question-answer':
        return <QuestionAnswerVerifier
          step={currentStep}
          onComplete={handleStepComplete}
          contactName={contactName}
          isInitiator={true}
        />;
      case 'manual-fingerprint':
        return <ManualFingerprintVerifier
          step={currentStep}
          onComplete={handleStepComplete}
          fingerprint={fingerprint}
          contactName={contactName}
        />;
      default:
        return <div>Unknown verification method</div>;
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`verification-dialog-overlay ${className}`}>
      <div className="verification-dialog">
        <div className="dialog-header">
          <h2>Verify Identity</h2>
          <button className="close-button" onClick={onClose} aria-label="Close">
            ✕
          </button>
        </div>

        <div className="dialog-content">
          {currentStep === 0 && renderMethodSelector()}
          
          {currentStep > 0 && (
            <>
              {renderStepIndicator()}
              
              {error && (
                <div className="error-message" role="alert">
                  <div className="error-icon">⚠️</div>
                  <div className="error-text">{error}</div>
                </div>
              )}
              
              {isLoading ? (
                <div className="loading-state">
                  <div className="spinner"></div>
                  <div>Verifying identity...</div>
                </div>
              ) : (
                renderCurrentStep()
              )}
            </>
          )}
        </div>

        <div className="dialog-footer">
          <button 
            className="button secondary" 
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </button>
          
          {currentStep > 1 && (
            <button 
              className="button secondary" 
              onClick={() => setCurrentStep(currentStep - 1)}
              disabled={isLoading}
            >
              Back
            </button>
          )}
        </div>
      </div>
    </div>
  );
};



VerificationDialog.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onComplete: PropTypes.func.isRequired,
  verificationMethod: PropTypes.oneOf(['qr-code', 'question-answer', 'manual-fingerprint']),
  contactName: PropTypes.string,
  fingerprint: PropTypes.string,
  className: PropTypes.string
};

export default VerificationDialog;
