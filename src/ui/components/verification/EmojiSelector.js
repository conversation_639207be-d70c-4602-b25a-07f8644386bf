/**
 * Emoji Selector Component
 * 
 * This component provides an emoji picker for selecting emojis to use
 * in verification questions or answers.
 */

/**
 * Common emojis for verification
 * Grouped by category for easier selection
 */
const EMOJI_GROUPS = {
  'Smileys & People': [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
    '👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤌', '🤏', '✌️', '🤞',
    '🫶', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '👍'
  ],
  'Animals & Nature': [
    '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
    '🦁', '🐮', '🐷', '🐸', '🐵', '🙈', '🙉', '🙊', '🐔', '🐧',
    '🌵', '🌲', '🌳', '🌴', '🌱', '🌿', '☘️', '🍀', '🍁', '🍂',
    '🍃', '🌹', '🌷', '🌺', '🌸', '🌼', '🌻', '🥀', '💐', '🍄'
  ],
  'Food & Drink': [
    '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈',
    '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦',
    '🍔', '🍟', '🍕', '🌭', '🥪', '🌮', '🌯', '🥙', '🧆', '🥚',
    '🍿', '🧂', '🥫', '🍱', '🍘', '🍙', '🍚', '🍛', '🍜', '🍝'
  ],
  'Travel & Places': [
    '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐',
    '🛻', '🚚', '🚛', '🚜', '🛵', '🏍️', '🛺', '🚲', '🛴', '🚨',
    '🏠', '🏡', '🏘️', '🏚️', '🏗️', '🏭', '🏢', '🏬', '🏣', '🏤',
    '🏥', '🏦', '🏨', '🏪', '🏫', '🏩', '💒', '🏛️', '⛪', '🕌'
  ],
  'Activities': [
    '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
    '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳',
    '🎭', '🎨', '🎬', '🎤', '🎧', '🎼', '🎹', '🥁', '🪘', '🎷',
    '🎺', '🎸', '🪕', '🎻', '🎲', '♟️', '🎯', '🎳', '🎮', '🎰'
  ],
  'Objects': [
    '⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️',
    '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥', '📽️',
    '🔑', '🔒', '🔓', '🔏', '🔐', '🔑', '🗝️', '🔨', '🪓', '⛏️',
    '🔧', '🔩', '⚙️', '🗜️', '⚖️', '🦯', '🔗', '⛓️', '🪝', '🧰'
  ],
  'Symbols': [
    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
    '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
    '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐',
    '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐'
  ]
};

/**
 * EmojiSelector class
 */
export class EmojiSelector {
  /**
   * Create a new emoji selector
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = Object.assign({
      container: null,
      onSelect: () => {},
      recentEmojis: []
    }, options);
    
    this.state = {
      isOpen: false,
      activeGroup: Object.keys(EMOJI_GROUPS)[0],
      recentEmojis: this.options.recentEmojis.slice(0, 20)
    };
    
    // Bind methods
    this.open = this.open.bind(this);
    this.close = this.close.bind(this);
    this.toggle = this.toggle.bind(this);
    this.selectEmoji = this.selectEmoji.bind(this);
    this.setActiveGroup = this.setActiveGroup.bind(this);
    this.render = this.render.bind(this);
    
    // Create element
    this.element = null;
    
    // Initialize if container is provided
    if (this.options.container) {
      this.initialize(this.options.container);
    }
  }
  
  /**
   * Initialize the emoji selector
   * @param {HTMLElement} container - Container element
   */
  initialize(container) {
    this.options.container = container;
    
    // Create element
    this.element = document.createElement('div');
    this.element.className = 'emoji-selector';
    this.element.setAttribute('role', 'dialog');
    this.element.setAttribute('aria-label', 'Emoji selector');
    
    // Add to container
    container.appendChild(this.element);
    
    // Initial render
    this.render();
  }
  
  /**
   * Open the emoji selector
   */
  open() {
    this.state.isOpen = true;
    this.render();
  }
  
  /**
   * Close the emoji selector
   */
  close() {
    this.state.isOpen = false;
    this.render();
  }
  
  /**
   * Toggle the emoji selector
   */
  toggle() {
    this.state.isOpen ? this.close() : this.open();
  }
  
  /**
   * Select an emoji
   * @param {string} emoji - The selected emoji
   */
  selectEmoji(emoji) {
    // Add to recent emojis
    this.addToRecent(emoji);
    
    // Call callback
    if (typeof this.options.onSelect === 'function') {
      this.options.onSelect(emoji);
    }
    
    // Close selector
    this.close();
  }
  
  /**
   * Add emoji to recent list
   * @param {string} emoji - Emoji to add
   * @private
   */
  addToRecent(emoji) {
    // Remove emoji if it already exists in the list
    const index = this.state.recentEmojis.indexOf(emoji);
    if (index !== -1) {
      this.state.recentEmojis.splice(index, 1);
    }
    
    // Add emoji to the beginning of the list
    this.state.recentEmojis.unshift(emoji);
    
    // Limit to 20 recent emojis
    if (this.state.recentEmojis.length > 20) {
      this.state.recentEmojis.pop();
    }
  }
  
  /**
   * Set the active emoji group
   * @param {string} group - Group name
   */
  setActiveGroup(group) {
    if (EMOJI_GROUPS[group]) {
      this.state.activeGroup = group;
      this.render();
    }
  }
  
  /**
   * Render the emoji selector
   */
  render() {
    if (!this.element) return;
    
    // Hide if not open
    if (!this.state.isOpen) {
      this.element.style.display = 'none';
      return;
    }
    
    // Show element
    this.element.style.display = 'block';
    
    // Create content
    let content = `
      <div class="emoji-selector-header">
        <div class="emoji-selector-tabs">
    `;
    
    // Add tabs for each group
    Object.keys(EMOJI_GROUPS).forEach(group => {
      content += `
        <button class="emoji-group-tab ${group === this.state.activeGroup ? 'active' : ''}"
                data-group="${group}"
                aria-selected="${group === this.state.activeGroup ? 'true' : 'false'}"
                role="tab">
          ${this.getGroupIcon(group)}
        </button>
      `;
    });
    
    content += `
        </div>
        <button class="emoji-selector-close" aria-label="Close emoji selector">✕</button>
      </div>
      <div class="emoji-selector-content" role="tabpanel">
    `;
    
    // Add recent emojis if available
    if (this.state.recentEmojis.length > 0) {
      content += `
        <div class="emoji-group">
          <h3 class="emoji-group-title">Recently Used</h3>
          <div class="emoji-grid">
      `;
      
      this.state.recentEmojis.forEach(emoji => {
        content += `
          <button class="emoji-button" data-emoji="${emoji}" aria-label="Emoji ${emoji}">
            ${emoji}
          </button>
        `;
      });
      
      content += `
          </div>
        </div>
      `;
    }
    
    // Add emojis for active group
    content += `
      <div class="emoji-group">
        <h3 class="emoji-group-title">${this.state.activeGroup}</h3>
        <div class="emoji-grid">
    `;
    
    EMOJI_GROUPS[this.state.activeGroup].forEach(emoji => {
      content += `
        <button class="emoji-button" data-emoji="${emoji}" aria-label="Emoji ${emoji}">
          ${emoji}
        </button>
      `;
    });
    
    content += `
        </div>
      </div>
    </div>
    `;
    
    // Set content
    this.element.innerHTML = content;
    
    // Add event listeners
    this.addEventListeners();
  }
  
  /**
   * Add event listeners to emoji selector elements
   * @private
   */
  addEventListeners() {
    // Close button
    const closeButton = this.element.querySelector('.emoji-selector-close');
    if (closeButton) {
      closeButton.addEventListener('click', this.close);
    }
    
    // Group tabs
    const groupTabs = this.element.querySelectorAll('.emoji-group-tab');
    groupTabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const group = tab.getAttribute('data-group');
        this.setActiveGroup(group);
      });
    });
    
    // Emoji buttons
    const emojiButtons = this.element.querySelectorAll('.emoji-button');
    emojiButtons.forEach(button => {
      button.addEventListener('click', () => {
        const emoji = button.getAttribute('data-emoji');
        this.selectEmoji(emoji);
      });
    });
    
    // Close on outside click
    document.addEventListener('click', (event) => {
      if (this.state.isOpen && this.element && !this.element.contains(event.target)) {
        this.close();
      }
    });
    
    // Close on escape key
    document.addEventListener('keydown', (event) => {
      if (this.state.isOpen && event.key === 'Escape') {
        this.close();
      }
    });
  }
  
  /**
   * Get icon for emoji group
   * @param {string} group - Group name
   * @returns {string} Icon emoji
   * @private
   */
  getGroupIcon(group) {
    const icons = {
      'Smileys & People': '😀',
      'Animals & Nature': '🐶',
      'Food & Drink': '🍔',
      'Travel & Places': '🚗',
      'Activities': '⚽',
      'Objects': '💡',
      'Symbols': '❤️'
    };
    
    return icons[group] || '📋';
  }
}

/**
 * Factory function to create an emoji selector
 * @param {Object} options - Configuration options
 * @returns {EmojiSelector} A new EmojiSelector instance
 */
export function createEmojiSelector(options = {}) {
  return new EmojiSelector(options);
}
