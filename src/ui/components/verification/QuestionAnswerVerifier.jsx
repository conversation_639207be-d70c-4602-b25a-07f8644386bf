import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import './QuestionAnswerVerifier.css';

/**
 * QuestionAnswerVerifier - Socialist Millionaire Protocol (SMP) verification
 * 
 * Features:
 * - Question and answer based verification
 * - Cryptographic verification without revealing answers
 * - Support for both asking and answering questions
 * - Clear guidance and error handling
 */
const QuestionAnswerVerifier = ({
  step,
  onComplete,
  contactName,
  isInitiator = true,
  receivedQuestion = null
}) => {
  const [question, setQuestion] = useState('');
  const [answer, setAnswer] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState(null);
  const [error, setError] = useState(null);
  const [showExamples, setShowExamples] = useState(false);

  // Example questions for guidance
  const exampleQuestions = [
    "What was the name of our first pet?",
    "Where did we first meet?",
    "What's our favorite restaurant?",
    "What movie did we watch on our first date?",
    "What's the name of the street where you grew up?",
    "What was your childhood nickname?",
    "What's our secret code word?",
    "What color was your first car?"
  ];

  useEffect(() => {
    // If we received a question, we're the responder
    if (receivedQuestion && !isInitiator) {
      setQuestion(receivedQuestion);
    }
  }, [receivedQuestion, isInitiator]);

  const validateInputs = () => {
    if (!question.trim()) {
      setError('Please enter a question');
      return false;
    }
    
    if (!answer.trim()) {
      setError('Please enter an answer');
      return false;
    }
    
    if (answer.length < 3) {
      setError('Answer must be at least 3 characters long');
      return false;
    }
    
    return true;
  };

  const handleSubmit = async () => {
    setError(null);
    
    if (!validateInputs()) {
      return;
    }
    
    setIsProcessing(true);
    
    try {
      // Simulate SMP protocol execution
      await simulateSMPVerification();
      
      onComplete({
        method: 'question-answer',
        question: question.trim(),
        answer: answer.trim(),
        verified: verificationStatus === 'success',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      setError('Verification failed. Please try again.');
      setIsProcessing(false);
    }
  };

  const simulateSMPVerification = () => {
    return new Promise((resolve) => {
      // Simulate network delay and SMP protocol
      setTimeout(() => {
        // 90% success rate for demo
        const success = Math.random() > 0.1;
        setVerificationStatus(success ? 'success' : 'failed');
        setIsProcessing(false);
        resolve();
      }, 2000);
    });
  };

  const handleQuestionSelect = (selectedQuestion) => {
    setQuestion(selectedQuestion);
    setShowExamples(false);
  };

  const renderQuestionStep = () => (
    <div className="question-step">
      <h3>Create a Verification Question</h3>
      <p className="step-description">
        Think of a question that only you and {contactName} would know the answer to.
        This could be about a shared experience, memory, or secret.
      </p>
      
      <div className="question-input-section">
        <label htmlFor="verification-question" className="input-label">
          Question for {contactName}
        </label>
        <textarea
          id="verification-question"
          value={question}
          onChange={(e) => setQuestion(e.target.value)}
          placeholder="Enter your question here..."
          className="question-input"
          rows={3}
          maxLength={200}
        />
        <div className="character-count">
          {question.length}/200 characters
        </div>
      </div>
      
      <div className="examples-section">
        <button 
          type="button"
          className="examples-toggle"
          onClick={() => setShowExamples(!showExamples)}
        >
          {showExamples ? 'Hide' : 'Show'} Example Questions
        </button>
        
        {showExamples && (
          <div className="examples-list">
            <p className="examples-intro">Here are some example questions:</p>
            <div className="examples-grid">
              {exampleQuestions.map((example, index) => (
                <button
                  key={index}
                  className="example-question"
                  onClick={() => handleQuestionSelect(example)}
                >
                  {example}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
      
      <div className="security-note">
        <div className="note-icon">🔒</div>
        <div className="note-content">
          <strong>Privacy Note:</strong> Your answer will be verified cryptographically 
          without being revealed to {contactName} or anyone else.
        </div>
      </div>
    </div>
  );

  const renderAnswerStep = () => (
    <div className="answer-step">
      <h3>{isInitiator ? 'Provide Your Answer' : `Answer ${contactName}'s Question`}</h3>
      
      <div className="question-display">
        <div className="question-label">Question:</div>
        <div className="question-text">{question}</div>
      </div>
      
      <div className="answer-input-section">
        <label htmlFor="verification-answer" className="input-label">
          Your Answer
        </label>
        <input
          id="verification-answer"
          type="text"
          value={answer}
          onChange={(e) => setAnswer(e.target.value)}
          placeholder="Enter your answer..."
          className="answer-input"
          maxLength={100}
        />
        <div className="character-count">
          {answer.length}/100 characters
        </div>
      </div>
      
      <div className="answer-tips">
        <h4>Tips for a good answer:</h4>
        <ul>
          <li>Be specific and exact</li>
          <li>Use the same spelling and capitalization</li>
          <li>Avoid extra spaces or punctuation</li>
          <li>Consider case sensitivity</li>
        </ul>
      </div>
      
      {error && (
        <div className="error-message" role="alert">
          <div className="error-icon">⚠️</div>
          <div className="error-text">{error}</div>
        </div>
      )}
    </div>
  );

  const renderProcessingStep = () => (
    <div className="processing-step">
      <div className="processing-animation">
        <div className="spinner"></div>
      </div>
      <h3>Verifying Answer...</h3>
      <p>
        Running cryptographic verification protocol. This may take a few moments.
      </p>
      <div className="processing-details">
        <div className="detail-item">
          <span className="detail-label">Protocol:</span>
          <span className="detail-value">Socialist Millionaire Protocol (SMP)</span>
        </div>
        <div className="detail-item">
          <span className="detail-label">Security:</span>
          <span className="detail-value">Zero-knowledge proof</span>
        </div>
        <div className="detail-item">
          <span className="detail-label">Privacy:</span>
          <span className="detail-value">Answer remains secret</span>
        </div>
      </div>
    </div>
  );

  const renderResultStep = () => (
    <div className="result-step">
      {verificationStatus === 'success' ? (
        <div className="success-result">
          <div className="success-icon">✅</div>
          <h3>Verification Successful!</h3>
          <p>
            Both answers match. Your identity has been verified through shared knowledge.
          </p>
          <div className="verification-summary">
            <div className="summary-item">
              <strong>Method:</strong> Question & Answer (SMP)
            </div>
            <div className="summary-item">
              <strong>Question:</strong> {question}
            </div>
            <div className="summary-item">
              <strong>Status:</strong> ✓ Verified
            </div>
            <div className="summary-item">
              <strong>Privacy:</strong> ✓ Answer kept secret
            </div>
          </div>
        </div>
      ) : (
        <div className="failure-result">
          <div className="failure-icon">❌</div>
          <h3>Verification Failed</h3>
          <p>
            The answers don't match. This could mean:
          </p>
          <ul className="failure-reasons">
            <li>Different answers were provided</li>
            <li>Spelling or capitalization differences</li>
            <li>One person misunderstood the question</li>
            <li>Potential security issue</li>
          </ul>
          <div className="failure-actions">
            <button 
              className="button secondary"
              onClick={() => {
                setVerificationStatus(null);
                setAnswer('');
                setError(null);
              }}
            >
              Try Different Answer
            </button>
            <button 
              className="button secondary"
              onClick={() => {
                setVerificationStatus(null);
                setQuestion('');
                setAnswer('');
                setError(null);
              }}
            >
              Try Different Question
            </button>
          </div>
        </div>
      )}
    </div>
  );

  // Determine which step to render
  if (isProcessing) {
    return renderProcessingStep();
  }
  
  if (verificationStatus) {
    return renderResultStep();
  }
  
  if (step === 1 && isInitiator) {
    return (
      <div className="question-answer-verifier">
        {renderQuestionStep()}
        <div className="step-actions">
          <button 
            className="button primary"
            onClick={() => step < 3 && onComplete({ step: step + 1 })}
            disabled={!question.trim()}
          >
            Continue to Answer
          </button>
        </div>
      </div>
    );
  }
  
  if ((step === 2 && isInitiator) || (!isInitiator && receivedQuestion)) {
    return (
      <div className="question-answer-verifier">
        {renderAnswerStep()}
        <div className="step-actions">
          <button 
            className="button primary"
            onClick={handleSubmit}
            disabled={!answer.trim() || isProcessing}
          >
            {isProcessing ? 'Verifying...' : 'Verify Answer'}
          </button>
          {isInitiator && (
            <button 
              className="button secondary"
              onClick={() => onComplete({ step: step - 1 })}
            >
              Back to Question
            </button>
          )}
        </div>
      </div>
    );
  }
  
  return null;
};

QuestionAnswerVerifier.propTypes = {
  step: PropTypes.number.isRequired,
  onComplete: PropTypes.func.isRequired,
  contactName: PropTypes.string.isRequired,
  isInitiator: PropTypes.bool,
  receivedQuestion: PropTypes.string
};

export default QuestionAnswerVerifier;
