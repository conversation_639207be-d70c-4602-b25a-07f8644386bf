import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import './ManualFingerprintVerifier.css';

/**
 * ManualFingerprintVerifier - Manual fingerprint comparison verification
 * 
 * Features:
 * - Side-by-side fingerprint display
 * - Copy to clipboard functionality
 * - Multiple verification channels guidance
 * - Clear comparison interface
 */
const ManualFingerprintVerifier = ({
  step,
  onComplete,
  fingerprint,
  contactName,
  contactFingerprint = null
}) => {
  const [userConfirmation, setUserConfirmation] = useState(null);
  const [comparisonMethod, setComparisonMethod] = useState('');
  const [notes, setNotes] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [copied, setCopied] = useState(false);

  // Format fingerprint for display
  const formatFingerprint = (fp) => {
    if (!fp) return '';
    // Add spaces every 4 characters for readability
    return fp.replace(/(.{4})/g, '$1 ').trim().toUpperCase();
  };

  // Generate a mock contact fingerprint for demo
  useEffect(() => {
    if (!contactFingerprint && fingerprint) {
      // In real implementation, this would come from the contact
      // For demo, we'll generate a similar but different fingerprint
      const mockContactFingerprint = fingerprint.split('').map((char, index) => {
        // Change a few characters to simulate different fingerprint
        if (index % 7 === 0 && Math.random() > 0.7) {
          return String.fromCharCode(char.charCodeAt(0) + 1);
        }
        return char;
      }).join('');
    }
  }, [fingerprint, contactFingerprint]);

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleConfirmation = (confirmed) => {
    setUserConfirmation(confirmed);
    if (confirmed) {
      onComplete({
        method: 'manual-fingerprint',
        verified: true,
        comparisonMethod,
        notes,
        timestamp: new Date().toISOString()
      });
    }
  };

  const renderFingerprintComparison = () => (
    <div className="fingerprint-comparison">
      <h3>Compare Fingerprints</h3>
      <p className="comparison-instructions">
        Compare your fingerprint with {contactName}'s fingerprint through a secure channel 
        (phone call, in person, encrypted message, etc.).
      </p>
      
      <div className="fingerprints-container">
        <div className="fingerprint-section">
          <div className="fingerprint-header">
            <h4>Your Fingerprint</h4>
            <button 
              className="copy-button"
              onClick={() => copyToClipboard(fingerprint)}
              title="Copy to clipboard"
            >
              {copied ? '✓ Copied' : '📋 Copy'}
            </button>
          </div>
          <div className="fingerprint-display">
            <div className="fingerprint-text">
              {formatFingerprint(fingerprint)}
            </div>
          </div>
        </div>
        
        <div className="fingerprint-section">
          <div className="fingerprint-header">
            <h4>{contactName}'s Fingerprint</h4>
          </div>
          <div className="fingerprint-display">
            {contactFingerprint ? (
              <div className="fingerprint-text">
                {formatFingerprint(contactFingerprint)}
              </div>
            ) : (
              <div className="fingerprint-placeholder">
                <div className="placeholder-icon">👤</div>
                <p>Ask {contactName} to share their fingerprint with you</p>
              </div>
            )}
          </div>
        </div>
      </div>
      
      <div className="verification-methods">
        <h4>Secure Verification Channels</h4>
        <div className="methods-grid">
          <div className="method-card">
            <div className="method-icon">📞</div>
            <div className="method-info">
              <div className="method-title">Phone Call</div>
              <div className="method-description">Read fingerprints to each other</div>
            </div>
          </div>
          
          <div className="method-card">
            <div className="method-icon">👥</div>
            <div className="method-info">
              <div className="method-title">In Person</div>
              <div className="method-description">Compare fingerprints face-to-face</div>
            </div>
          </div>
          
          <div className="method-card">
            <div className="method-icon">📧</div>
            <div className="method-info">
              <div className="method-title">Encrypted Email</div>
              <div className="method-description">Exchange via secure email</div>
            </div>
          </div>
          
          <div className="method-card">
            <div className="method-icon">💬</div>
            <div className="method-info">
              <div className="method-title">Secure Messaging</div>
              <div className="method-description">Use another encrypted app</div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="comparison-method-selection">
        <label htmlFor="comparison-method" className="input-label">
          How did you verify the fingerprints? (Optional)
        </label>
        <select
          id="comparison-method"
          value={comparisonMethod}
          onChange={(e) => setComparisonMethod(e.target.value)}
          className="method-select"
        >
          <option value="">Select verification method...</option>
          <option value="phone-call">Phone call</option>
          <option value="in-person">In person</option>
          <option value="encrypted-email">Encrypted email</option>
          <option value="secure-messaging">Secure messaging app</option>
          <option value="other">Other secure channel</option>
        </select>
      </div>
      
      {showAdvanced && (
        <div className="advanced-options">
          <label htmlFor="verification-notes" className="input-label">
            Additional Notes (Optional)
          </label>
          <textarea
            id="verification-notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add any additional verification details..."
            className="notes-input"
            rows={3}
          />
        </div>
      )}
      
      <button 
        className="advanced-toggle"
        onClick={() => setShowAdvanced(!showAdvanced)}
      >
        {showAdvanced ? 'Hide' : 'Show'} Advanced Options
      </button>
    </div>
  );

  const renderConfirmationStep = () => (
    <div className="confirmation-step">
      <h3>Confirm Verification</h3>
      
      <div className="confirmation-question">
        <div className="question-icon">🔍</div>
        <div className="question-content">
          <h4>Do the fingerprints match exactly?</h4>
          <p>
            Compare every character carefully. The fingerprints must match 
            completely for secure verification.
          </p>
        </div>
      </div>
      
      <div className="fingerprint-summary">
        <div className="summary-section">
          <div className="summary-label">Your Fingerprint:</div>
          <div className="summary-value">{formatFingerprint(fingerprint)}</div>
        </div>
        
        {contactFingerprint && (
          <div className="summary-section">
            <div className="summary-label">{contactName}'s Fingerprint:</div>
            <div className="summary-value">{formatFingerprint(contactFingerprint)}</div>
          </div>
        )}
        
        {comparisonMethod && (
          <div className="summary-section">
            <div className="summary-label">Verification Method:</div>
            <div className="summary-value">
              {comparisonMethod.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </div>
          </div>
        )}
      </div>
      
      <div className="security-warning">
        <div className="warning-icon">⚠️</div>
        <div className="warning-content">
          <strong>Important:</strong> Only confirm if you are absolutely certain 
          the fingerprints match and you verified them through a secure channel.
        </div>
      </div>
      
      <div className="confirmation-actions">
        <button 
          className="button danger"
          onClick={() => handleConfirmation(false)}
        >
          ❌ Fingerprints Don't Match
        </button>
        
        <button 
          className="button success"
          onClick={() => handleConfirmation(true)}
        >
          ✅ Fingerprints Match
        </button>
      </div>
    </div>
  );

  const renderResultStep = () => (
    <div className="result-step">
      {userConfirmation ? (
        <div className="success-result">
          <div className="success-icon">✅</div>
          <h3>Verification Successful!</h3>
          <p>
            You have successfully verified {contactName}'s identity through 
            manual fingerprint comparison.
          </p>
          <div className="verification-details">
            <div className="detail-item">
              <strong>Method:</strong> Manual Fingerprint Comparison
            </div>
            <div className="detail-item">
              <strong>Verified:</strong> {new Date().toLocaleString()}
            </div>
            {comparisonMethod && (
              <div className="detail-item">
                <strong>Channel:</strong> {comparisonMethod.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </div>
            )}
            <div className="detail-item">
              <strong>Security:</strong> ✓ High (Manual verification)
            </div>
          </div>
        </div>
      ) : (
        <div className="failure-result">
          <div className="failure-icon">❌</div>
          <h3>Verification Failed</h3>
          <p>
            The fingerprints don't match. This could indicate a security issue 
            or an error in the verification process.
          </p>
          <div className="failure-actions">
            <button 
              className="button secondary"
              onClick={() => setUserConfirmation(null)}
            >
              Try Again
            </button>
            <button 
              className="button secondary"
              onClick={() => onComplete({ method: 'manual-fingerprint', verified: false })}
            >
              Cancel Verification
            </button>
          </div>
        </div>
      )}
    </div>
  );

  if (step === 1) {
    return (
      <div className="manual-fingerprint-verifier">
        {renderFingerprintComparison()}
        <div className="step-actions">
          <button 
            className="button primary"
            onClick={() => onComplete({ step: 2 })}
          >
            Continue to Confirmation
          </button>
        </div>
      </div>
    );
  }

  if (step === 2 && userConfirmation === null) {
    return (
      <div className="manual-fingerprint-verifier">
        {renderConfirmationStep()}
      </div>
    );
  }

  return (
    <div className="manual-fingerprint-verifier">
      {renderResultStep()}
    </div>
  );
};

ManualFingerprintVerifier.propTypes = {
  step: PropTypes.number.isRequired,
  onComplete: PropTypes.func.isRequired,
  fingerprint: PropTypes.string.isRequired,
  contactName: PropTypes.string.isRequired,
  contactFingerprint: PropTypes.string
};

export default ManualFingerprintVerifier;
