/* Question Answer Verifier Styles */
.question-answer-verifier {
  padding: 1rem 0;
}

/* Question Step */
.question-step h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.step-description {
  margin: 0 0 2rem 0;
  color: #64748b;
  line-height: 1.5;
}

.question-input-section {
  margin-bottom: 2rem;
}

.input-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.question-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  line-height: 1.4;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.question-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.character-count {
  text-align: right;
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.25rem;
}

/* Examples Section */
.examples-section {
  margin-bottom: 2rem;
}

.examples-toggle {
  background: none;
  border: none;
  color: #2563eb;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
}

.examples-toggle:hover {
  color: #1d4ed8;
}

.examples-list {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.examples-intro {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  color: #64748b;
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.5rem;
}

.example-question {
  padding: 0.5rem 0.75rem;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
}

.example-question:hover {
  border-color: #2563eb;
  background: #eff6ff;
}

/* Security Note */
.security-note {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.note-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.note-content {
  font-size: 0.875rem;
  color: #0c4a6e;
  line-height: 1.4;
}

/* Answer Step */
.answer-step h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.question-display {
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.question-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.question-text {
  font-size: 1rem;
  color: #1e293b;
  font-weight: 500;
}

.answer-input-section {
  margin-bottom: 2rem;
}

.answer-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.answer-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.answer-tips {
  background: #fffbeb;
  border: 1px solid #fed7aa;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.answer-tips h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #92400e;
}

.answer-tips ul {
  margin: 0;
  padding-left: 1.25rem;
  color: #92400e;
  font-size: 0.875rem;
}

.answer-tips li {
  margin-bottom: 0.25rem;
}

/* Processing Step */
.processing-step {
  text-align: center;
  padding: 2rem 0;
}

.processing-animation {
  margin-bottom: 2rem;
}

.spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-step h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.processing-step p {
  margin: 0 0 2rem 0;
  color: #64748b;
}

.processing-details {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1.5rem;
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.875rem;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #64748b;
}

.detail-value {
  color: #1e293b;
}

/* Result Step */
.result-step {
  text-align: center;
  padding: 2rem 0;
}

.success-result .success-icon,
.failure-result .failure-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.success-result h3 {
  color: #10b981;
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.failure-result h3 {
  color: #ef4444;
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.success-result p,
.failure-result p {
  margin: 0 0 2rem 0;
  color: #64748b;
}

.verification-summary {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 6px;
  padding: 1.5rem;
  text-align: left;
  margin-bottom: 2rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dcfce7;
  font-size: 0.875rem;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item strong {
  color: #166534;
  font-weight: 600;
}

.failure-reasons {
  text-align: left;
  margin: 0 0 2rem 0;
  padding-left: 1.25rem;
  color: #64748b;
}

.failure-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.error-icon {
  font-size: 1.25rem;
  color: #ef4444;
}

.error-text {
  color: #dc2626;
  font-weight: 500;
  font-size: 0.875rem;
}

/* Step Actions */
.step-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

/* Button Styles */
.button {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.button:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button.primary {
  background: #2563eb;
  color: white;
}

.button.primary:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.button.secondary {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.button.secondary:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 640px) {
  .examples-grid {
    grid-template-columns: 1fr;
  }
  
  .step-actions {
    flex-direction: column;
  }
  
  .failure-actions {
    flex-direction: column;
  }
  
  .button {
    width: 100%;
  }
  
  .processing-details {
    margin: 0;
  }
}
