/**
 * Styles for the Emoji Selector component
 */

.emoji-selector {
  position: absolute;
  z-index: 1000;
  width: 320px;
  max-width: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: none;
}

.emoji-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.emoji-selector-tabs {
  display: flex;
  overflow-x: auto;
  scrollbar-width: thin;
  -ms-overflow-style: none;
}

.emoji-selector-tabs::-webkit-scrollbar {
  height: 4px;
}

.emoji-selector-tabs::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 2px;
}

.emoji-group-tab {
  padding: 8px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  font-size: 18px;
  cursor: pointer;
  min-width: 40px;
  text-align: center;
}

.emoji-group-tab.active {
  border-bottom-color: #0078d4;
}

.emoji-selector-close {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #666;
  padding: 4px 8px;
}

.emoji-selector-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
}

.emoji-group {
  margin-bottom: 16px;
}

.emoji-group-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #666;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
}

.emoji-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;
  transition: background-color 0.2s;
}

.emoji-button:hover {
  background-color: #f0f0f0;
}

.emoji-button:focus {
  outline: 2px solid #0078d4;
  outline-offset: -2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .emoji-selector {
    background-color: #2d2d2d;
    color: #f0f0f0;
  }
  
  .emoji-selector-header {
    border-bottom-color: #444;
  }
  
  .emoji-selector-close {
    color: #ccc;
  }
  
  .emoji-group-title {
    color: #ccc;
  }
  
  .emoji-button:hover {
    background-color: #3d3d3d;
  }
  
  .emoji-selector-tabs::-webkit-scrollbar-thumb {
    background-color: #666;
  }
}

/* Mobile styles */
@media (max-width: 480px) {
  .emoji-selector {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 8px 8px 0 0;
  }
  
  .emoji-grid {
    grid-template-columns: repeat(7, 1fr);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .emoji-button {
    transition: none;
  }
}

/* High contrast mode */
@media (forced-colors: active) {
  .emoji-selector {
    border: 2px solid CanvasText;
  }
  
  .emoji-group-tab.active {
    border-bottom-color: Highlight;
  }
  
  .emoji-button:focus {
    outline: 2px solid Highlight;
  }
}
