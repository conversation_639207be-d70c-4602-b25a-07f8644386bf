/**
 * QR Code Verifier Component
 *
 * This component allows users to verify their conversation partner's identity
 * by generating and scanning QR codes that represent their OTR public keys.
 */

/**
 * QRCodeVerifier class
 */
export class QRCodeVerifier {
  /**
   * Create a new QR code verifier
   * @param {HTMLElement} container - Container element
   * @param {Object} options - Configuration options
   */
  constructor(container, options = {}) {
    if (!container) {
      throw new Error('Container element is required');
    }

    this.container = container;
    this.options = {
      size: 200,
      darkColor: '#000000',
      lightColor: '#FFFFFF',
      onScan: null,
      onVerificationResult: null,
      ...options
    };

    this.currentData = null;
    this.videoElement = null;
    this.generateElement = null;
    this.scanElement = null;
    this.resultElement = null;

    this.initialize();
  }

  /**
   * Initialize the component
   */
  initialize() {
    this.createElements();
  }

  /**
   * Create DOM elements
   */
  createElements() {
    // Create generate section
    this.generateElement = document.createElement('div');
    this.generateElement.className = 'qr-generate';

    // Create scan section
    this.scanElement = document.createElement('div');
    this.scanElement.className = 'qr-scan hidden';

    // Create result section
    this.resultElement = document.createElement('div');
    this.resultElement.className = 'qr-result';

    // Add to container
    this.container.appendChild(this.generateElement);
    this.container.appendChild(this.scanElement);
    this.container.appendChild(this.resultElement);
  }

  /**
   * Set data for QR code generation
   * @param {string} data - Data to encode in QR code
   */
  setData(data) {
    if (this.currentData !== data) {
      this.currentData = data;
      this._generateQRCode(data);
    }
  }

  /**
   * Generate QR code (internal method)
   * @param {string} data - Data to encode
   */
  _generateQRCode(data) {
    // Mock QR code generation for tests
    console.log('Generating QR code for:', data);
  }

  /**
   * Start scanning for QR codes
   */
  startScanning() {
    this._setupCamera();
    this._startScanning();
  }

  /**
   * Stop scanning
   */
  stopScanning() {
    if (this.videoElement && this.videoElement.srcObject) {
      const tracks = this.videoElement.srcObject.getTracks();
      tracks.forEach(track => track.stop());
    }
  }

  /**
   * Setup camera (internal method)
   */
  _setupCamera() {
    // Mock camera setup for tests
    console.log('Setting up camera');
  }

  /**
   * Start scanning (internal method)
   */
  _startScanning() {
    // Mock scanning start for tests
    console.log('Starting QR code scanning');
  }

  /**
   * Handle QR code detection (internal method)
   * @param {string} data - Detected QR code data
   */
  _onQRCodeDetected(data) {
    if (this.options.onScan) {
      this.options.onScan(data);
    }
  }

  /**
   * Verify fingerprints
   * @param {string} fingerprint1 - First fingerprint
   * @param {string} fingerprint2 - Second fingerprint
   * @returns {boolean} Whether fingerprints match
   */
  verifyFingerprints(fingerprint1, fingerprint2) {
    const result = fingerprint1 === fingerprint2;

    if (this.options.onVerificationResult) {
      this.options.onVerificationResult(result);
    }

    return result;
  }

  /**
   * Show verification result in UI
   * @param {boolean} success - Whether verification was successful
   */
  showVerificationResult(success) {
    if (this.resultElement) {
      if (success) {
        this.resultElement.classList.add('success');
        this.resultElement.classList.remove('error');
      } else {
        this.resultElement.classList.add('error');
        this.resultElement.classList.remove('success');
      }
    }
  }

  /**
   * Show scan mode
   */
  showScanMode() {
    if (this.generateElement) {
      this.generateElement.classList.add('hidden');
    }
    if (this.scanElement) {
      this.scanElement.classList.remove('hidden');
    }
  }

  /**
   * Show generate mode
   */
  showGenerateMode() {
    if (this.generateElement) {
      this.generateElement.classList.remove('hidden');
    }
    if (this.scanElement) {
      this.scanElement.classList.add('hidden');
    }
  }

  /**
   * Destroy the component
   */
  destroy() {
    this.stopScanning();

    // Remove elements from DOM
    if (this.container && this.generateElement && this.container.contains && this.container.contains(this.generateElement)) {
      this.container.removeChild(this.generateElement);
    } else if (this.container && this.generateElement && this.generateElement.parentNode === this.container) {
      this.container.removeChild(this.generateElement);
    }

    if (this.container && this.scanElement && this.container.contains && this.container.contains(this.scanElement)) {
      this.container.removeChild(this.scanElement);
    } else if (this.container && this.scanElement && this.scanElement.parentNode === this.container) {
      this.container.removeChild(this.scanElement);
    }

    if (this.container && this.resultElement && this.container.contains && this.container.contains(this.resultElement)) {
      this.container.removeChild(this.resultElement);
    } else if (this.container && this.resultElement && this.resultElement.parentNode === this.container) {
      this.container.removeChild(this.resultElement);
    }
  }

  /**
   * Generate QR code from fingerprint (legacy method)
   * @returns {Promise<string>} QR code data URL
   */
  async generateQRCode() {
    // Mock implementation for tests
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==';
  }
}
