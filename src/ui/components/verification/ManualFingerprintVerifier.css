/* Manual Fingerprint Verifier Styles */
.manual-fingerprint-verifier {
  padding: 1rem 0;
}

/* Fingerprint Comparison */
.fingerprint-comparison h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.comparison-instructions {
  margin: 0 0 2rem 0;
  color: #64748b;
  line-height: 1.5;
}

.fingerprints-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.fingerprint-section {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.fingerprint-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.fingerprint-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.copy-button {
  background: #2563eb;
  color: white;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: #1d4ed8;
}

.fingerprint-display {
  padding: 1.5rem;
  background: white;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fingerprint-text {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  color: #1e293b;
  word-break: break-all;
  text-align: center;
  background: #f8fafc;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.fingerprint-placeholder {
  text-align: center;
  color: #9ca3af;
}

.placeholder-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.fingerprint-placeholder p {
  margin: 0;
  font-size: 0.875rem;
}

/* Verification Methods */
.verification-methods {
  margin-bottom: 2rem;
}

.verification-methods h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.method-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: #f8fafc;
  transition: all 0.2s ease;
}

.method-card:hover {
  border-color: #cbd5e1;
  background: #f1f5f9;
}

.method-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.method-info {
  flex: 1;
}

.method-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.method-description {
  font-size: 0.75rem;
  color: #64748b;
  line-height: 1.3;
}

/* Comparison Method Selection */
.comparison-method-selection {
  margin-bottom: 1rem;
}

.input-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.method-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s ease;
}

.method-select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Advanced Options */
.advanced-options {
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.notes-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  transition: border-color 0.2s ease;
}

.notes-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.advanced-toggle {
  background: none;
  border: none;
  color: #2563eb;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
}

.advanced-toggle:hover {
  color: #1d4ed8;
}

/* Confirmation Step */
.confirmation-step h3 {
  margin: 0 0 2rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
}

.confirmation-question {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: #fffbeb;
  border: 1px solid #fed7aa;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.question-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.question-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #92400e;
}

.question-content p {
  margin: 0;
  color: #92400e;
  font-size: 0.875rem;
  line-height: 1.4;
}

.fingerprint-summary {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.summary-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.summary-section:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-weight: 600;
  color: #64748b;
  font-size: 0.875rem;
}

.summary-value {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
  color: #1e293b;
  background: white;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  word-break: break-all;
}

.security-warning {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  margin-bottom: 2rem;
}

.warning-icon {
  font-size: 1.25rem;
  color: #ef4444;
  flex-shrink: 0;
}

.warning-content {
  color: #dc2626;
  font-size: 0.875rem;
  line-height: 1.4;
}

.confirmation-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Result Step */
.result-step {
  text-align: center;
  padding: 2rem 0;
}

.success-result .success-icon,
.failure-result .failure-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.success-result h3 {
  color: #10b981;
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.failure-result h3 {
  color: #ef4444;
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.success-result p,
.failure-result p {
  margin: 0 0 2rem 0;
  color: #64748b;
}

.verification-details {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: left;
  margin-bottom: 2rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dcfce7;
  font-size: 0.875rem;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item strong {
  color: #166534;
  font-weight: 600;
}

.failure-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Step Actions */
.step-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

/* Button Styles */
.button {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.button:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button.primary {
  background: #2563eb;
  color: white;
}

.button.primary:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.button.secondary {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.button.secondary:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.button.success {
  background: #10b981;
  color: white;
}

.button.success:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
}

.button.danger {
  background: #ef4444;
  color: white;
}

.button.danger:hover:not(:disabled) {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .fingerprints-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .methods-grid {
    grid-template-columns: 1fr;
  }
  
  .confirmation-actions {
    flex-direction: column;
  }
  
  .failure-actions {
    flex-direction: column;
  }
  
  .step-actions {
    flex-direction: column;
  }
  
  .button {
    width: 100%;
  }
  
  .fingerprint-text {
    font-size: 0.75rem;
  }
}
