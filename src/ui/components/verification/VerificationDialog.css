/* Verification Dialog Styles */
.verification-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.verification-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Dialog Header */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.dialog-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #64748b;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #f1f5f9;
  color: #1e293b;
}

.close-button:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Dialog Content */
.dialog-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

/* Step Indicator */
.verification-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 1rem;
  left: calc(100% + 0.5rem);
  width: 1rem;
  height: 2px;
  background: #e2e8f0;
  transition: background 0.3s ease;
}

.step.completed:not(:last-child)::after {
  background: #10b981;
}

.step-number {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: #e2e8f0;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: #2563eb;
  color: white;
}

.step.completed .step-number {
  background: #10b981;
  color: white;
}

.step-label {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  text-align: center;
}

.step.active .step-label {
  color: #2563eb;
}

/* Method Selector */
.method-selector h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
}

.method-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.method-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.method-option:hover {
  border-color: #cbd5e1;
  background: #f8fafc;
}

.method-option.selected {
  border-color: #2563eb;
  background: #eff6ff;
}

.method-option:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

.method-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.method-info {
  flex: 1;
}

.method-title {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.method-description {
  font-size: 0.875rem;
  color: #64748b;
}

/* Verification Steps */
.verification-step {
  text-align: center;
}

.qr-code-step .qr-placeholder {
  margin-bottom: 2rem;
}

.qr-code-display {
  width: 200px;
  height: 200px;
  margin: 0 auto 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

.qr-mock {
  font-weight: 600;
  color: #64748b;
  font-size: 0.875rem;
}

.verification-confirm {
  padding: 2rem 0;
}

.success-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.verification-confirm h3 {
  margin: 0 0 1rem 0;
  color: #10b981;
  font-size: 1.5rem;
}

.verification-confirm p {
  color: #64748b;
  margin-bottom: 2rem;
}

/* Error Message */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.error-icon {
  font-size: 1.25rem;
  color: #ef4444;
}

.error-text {
  color: #dc2626;
  font-weight: 500;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem 0;
  color: #64748b;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dialog Footer */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

/* Buttons */
.button {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.button:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button.primary {
  background: #2563eb;
  color: white;
}

.button.primary:hover:not(:disabled) {
  background: #1d4ed8;
}

.button.secondary {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.button.secondary:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 640px) {
  .verification-dialog {
    width: 95%;
    margin: 1rem;
  }
  
  .dialog-header,
  .dialog-content,
  .dialog-footer {
    padding: 1rem;
  }
  
  .verification-steps {
    gap: 0.5rem;
  }
  
  .step-number {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.75rem;
  }
  
  .method-option {
    padding: 0.75rem;
  }
  
  .method-icon {
    font-size: 1.5rem;
  }
}
