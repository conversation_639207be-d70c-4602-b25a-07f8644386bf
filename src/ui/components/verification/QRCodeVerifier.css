/* QR Code Verifier Styles */
.qr-code-verifier {
  padding: 1rem 0;
}

.qr-both-modes {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.mode-separator {
  text-align: center;
  position: relative;
  margin: 1rem 0;
}

.mode-separator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e2e8f0;
  z-index: 1;
}

.mode-separator span {
  background: white;
  padding: 0 1rem;
  color: #64748b;
  font-weight: 500;
  font-size: 0.875rem;
  position: relative;
  z-index: 2;
}

/* QR Code Display */
.qr-display-section {
  text-align: center;
}

.qr-display-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.qr-code-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.qr-code-placeholder {
  width: 200px;
  height: 200px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qr-pattern {
  width: 120px;
  height: 120px;
  margin-bottom: 0.5rem;
}

.qr-squares {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(5, 1fr);
  gap: 2px;
  width: 100%;
  height: 100%;
}

.qr-square {
  background: #f1f5f9;
  border-radius: 1px;
}

.qr-square.filled {
  background: #1e293b;
}

.qr-data-preview {
  font-size: 0.75rem;
  color: #64748b;
  text-align: center;
  word-break: break-all;
}

.qr-instructions {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0;
  line-height: 1.4;
}

/* QR Code Scanner */
.qr-scanner-section {
  text-align: center;
}

.qr-scanner-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.scanner-start {
  padding: 2rem;
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  background: #f8fafc;
}

.camera-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.scanner-start p {
  margin: 0 0 1.5rem 0;
  color: #64748b;
  font-size: 0.875rem;
}

.scanner-active {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.camera-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.camera-video {
  width: 100%;
  max-width: 400px;
  height: auto;
  display: block;
}

.scan-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}

.scan-frame {
  width: 200px;
  height: 200px;
  border: 3px solid #10b981;
  border-radius: 8px;
  position: relative;
  animation: pulse 2s infinite;
}

.scan-frame::before,
.scan-frame::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #10b981;
}

.scan-frame::before {
  top: -3px;
  left: -3px;
  border-right: none;
  border-bottom: none;
}

.scan-frame::after {
  bottom: -3px;
  right: -3px;
  border-left: none;
  border-top: none;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.scan-instructions {
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Error States */
.camera-error,
.no-camera {
  padding: 2rem;
  text-align: center;
  border: 1px solid #fed7aa;
  border-radius: 8px;
  background: #fffbeb;
}

.error-icon,
.info-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.camera-error p,
.no-camera p {
  margin: 0 0 1.5rem 0;
  color: #92400e;
  font-weight: 500;
}

/* Manual Input */
.manual-input {
  margin-top: 1.5rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
}

.manual-input h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.manual-code-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  font-family: 'Monaco', 'Menlo', monospace;
  resize: vertical;
  margin-bottom: 1rem;
}

.manual-code-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.manual-input-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

/* Verification Result */
.verification-result {
  text-align: center;
  padding: 2rem;
}

.success-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.verification-result h3 {
  margin: 0 0 1rem 0;
  color: #10b981;
  font-size: 1.5rem;
  font-weight: 600;
}

.verification-result p {
  margin: 0 0 2rem 0;
  color: #64748b;
  font-size: 1rem;
}

.verification-details {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #dcfce7;
  font-size: 0.875rem;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item strong {
  color: #166534;
  font-weight: 600;
}

/* Button Styles */
.button {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.button:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button.primary {
  background: #2563eb;
  color: white;
}

.button.primary:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.button.secondary {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
}

.button.secondary:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 640px) {
  .qr-both-modes {
    gap: 1.5rem;
  }
  
  .qr-code-placeholder {
    width: 160px;
    height: 160px;
  }
  
  .qr-pattern {
    width: 100px;
    height: 100px;
  }
  
  .camera-video {
    max-width: 300px;
  }
  
  .scan-frame {
    width: 150px;
    height: 150px;
  }
  
  .manual-input-actions {
    flex-direction: column;
  }
  
  .button {
    width: 100%;
  }
}
