/**
 * QR Code Verifier Component Styles
 */

/* QR scanner container */
.qr-scanner-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 10000;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Video container */
.qr-video-container {
  position: relative;
  width: 90%;
  max-width: 500px;
  overflow: hidden;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.qr-video-container video {
  width: 100%;
  height: auto;
  display: block;
}

/* Canvas element */
#qr-canvas {
  display: none;
}

/* Scanner overlay */
.qr-scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  pointer-events: none;
  color: white;
}

/* Scanner header and footer */
.qr-scanner-header, .qr-scanner-footer {
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.6);
  pointer-events: auto;
  text-align: center;
}

.qr-scanner-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: bold;
}

.qr-scanner-header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

/* Scanner buttons */
.qr-scanner-footer {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.qr-scanner-footer button {
  flex: 1;
  padding: 10px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.qr-scanner-cancel {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.qr-scanner-cancel:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.qr-scanner-manual {
  background-color: #0078d4;
  color: white;
}

.qr-scanner-manual:hover {
  background-color: #006cbe;
}

/* Scan indicator */
.qr-scan-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #0078d4;
  animation: scan-animation 2s linear infinite;
  z-index: 2;
}

@keyframes scan-animation {
  0% {
    top: 0;
  }
  50% {
    top: calc(100% - 2px);
  }
  100% {
    top: 0;
  }
}

/* Targeting area */
.qr-video-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200px;
  height: 200px;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.qr-video-container::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.8);
  z-index: 1;
}

/* Success state */
.qr-video-container.scan-success::before {
  border-color: #2ecc71;
  animation: success-pulse 0.5s ease-out;
}

@keyframes success-pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

/* Error message */
.qr-error-message {
  position: absolute;
  bottom: 80px;
  left: 0;
  width: 100%;
  background-color: rgba(231, 76, 60, 0.9);
  color: white;
  padding: 10px;
  text-align: center;
  font-size: 14px;
  z-index: 3;
}

/* Camera error */
.qr-scanner-error {
  padding: 30px 20px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  text-align: center;
  color: white;
}

.qr-scanner-error h2 {
  margin: 0 0 16px 0;
  color: #e74c3c;
}

.qr-scanner-error p {
  margin: 0 0 24px 0;
}

.qr-scanner-error button {
  margin: 0 8px;
  padding: 10px 20px;
  border-radius: 4px;
  border: none;
  font-size: 14px;
  cursor: pointer;
}

.qr-scanner-close {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.qr-scanner-close:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Responsive styles */
@media (max-width: 480px) {
  .qr-video-container::before {
    width: 160px;
    height: 160px;
  }
  
  .qr-scanner-footer {
    flex-direction: column;
  }
}
