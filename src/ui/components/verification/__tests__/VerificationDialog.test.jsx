import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import VerificationDialog from '../VerificationDialog.jsx';

// Mock the verification components
jest.mock('../QRCodeVerifier.jsx', () => {
  return function MockQRCodeVerifier({ onComplete }) {
    return (
      <div data-testid="qr-code-verifier">
        <button onClick={() => onComplete({ method: 'qr-code', verified: true })}>
          Complete QR Verification
        </button>
      </div>
    );
  };
});

jest.mock('../QuestionAnswerVerifier.jsx', () => {
  return function MockQuestionAnswerVerifier({ onComplete }) {
    return (
      <div data-testid="question-answer-verifier">
        <button onClick={() => onComplete({ method: 'question-answer', verified: true })}>
          Complete Q&A Verification
        </button>
      </div>
    );
  };
});

jest.mock('../ManualFingerprintVerifier.jsx', () => {
  return function MockManualFingerprintVerifier({ onComplete }) {
    return (
      <div data-testid="manual-fingerprint-verifier">
        <button onClick={() => onComplete({ method: 'manual-fingerprint', verified: true })}>
          Complete Manual Verification
        </button>
      </div>
    );
  };
});

describe('VerificationDialog', () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    onComplete: jest.fn(),
    contactName: 'Alice',
    fingerprint: 'test-fingerprint-123'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when open', () => {
    render(<VerificationDialog {...defaultProps} />);
    expect(screen.getByText('Verify Identity')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<VerificationDialog {...defaultProps} isOpen={false} />);
    expect(screen.queryByText('Verify Identity')).not.toBeInTheDocument();
  });

  it('shows method selection by default', () => {
    render(<VerificationDialog {...defaultProps} />);
    expect(screen.getByText('Choose Verification Method')).toBeInTheDocument();
    expect(screen.getByText('QR Code')).toBeInTheDocument();
    expect(screen.getByText('Question & Answer')).toBeInTheDocument();
    expect(screen.getByText('Manual Verification')).toBeInTheDocument();
  });

  it('allows selecting QR Code method', async () => {
    const user = userEvent.setup();
    render(<VerificationDialog {...defaultProps} />);
    
    const qrOption = screen.getByText('QR Code').closest('button');
    await user.click(qrOption);
    
    expect(qrOption).toHaveClass('selected');
  });

  it('allows selecting Question & Answer method', async () => {
    const user = userEvent.setup();
    render(<VerificationDialog {...defaultProps} />);
    
    const qaOption = screen.getByText('Question & Answer').closest('button');
    await user.click(qaOption);
    
    expect(qaOption).toHaveClass('selected');
  });

  it('allows selecting Manual Verification method', async () => {
    const user = userEvent.setup();
    render(<VerificationDialog {...defaultProps} />);
    
    const manualOption = screen.getByText('Manual Verification').closest('button');
    await user.click(manualOption);
    
    expect(manualOption).toHaveClass('selected');
  });

  it('shows QR Code verifier when QR method is selected and step > 0', () => {
    render(<VerificationDialog {...defaultProps} verificationMethod="qr-code" />);
    
    // Simulate moving to step 1
    const dialog = screen.getByRole('dialog', { hidden: true });
    fireEvent.click(screen.getByText('QR Code').closest('button'));
    
    // Should show QR verifier in next step
    expect(screen.getByTestId('qr-code-verifier')).toBeInTheDocument();
  });

  it('shows Question & Answer verifier when Q&A method is selected', () => {
    render(<VerificationDialog {...defaultProps} verificationMethod="question-answer" />);
    expect(screen.getByTestId('question-answer-verifier')).toBeInTheDocument();
  });

  it('shows Manual Fingerprint verifier when manual method is selected', () => {
    render(<VerificationDialog {...defaultProps} verificationMethod="manual-fingerprint" />);
    expect(screen.getByTestId('manual-fingerprint-verifier')).toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', async () => {
    const user = userEvent.setup();
    const onClose = jest.fn();
    render(<VerificationDialog {...defaultProps} onClose={onClose} />);
    
    const closeButton = screen.getByLabelText('Close');
    await user.click(closeButton);
    
    expect(onClose).toHaveBeenCalled();
  });

  it('calls onComplete when verification is completed', async () => {
    const user = userEvent.setup();
    const onComplete = jest.fn();
    render(
      <VerificationDialog 
        {...defaultProps} 
        onComplete={onComplete}
        verificationMethod="qr-code"
      />
    );
    
    const completeButton = screen.getByText('Complete QR Verification');
    await user.click(completeButton);
    
    await waitFor(() => {
      expect(onComplete).toHaveBeenCalledWith({
        method: 'qr-code',
        data: { method: 'qr-code', verified: true },
        verified: true,
        timestamp: expect.any(String)
      });
    });
  });

  it('shows loading state during verification', async () => {
    const user = userEvent.setup();
    render(
      <VerificationDialog 
        {...defaultProps} 
        verificationMethod="qr-code"
      />
    );
    
    const completeButton = screen.getByText('Complete QR Verification');
    await user.click(completeButton);
    
    expect(screen.getByText('Verifying identity...')).toBeInTheDocument();
    expect(screen.getByRole('progressbar', { hidden: true })).toBeInTheDocument();
  });

  it('shows step indicator for multi-step verification', () => {
    render(
      <VerificationDialog 
        {...defaultProps} 
        verificationMethod="question-answer"
      />
    );
    
    const stepIndicators = screen.getAllByText(/Step \d/);
    expect(stepIndicators.length).toBeGreaterThan(0);
  });

  it('handles verification errors gracefully', async () => {
    const user = userEvent.setup();
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // Mock a verification component that throws an error
    jest.doMock('../QRCodeVerifier.jsx', () => {
      return function ErrorQRCodeVerifier() {
        throw new Error('Verification failed');
      };
    });
    
    render(
      <VerificationDialog 
        {...defaultProps} 
        verificationMethod="qr-code"
      />
    );
    
    // Should handle error gracefully and not crash
    expect(screen.getByText('Verify Identity')).toBeInTheDocument();
    
    consoleSpy.mockRestore();
  });

  it('resets state when dialog is reopened', () => {
    const { rerender } = render(
      <VerificationDialog {...defaultProps} isOpen={false} />
    );
    
    rerender(<VerificationDialog {...defaultProps} isOpen={true} />);
    
    // Should show method selection again
    expect(screen.getByText('Choose Verification Method')).toBeInTheDocument();
  });

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup();
    render(<VerificationDialog {...defaultProps} />);
    
    // Tab to first method option
    await user.tab();
    expect(screen.getByText('QR Code').closest('button')).toHaveFocus();
    
    // Tab to next method option
    await user.tab();
    expect(screen.getByText('Question & Answer').closest('button')).toHaveFocus();
  });

  it('supports escape key to close dialog', async () => {
    const user = userEvent.setup();
    const onClose = jest.fn();
    render(<VerificationDialog {...defaultProps} onClose={onClose} />);
    
    await user.keyboard('{Escape}');
    
    expect(onClose).toHaveBeenCalled();
  });

  it('has proper ARIA attributes for accessibility', () => {
    render(<VerificationDialog {...defaultProps} />);
    
    const dialog = screen.getByRole('dialog', { hidden: true });
    expect(dialog).toHaveAttribute('aria-modal', 'true');
    expect(dialog).toHaveAttribute('aria-labelledby');
  });

  it('traps focus within the dialog', async () => {
    const user = userEvent.setup();
    render(<VerificationDialog {...defaultProps} />);
    
    const closeButton = screen.getByLabelText('Close');
    const firstMethodButton = screen.getByText('QR Code').closest('button');
    
    // Focus should be trapped within dialog
    closeButton.focus();
    await user.tab();
    expect(firstMethodButton).toHaveFocus();
  });

  it('passes correct props to verification components', () => {
    render(
      <VerificationDialog 
        {...defaultProps} 
        verificationMethod="manual-fingerprint"
        contactName="Bob"
        fingerprint="abc123"
      />
    );
    
    const verifier = screen.getByTestId('manual-fingerprint-verifier');
    expect(verifier).toBeInTheDocument();
    
    // Props should be passed correctly to the component
    // (This would be verified by checking the component's behavior)
  });
});
