/* Status Indicator Styles */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.status-indicator.clickable {
  cursor: pointer;
}

.status-indicator.clickable:hover {
  background: rgba(0, 0, 0, 0.05);
}

.status-indicator.clickable:focus {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Status Colors */
.status-indicator.success {
  color: #10b981;
}

.status-indicator.warning {
  color: #f59e0b;
}

.status-indicator.danger {
  color: #ef4444;
}

.status-indicator.verified {
  color: #059669;
}

/* Status Icon */
.status-icon {
  font-size: 1.25rem;
  line-height: 1;
}

/* Status Content */
.status-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.status-label {
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.2;
}

.status-description {
  font-size: 0.75rem;
  color: #64748b;
  line-height: 1.3;
}

/* Sizes */
.status-indicator.size-sm {
  padding: 0.25rem;
  gap: 0.5rem;
}

.status-indicator.size-sm .status-icon {
  font-size: 1rem;
}

.status-indicator.size-sm .status-label {
  font-size: 0.75rem;
}

.status-indicator.size-sm .status-description {
  font-size: 0.625rem;
}

.status-indicator.size-lg {
  padding: 0.75rem;
  gap: 1rem;
}

.status-indicator.size-lg .status-icon {
  font-size: 1.5rem;
}

.status-indicator.size-lg .status-label {
  font-size: 1rem;
}

.status-indicator.size-lg .status-description {
  font-size: 0.875rem;
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.status-badge.success {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.warning {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.danger {
  background: #fee2e2;
  color: #991b1b;
}

.status-badge.verified {
  background: #d1fae5;
  color: #047857;
}

.badge-icon {
  font-size: 0.75rem;
}

.badge-label {
  font-size: inherit;
}

/* Badge Sizes */
.status-badge.size-xs {
  padding: 0.125rem 0.25rem;
  font-size: 0.625rem;
  gap: 0.125rem;
}

.status-badge.size-xs .badge-icon {
  font-size: 0.625rem;
}

.status-badge.size-md {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  gap: 0.375rem;
}

.status-badge.size-md .badge-icon {
  font-size: 0.875rem;
}

/* Security Alert */
.security-alert {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid;
  margin: 1rem 0;
}

.security-alert.info {
  background: #eff6ff;
  border-color: #bfdbfe;
  color: #1e40af;
}

.security-alert.warning {
  background: #fffbeb;
  border-color: #fed7aa;
  color: #92400e;
}

.security-alert.danger {
  background: #fef2f2;
  border-color: #fecaca;
  color: #991b1b;
}

.security-alert.success {
  background: #f0fdf4;
  border-color: #bbf7d0;
  color: #166534;
}

.alert-icon {
  font-size: 1.25rem;
  line-height: 1;
  flex-shrink: 0;
}

.alert-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.alert-message {
  font-weight: 500;
  line-height: 1.4;
}

.alert-action {
  align-self: flex-start;
  background: none;
  border: 1px solid currentColor;
  color: inherit;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.alert-action:hover {
  background: currentColor;
  color: white;
}

.alert-action:focus {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}

.alert-dismiss {
  background: none;
  border: none;
  color: inherit;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  line-height: 1;
  flex-shrink: 0;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.alert-dismiss:hover {
  opacity: 1;
}

.alert-dismiss:focus {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}

/* Progress Indicator */
.progress-indicator {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.progress-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-status {
  font-weight: 500;
  color: #374151;
}

.progress-percentage {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 0.5rem;
  background: #e5e7eb;
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #2563eb;
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

/* Progress Sizes */
.progress-indicator.size-sm .progress-bar {
  height: 0.25rem;
}

.progress-indicator.size-sm .progress-status {
  font-size: 0.875rem;
}

.progress-indicator.size-sm .progress-percentage {
  font-size: 0.75rem;
}

.progress-indicator.size-lg .progress-bar {
  height: 0.75rem;
}

.progress-indicator.size-lg .progress-status {
  font-size: 1.125rem;
}

.progress-indicator.size-lg .progress-percentage {
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 640px) {
  .security-alert {
    padding: 0.75rem;
    gap: 0.5rem;
  }
  
  .alert-content {
    gap: 0.5rem;
  }
  
  .status-indicator.size-lg {
    padding: 0.5rem;
    gap: 0.75rem;
  }
}
