import React from 'react';
import PropTypes from 'prop-types';
import './StatusIndicator.css';

/**
 * StatusIndicator - Visual indicator for OTR session status
 * 
 * Displays current encryption and verification status with clear visual cues
 */
const StatusIndicator = ({
  status = 'disconnected',
  size = 'md',
  showLabel = true,
  showDetails = false,
  className = '',
  onClick
}) => {
  const getStatusConfig = (status) => {
    const configs = {
      disconnected: {
        icon: '🔴',
        label: 'Not Connected',
        description: 'No OTR session active',
        color: 'danger',
        ariaLabel: 'OTR session disconnected'
      },
      connecting: {
        icon: '🟡',
        label: 'Connecting',
        description: 'Establishing OTR session',
        color: 'warning',
        ariaLabel: 'OTR session connecting'
      },
      connected: {
        icon: '🟢',
        label: 'Connected',
        description: 'OTR session active, messages encrypted',
        color: 'success',
        ariaLabel: 'OTR session connected and encrypted'
      },
      verified: {
        icon: '✅',
        label: 'Verified',
        description: 'Identity verified, secure communication',
        color: 'verified',
        ariaLabel: 'OTR session verified and secure'
      },
      unverified: {
        icon: '⚠️',
        label: 'Unverified',
        description: 'Connected but identity not verified',
        color: 'warning',
        ariaLabel: 'OTR session connected but unverified'
      },
      error: {
        icon: '❌',
        label: 'Error',
        description: 'Connection error or security issue',
        color: 'danger',
        ariaLabel: 'OTR session error'
      }
    };
    
    return configs[status] || configs.disconnected;
  };

  const config = getStatusConfig(status);
  
  const handleClick = () => {
    if (onClick) {
      onClick(status);
    }
  };

  const handleKeyDown = (event) => {
    if (onClick && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      onClick(status);
    }
  };

  return (
    <div 
      className={`status-indicator ${config.color} size-${size} ${className} ${onClick ? 'clickable' : ''}`}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      role={onClick ? 'button' : 'status'}
      tabIndex={onClick ? 0 : -1}
      aria-label={config.ariaLabel}
    >
      <div className="status-icon">
        {config.icon}
      </div>
      
      {showLabel && (
        <div className="status-content">
          <div className="status-label">
            {config.label}
          </div>
          
          {showDetails && (
            <div className="status-description">
              {config.description}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * StatusBadge - Compact status indicator for inline use
 */
const StatusBadge = ({
  status = 'disconnected',
  size = 'sm',
  className = ''
}) => {
  const getStatusConfig = (status) => {
    const configs = {
      encrypted: {
        icon: '🔒',
        label: 'Encrypted',
        color: 'success'
      },
      unencrypted: {
        icon: '🔓',
        label: 'Unencrypted',
        color: 'danger'
      },
      verified: {
        icon: '✓',
        label: 'Verified',
        color: 'verified'
      },
      unverified: {
        icon: '?',
        label: 'Unverified',
        color: 'warning'
      }
    };
    
    return configs[status] || configs.unencrypted;
  };

  const config = getStatusConfig(status);

  return (
    <span 
      className={`status-badge ${config.color} size-${size} ${className}`}
      aria-label={`Message is ${config.label.toLowerCase()}`}
    >
      <span className="badge-icon">{config.icon}</span>
      <span className="badge-label">{config.label}</span>
    </span>
  );
};

/**
 * SecurityAlert - Prominent alert for security issues
 */
const SecurityAlert = ({
  level = 'warning',
  message,
  action,
  onAction,
  onDismiss,
  className = ''
}) => {
  const getLevelConfig = (level) => {
    const configs = {
      info: {
        icon: 'ℹ️',
        color: 'info'
      },
      warning: {
        icon: '⚠️',
        color: 'warning'
      },
      error: {
        icon: '❌',
        color: 'danger'
      },
      success: {
        icon: '✅',
        color: 'success'
      }
    };
    
    return configs[level] || configs.warning;
  };

  const config = getLevelConfig(level);

  return (
    <div 
      className={`security-alert ${config.color} ${className}`}
      role="alert"
      aria-live="polite"
    >
      <div className="alert-icon">
        {config.icon}
      </div>
      
      <div className="alert-content">
        <div className="alert-message">
          {message}
        </div>
        
        {action && onAction && (
          <button 
            className="alert-action"
            onClick={onAction}
          >
            {action}
          </button>
        )}
      </div>
      
      {onDismiss && (
        <button 
          className="alert-dismiss"
          onClick={onDismiss}
          aria-label="Dismiss alert"
        >
          ✕
        </button>
      )}
    </div>
  );
};

/**
 * ProgressIndicator - Shows progress of ongoing operations
 */
const ProgressIndicator = ({
  progress = 0,
  status = 'Connecting...',
  showPercentage = false,
  size = 'md',
  className = ''
}) => {
  return (
    <div className={`progress-indicator size-${size} ${className}`}>
      <div className="progress-content">
        <div className="progress-status">{status}</div>
        {showPercentage && (
          <div className="progress-percentage">{Math.round(progress)}%</div>
        )}
      </div>
      
      <div className="progress-bar">
        <div 
          className="progress-fill"
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
          role="progressbar"
          aria-valuenow={progress}
          aria-valuemin="0"
          aria-valuemax="100"
        />
      </div>
    </div>
  );
};

// PropTypes
StatusIndicator.propTypes = {
  status: PropTypes.oneOf(['disconnected', 'connecting', 'connected', 'verified', 'unverified', 'error']),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  showLabel: PropTypes.bool,
  showDetails: PropTypes.bool,
  className: PropTypes.string,
  onClick: PropTypes.func
};

StatusBadge.propTypes = {
  status: PropTypes.oneOf(['encrypted', 'unencrypted', 'verified', 'unverified']),
  size: PropTypes.oneOf(['xs', 'sm', 'md']),
  className: PropTypes.string
};

SecurityAlert.propTypes = {
  level: PropTypes.oneOf(['info', 'warning', 'error', 'success']),
  message: PropTypes.string.isRequired,
  action: PropTypes.string,
  onAction: PropTypes.func,
  onDismiss: PropTypes.func,
  className: PropTypes.string
};

ProgressIndicator.propTypes = {
  progress: PropTypes.number,
  status: PropTypes.string,
  showPercentage: PropTypes.bool,
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  className: PropTypes.string
};

export { StatusIndicator, StatusBadge, SecurityAlert, ProgressIndicator };
export default StatusIndicator;
