/**
 * OTR Status Indicator Component
 *
 * This component displays the current OTR encryption and verification status.
 * It provides visual feedback about whether a conversation is encrypted and verified.
 */

/**
 * Encryption status constants
 */
export const STATUS = {
  DISABLED: 'disabled',
  ENCRYPTED: 'encrypted',
  VERIFIED: 'verified',
  WARNING: 'warning',
  ERROR: 'error'
};

/**
 * Import OTR state constants from protocol
 */
import { STATE } from '../../../core/protocol/state';
export { STATE };

/**
 * Simple StatusIndicator class for tests
 */
export class StatusIndicator {
  constructor(container, options = {}) {
    if (!container) {
      throw new Error('Container element is required');
    }

    this.container = container;
    this.options = {
      showText: false,
      clickable: false,
      onClick: null,
      ...options
    };

    this.currentState = STATE.PLAINTEXT;
    this.element = document.createElement('div');
    this.element.className = 'status-indicator';

    if (this.options.showText) {
      this.textElement = document.createElement('span');
      this.textElement.className = 'status-text';
      this.element.appendChild(this.textElement);
    }

    if (this.options.clickable) {
      this._clickHandler = () => {
        if (this.options.onClick) {
          this.options.onClick(this.currentState);
        }
      };
      this.element.addEventListener('click', this._clickHandler);
    }

    this.container.appendChild(this.element);
  }

  updateState(state) {
    this.currentState = state;

    // Remove existing state classes (individually for test compatibility)
    this.element.classList.remove('encrypted');
    this.element.classList.remove('in-progress');

    // Add appropriate classes based on state
    switch (state) {
      case STATE.ENCRYPTED: // 4
        this.element.classList.add('encrypted');
        break;
      case STATE.AWAITING_DHKEY: // 1
      case STATE.AWAITING_REVEALSIG: // 2
      case STATE.AWAITING_SIG: // 3
        this.element.classList.add('in-progress');
        break;
    }

    if (this.textElement) {
      switch (state) {
        case STATE.PLAINTEXT: // 0
          this.textElement.innerHTML = 'Not Encrypted';
          break;
        case STATE.ENCRYPTED: // 4
          this.textElement.innerHTML = 'Encrypted';
          break;
        case STATE.FINISHED: // 5
          this.textElement.innerHTML = 'Finished';
          break;
        case STATE.AWAITING_DHKEY: // 1
        case STATE.AWAITING_REVEALSIG: // 2
        case STATE.AWAITING_SIG: // 3
          this.textElement.innerHTML = 'Establishing';
          break;
        default:
          this.textElement.innerHTML = 'Unknown';
      }
    }
  }

  destroy() {
    // Remove event listeners
    if (this.options.clickable && this.element) {
      this.element.removeEventListener('click', this._clickHandler);
    }

    // Remove from DOM
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }

    // Also remove from container if it's still there
    if (this.container && this.container.removeChild && this.element && this.container.contains && this.container.contains(this.element)) {
      this.container.removeChild(this.element);
    } else if (this.container && this.container.removeChild && this.element && this.element.parentNode === this.container) {
      this.container.removeChild(this.element);
    }
  }
}

/**
 * OtrStatusIndicator class (full implementation)
 */
export class OtrStatusIndicator {
  /**
   * Create a new OTR status indicator
   * @param {HTMLElement} container - Container element to attach the status indicator
   * @param {Object} options - Configuration options
   */
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      compact: false,
      showDetails: false,
      ...options
    };
    
    this.state = {
      status: STATUS.DISABLED,
      detail: '',
      verificationDate: null,
      expanded: false,
      optionsOpen: false
    };
    
    this.elements = {
      root: null,
      icon: null,
      label: null,
      details: null,
      optionsMenu: null
    };
    
    this._boundHandlers = {
      onClick: this._handleClick.bind(this),
      onMouseEnter: this._handleMouseEnter.bind(this),
      onMouseLeave: this._handleMouseLeave.bind(this),
      onKeyDown: this._handleKeyDown.bind(this)
    };
  }
  
  /**
   * Initialize the component
   */
  initialize() {
    // Create DOM elements
    this._createElements();
    
    // Attach event listeners
    this._attachEventListeners();
    
    // Render initial state
    this.render();
    
    return this;
  }
  
  /**
   * Create DOM elements
   * @private
   */
  _createElements() {
    // Create root element
    this.elements.root = document.createElement('div');
    this.elements.root.className = 'otr-status-indicator';
    this.elements.root.setAttribute('role', 'status');
    this.elements.root.setAttribute('aria-live', 'polite');
    this.elements.root.setAttribute('tabindex', '0');
    
    // Create icon element
    this.elements.icon = document.createElement('span');
    this.elements.icon.className = 'otr-status-icon';
    
    // Create label element
    this.elements.label = document.createElement('span');
    this.elements.label.className = 'otr-status-label';
    
    // Create details element
    this.elements.details = document.createElement('div');
    this.elements.details.className = 'otr-status-details';
    this.elements.details.style.display = 'none';
    
    // Create options menu element
    this.elements.optionsMenu = document.createElement('div');
    this.elements.optionsMenu.className = 'otr-status-options';
    this.elements.optionsMenu.style.display = 'none';
    
    // Assemble elements
    this.elements.root.appendChild(this.elements.icon);
    this.elements.root.appendChild(this.elements.label);
    this.elements.root.appendChild(this.elements.details);
    this.elements.root.appendChild(this.elements.optionsMenu);
    
    // Add to container
    this.container.appendChild(this.elements.root);
  }
  
  /**
   * Attach event listeners
   * @private
   */
  _attachEventListeners() {
    this.elements.root.addEventListener('click', this._boundHandlers.onClick);
    this.elements.root.addEventListener('mouseenter', this._boundHandlers.onMouseEnter);
    this.elements.root.addEventListener('mouseleave', this._boundHandlers.onMouseLeave);
    this.elements.root.addEventListener('keydown', this._boundHandlers.onKeyDown);
  }
  
  /**
   * Render the component
   */
  render() {
    // Update icon based on status
    this._updateIcon();
    
    // Update label text
    this._updateLabel();
    
    // Update details content
    this._updateDetails();
    
    // Update ARIA attributes
    this._updateAriaAttributes();
    
    // Apply appropriate CSS classes
    this._updateClasses();
  }
  
  /**
   * Update the component's state
   * @param {Object} newState - New state object
   */
  update(newState) {
    this.state = {
      ...this.state,
      ...newState
    };
    
    this.render();
    
    // Announce status change to screen readers if status has changed
    if (newState.status) {
      this._announceStatusChange();
    }
  }
  
  /**
   * Update status
   * @param {string} status - New status
   * @param {string} detail - Status details
   */
  updateStatus(status, detail = '') {
    this.update({
      status,
      detail
    });
  }
  
  /**
   * Set verification date
   * @param {Date} date - Verification date
   */
  setVerificationDate(date) {
    this.update({
      verificationDate: date
    });
  }
  
  /**
   * Expand the status indicator to show details
   */
  expand() {
    if (!this.state.expanded) {
      this.update({
        expanded: true
      });
      
      this.elements.details.style.display = 'block';
    }
  }
  
  /**
   * Collapse the status indicator to hide details
   */
  collapse() {
    if (this.state.expanded) {
      this.update({
        expanded: false
      });
      
      this.elements.details.style.display = 'none';
    }
  }
  
  /**
   * Toggle options menu
   */
  toggleOptions() {
    const optionsOpen = !this.state.optionsOpen;
    
    this.update({
      optionsOpen
    });
    
    this.elements.optionsMenu.style.display = optionsOpen ? 'block' : 'none';
  }
  
  /**
   * Update the status icon
   * @private
   */
  _updateIcon() {
    // Implementation varies based on icon system (SVG, character, etc.)
    // This is a placeholder implementation using text characters
    switch (this.state.status) {
      case STATUS.DISABLED:
        this.elements.icon.textContent = '●';
        break;
      case STATUS.ENCRYPTED:
        this.elements.icon.textContent = '🔒';
        break;
      case STATUS.VERIFIED:
        this.elements.icon.textContent = '🔒✓';
        break;
      case STATUS.WARNING:
        this.elements.icon.textContent = '⚠️';
        break;
      case STATUS.ERROR:
        this.elements.icon.textContent = '❌';
        break;
      default:
        this.elements.icon.textContent = '●';
    }
  }
  
  /**
   * Update the status label
   * @private
   */
  _updateLabel() {
    switch (this.state.status) {
      case STATUS.DISABLED:
        this.elements.label.textContent = 'Unencrypted';
        break;
      case STATUS.ENCRYPTED:
        this.elements.label.textContent = 'Encrypted';
        break;
      case STATUS.VERIFIED:
        this.elements.label.textContent = 'Encrypted & Verified';
        break;
      case STATUS.WARNING:
        this.elements.label.textContent = 'Warning: ' + (this.state.detail || 'Verification Failed');
        break;
      case STATUS.ERROR:
        this.elements.label.textContent = 'Error: ' + (this.state.detail || 'Encryption Error');
        break;
      default:
        this.elements.label.textContent = 'Unencrypted';
    }
  }
  
  /**
   * Update details content
   * @private
   */
  _updateDetails() {
    if (!this.state.expanded) return;
    
    let detailsHTML = '';
    
    switch (this.state.status) {
      case STATUS.DISABLED:
        detailsHTML = `
          <p>Your messages are not encrypted.</p>
          <p>Click to enable OTR encryption.</p>
        `;
        break;
      case STATUS.ENCRYPTED:
        detailsHTML = `
          <p>Your messages are encrypted.</p>
          <p>Partner not verified. Click to verify.</p>
        `;
        break;
      case STATUS.VERIFIED:
        const dateStr = this.state.verificationDate ? 
          this.state.verificationDate.toLocaleDateString() : 'unknown date';
        
        detailsHTML = `
          <p>Your messages are encrypted.</p>
          <p>Partner's identity verified on ${dateStr}.</p>
          <button class="otr-options-button">Options ▼</button>
        `;
        break;
      case STATUS.WARNING:
        detailsHTML = `
          <p>Partner could not be verified.</p>
          <p>Messages are still encrypted.</p>
          <div class="otr-action-buttons">
            <button class="otr-try-again-button">Try Again</button>
            <button class="otr-ignore-button">Ignore</button>
          </div>
        `;
        break;
      case STATUS.ERROR:
        detailsHTML = `
          <p>${this.state.detail || 'An error occurred with encryption.'}</p>
          <button class="otr-fix-button">Fix Issue</button>
        `;
        break;
    }
    
    this.elements.details.innerHTML = detailsHTML;
    
    // Add event listeners to buttons
    const optionsButton = this.elements.details.querySelector('.otr-options-button');
    if (optionsButton) {
      optionsButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggleOptions();
      });
    }
  }
  
  /**
   * Update options menu content
   * @private
   */
  _updateOptionsMenu() {
    if (!this.state.optionsOpen) return;
    
    this.elements.optionsMenu.innerHTML = `
      <ul>
        <li><button class="otr-menu-item" data-action="reverify">Re-verify Partner</button></li>
        <li><button class="otr-menu-item" data-action="fingerprint">View Fingerprint</button></li>
        <li><button class="otr-menu-item" data-action="end">End Encrypted Session</button></li>
      </ul>
    `;
    
    // Add event listeners to menu items
    const menuItems = this.elements.optionsMenu.querySelectorAll('.otr-menu-item');
    menuItems.forEach(item => {
      item.addEventListener('click', (e) => {
        e.stopPropagation();
        this._handleMenuAction(item.dataset.action);
      });
    });
  }
  
  /**
   * Update ARIA attributes for accessibility
   * @private
   */
  _updateAriaAttributes() {
    // Set appropriate ARIA attributes based on state
    this.elements.root.setAttribute('aria-label', `OTR status: ${this.elements.label.textContent}`);
    
    if (this.state.expanded) {
      this.elements.root.setAttribute('aria-expanded', 'true');
    } else {
      this.elements.root.setAttribute('aria-expanded', 'false');
    }
  }
  
  /**
   * Update CSS classes based on state
   * @private
   */
  _updateClasses() {
    // Remove all status classes
    this.elements.root.classList.remove(
      'otr-status-disabled',
      'otr-status-encrypted',
      'otr-status-verified',
      'otr-status-warning',
      'otr-status-error'
    );
    
    // Add current status class
    this.elements.root.classList.add(`otr-status-${this.state.status}`);
    
    // Add or remove expanded class
    if (this.state.expanded) {
      this.elements.root.classList.add('otr-status-expanded');
    } else {
      this.elements.root.classList.remove('otr-status-expanded');
    }
    
    // Add or remove compact class
    if (this.options.compact) {
      this.elements.root.classList.add('otr-status-compact');
    } else {
      this.elements.root.classList.remove('otr-status-compact');
    }
  }
  
  /**
   * Announce status change to screen readers
   * @private
   */
  _announceStatusChange() {
    // Create a visually hidden element for screen reader announcements
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', 'assertive');
    announcer.style.position = 'absolute';
    announcer.style.width = '1px';
    announcer.style.height = '1px';
    announcer.style.overflow = 'hidden';
    announcer.style.clip = 'rect(0, 0, 0, 0)';
    announcer.textContent = `OTR status changed to: ${this.elements.label.textContent}`;
    
    document.body.appendChild(announcer);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcer);
    }, 1000);
  }
  
  /**
   * Handle click events
   * @param {Event} event - Click event
   * @private
   */
  _handleClick(event) {
    if (this.state.expanded) {
      this.collapse();
    } else {
      this.expand();
    }
  }
  
  /**
   * Handle mouse enter events
   * @param {Event} event - Mouse enter event
   * @private
   */
  _handleMouseEnter(event) {
    if (this.options.expandOnHover) {
      this.expand();
    }
  }
  
  /**
   * Handle mouse leave events
   * @param {Event} event - Mouse leave event
   * @private
   */
  _handleMouseLeave(event) {
    if (this.options.expandOnHover) {
      this.collapse();
    }
  }
  
  /**
   * Handle keydown events
   * @param {KeyboardEvent} event - Keydown event
   * @private
   */
  _handleKeyDown(event) {
    // Handle keyboard navigation
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        if (this.state.expanded) {
          this.collapse();
        } else {
          this.expand();
        }
        break;
      case 'Escape':
        if (this.state.expanded) {
          event.preventDefault();
          this.collapse();
        }
        break;
    }
  }
  
  /**
   * Handle menu action
   * @param {string} action - Menu action
   * @private
   */
  _handleMenuAction(action) {
    this.toggleOptions(); // Close menu
    
    // Dispatch appropriate event based on action
    const actionEvent = new CustomEvent(`otr-action-${action}`, {
      bubbles: true,
      detail: { status: this.state.status }
    });
    
    this.elements.root.dispatchEvent(actionEvent);
  }
  
  /**
   * Clean up event listeners
   */
  destroy() {
    this.elements.root.removeEventListener('click', this._boundHandlers.onClick);
    this.elements.root.removeEventListener('mouseenter', this._boundHandlers.onMouseEnter);
    this.elements.root.removeEventListener('mouseleave', this._boundHandlers.onMouseLeave);
    this.elements.root.removeEventListener('keydown', this._boundHandlers.onKeyDown);
    
    // Remove from DOM
    if (this.elements.root && this.elements.root.parentNode) {
      this.elements.root.parentNode.removeChild(this.elements.root);
    }
  }
}

/**
 * Factory function to create an OTR status indicator
 * @param {HTMLElement} container - The container element to attach the status to
 * @param {Object} options - Configuration options
 * @returns {OtrStatusIndicator} A new OtrStatusIndicator instance
 */
export function createStatusIndicator(container, options = {}) {
  const indicator = new OtrStatusIndicator(container, options);
  return indicator.initialize();
} 