import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { StatusIndicator, StatusBadge, SecurityAlert, ProgressIndicator } from '../StatusIndicator.jsx';

describe('StatusIndicator', () => {
  it('renders with default props', () => {
    render(<StatusIndicator />);
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('displays correct status for disconnected state', () => {
    render(<StatusIndicator status="disconnected" showLabel={true} />);
    expect(screen.getByText('Not Connected')).toBeInTheDocument();
    expect(screen.getByLabelText('OTR session disconnected')).toBeInTheDocument();
  });

  it('displays correct status for connected state', () => {
    render(<StatusIndicator status="connected" showLabel={true} />);
    expect(screen.getByText('Connected')).toBeInTheDocument();
    expect(screen.getByLabelText('OTR session connected and encrypted')).toBeInTheDocument();
  });

  it('displays correct status for verified state', () => {
    render(<StatusIndicator status="verified" showLabel={true} />);
    expect(screen.getByText('Verified')).toBeInTheDocument();
    expect(screen.getByLabelText('OTR session verified and secure')).toBeInTheDocument();
  });

  it('shows details when showDetails is true', () => {
    render(<StatusIndicator status="connected" showDetails={true} />);
    expect(screen.getByText('OTR session active, messages encrypted')).toBeInTheDocument();
  });

  it('hides label when showLabel is false', () => {
    render(<StatusIndicator status="connected" showLabel={false} />);
    expect(screen.queryByText('Connected')).not.toBeInTheDocument();
  });

  it('applies correct size classes', () => {
    const { container } = render(<StatusIndicator size="lg" />);
    expect(container.firstChild).toHaveClass('size-lg');
  });

  it('applies correct color classes', () => {
    const { container } = render(<StatusIndicator status="verified" />);
    expect(container.firstChild).toHaveClass('verified');
  });

  it('handles click events when onClick is provided', async () => {
    const user = userEvent.setup();
    const handleClick = jest.fn();
    render(<StatusIndicator status="connected" onClick={handleClick} />);
    
    const indicator = screen.getByRole('button');
    await user.click(indicator);
    
    expect(handleClick).toHaveBeenCalledWith('connected');
  });

  it('supports keyboard interaction when clickable', async () => {
    const user = userEvent.setup();
    const handleClick = jest.fn();
    render(<StatusIndicator status="connected" onClick={handleClick} />);
    
    const indicator = screen.getByRole('button');
    indicator.focus();
    await user.keyboard('{Enter}');
    
    expect(handleClick).toHaveBeenCalledWith('connected');
  });

  it('has proper accessibility attributes', () => {
    render(<StatusIndicator status="verified" />);
    const indicator = screen.getByLabelText('OTR session verified and secure');
    expect(indicator).toHaveAttribute('role', 'status');
  });

  it('becomes clickable when onClick is provided', () => {
    const handleClick = jest.fn();
    const { container } = render(<StatusIndicator onClick={handleClick} />);
    
    expect(container.firstChild).toHaveClass('clickable');
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});

describe('StatusBadge', () => {
  it('renders with default props', () => {
    render(<StatusBadge />);
    expect(screen.getByText('Unencrypted')).toBeInTheDocument();
  });

  it('displays encrypted status correctly', () => {
    render(<StatusBadge status="encrypted" />);
    expect(screen.getByText('Encrypted')).toBeInTheDocument();
    expect(screen.getByLabelText('Message is encrypted')).toBeInTheDocument();
  });

  it('displays verified status correctly', () => {
    render(<StatusBadge status="verified" />);
    expect(screen.getByText('Verified')).toBeInTheDocument();
    expect(screen.getByLabelText('Message is verified')).toBeInTheDocument();
  });

  it('applies correct size classes', () => {
    const { container } = render(<StatusBadge size="md" />);
    expect(container.firstChild).toHaveClass('size-md');
  });

  it('applies correct color classes for different statuses', () => {
    const { container: encryptedContainer } = render(<StatusBadge status="encrypted" />);
    expect(encryptedContainer.firstChild).toHaveClass('success');
    
    const { container: unencryptedContainer } = render(<StatusBadge status="unencrypted" />);
    expect(unencryptedContainer.firstChild).toHaveClass('danger');
  });
});

describe('SecurityAlert', () => {
  const defaultProps = {
    message: 'Test alert message'
  };

  it('renders with required props', () => {
    render(<SecurityAlert {...defaultProps} />);
    expect(screen.getByText('Test alert message')).toBeInTheDocument();
  });

  it('displays correct icon for different levels', () => {
    const { rerender } = render(<SecurityAlert {...defaultProps} level="warning" />);
    expect(screen.getByText('⚠️')).toBeInTheDocument();
    
    rerender(<SecurityAlert {...defaultProps} level="error" />);
    expect(screen.getByText('❌')).toBeInTheDocument();
    
    rerender(<SecurityAlert {...defaultProps} level="success" />);
    expect(screen.getByText('✅')).toBeInTheDocument();
  });

  it('applies correct color classes for different levels', () => {
    const { container } = render(<SecurityAlert {...defaultProps} level="error" />);
    expect(container.firstChild).toHaveClass('danger');
  });

  it('shows action button when action and onAction are provided', async () => {
    const user = userEvent.setup();
    const handleAction = jest.fn();
    render(
      <SecurityAlert 
        {...defaultProps} 
        action="Take Action" 
        onAction={handleAction} 
      />
    );
    
    const actionButton = screen.getByText('Take Action');
    expect(actionButton).toBeInTheDocument();
    
    await user.click(actionButton);
    expect(handleAction).toHaveBeenCalled();
  });

  it('shows dismiss button when onDismiss is provided', async () => {
    const user = userEvent.setup();
    const handleDismiss = jest.fn();
    render(<SecurityAlert {...defaultProps} onDismiss={handleDismiss} />);
    
    const dismissButton = screen.getByLabelText('Dismiss alert');
    expect(dismissButton).toBeInTheDocument();
    
    await user.click(dismissButton);
    expect(handleDismiss).toHaveBeenCalled();
  });

  it('has proper ARIA attributes for accessibility', () => {
    render(<SecurityAlert {...defaultProps} />);
    const alert = screen.getByRole('alert');
    expect(alert).toHaveAttribute('aria-live', 'polite');
  });

  it('supports custom className', () => {
    const { container } = render(<SecurityAlert {...defaultProps} className="custom-class" />);
    expect(container.firstChild).toHaveClass('custom-class');
  });
});

describe('ProgressIndicator', () => {
  it('renders with default props', () => {
    render(<ProgressIndicator />);
    expect(screen.getByText('Connecting...')).toBeInTheDocument();
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('displays custom status text', () => {
    render(<ProgressIndicator status="Loading data..." />);
    expect(screen.getByText('Loading data...')).toBeInTheDocument();
  });

  it('shows percentage when showPercentage is true', () => {
    render(<ProgressIndicator progress={75} showPercentage={true} />);
    expect(screen.getByText('75%')).toBeInTheDocument();
  });

  it('hides percentage when showPercentage is false', () => {
    render(<ProgressIndicator progress={75} showPercentage={false} />);
    expect(screen.queryByText('75%')).not.toBeInTheDocument();
  });

  it('sets correct progress bar width', () => {
    render(<ProgressIndicator progress={60} />);
    const progressFill = screen.getByRole('progressbar').querySelector('.progress-fill');
    expect(progressFill).toHaveStyle('width: 60%');
  });

  it('clamps progress values to 0-100 range', () => {
    const { rerender } = render(<ProgressIndicator progress={-10} />);
    let progressFill = screen.getByRole('progressbar').querySelector('.progress-fill');
    expect(progressFill).toHaveStyle('width: 0%');
    
    rerender(<ProgressIndicator progress={150} />);
    progressFill = screen.getByRole('progressbar').querySelector('.progress-fill');
    expect(progressFill).toHaveStyle('width: 100%');
  });

  it('has proper ARIA attributes', () => {
    render(<ProgressIndicator progress={50} />);
    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toHaveAttribute('aria-valuenow', '50');
    expect(progressBar).toHaveAttribute('aria-valuemin', '0');
    expect(progressBar).toHaveAttribute('aria-valuemax', '100');
  });

  it('applies correct size classes', () => {
    const { container } = render(<ProgressIndicator size="lg" />);
    expect(container.firstChild).toHaveClass('size-lg');
  });

  it('supports custom className', () => {
    const { container } = render(<ProgressIndicator className="custom-progress" />);
    expect(container.firstChild).toHaveClass('custom-progress');
  });
});
