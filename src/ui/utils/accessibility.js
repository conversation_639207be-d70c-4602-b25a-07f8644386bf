/**
 * Accessibility utilities and audit system for WebOTR UX
 * 
 * Provides comprehensive accessibility testing, WCAG compliance checking,
 * and assistive technology support for all components
 */

// WCAG 2.1 AA compliance checker
export class AccessibilityAuditor {
  constructor() {
    this.violations = [];
    this.warnings = [];
    this.passes = [];
    this.wcagLevel = 'AA';
  }

  // Main audit function
  async auditElement(element) {
    this.violations = [];
    this.warnings = [];
    this.passes = [];

    // Run all accessibility checks
    this.checkColorContrast(element);
    this.checkKeyboardNavigation(element);
    this.checkAriaLabels(element);
    this.checkHeadingStructure(element);
    this.checkFormLabels(element);
    this.checkFocusManagement(element);
    this.checkImageAltText(element);
    this.checkLinkPurpose(element);
    this.checkLanguageAttributes(element);
    this.checkSemanticStructure(element);

    return this.generateReport();
  }

  // Color contrast checking
  checkColorContrast(element) {
    const textElements = element.querySelectorAll('*');
    
    textElements.forEach(el => {
      const styles = window.getComputedStyle(el);
      const color = styles.color;
      const backgroundColor = styles.backgroundColor;
      const fontSize = parseFloat(styles.fontSize);
      
      if (color && backgroundColor && color !== backgroundColor) {
        const contrast = this.calculateContrastRatio(color, backgroundColor);
        const isLargeText = fontSize >= 18 || (fontSize >= 14 && styles.fontWeight >= 700);
        const requiredRatio = isLargeText ? 3 : 4.5;
        
        if (contrast < requiredRatio) {
          this.violations.push({
            type: 'color-contrast',
            element: el,
            message: `Insufficient color contrast: ${contrast.toFixed(2)}:1 (required: ${requiredRatio}:1)`,
            wcag: '1.4.3',
            severity: 'error'
          });
        } else {
          this.passes.push({
            type: 'color-contrast',
            element: el,
            message: `Good color contrast: ${contrast.toFixed(2)}:1`
          });
        }
      }
    });
  }

  // Calculate contrast ratio between two colors
  calculateContrastRatio(color1, color2) {
    const rgb1 = this.parseColor(color1);
    const rgb2 = this.parseColor(color2);
    
    if (!rgb1 || !rgb2) return 21; // Assume good contrast if can't parse
    
    const l1 = this.getRelativeLuminance(rgb1);
    const l2 = this.getRelativeLuminance(rgb2);
    
    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  parseColor(color) {
    const div = document.createElement('div');
    div.style.color = color;
    document.body.appendChild(div);
    const computed = window.getComputedStyle(div).color;
    document.body.removeChild(div);
    
    const match = computed.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    return match ? [parseInt(match[1]), parseInt(match[2]), parseInt(match[3])] : null;
  }

  getRelativeLuminance([r, g, b]) {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }

  // Keyboard navigation checking
  checkKeyboardNavigation(element) {
    const interactiveElements = element.querySelectorAll(
      'button, a, input, select, textarea, [tabindex], [role="button"], [role="link"]'
    );
    
    interactiveElements.forEach(el => {
      const tabIndex = el.getAttribute('tabindex');
      const isVisible = this.isElementVisible(el);
      
      if (isVisible && tabIndex === '-1' && !el.disabled) {
        this.warnings.push({
          type: 'keyboard-navigation',
          element: el,
          message: 'Interactive element not keyboard accessible',
          wcag: '2.1.1',
          severity: 'warning'
        });
      }
      
      if (isVisible && !el.disabled && tabIndex !== '-1') {
        this.passes.push({
          type: 'keyboard-navigation',
          element: el,
          message: 'Element is keyboard accessible'
        });
      }
    });
  }

  // ARIA labels checking
  checkAriaLabels(element) {
    const elementsNeedingLabels = element.querySelectorAll(
      'button, input, select, textarea, [role="button"], [role="checkbox"], [role="radio"]'
    );
    
    elementsNeedingLabels.forEach(el => {
      const hasAriaLabel = el.getAttribute('aria-label');
      const hasAriaLabelledBy = el.getAttribute('aria-labelledby');
      const hasLabel = el.labels && el.labels.length > 0;
      const hasTextContent = el.textContent.trim().length > 0;
      
      if (!hasAriaLabel && !hasAriaLabelledBy && !hasLabel && !hasTextContent) {
        this.violations.push({
          type: 'aria-labels',
          element: el,
          message: 'Interactive element missing accessible name',
          wcag: '4.1.2',
          severity: 'error'
        });
      } else {
        this.passes.push({
          type: 'aria-labels',
          element: el,
          message: 'Element has accessible name'
        });
      }
    });
  }

  // Heading structure checking
  checkHeadingStructure(element) {
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;
    
    headings.forEach(heading => {
      const level = parseInt(heading.tagName.charAt(1));
      
      if (level > previousLevel + 1) {
        this.violations.push({
          type: 'heading-structure',
          element: heading,
          message: `Heading level skipped from h${previousLevel} to h${level}`,
          wcag: '1.3.1',
          severity: 'error'
        });
      }
      
      previousLevel = level;
    });
    
    if (headings.length > 0) {
      this.passes.push({
        type: 'heading-structure',
        message: `Found ${headings.length} headings with proper structure`
      });
    }
  }

  // Form labels checking
  checkFormLabels(element) {
    const formControls = element.querySelectorAll('input, select, textarea');
    
    formControls.forEach(control => {
      const type = control.type;
      if (type === 'hidden' || type === 'submit' || type === 'button') return;
      
      const hasLabel = control.labels && control.labels.length > 0;
      const hasAriaLabel = control.getAttribute('aria-label');
      const hasAriaLabelledBy = control.getAttribute('aria-labelledby');
      
      if (!hasLabel && !hasAriaLabel && !hasAriaLabelledBy) {
        this.violations.push({
          type: 'form-labels',
          element: control,
          message: 'Form control missing label',
          wcag: '3.3.2',
          severity: 'error'
        });
      } else {
        this.passes.push({
          type: 'form-labels',
          element: control,
          message: 'Form control has proper label'
        });
      }
    });
  }

  // Focus management checking
  checkFocusManagement(element) {
    const focusableElements = element.querySelectorAll(
      'button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    focusableElements.forEach(el => {
      const styles = window.getComputedStyle(el);
      const hasFocusIndicator = styles.outline !== 'none' || 
                               styles.boxShadow !== 'none' ||
                               styles.border !== styles.borderColor;
      
      if (!hasFocusIndicator) {
        this.warnings.push({
          type: 'focus-management',
          element: el,
          message: 'Element may lack visible focus indicator',
          wcag: '2.4.7',
          severity: 'warning'
        });
      }
    });
  }

  // Image alt text checking
  checkImageAltText(element) {
    const images = element.querySelectorAll('img');
    
    images.forEach(img => {
      const alt = img.getAttribute('alt');
      const role = img.getAttribute('role');
      
      if (alt === null && role !== 'presentation') {
        this.violations.push({
          type: 'image-alt',
          element: img,
          message: 'Image missing alt attribute',
          wcag: '1.1.1',
          severity: 'error'
        });
      } else if (alt === '' && role !== 'presentation') {
        this.warnings.push({
          type: 'image-alt',
          element: img,
          message: 'Image has empty alt text - ensure it\'s decorative',
          wcag: '1.1.1',
          severity: 'warning'
        });
      } else {
        this.passes.push({
          type: 'image-alt',
          element: img,
          message: 'Image has appropriate alt text'
        });
      }
    });
  }

  // Link purpose checking
  checkLinkPurpose(element) {
    const links = element.querySelectorAll('a');
    
    links.forEach(link => {
      const text = link.textContent.trim();
      const ariaLabel = link.getAttribute('aria-label');
      const title = link.getAttribute('title');
      
      const linkText = ariaLabel || text || title;
      
      if (!linkText || linkText.length < 3) {
        this.violations.push({
          type: 'link-purpose',
          element: link,
          message: 'Link purpose unclear or missing',
          wcag: '2.4.4',
          severity: 'error'
        });
      } else if (['click here', 'read more', 'more'].includes(linkText.toLowerCase())) {
        this.warnings.push({
          type: 'link-purpose',
          element: link,
          message: 'Link text is not descriptive',
          wcag: '2.4.4',
          severity: 'warning'
        });
      } else {
        this.passes.push({
          type: 'link-purpose',
          element: link,
          message: 'Link has descriptive text'
        });
      }
    });
  }

  // Language attributes checking
  checkLanguageAttributes(element) {
    const hasLang = element.getAttribute('lang') || 
                   document.documentElement.getAttribute('lang');
    
    if (!hasLang) {
      this.violations.push({
        type: 'language',
        element: element,
        message: 'Page language not specified',
        wcag: '3.1.1',
        severity: 'error'
      });
    } else {
      this.passes.push({
        type: 'language',
        message: 'Page language specified'
      });
    }
  }

  // Semantic structure checking
  checkSemanticStructure(element) {
    const landmarks = element.querySelectorAll(
      'main, nav, aside, section, article, header, footer, [role="main"], [role="navigation"], [role="complementary"]'
    );
    
    if (landmarks.length === 0) {
      this.warnings.push({
        type: 'semantic-structure',
        element: element,
        message: 'No landmark elements found',
        wcag: '1.3.1',
        severity: 'warning'
      });
    } else {
      this.passes.push({
        type: 'semantic-structure',
        message: `Found ${landmarks.length} landmark elements`
      });
    }
  }

  // Utility function to check if element is visible
  isElementVisible(element) {
    const styles = window.getComputedStyle(element);
    return styles.display !== 'none' && 
           styles.visibility !== 'hidden' && 
           styles.opacity !== '0';
  }

  // Generate accessibility report
  generateReport() {
    const totalIssues = this.violations.length + this.warnings.length;
    const score = Math.max(0, 100 - (this.violations.length * 10) - (this.warnings.length * 5));
    
    return {
      score,
      level: score >= 90 ? 'excellent' : score >= 70 ? 'good' : score >= 50 ? 'fair' : 'poor',
      violations: this.violations,
      warnings: this.warnings,
      passes: this.passes,
      summary: {
        total: totalIssues,
        violations: this.violations.length,
        warnings: this.warnings.length,
        passes: this.passes.length
      },
      wcagCompliance: this.violations.length === 0 ? 'compliant' : 'non-compliant'
    };
  }
}

// Screen reader utilities
export class ScreenReaderUtils {
  static announce(message, priority = 'polite') {
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', priority);
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.textContent = message;
    
    document.body.appendChild(announcer);
    
    setTimeout(() => {
      document.body.removeChild(announcer);
    }, 1000);
  }

  static announceError(message) {
    this.announce(message, 'assertive');
  }

  static announceSuccess(message) {
    this.announce(message, 'polite');
  }
}

// Keyboard navigation utilities
export class KeyboardNavigation {
  static trapFocus(element) {
    const focusableElements = element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    const handleTabKey = (e) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    };
    
    element.addEventListener('keydown', handleTabKey);
    
    return () => {
      element.removeEventListener('keydown', handleTabKey);
    };
  }

  static handleEscapeKey(callback) {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        callback();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }
}

// Export accessibility utilities
export const accessibilityAuditor = new AccessibilityAuditor();

export default {
  AccessibilityAuditor,
  ScreenReaderUtils,
  KeyboardNavigation,
  accessibilityAuditor
};
