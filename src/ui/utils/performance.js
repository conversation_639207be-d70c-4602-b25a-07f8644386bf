/**
 * Performance optimization utilities for WebOTR UX
 * 
 * Provides code splitting, lazy loading, and performance monitoring
 * to ensure optimal user experience across all devices
 */

import React, { Suspense, lazy } from 'react';

// Performance monitoring
export class PerformanceMonitor {
  constructor() {
    this.metrics = {
      componentLoadTimes: new Map(),
      renderTimes: new Map(),
      userInteractions: [],
      memoryUsage: [],
      networkRequests: []
    };
    
    this.startTime = performance.now();
    this.setupPerformanceObserver();
  }

  setupPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      // Monitor navigation timing
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordNavigationTiming(entry);
        }
      });
      navObserver.observe({ entryTypes: ['navigation'] });

      // Monitor resource loading
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordResourceTiming(entry);
        }
      });
      resourceObserver.observe({ entryTypes: ['resource'] });

      // Monitor user interactions
      const interactionObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordUserInteraction(entry);
        }
      });
      interactionObserver.observe({ entryTypes: ['event'] });
    }
  }

  recordNavigationTiming(entry) {
    this.metrics.navigationTiming = {
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      firstPaint: entry.responseEnd - entry.requestStart,
      domInteractive: entry.domInteractive - entry.navigationStart
    };
  }

  recordResourceTiming(entry) {
    this.metrics.networkRequests.push({
      name: entry.name,
      duration: entry.duration,
      size: entry.transferSize,
      timestamp: entry.startTime
    });
  }

  recordUserInteraction(entry) {
    this.metrics.userInteractions.push({
      type: entry.name,
      duration: entry.duration,
      timestamp: entry.startTime
    });
  }

  recordComponentLoad(componentName, loadTime) {
    this.metrics.componentLoadTimes.set(componentName, loadTime);
  }

  recordRenderTime(componentName, renderTime) {
    this.metrics.renderTimes.set(componentName, renderTime);
  }

  recordMemoryUsage() {
    if ('memory' in performance) {
      this.metrics.memoryUsage.push({
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
        timestamp: performance.now()
      });
    }
  }

  getMetrics() {
    return {
      ...this.metrics,
      uptime: performance.now() - this.startTime
    };
  }

  getPerformanceScore() {
    const metrics = this.getMetrics();
    let score = 100;

    // Deduct points for slow component loads
    const avgComponentLoad = Array.from(metrics.componentLoadTimes.values())
      .reduce((sum, time) => sum + time, 0) / metrics.componentLoadTimes.size || 0;
    if (avgComponentLoad > 100) score -= 10;
    if (avgComponentLoad > 500) score -= 20;

    // Deduct points for slow renders
    const avgRenderTime = Array.from(metrics.renderTimes.values())
      .reduce((sum, time) => sum + time, 0) / metrics.renderTimes.size || 0;
    if (avgRenderTime > 16) score -= 10; // 60fps threshold
    if (avgRenderTime > 33) score -= 20; // 30fps threshold

    // Deduct points for slow interactions
    const slowInteractions = metrics.userInteractions.filter(i => i.duration > 100).length;
    score -= slowInteractions * 5;

    return Math.max(0, Math.min(100, score));
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Lazy loading utilities
export function createLazyComponent(importFunc, fallback = null) {
  const LazyComponent = lazy(importFunc);
  
  return function LazyWrapper(props) {
    const startTime = performance.now();
    
    React.useEffect(() => {
      const loadTime = performance.now() - startTime;
      performanceMonitor.recordComponentLoad(LazyComponent.name || 'LazyComponent', loadTime);
    }, []);

    return (
      <Suspense fallback={fallback || <ComponentLoader />}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
}

// Performance-aware component wrapper
export function withPerformanceMonitoring(WrappedComponent) {
  return function PerformanceMonitoredComponent(props) {
    const renderStartTime = React.useRef();
    const componentName = WrappedComponent.displayName || WrappedComponent.name || 'Component';

    React.useLayoutEffect(() => {
      renderStartTime.current = performance.now();
    });

    React.useEffect(() => {
      if (renderStartTime.current) {
        const renderTime = performance.now() - renderStartTime.current;
        performanceMonitor.recordRenderTime(componentName, renderTime);
      }
    });

    return <WrappedComponent {...props} />;
  };
}

// Loading components
export function ComponentLoader({ message = 'Loading...' }) {
  return (
    <div className="component-loader">
      <div className="loader-spinner"></div>
      <div className="loader-message">{message}</div>
    </div>
  );
}

export function VerificationLoader() {
  return (
    <ComponentLoader message="Loading verification component..." />
  );
}

export function StatusLoader() {
  return (
    <ComponentLoader message="Loading status indicators..." />
  );
}

// Code splitting for verification components
export const LazyVerificationDialog = createLazyComponent(
  () => import('../components/verification/VerificationDialog.jsx'),
  <VerificationLoader />
);

export const LazyQRCodeVerifier = createLazyComponent(
  () => import('../components/verification/QRCodeVerifier.jsx'),
  <VerificationLoader />
);

export const LazyQuestionAnswerVerifier = createLazyComponent(
  () => import('../components/verification/QuestionAnswerVerifier.jsx'),
  <VerificationLoader />
);

export const LazyManualFingerprintVerifier = createLazyComponent(
  () => import('../components/verification/ManualFingerprintVerifier.jsx'),
  <VerificationLoader />
);

// Memory management utilities
export function cleanupUnusedComponents() {
  // Force garbage collection if available (development only)
  if (window.gc && process.env.NODE_ENV === 'development') {
    window.gc();
  }
  
  // Record memory usage
  performanceMonitor.recordMemoryUsage();
}

// Debounce utility for performance
export function debounce(func, wait, immediate = false) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
}

// Throttle utility for performance
export function throttle(func, limit) {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Image optimization utilities
export function optimizeImage(src, options = {}) {
  const {
    width = 'auto',
    height = 'auto',
    quality = 85,
    format = 'webp'
  } = options;

  // In a real implementation, this would integrate with an image optimization service
  // For now, return the original src with optimization parameters
  return `${src}?w=${width}&h=${height}&q=${quality}&f=${format}`;
}

// Bundle size monitoring
export function getBundleSize() {
  if ('connection' in navigator) {
    return {
      effectiveType: navigator.connection.effectiveType,
      downlink: navigator.connection.downlink,
      rtt: navigator.connection.rtt,
      saveData: navigator.connection.saveData
    };
  }
  return null;
}

// Performance recommendations
export function getPerformanceRecommendations() {
  const metrics = performanceMonitor.getMetrics();
  const recommendations = [];

  // Check component load times
  const slowComponents = Array.from(metrics.componentLoadTimes.entries())
    .filter(([, time]) => time > 200);
  
  if (slowComponents.length > 0) {
    recommendations.push({
      type: 'component-loading',
      severity: 'medium',
      message: `${slowComponents.length} components are loading slowly`,
      components: slowComponents.map(([name]) => name)
    });
  }

  // Check render performance
  const slowRenders = Array.from(metrics.renderTimes.entries())
    .filter(([, time]) => time > 16);
  
  if (slowRenders.length > 0) {
    recommendations.push({
      type: 'render-performance',
      severity: 'high',
      message: `${slowRenders.length} components are rendering slowly`,
      components: slowRenders.map(([name]) => name)
    });
  }

  // Check memory usage
  const latestMemory = metrics.memoryUsage[metrics.memoryUsage.length - 1];
  if (latestMemory && latestMemory.used / latestMemory.total > 0.8) {
    recommendations.push({
      type: 'memory-usage',
      severity: 'high',
      message: 'High memory usage detected',
      usage: `${Math.round(latestMemory.used / 1024 / 1024)}MB`
    });
  }

  return recommendations;
}

// Export performance utilities
export default {
  performanceMonitor,
  createLazyComponent,
  withPerformanceMonitoring,
  ComponentLoader,
  LazyVerificationDialog,
  LazyQRCodeVerifier,
  LazyQuestionAnswerVerifier,
  LazyManualFingerprintVerifier,
  cleanupUnusedComponents,
  debounce,
  throttle,
  optimizeImage,
  getBundleSize,
  getPerformanceRecommendations
};
