/**
 * Cross-browser compatibility utilities for WebOTR UX
 * 
 * Provides feature detection, polyfills, and browser-specific
 * optimizations to ensure consistent experience across all browsers
 */

// Browser detection and feature support
export class BrowserCompatibility {
  constructor() {
    this.userAgent = navigator.userAgent;
    this.browser = this.detectBrowser();
    this.features = this.detectFeatures();
    this.polyfillsLoaded = new Set();
  }

  // Detect browser type and version
  detectBrowser() {
    const ua = this.userAgent;
    
    if (ua.includes('Chrome') && !ua.includes('Edg')) {
      const match = ua.match(/Chrome\/(\d+)/);
      return { name: 'Chrome', version: match ? parseInt(match[1]) : 0 };
    }
    
    if (ua.includes('Firefox')) {
      const match = ua.match(/Firefox\/(\d+)/);
      return { name: 'Firefox', version: match ? parseInt(match[1]) : 0 };
    }
    
    if (ua.includes('Safari') && !ua.includes('Chrome')) {
      const match = ua.match(/Version\/(\d+)/);
      return { name: 'Safari', version: match ? parseInt(match[1]) : 0 };
    }
    
    if (ua.includes('Edg')) {
      const match = ua.match(/Edg\/(\d+)/);
      return { name: 'Edge', version: match ? parseInt(match[1]) : 0 };
    }
    
    return { name: 'Unknown', version: 0 };
  }

  // Detect feature support
  detectFeatures() {
    return {
      // ES6+ features
      arrow_functions: this.testFeature(() => eval('(() => true)()')),
      template_literals: this.testFeature(() => eval('`test`')),
      destructuring: this.testFeature(() => eval('const {a} = {a: 1}')),
      spread_operator: this.testFeature(() => eval('[...[], 1]')),
      async_await: this.testFeature(() => eval('async function test() { await Promise.resolve(); }')),
      
      // Web APIs
      fetch: typeof fetch !== 'undefined',
      websockets: typeof WebSocket !== 'undefined',
      webrtc: typeof RTCPeerConnection !== 'undefined',
      geolocation: 'geolocation' in navigator,
      notifications: 'Notification' in window,
      service_worker: 'serviceWorker' in navigator,
      web_workers: typeof Worker !== 'undefined',
      
      // Media APIs
      getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      webgl: this.testWebGL(),
      canvas: this.testCanvas(),
      
      // Storage APIs
      localStorage: this.testLocalStorage(),
      sessionStorage: this.testSessionStorage(),
      indexedDB: 'indexedDB' in window,
      
      // CSS features
      css_grid: this.testCSSFeature('display', 'grid'),
      css_flexbox: this.testCSSFeature('display', 'flex'),
      css_variables: this.testCSSFeature('--test', 'value'),
      css_transforms: this.testCSSFeature('transform', 'translateX(1px)'),
      
      // Security features
      crypto: 'crypto' in window && 'subtle' in window.crypto,
      csp: this.testCSP(),
      
      // Performance APIs
      performance_observer: 'PerformanceObserver' in window,
      intersection_observer: 'IntersectionObserver' in window,
      mutation_observer: 'MutationObserver' in window
    };
  }

  testFeature(testFunc) {
    try {
      testFunc();
      return true;
    } catch (e) {
      return false;
    }
  }

  testWebGL() {
    try {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
    } catch (e) {
      return false;
    }
  }

  testCanvas() {
    try {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext && canvas.getContext('2d'));
    } catch (e) {
      return false;
    }
  }

  testLocalStorage() {
    try {
      const test = 'test';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (e) {
      return false;
    }
  }

  testSessionStorage() {
    try {
      const test = 'test';
      sessionStorage.setItem(test, test);
      sessionStorage.removeItem(test);
      return true;
    } catch (e) {
      return false;
    }
  }

  testCSSFeature(property, value) {
    try {
      const element = document.createElement('div');
      element.style[property] = value;
      return element.style[property] === value;
    } catch (e) {
      return false;
    }
  }

  testCSP() {
    return 'SecurityPolicyViolationEvent' in window;
  }

  // Load polyfills based on feature detection
  async loadPolyfills() {
    const polyfills = [];

    // Fetch polyfill
    if (!this.features.fetch) {
      polyfills.push(this.loadPolyfill('fetch', () => import('whatwg-fetch')));
    }

    // Promise polyfill
    if (typeof Promise === 'undefined') {
      polyfills.push(this.loadPolyfill('promise', () => import('es6-promise/auto')));
    }

    // IntersectionObserver polyfill
    if (!this.features.intersection_observer) {
      polyfills.push(this.loadPolyfill('intersection-observer', 
        () => import('intersection-observer')));
    }

    // ResizeObserver polyfill
    if (typeof ResizeObserver === 'undefined') {
      polyfills.push(this.loadPolyfill('resize-observer', 
        () => import('@juggle/resize-observer')));
    }

    // CSS custom properties polyfill for IE
    if (this.browser.name === 'IE' || 
        (this.browser.name === 'Edge' && this.browser.version < 16)) {
      polyfills.push(this.loadPolyfill('css-vars', 
        () => import('css-vars-ponyfill')));
    }

    await Promise.all(polyfills);
    return this.polyfillsLoaded;
  }

  async loadPolyfill(name, importFunc) {
    if (this.polyfillsLoaded.has(name)) {
      return;
    }

    try {
      await importFunc();
      this.polyfillsLoaded.add(name);
      console.log(`Polyfill loaded: ${name}`);
    } catch (error) {
      console.warn(`Failed to load polyfill: ${name}`, error);
    }
  }

  // Get browser-specific optimizations
  getBrowserOptimizations() {
    const optimizations = {
      prefixes: [],
      features: {},
      workarounds: []
    };

    switch (this.browser.name) {
      case 'Chrome':
        optimizations.prefixes = ['-webkit-'];
        optimizations.features.webkitUserSelect = true;
        break;
        
      case 'Firefox':
        optimizations.prefixes = ['-moz-'];
        optimizations.features.mozUserSelect = true;
        break;
        
      case 'Safari':
        optimizations.prefixes = ['-webkit-'];
        optimizations.features.webkitUserSelect = true;
        optimizations.workarounds.push('safari-date-input');
        break;
        
      case 'Edge':
        if (this.browser.version < 79) { // Legacy Edge
          optimizations.prefixes = ['-ms-'];
          optimizations.workarounds.push('edge-legacy-grid');
        } else { // Chromium Edge
          optimizations.prefixes = ['-webkit-'];
        }
        break;
    }

    return optimizations;
  }

  // Check if browser meets minimum requirements
  meetsMinimumRequirements() {
    const requirements = {
      Chrome: 70,
      Firefox: 65,
      Safari: 12,
      Edge: 79
    };

    const minVersion = requirements[this.browser.name];
    if (!minVersion) {
      return false; // Unknown browser
    }

    return this.browser.version >= minVersion;
  }

  // Get compatibility warnings
  getCompatibilityWarnings() {
    const warnings = [];

    if (!this.meetsMinimumRequirements()) {
      warnings.push({
        type: 'browser-version',
        severity: 'high',
        message: `Your browser version (${this.browser.name} ${this.browser.version}) may not be fully supported. Please update to the latest version.`
      });
    }

    if (!this.features.fetch) {
      warnings.push({
        type: 'missing-feature',
        severity: 'medium',
        message: 'Fetch API not supported. Network requests may be slower.'
      });
    }

    if (!this.features.getUserMedia) {
      warnings.push({
        type: 'missing-feature',
        severity: 'medium',
        message: 'Camera access not supported. QR code scanning will not work.'
      });
    }

    if (!this.features.crypto) {
      warnings.push({
        type: 'missing-feature',
        severity: 'high',
        message: 'Web Crypto API not supported. Security features may be limited.'
      });
    }

    if (!this.features.localStorage) {
      warnings.push({
        type: 'missing-feature',
        severity: 'medium',
        message: 'Local storage not supported. Settings will not be saved.'
      });
    }

    return warnings;
  }

  // Apply browser-specific CSS
  applyBrowserSpecificCSS() {
    const style = document.createElement('style');
    let css = '';

    // Safari-specific fixes
    if (this.browser.name === 'Safari') {
      css += `
        /* Safari input styling fixes */
        input[type="text"], input[type="password"], textarea {
          -webkit-appearance: none;
          border-radius: 0;
        }
        
        /* Safari flexbox fixes */
        .flex-container {
          -webkit-flex-wrap: wrap;
        }
      `;
    }

    // Firefox-specific fixes
    if (this.browser.name === 'Firefox') {
      css += `
        /* Firefox button styling */
        button::-moz-focus-inner {
          border: 0;
          padding: 0;
        }
        
        /* Firefox scrollbar styling */
        * {
          scrollbar-width: thin;
          scrollbar-color: #cbd5e1 #f1f5f9;
        }
      `;
    }

    // IE/Edge legacy fixes
    if (this.browser.name === 'Edge' && this.browser.version < 79) {
      css += `
        /* Edge legacy grid fallbacks */
        .grid-container {
          display: -ms-grid;
        }
        
        /* Edge legacy flexbox */
        .flex-container {
          display: -ms-flexbox;
        }
      `;
    }

    if (css) {
      style.textContent = css;
      document.head.appendChild(style);
    }
  }

  // Generate compatibility report
  generateCompatibilityReport() {
    return {
      browser: this.browser,
      features: this.features,
      meetsRequirements: this.meetsMinimumRequirements(),
      warnings: this.getCompatibilityWarnings(),
      optimizations: this.getBrowserOptimizations(),
      polyfillsLoaded: Array.from(this.polyfillsLoaded),
      score: this.calculateCompatibilityScore()
    };
  }

  calculateCompatibilityScore() {
    const totalFeatures = Object.keys(this.features).length;
    const supportedFeatures = Object.values(this.features).filter(Boolean).length;
    const baseScore = (supportedFeatures / totalFeatures) * 100;
    
    // Deduct points for old browser versions
    let versionPenalty = 0;
    if (!this.meetsMinimumRequirements()) {
      versionPenalty = 20;
    }
    
    return Math.max(0, Math.round(baseScore - versionPenalty));
  }
}

// Global compatibility instance
export const browserCompatibility = new BrowserCompatibility();

// Initialize compatibility on load
export async function initializeCompatibility() {
  await browserCompatibility.loadPolyfills();
  browserCompatibility.applyBrowserSpecificCSS();
  
  const report = browserCompatibility.generateCompatibilityReport();
  console.log('Browser Compatibility Report:', report);
  
  return report;
}

// Utility functions
export function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

export function isTouch() {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

export function supportsHover() {
  return window.matchMedia('(hover: hover)').matches;
}

export function getViewportSize() {
  return {
    width: Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0),
    height: Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0)
  };
}

export function isHighDPI() {
  return window.devicePixelRatio > 1;
}

// Export compatibility utilities
export default {
  BrowserCompatibility,
  browserCompatibility,
  initializeCompatibility,
  isMobile,
  isTouch,
  supportsHover,
  getViewportSize,
  isHighDPI
};
