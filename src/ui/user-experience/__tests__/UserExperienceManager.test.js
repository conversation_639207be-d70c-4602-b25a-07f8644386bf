/**
 * User Experience Manager Tests
 * 
 * Comprehensive test suite for UserExperienceManager:
 * - Initialization and configuration
 * - Security operation simplification
 * - User interaction handling
 * - Accessibility and usability
 */

import { UserExperienceManager } from '../UserExperienceManager.js';
import { SecurityIntegration } from '../SecurityIntegration.js';

// Mock DOM environment
global.document = {
  createElement: jest.fn(() => ({
    style: {},
    classList: {
      add: jest.fn(),
      remove: jest.fn(),
      contains: jest.fn()
    },
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    appendChild: jest.fn(),
    removeChild: jest.fn(),
    setAttribute: jest.fn(),
    getAttribute: jest.fn(),
    innerHTML: '',
    textContent: ''
  })),
  getElementById: jest.fn(),
  querySelector: jest.fn(),
  querySelectorAll: jest.fn(() => []),
  body: {
    appendChild: jest.fn(),
    removeChild: jest.fn()
  }
};

global.window = {
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  getComputedStyle: jest.fn(() => ({})),
  requestAnimationFrame: jest.fn(cb => setTimeout(cb, 16)),
  cancelAnimationFrame: jest.fn()
};

describe('UserExperienceManager', () => {
  let userExperienceManager;
  let mockSecurityIntegration;
  let mockContainer;
  
  beforeEach(async () => {
    // Create mock container element
    mockContainer = {
      style: {},
      classList: {
        add: jest.fn(),
        remove: jest.fn(),
        contains: jest.fn()
      },
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      appendChild: jest.fn(),
      removeChild: jest.fn(),
      setAttribute: jest.fn(),
      getAttribute: jest.fn(),
      innerHTML: '',
      textContent: '',
      children: [],
      querySelector: jest.fn(),
      querySelectorAll: jest.fn(() => [])
    };
    
    document.getElementById.mockReturnValue(mockContainer);
    document.querySelector.mockReturnValue(mockContainer);
    
    // Create mock security integration
    mockSecurityIntegration = {
      initialize: jest.fn().mockResolvedValue({ success: true }),
      startOTR: jest.fn().mockResolvedValue({ success: true }),
      endOTR: jest.fn().mockResolvedValue({ success: true }),
      rotateKeys: jest.fn().mockResolvedValue({ success: true, keyGeneration: 1 }),
      getStatus: jest.fn().mockReturnValue({
        initialized: true,
        otrActive: false,
        keyGeneration: 0,
        securityLevel: 'HIGH'
      }),
      on: jest.fn(),
      off: jest.fn(),
      shutdown: jest.fn()
    };
    
    // Initialize UserExperienceManager
    userExperienceManager = new UserExperienceManager({
      containerId: 'webottr-container',
      theme: 'modern',
      accessibility: true,
      animations: true,
      notifications: true,
      securityIntegration: mockSecurityIntegration
    });
  });
  
  afterEach(async () => {
    if (userExperienceManager) {
      await userExperienceManager.shutdown();
    }
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should initialize with default configuration', async () => {
      const result = await userExperienceManager.initialize();
      
      expect(result.success).toBe(true);
      expect(userExperienceManager.state.initialized).toBe(true);
      expect(mockSecurityIntegration.initialize).toHaveBeenCalled();
    });

    test('should initialize with custom configuration', async () => {
      const customManager = new UserExperienceManager({
        containerId: 'custom-container',
        theme: 'dark',
        accessibility: false,
        animations: false,
        notifications: false,
        securityIntegration: mockSecurityIntegration
      });
      
      const result = await customManager.initialize();
      
      expect(result.success).toBe(true);
      expect(customManager.config.theme).toBe('dark');
      expect(customManager.config.accessibility).toBe(false);
      expect(customManager.config.animations).toBe(false);
      
      await customManager.shutdown();
    });

    test('should handle missing container gracefully', async () => {
      document.getElementById.mockReturnValue(null);
      
      await expect(userExperienceManager.initialize()).rejects.toThrow('Container not found');
      expect(userExperienceManager.state.initialized).toBe(false);
    });

    test('should handle security integration failure', async () => {
      mockSecurityIntegration.initialize.mockRejectedValueOnce(new Error('Security init failed'));
      
      await expect(userExperienceManager.initialize()).rejects.toThrow('Security init failed');
      expect(userExperienceManager.state.initialized).toBe(false);
    });
  });

  describe('User Interface Creation', () => {
    beforeEach(async () => {
      await userExperienceManager.initialize();
    });

    test('should create main interface elements', () => {
      userExperienceManager.createInterface();
      
      expect(mockContainer.appendChild).toHaveBeenCalled();
      expect(document.createElement).toHaveBeenCalledWith('div');
      expect(userExperienceManager.elements.mainPanel).toBeDefined();
      expect(userExperienceManager.elements.statusIndicator).toBeDefined();
      expect(userExperienceManager.elements.controlButtons).toBeDefined();
    });

    test('should apply correct theme styling', () => {
      userExperienceManager.createInterface();
      
      const calls = document.createElement.mock.calls;
      const elements = calls.map(call => call[0]);
      
      expect(elements).toContain('div');
      expect(mockContainer.classList.add).toHaveBeenCalledWith('webottr-modern-theme');
    });

    test('should create accessibility features when enabled', () => {
      userExperienceManager.config.accessibility = true;
      userExperienceManager.createInterface();
      
      expect(userExperienceManager.accessibilityManager).toBeDefined();
      expect(userExperienceManager.elements.mainPanel.setAttribute).toHaveBeenCalledWith('role', 'application');
      expect(userExperienceManager.elements.mainPanel.setAttribute).toHaveBeenCalledWith('aria-label', 'WebOTR Secure Messaging');
    });

    test('should skip accessibility features when disabled', () => {
      userExperienceManager.config.accessibility = false;
      userExperienceManager.createInterface();
      
      expect(userExperienceManager.accessibilityManager).toBeUndefined();
    });
  });

  describe('Security Operation Simplification', () => {
    beforeEach(async () => {
      await userExperienceManager.initialize();
      userExperienceManager.createInterface();
    });

    test('should simplify OTR start operation', async () => {
      const result = await userExperienceManager.startSecureMessaging();
      
      expect(result.success).toBe(true);
      expect(mockSecurityIntegration.startOTR).toHaveBeenCalled();
      expect(userExperienceManager.state.secureMessagingActive).toBe(true);
    });

    test('should simplify OTR end operation', async () => {
      await userExperienceManager.startSecureMessaging();
      
      const result = await userExperienceManager.endSecureMessaging();
      
      expect(result.success).toBe(true);
      expect(mockSecurityIntegration.endOTR).toHaveBeenCalled();
      expect(userExperienceManager.state.secureMessagingActive).toBe(false);
    });

    test('should simplify key rotation operation', async () => {
      await userExperienceManager.startSecureMessaging();
      
      const result = await userExperienceManager.rotateSecurityKeys();
      
      expect(result.success).toBe(true);
      expect(result.keyGeneration).toBe(1);
      expect(mockSecurityIntegration.rotateKeys).toHaveBeenCalled();
    });

    test('should provide user-friendly error messages', async () => {
      mockSecurityIntegration.startOTR.mockRejectedValueOnce(new Error('Network timeout'));
      
      const result = await userExperienceManager.startSecureMessaging();
      
      expect(result.success).toBe(false);
      expect(result.userMessage).toContain('connection');
      expect(result.userMessage).not.toContain('Network timeout'); // Technical error hidden
    });

    test('should handle concurrent operations gracefully', async () => {
      const operation1 = userExperienceManager.startSecureMessaging();
      const operation2 = userExperienceManager.startSecureMessaging();
      
      const results = await Promise.allSettled([operation1, operation2]);
      
      expect(results[0].status).toBe('fulfilled');
      expect(results[1].status).toBe('fulfilled');
      expect(results[1].value.userMessage).toContain('already in progress');
    });
  });

  describe('User Interaction Handling', () => {
    beforeEach(async () => {
      await userExperienceManager.initialize();
      userExperienceManager.createInterface();
    });

    test('should handle button clicks', () => {
      const startButton = userExperienceManager.elements.startButton;
      const clickHandler = jest.fn();
      
      userExperienceManager.setupEventListeners();
      
      // Simulate button click
      const clickEvent = { type: 'click', target: startButton };
      startButton.addEventListener.mock.calls[0][1](clickEvent);
      
      expect(startButton.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
    });

    test('should handle keyboard navigation', () => {
      userExperienceManager.setupEventListeners();
      
      const keydownEvent = {
        type: 'keydown',
        key: 'Enter',
        target: userExperienceManager.elements.startButton
      };
      
      // Simulate keydown event
      mockContainer.addEventListener.mock.calls
        .find(call => call[0] === 'keydown')[1](keydownEvent);
      
      expect(mockContainer.addEventListener).toHaveBeenCalledWith('keydown', expect.any(Function));
    });

    test('should provide visual feedback for interactions', async () => {
      const button = userExperienceManager.elements.startButton;
      
      await userExperienceManager.startSecureMessaging();
      
      expect(button.classList.add).toHaveBeenCalledWith('loading');
      expect(button.classList.remove).toHaveBeenCalledWith('loading');
    });

    test('should handle touch interactions on mobile', () => {
      userExperienceManager.setupEventListeners();
      
      const touchEvent = {
        type: 'touchstart',
        touches: [{ clientX: 100, clientY: 100 }]
      };
      
      // Simulate touch event
      mockContainer.addEventListener.mock.calls
        .find(call => call[0] === 'touchstart')[1](touchEvent);
      
      expect(mockContainer.addEventListener).toHaveBeenCalledWith('touchstart', expect.any(Function));
    });
  });

  describe('Status Display and Updates', () => {
    beforeEach(async () => {
      await userExperienceManager.initialize();
      userExperienceManager.createInterface();
    });

    test('should display current security status', () => {
      userExperienceManager.updateSecurityStatus();
      
      const statusIndicator = userExperienceManager.elements.statusIndicator;
      expect(statusIndicator.textContent).toContain('Not Connected');
      expect(statusIndicator.classList.add).toHaveBeenCalledWith('status-disconnected');
    });

    test('should update status when security state changes', async () => {
      mockSecurityIntegration.getStatus.mockReturnValue({
        initialized: true,
        otrActive: true,
        keyGeneration: 1,
        securityLevel: 'HIGH'
      });
      
      await userExperienceManager.startSecureMessaging();
      userExperienceManager.updateSecurityStatus();
      
      const statusIndicator = userExperienceManager.elements.statusIndicator;
      expect(statusIndicator.textContent).toContain('Secure');
      expect(statusIndicator.classList.add).toHaveBeenCalledWith('status-secure');
    });

    test('should display key generation information', () => {
      mockSecurityIntegration.getStatus.mockReturnValue({
        initialized: true,
        otrActive: true,
        keyGeneration: 5,
        securityLevel: 'HIGH'
      });
      
      userExperienceManager.updateSecurityStatus();
      
      const keyGenElement = userExperienceManager.elements.keyGeneration;
      expect(keyGenElement.textContent).toBe('5');
    });

    test('should show security level indicator', () => {
      mockSecurityIntegration.getStatus.mockReturnValue({
        initialized: true,
        otrActive: true,
        keyGeneration: 1,
        securityLevel: 'MAXIMUM'
      });
      
      userExperienceManager.updateSecurityStatus();
      
      const securityLevel = userExperienceManager.elements.securityLevel;
      expect(securityLevel.textContent).toBe('Maximum Security');
      expect(securityLevel.classList.add).toHaveBeenCalledWith('security-maximum');
    });
  });

  describe('Notifications and Feedback', () => {
    beforeEach(async () => {
      await userExperienceManager.initialize();
      userExperienceManager.createInterface();
    });

    test('should show success notifications', () => {
      userExperienceManager.showNotification('Operation completed successfully', 'success');
      
      expect(document.createElement).toHaveBeenCalledWith('div');
      expect(mockContainer.appendChild).toHaveBeenCalled();
    });

    test('should show error notifications', () => {
      userExperienceManager.showNotification('Operation failed', 'error');
      
      const notification = userExperienceManager.elements.notification;
      expect(notification.classList.add).toHaveBeenCalledWith('notification-error');
    });

    test('should auto-hide notifications after timeout', (done) => {
      userExperienceManager.showNotification('Test message', 'info');
      
      setTimeout(() => {
        const notification = userExperienceManager.elements.notification;
        expect(notification.classList.add).toHaveBeenCalledWith('notification-hidden');
        done();
      }, 3100); // Default timeout is 3000ms
    });

    test('should handle notification queue', () => {
      userExperienceManager.showNotification('Message 1', 'info');
      userExperienceManager.showNotification('Message 2', 'success');
      userExperienceManager.showNotification('Message 3', 'warning');
      
      expect(userExperienceManager.notificationQueue).toHaveLength(3);
    });

    test('should provide haptic feedback on supported devices', () => {
      // Mock vibration API
      global.navigator = { vibrate: jest.fn() };
      
      userExperienceManager.provideHapticFeedback('success');
      
      expect(navigator.vibrate).toHaveBeenCalledWith([100]);
    });
  });

  describe('Accessibility Features', () => {
    beforeEach(async () => {
      userExperienceManager.config.accessibility = true;
      await userExperienceManager.initialize();
      userExperienceManager.createInterface();
    });

    test('should provide screen reader support', () => {
      const statusElement = userExperienceManager.elements.statusIndicator;
      
      expect(statusElement.setAttribute).toHaveBeenCalledWith('aria-live', 'polite');
      expect(statusElement.setAttribute).toHaveBeenCalledWith('aria-atomic', 'true');
    });

    test('should support keyboard navigation', () => {
      const buttons = [
        userExperienceManager.elements.startButton,
        userExperienceManager.elements.endButton,
        userExperienceManager.elements.rotateButton
      ];
      
      buttons.forEach(button => {
        expect(button.setAttribute).toHaveBeenCalledWith('tabindex', '0');
      });
    });

    test('should provide high contrast mode', () => {
      userExperienceManager.enableHighContrastMode();
      
      expect(mockContainer.classList.add).toHaveBeenCalledWith('high-contrast');
    });

    test('should support reduced motion preferences', () => {
      // Mock prefers-reduced-motion
      global.window.matchMedia = jest.fn(() => ({
        matches: true,
        addEventListener: jest.fn(),
        removeEventListener: jest.fn()
      }));
      
      userExperienceManager.checkMotionPreferences();
      
      expect(userExperienceManager.config.animations).toBe(false);
    });

    test('should announce status changes to screen readers', () => {
      const announcement = userExperienceManager.announceToScreenReader('Security enabled');
      
      expect(announcement.setAttribute).toHaveBeenCalledWith('aria-live', 'assertive');
      expect(announcement.textContent).toBe('Security enabled');
    });
  });

  describe('Animation and Visual Effects', () => {
    beforeEach(async () => {
      userExperienceManager.config.animations = true;
      await userExperienceManager.initialize();
      userExperienceManager.createInterface();
    });

    test('should animate status transitions', () => {
      userExperienceManager.animateStatusChange('connecting', 'connected');
      
      expect(window.requestAnimationFrame).toHaveBeenCalled();
    });

    test('should animate button interactions', () => {
      const button = userExperienceManager.elements.startButton;
      
      userExperienceManager.animateButtonPress(button);
      
      expect(button.classList.add).toHaveBeenCalledWith('button-pressed');
    });

    test('should respect reduced motion preferences', () => {
      userExperienceManager.config.animations = false;
      
      userExperienceManager.animateStatusChange('connecting', 'connected');
      
      // Should not use requestAnimationFrame when animations disabled
      expect(window.requestAnimationFrame).not.toHaveBeenCalled();
    });

    test('should provide smooth transitions', () => {
      const element = userExperienceManager.elements.mainPanel;
      
      userExperienceManager.smoothTransition(element, { opacity: 0 }, { opacity: 1 });
      
      expect(element.style.transition).toContain('opacity');
    });
  });

  describe('Error Handling and Recovery', () => {
    beforeEach(async () => {
      await userExperienceManager.initialize();
      userExperienceManager.createInterface();
    });

    test('should handle security integration errors gracefully', async () => {
      mockSecurityIntegration.startOTR.mockRejectedValueOnce(new Error('Security error'));
      
      const result = await userExperienceManager.startSecureMessaging();
      
      expect(result.success).toBe(false);
      expect(result.userMessage).toBeDefined();
      expect(userExperienceManager.elements.notification).toBeDefined();
    });

    test('should provide recovery suggestions', async () => {
      mockSecurityIntegration.startOTR.mockRejectedValueOnce(new Error('Network error'));
      
      const result = await userExperienceManager.startSecureMessaging();
      
      expect(result.recoverySuggestions).toContain('Check your internet connection');
    });

    test('should maintain UI state consistency during errors', async () => {
      const initialState = { ...userExperienceManager.state };
      
      mockSecurityIntegration.startOTR.mockRejectedValueOnce(new Error('Test error'));
      
      await userExperienceManager.startSecureMessaging();
      
      expect(userExperienceManager.state.secureMessagingActive).toBe(initialState.secureMessagingActive);
    });

    test('should handle DOM manipulation errors', () => {
      mockContainer.appendChild.mockImplementationOnce(() => {
        throw new Error('DOM error');
      });
      
      expect(() => {
        userExperienceManager.showNotification('Test', 'info');
      }).not.toThrow();
    });
  });

  describe('Performance and Optimization', () => {
    beforeEach(async () => {
      await userExperienceManager.initialize();
      userExperienceManager.createInterface();
    });

    test('should debounce rapid user interactions', () => {
      const debouncedFunction = userExperienceManager.debounce(jest.fn(), 100);
      
      // Rapid calls
      debouncedFunction();
      debouncedFunction();
      debouncedFunction();
      
      expect(debouncedFunction.fn).not.toHaveBeenCalled();
      
      // Wait for debounce
      setTimeout(() => {
        expect(debouncedFunction.fn).toHaveBeenCalledTimes(1);
      }, 150);
    });

    test('should throttle status updates', () => {
      const throttledUpdate = userExperienceManager.throttle(
        userExperienceManager.updateSecurityStatus.bind(userExperienceManager),
        100
      );
      
      // Rapid calls
      throttledUpdate();
      throttledUpdate();
      throttledUpdate();
      
      expect(throttledUpdate.fn).toHaveBeenCalledTimes(1);
    });

    test('should cleanup resources on shutdown', async () => {
      const shutdownResult = await userExperienceManager.shutdown();
      
      expect(shutdownResult.success).toBe(true);
      expect(mockSecurityIntegration.shutdown).toHaveBeenCalled();
      expect(userExperienceManager.state.initialized).toBe(false);
    });

    test('should measure and report performance metrics', () => {
      const startTime = performance.now();
      
      userExperienceManager.measurePerformance('ui-update', () => {
        userExperienceManager.updateSecurityStatus();
      });
      
      const metrics = userExperienceManager.getPerformanceMetrics();
      
      expect(metrics['ui-update']).toBeDefined();
      expect(metrics['ui-update'].averageTime).toBeGreaterThan(0);
    });
  });
});
