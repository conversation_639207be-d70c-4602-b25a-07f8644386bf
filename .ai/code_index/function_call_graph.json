{"version": "1.0", "functions": {"core.utils.formatDate": {"file": "src/core/utils.js", "lineStart": 10, "lineEnd": 15, "calledBy": ["ui.components.DateDisplay", "api.controllers.EventController.create"], "calls": ["core.utils.padZero"], "parameters": ["date", "format"], "returns": "string", "description": "Formats a date according to the specified format string"}, "core.utils.padZero": {"file": "src/core/utils.js", "lineStart": 5, "lineEnd": 8, "calledBy": ["core.utils.formatDate"], "calls": [], "parameters": ["num", "size"], "returns": "string", "description": "Pads a number with leading zeros to the specified size"}, "api.controllers.EventController.create": {"file": "src/api/controllers/EventController.js", "lineStart": 25, "lineEnd": 45, "calledBy": ["api.routes"], "calls": ["core.utils.formatDate", "database.models.Event.create", "notifications.services.NotificationService.sendEventCreatedNotification"], "parameters": ["req", "res"], "returns": "Promise<Response>", "description": "Creates a new event and sends notifications"}, "ui.components.DateDisplay": {"file": "src/ui/components/DateDisplay.jsx", "lineStart": 5, "lineEnd": 15, "calledBy": ["ui.components.EventCard", "ui.pages.EventDetails"], "calls": ["core.utils.formatDate"], "parameters": ["date", "format"], "returns": "ReactComponent", "description": "Displays a formatted date"}}}