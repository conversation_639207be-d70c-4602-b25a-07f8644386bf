# Code Review Guidelines

This document outlines how <PERSON> should approach code reviews for this project.

## Purpose

The purpose of code reviews is to:
- Ensure code quality and consistency
- Identify potential bugs, security issues, and performance problems
- Share knowledge and best practices
- Verify that code meets requirements and follows project standards

## Code Review Process

When asked to review code, <PERSON> should:

1. **Understand the Context**: Review the associated issue/ticket and requirements first
2. **Check for Functionality**: Verify the code accomplishes its intended purpose
3. **Examine Code Quality**: Review for readability, maintainability, and adherence to standards
4. **Look for Edge Cases**: Identify potential edge cases or error conditions
5. **Suggest Improvements**: Provide constructive feedback with specific suggestions
6. **Prioritize Feedback**: Distinguish between critical issues and minor suggestions

## Review Checklist

### Functionality
- Does the code accomplish the stated requirements?
- Are all edge cases handled appropriately?
- Is error handling comprehensive and appropriate?

### Code Quality
- Is the code readable and well-structured?
- Are functions and methods appropriately sized and focused?
- Are variable and function names clear and descriptive?
- Is there appropriate documentation and comments?
- Is there unnecessary code duplication?

### Performance
- Are there any obvious performance issues?
- Are database queries optimized?
- Are there any potential memory leaks?
- Is caching used appropriately where beneficial?

### Security
- Are inputs properly validated and sanitized?
- Are authentication and authorization checks in place?
- Are there any potential SQL injection, XSS, or CSRF vulnerabilities?
- Is sensitive data properly protected?

### Testing
- Are there appropriate unit tests?
- Do tests cover edge cases and error conditions?
- Is test coverage adequate?
- Are tests clear and maintainable?

## Feedback Style

When providing feedback, Claude should:

1. **Be Specific**: Point to exact lines or blocks of code
2. **Be Constructive**: Explain why something is an issue and how to improve it
3. **Provide Examples**: When suggesting changes, provide example code when helpful
4. **Use a Positive Tone**: Focus on improving the code, not criticizing the developer
5. **Explain Reasoning**: Provide context for why a change is recommended
6. **Reference Standards**: Link to project standards or external best practices when relevant

## Example Feedback Format

```
## Summary
[Brief overview of the code and major findings]

## Critical Issues
- [Issue 1]: [Explanation and suggestion]
- [Issue 2]: [Explanation and suggestion]

## Suggestions for Improvement
- [Suggestion 1]: [Explanation and example]
- [Suggestion 2]: [Explanation and example]

## Positive Aspects
- [Positive point 1]
- [Positive point 2]
```

## Language-Specific Considerations

### Python
- PEP 8 compliance
- Type hints usage
- Docstring format
- Appropriate use of Python idioms

### JavaScript/TypeScript
- ESLint/TSLint compliance
- Proper async/await usage
- Type definitions (for TypeScript)
- Modern ES features usage

### SQL
- Query optimization
- Proper indexing
- Transaction management
- Security considerations

## Final Considerations

- Always balance thoroughness with pragmatism
- Consider the context and urgency of the change
- Focus on issues that matter most for maintainability and correctness
- Remember that the goal is to improve the codebase, not achieve perfection

## Related Documentation

- See `.ai/docs/4-acceptance/code_quality/` for other code quality standards
- See `.ai/docs/4-acceptance/security/` for security guidelines
- See `.ai/docs/4-acceptance/performance/` for performance guidelines
- See `.ai/docs/3-development/patterns/` for implementation patterns

---

*Last Updated: 2024-03-21* 