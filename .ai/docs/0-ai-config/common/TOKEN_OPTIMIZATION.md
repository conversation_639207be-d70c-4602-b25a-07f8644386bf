# Token Usage Optimization Guide

This document provides guidelines for optimizing token usage when working with AI assistants, particularly for large codebases and complex tasks.

## Token Usage Principles

1. **Be Concise**: Keep prompts focused and relevant
2. **Use References**: Point to docs rather than pasting full content
3. **Chunk Large Tasks**: Break complex tasks into smaller interactions
4. **Exclude Irrelevant Details**: Only provide necessary context
5. **Use Tool Calls**: Leverage AI's ability to call tools when available

## Effective Iteration

Iterative prompting can improve results:

1. **Start Broad**: Begin with a general prompt
2. **Refine Gradually**: Add specificity based on responses
3. **Clarify Misunderstandings**: Explicitly correct misinterpretations
4. **Provide Feedback**: Tell <PERSON> what was useful and what wasn't
5. **Build on Previous Context**: Reference earlier parts of the conversation

## Troubleshooting Token Issues

If AI's responses aren't meeting expectations:

1. **Add Structure**: Break down the request into clearer steps
2. **Provide Examples**: Show exactly what you're looking for
3. **Specify Format**: Request a specific output format
4. **Check Context Window**: Ensure key information is in recent context
5. **Consider Token Limits**: Complex responses may hit token limits

## Best Practices for Large Codebases

1. **Reference Specific Files**: Use file paths instead of pasting code
2. **Use Semantic Search**: Let AI search for relevant code
3. **Incremental Context**: Build context gradually
4. **Focus on Changes**: Only show modified code sections
5. **Use Documentation**: Reference docs instead of code when possible

## Token Optimization Examples

### Good Example
```
Task: Review the changes in feature_branch_workflow.md

Context: See .ai/docs/2-technical-design/workflows/feature_branch_workflow.md
Changes: Lines 45-60 modified to add new validation step
```

### Bad Example
```
Task: Review the changes in feature_branch_workflow.md

Context: [Pastes entire 200-line file]
Changes: [Pastes entire diff]
```

## Monitoring Token Usage

1. **Track Response Length**: Monitor AI response sizes
2. **Identify Patterns**: Note which prompts use many tokens
3. **Optimize Common Tasks**: Create templates for frequent operations
4. **Review Context**: Ensure context is relevant and necessary
5. **Clean Up History**: Remove unnecessary context from conversations

## Tool-Specific Optimization

### For Code Review
- Use semantic search to find relevant code
- Reference specific line numbers
- Focus on changed sections

### For Architecture Design
- Reference existing architecture docs
- Use diagrams for complex relationships
- Focus on new components

### For Debugging
- Include only relevant error messages
- Reference specific code sections
- Focus on the problem area

## Common Pitfalls

1. **Over-Context**: Including too much background information
2. **Redundant Information**: Repeating information already in docs
3. **Unnecessary Details**: Including irrelevant code or context
4. **Poor Structure**: Making AI parse poorly organized information
5. **Missing References**: Not using available documentation

## Optimization Checklist

Before sending a prompt:
1. [ ] Remove unnecessary context
2. [ ] Use file references instead of pasting code
3. [ ] Structure information clearly
4. [ ] Focus on specific changes or issues
5. [ ] Reference existing documentation
6. [ ] Break down complex tasks
7. [ ] Specify clear expectations
8. [ ] Include only relevant examples 