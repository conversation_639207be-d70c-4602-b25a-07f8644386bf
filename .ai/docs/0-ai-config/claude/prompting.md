# AI Prompting Guide

This document provides guidance on how to effectively prompt <PERSON> for assistance with this project. It includes examples of well-structured prompts for common tasks.

## Purpose

Effective prompting helps <PERSON> provide more accurate, relevant, and useful responses. This guide helps team members craft prompts that clearly communicate their needs and context to <PERSON>.

## General Prompting Principles

1. **Be Specific**: Clearly state what you need
2. **Provide Context**: Include relevant background information
3. **Set Expectations**: Specify the format or level of detail you want
4. **Use Examples**: Show examples of desired output when possible
5. **Break Down Complex Tasks**: Split complex requests into smaller steps
6. **Iterate**: Refine your prompt based on <PERSON>'s responses

## Prompt Structure

A well-structured prompt typically includes:

1. **Task Description**: What you want <PERSON> to do
2. **Context**: Relevant background information
3. **Constraints**: Any limitations or requirements
4. **Format**: How you want the response structured
5. **Examples**: Sample outputs (if applicable)

## Example Prompts by Task

### Code Implementation

```
Task: Implement a function to [specific functionality].

Context: This is part of the [component/module] which handles [purpose]. It needs to integrate with [related components].

Requirements:
- The function should [specific behavior]
- It needs to handle [edge cases]
- Follow our [specific coding standards]
- Include error handling for [specific scenarios]

Example usage:
```python
# How the function would be called
result = my_function(param1, param2)
```

Please include unit tests and documentation.
```

### Code Review

```
Task: Review this code for issues and improvements.

Code:
```python
def calculate_total(items):
    total = 0
    for item in items:
        total += item.price * item.quantity
    return total
```

Focus areas:
- Performance considerations
- Error handling
- Edge cases
- Adherence to our coding standards
- Potential bugs

Please provide specific suggestions for improvements with code examples.
```

### Debugging

```
Task: Help debug an issue with [specific functionality].

Error message:
```
[Paste exact error message]
```

Context:
- This occurs when [specific conditions]
- The expected behavior is [description]
- I've already tried [previous attempts]

Relevant code:
```python
# Paste the relevant code snippet
```

What might be causing this issue and how can I fix it?
```

### Architecture Design

```
Task: Help design the architecture for [specific feature/component].

Requirements:
- The feature needs to [specific functionality]
- It will interact with [existing components]
- Performance considerations include [specific requirements]
- Security considerations include [specific requirements]

Current architecture:
[Brief description or diagram of current architecture]

Please suggest an architecture approach with:
- Component diagram (using Mermaid)
- Key interfaces
- Data flow
- Considerations for scalability and maintainability
```

## Project-Specific Prompting

### Feature Implementation

```
Task: Help implement the [feature name] as described in .ai/docs/2-technical-design/features/[feature_plan.md].

Current status:
- [What's already been implemented]
- [What's still needed]

Specific assistance needed:
- [Specific aspect you need help with]

Please follow our feature branch workflow as described in .ai/docs/2-technical-design/workflows/feature_branch_workflow.md.
```

### Architecture Questions

```
Task: Explain how [specific component] fits into our overall architecture.

I'm trying to understand:
- [Specific question about the architecture]
- [Another specific question]

Please reference the architecture documentation in .ai/docs/1-context/architecture.md in your explanation.
```

### Troubleshooting Project-Specific Issues

```
Task: Help troubleshoot [specific issue] with [component].

Error/Issue:
[Description of the problem]

I've checked the troubleshooting guide (.ai/docs/3-development/troubleshooting.md) and tried:
- [Solution 1 already attempted]
- [Solution 2 already attempted]

Relevant configuration:
[Any relevant configuration details]
```

## Token Optimization

For optimal token usage:
1. Keep prompts concise but complete
2. Remove unnecessary context
3. Use clear, direct language
4. Structure information hierarchically
5. Reference existing documentation instead of repeating it

## Best Practices

1. **Version Control**: Always reference specific versions of documentation
2. **Error Handling**: Include error messages and stack traces when relevant
3. **Code Snippets**: Use appropriate code blocks with language specification
4. **Context**: Provide relevant background information
5. **Iteration**: Start with a basic prompt and refine based on responses 