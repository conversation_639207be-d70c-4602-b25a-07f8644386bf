# AI Development Rules

This document outlines the rules and guidelines for AI-assisted development in this project.

## Core Principles

1. **Security First**: Never expose sensitive information or credentials
2. **Code Quality**: Maintain high standards for code quality and documentation
3. **Consistency**: Follow established patterns and standards
4. **Transparency**: Document AI-assisted changes clearly
5. **Validation**: Ensure all changes meet project requirements

## Development Workflow

### 1. Initial Setup
- Review project architecture and documentation
- Understand existing patterns and standards
- Set up development environment
- Configure AI tools appropriately

### 2. Feature Development
- Follow feature branch workflow
- Use semantic search for relevant code
- Reference existing patterns
- Document AI-assisted changes
- Include tests and documentation

### 3. Code Review
- Review AI-generated code thoroughly
- Ensure security best practices
- Verify performance considerations
- Check documentation completeness
- Validate test coverage

### 4. Testing
- Write comprehensive tests
- Include edge cases
- Test security implications
- Verify performance
- Document test scenarios

## AI Tool Usage Guidelines

### Code Generation
1. **Structure**:
   - Follow project patterns
   - Use consistent naming
   - Include proper documentation
   - Add type annotations

2. **Security**:
   - No hardcoded credentials
   - Secure key handling
   - Input validation
   - Error handling

3. **Performance**:
   - Optimize algorithms
   - Minimize dependencies
   - Consider scalability
   - Profile critical paths

### Documentation
1. **Content**:
   - Clear explanations
   - Code examples
   - Usage patterns
   - Security considerations

2. **Format**:
   - Follow project standards
   - Use consistent style
   - Include diagrams
   - Reference related docs

### Testing
1. **Coverage**:
   - Unit tests
   - Integration tests
   - Edge cases
   - Error scenarios

2. **Quality**:
   - Clear test names
   - Proper setup/teardown
   - Isolation
   - Performance tests

## Validation Checklist

### Code Generation
- [ ] Follows project patterns
- [ ] Includes documentation
- [ ] Has proper tests
- [ ] Handles errors
- [ ] Is secure
- [ ] Performs well

### Documentation
- [ ] Follows standards
- [ ] Is complete
- [ ] Has examples
- [ ] References related docs
- [ ] Is up-to-date

### Testing
- [ ] Covers functionality
- [ ] Tests edge cases
- [ ] Verifies security
- [ ] Checks performance
- [ ] Is maintainable

## Common Pitfalls

1. **Security**:
   - Exposing sensitive data
   - Missing input validation
   - Insecure defaults
   - Poor key handling

2. **Performance**:
   - Inefficient algorithms
   - Memory leaks
   - Blocking operations
   - Poor scaling

3. **Maintainability**:
   - Poor documentation
   - Complex code
   - Missing tests
   - Inconsistent style

## Best Practices

1. **Code Generation**:
   - Use semantic search
   - Reference patterns
   - Include tests
   - Document changes

2. **Documentation**:
   - Be clear
   - Include examples
   - Reference docs
   - Keep updated

3. **Testing**:
   - Write tests first
   - Cover edge cases
   - Test security
   - Check performance

## Tool-Specific Guidelines

### For Code Generation
- Use semantic search
- Reference patterns
- Include tests
- Document changes

### For Documentation
- Follow standards
- Include examples
- Reference docs
- Keep updated

### For Testing
- Write tests first
- Cover edge cases
- Test security
- Check performance

## Review Process

1. **Self-Review**:
   - Check against checklist
   - Verify security
   - Test functionality
   - Review documentation

2. **Peer Review**:
   - Get human review
   - Address feedback
   - Update documentation
   - Fix issues

3. **Final Check**:
   - Verify all requirements
   - Check documentation
   - Run all tests
   - Security audit 