# AI-Driven Feature Development

This document outlines how the AI should approach feature development using the project roadmap as a guide.

## Roadmap-Driven Development Process

### 1. Roadmap Structure

The roadmap in `.ai/docs/1-context/roadmap.md` serves as the source of truth for feature development. It is structured as follows:

```markdown
## Feature Development Queue

### Ready for Development
- [ ] Feature A: #123 - Brief description
  - [ ] Subtask A1: Implement X functionality
  - [ ] Subtask A2: Create tests for X
- [ ] Feature B: #124 - Brief description

### In Progress
- [ ] Feature C: #125 - Brief description (Branch: feature/implement-feature-c)
  - [x] Subtask C1: Research implementation options
  - [ ] Subtask C2: Implement core functionality
  - [ ] Subtask C3: Add documentation

### Completed
- [x] Feature D: #126 - Brief description (Merged in PR #45)
```

### 2. Feature Selection

The AI should:

1. Review the roadmap to identify the highest priority feature in the "Ready for Development" section
2. Update the roadmap by moving the selected feature to "In Progress"
3. Add the branch name to the feature entry in the format: `(Branch: feature/feature-name)`

### 3. Feature Planning

For each new feature, the AI should:

1. Create a feature plan document in `.ai/docs/2-technical-design/feature_plans/`
2. Break down the feature into discrete, manageable tasks
3. Identify dependencies and potential challenges
4. Outline the testing strategy
5. Include a Mermaid diagram visualizing the feature workflow or architecture

Example feature plan structure:

```markdown
# Feature Plan: User Authentication

## Overview
Implement user authentication system with login, registration, and password reset.

## Tasks
1. Create user model and database schema
2. Implement registration endpoint
3. Implement login endpoint and JWT token generation
4. Create password reset functionality
5. Add authentication middleware
6. Create frontend login and registration forms
7. Implement client-side token management

## Dependencies
- PostgreSQL database
- JWT library
- Email service for password reset

## Testing Strategy
- Unit tests for each endpoint
- Integration tests for authentication flow
- Frontend component tests
- E2E test for complete authentication flow

## Feature Workflow

```mermaid
graph TD
    A[User Enters Credentials] --> B{Valid?}
    B -->|Yes| C[Generate JWT]
    B -->|No| D[Show Error]
    C --> E[Set Auth Cookie]
    E --> F[Redirect to Dashboard]
    D --> G[Return to Login]
```

## Estimated Completion
2 development sessions
```

### 4. Branch Management

The AI should:

1. Follow the Git workflow described in `.ai/docs/2-technical-design/workflows/feature_branch_workflow.md`
2. Create a feature branch with a descriptive name: `feature/implement-user-auth`
3. Set up remote tracking immediately
4. Always remain aware of the current branch

### 5. Implementation Strategy

For each feature, the AI should:

1. Work through tasks in a logical order
2. Make atomic commits with semantic commit messages
3. Push changes frequently (at least once per development session)
4. Update the feature plan with progress
5. Update the roadmap with completed subtasks

### 6. Documentation Updates

As part of feature development, the AI should update:

1. API documentation for any new endpoints
2. Database schema documentation for data model changes
3. UI component documentation for new frontend elements
4. Architecture documentation if the system design is affected
5. Add daily summaries to `.ai/docs/3-development/daily_summaries/` describing progress

For all documentation, the AI must use Mermaid diagrams to visualize:
- API flows (sequence diagrams)
- Data models (class or ER diagrams)
- Process flows (flowcharts)
- State transitions (state diagrams)
- Component relationships (flowcharts or class diagrams)

Example of a Mermaid sequence diagram for API documentation:

```mermaid
sequenceDiagram
    participant Client
    participant AuthController
    participant UserService
    participant Database
    
    Client->>AuthController: POST /api/auth/login (credentials)
    AuthController->>UserService: validateUser(email, password)
    UserService->>Database: findUserByEmail(email)
    Database-->>UserService: User object
    UserService->>UserService: verifyPassword(password, hash)
    UserService-->>AuthController: Validation result
    
    alt Valid credentials
        AuthController->>AuthController: generateJWT(user)
        AuthController-->>Client: 200 OK with token
    else Invalid credentials
        AuthController-->>Client: 401 Unauthorized
    end
```

### 7. Feature Completion

When completing a feature, the AI should:

1. Ensure all tests pass
2. Verify all documentation is updated
3. Create or update user documentation in `docs/`
4. Push final changes
5. Update the roadmap:
   - Move the feature from "In Progress" to "Completed"
   - Add PR reference when available

### 8. Daily Workflow

Each development session should follow this pattern:

1. Begin by checking the roadmap and feature plan
2. Pull latest changes from the current branch
3. Implement the next logical step in the feature plan
4. Write tests for the implemented functionality
5. Update documentation
6. Commit and push changes
7. Update roadmap and feature plan with progress
8. Create a daily summary

### 9. Status Reporting

The AI should maintain:

1. Daily summaries in `.ai/docs/3-development/daily_summaries/YYYY-MM-DD-feature-progress.md`
2. Updated roadmap status
3. Feature plan with current progress
4. Project status reports in `.ai/docs/3-development/status_reports/` for weekly summaries

All status reports should include Mermaid diagrams to visualize:
- Progress (Gantt charts)
- Completed vs. remaining work (flowcharts or pie charts)
- Dependencies and blockers (flowcharts)

## Handling Roadmap Changes

If priorities change or new features need to be added:

1. The AI should recognize updated roadmap items
2. Complete the current task in progress
3. Commit and push all current changes
4. Update the feature plan with the current status
5. Switch to the new priority as directed by the updated roadmap

## Communication About Feature Development

When discussing feature development with users, the AI should:

1. Reference the roadmap and feature plan
2. Explain the current implementation stage
3. Describe any challenges or blockers
4. Provide estimates for completion
5. Suggest improvements or alternatives when appropriate
6. Use Mermaid diagrams to visualize concepts when explaining technical details

## Related Documentation

- See `.ai/docs/1-context/roadmap.md` for project roadmap
- See `.ai/docs/2-technical-design/workflows/feature_branch_workflow.md` for Git workflow
- See `.ai/docs/2-technical-design/workflows/ai_development_workflow.md` for AI development workflow
- See `.ai/docs/4-acceptance/code_quality/code_review_guidelines.md` for code review standards
- See `.ai/docs/3-development/patterns/` for implementation patterns

---

*Last Updated: 2024-03-21* 