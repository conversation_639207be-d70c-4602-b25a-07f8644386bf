# Project Glossary

This document contains project-specific terminology and definitions to help <PERSON> understand domain-specific language used in this project.

## Purpose

The glossary serves as a reference for technical terms, acronyms, and domain-specific language used throughout the project. This helps ensure consistent understanding and communication between team members and <PERSON>.

## How to Use This Glossary

- Add new terms as they are introduced in the project
- Keep definitions clear and concise
- Include examples where helpful
- Link to more detailed documentation when available
- Organize terms by category for easier reference

## Technical Terms

### Architecture

| Term | Definition | Example/Notes |
|------|------------|---------------|
| OTR Protocol | Off-the-Record Messaging Protocol | Used for secure, deniable messaging |
| Web Crypto API | Browser's built-in cryptographic functionality | Used for secure key generation and encryption |
| Platform Adapter | Component that integrates with messaging platforms | Intercepts and processes messages in Teams, Discord, etc. |
| Message Hook | System for intercepting and modifying messages | Uses prototype method hooking for XHR/Fetch/WebSocket |

### Development

| Term | Definition | Example/Notes |
|------|------------|---------------|
| CI/CD | Continuous Integration/Continuous Deployment | Our CI/CD pipeline uses GitHub Actions |
| TDD | Test-Driven Development | Writing tests before implementing features |
| Feature Flag | A technique to enable/disable features at runtime | Used for gradual rollouts and A/B testing |
| Technical Debt | Implied cost of additional work caused by choosing an easy solution now instead of a better approach | Tracked in our backlog with "Tech Debt" label |

### Cryptography

| Term | Definition | Example/Notes |
|------|------------|---------------|
| AES-CTR | Advanced Encryption Standard in Counter Mode | Symmetric encryption for message content |
| Diffie-Hellman | Key exchange protocol | Used for establishing shared secrets |
| SHA-256 HMAC | Hash-based Message Authentication Code | Ensures message integrity and authenticity |
| SMP | Socialist Millionaire Protocol | Used for secure authentication between parties |

## Domain-Specific Terms

### Messaging Concepts

| Term | Definition | Example/Notes |
|------|------------|---------------|
| Perfect Forward Secrecy | Property where past messages remain secure if keys are compromised | Achieved through key rotation |
| Deniable Authentication | Ability to authenticate messages without proving authorship to third parties | Core feature of OTR |
| Session | A secure communication channel between two parties | Managed by the OTR protocol |
| Key Rotation | Regular updating of encryption keys | Ensures forward secrecy |

### Platform Integration

| Term | Definition | Permissions |
|------|------------|-------------|
| Content Script | Browser extension script that runs in web pages | Can access and modify page content |
| Background Script | Browser extension script that runs independently | Can manage state and coordinate across tabs |
| Message Interception | Process of capturing and modifying messages | Requires specific browser permissions |
| Platform Hook | Integration point with messaging platform | Platform-specific implementation |

## Project-Specific Acronyms

| Acronym | Full Form | Description |
|---------|-----------|-------------|
| OTR | Off-the-Record | Protocol for secure and deniable messaging |
| DH | Diffie-Hellman | Key exchange algorithm |
| SMP | Socialist Millionaire Protocol | Secure authentication protocol |
| PFS | Perfect Forward Secrecy | Security property for past messages |

## External Systems and Integrations

| System | Purpose | Integration Method |
|--------|---------|-------------------|
| Microsoft Teams | Enterprise messaging platform | Browser extension with message hooks |
| Discord | Gaming-focused chat platform | Browser extension with message hooks |
| Slack | Business messaging platform | Browser extension with message hooks |
| Web Browsers | Host environment | Browser extension APIs |

## How to Update This Glossary

1. Add new terms as they are introduced in the project
2. Review the glossary periodically to ensure it remains current
3. Archive terms that are no longer relevant
4. Ensure definitions are clear and understandable to new team members

## Related Documentation

- See `.ai/docs/1-context/architecture.md` for system architecture details
- See `.ai/docs/2-technical-design/security/` for security implementation details
- See `.ai/docs/2-technical-design/workflows/` for development workflows
- See `.ai/docs/3-development/patterns/` for implementation patterns

---

*Note: This glossary should be updated as new terms and concepts are introduced to the project.*

*Last Updated: 2024-03-21* 