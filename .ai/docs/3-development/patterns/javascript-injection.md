# JavaScript Injection Pattern for Chat Applications

This document outlines our approach to message interception through JavaScript injection, focusing on how we can hook into chat interfaces.

## Overview

The WebOTR framework will use JavaScript injection to intercept outgoing and incoming messages in web-based chat applications. This approach mimics how a browser extension would operate, but initially will be implemented as a direct script injection into our test-chat-sim for development and testing purposes.

## Injection Patterns

### 1. Test Chat Simulator Injection

Our test-chat-sim provides an ideal controlled environment to develop and test the injection patterns. The implementation involves:

```javascript
// webOTR-inject.js - Main injection script

(function() {
  // Configuration
  const config = {
    debug: true,
    encryptionEnabled: true,
    logNetworkTraffic: true
  };

  // Store original methods to allow for clean unhooking
  const originalMethods = {};
  
  // Initialize WebOTR
  const webOTR = {
    // Basic initialization
    init: function() {
      console.log('WebOTR injection initialized');
      this.hookSendButtons();
      this.observeDOMForMessages();
    },
    
    // Hook into send buttons
    hookSendButtons: function() {
      // Find all forms with a message input and send button
      const chatForms = document.querySelectorAll('.message-input-form');
      
      chatForms.forEach(form => {
        // Store original submit handler
        originalMethods[form.id] = form.onsubmit;
        
        // Replace with our intercepting handler
        form.onsubmit = this.createInterceptHandler(form, originalMethods[form.id]);
        
        if (config.debug) {
          console.log(`WebOTR: Hooked into chat form: ${form.id || 'unnamed'}`);
        }
      });
    },
    
    // Create a handler that intercepts form submissions
    createInterceptHandler: function(form, originalHandler) {
      return function(event) {
        // Prevent default form submission
        event.preventDefault();
        
        // Get the message input element
        const inputElement = form.querySelector('input[type="text"]');
        if (!inputElement) {
          console.error('WebOTR: No input element found in form');
          return false;
        }
        
        // Get the message content
        const originalMessage = inputElement.value;
        
        // Process the message (encrypt, etc.)
        webOTR.processOutgoingMessage(originalMessage, form)
          .then(processedMessage => {
            // Update the input value with the processed message
            inputElement.value = processedMessage;
            
            // Call the original handler if it exists
            if (typeof originalHandler === 'function') {
              // Use call to maintain the original context
              originalHandler.call(form, event);
            } else {
              // If no original handler, submit the form programmatically
              form.submit();
            }
          })
          .catch(error => {
            console.error('WebOTR: Error processing message', error);
          });
        
        return false;
      };
    },
    
    // Process an outgoing message
    processOutgoingMessage: async function(message, form) {
      if (!config.encryptionEnabled) {
        return message;
      }
      
      // Identify the sender based on form class or other attributes
      const isSenderAlice = form.closest('.chat-panel-alice') !== null;
      const sender = isSenderAlice ? 'alice' : 'bob';
      const recipient = isSenderAlice ? 'bob' : 'alice';
      
      if (config.debug) {
        console.log(`WebOTR: Processing message from ${sender} to ${recipient}`);
      }
      
      // This would call the actual WebOTR encryption API
      const encryptedMessage = await this.encryptMessage(message, sender, recipient);
      
      return encryptedMessage;
    },
    
    // Encrypt a message using WebOTR (placeholder)
    encryptMessage: async function(message, sender, recipient) {
      // In the real implementation, this would use the WebOTR library
      // For now, we'll just add a prefix for demonstration
      return `🔒 [${sender}→${recipient}] ${message}`;
    },
    
    // Observe the DOM for new incoming messages
    observeDOMForMessages: function() {
      // Set up a mutation observer to detect new messages
      const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
          if (mutation.type === 'childList') {
            this.checkForNewMessages(mutation.addedNodes);
          }
        });
      });
      
      // Start observing message containers
      const messageContainers = document.querySelectorAll('.messages-container');
      messageContainers.forEach(container => {
        observer.observe(container, { 
          childList: true, 
          subtree: true 
        });
        
        if (config.debug) {
          console.log(`WebOTR: Observing container for messages: ${container.className}`);
        }
      });
    },
    
    // Check for and process new messages
    checkForNewMessages: function(nodes) {
      nodes.forEach(node => {
        if (node.nodeType === Node.ELEMENT_NODE && 
            node.classList && 
            node.classList.contains('message')) {
            
          // Found a new message element
          const messageContent = node.querySelector('.message-content');
          if (messageContent && messageContent.textContent.startsWith('🔒')) {
            // This appears to be an encrypted message, try to decrypt it
            this.processIncomingMessage(messageContent);
          }
        }
        
        // Recursively check child nodes
        if (node.childNodes && node.childNodes.length) {
          this.checkForNewMessages(node.childNodes);
        }
      });
    },
    
    // Process an incoming encrypted message
    processIncomingMessage: function(messageElement) {
      const encryptedContent = messageElement.textContent;
      
      // In the real implementation, this would decrypt the message
      // For now, we'll just simulate by extracting the original text
      const match = encryptedContent.match(/🔒 \[(\w+)→(\w+)\] (.+)/);
      if (match) {
        const [_, sender, recipient, originalMessage] = match;
        
        if (config.debug) {
          console.log(`WebOTR: Decrypting message from ${sender} to ${recipient}`);
        }
        
        // Update the message content with the decrypted message
        messageElement.textContent = originalMessage;
        
        // Add visual indicator that this message was decrypted
        messageElement.parentNode.classList.add('webOTR-decrypted');
      }
    }
  };
  
  // Initialize when the DOM is fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => webOTR.init());
  } else {
    webOTR.init();
  }
})();
```

### 2. Generalized Approach for Web Chat Platforms

For real-world applications, the injection pattern needs to be more adaptable:

1. **Selector Configuration**: Define chat interface selectors for different platforms
2. **Feature Detection**: Auto-detect chat interface elements
3. **Fallback Mechanisms**: Multiple strategies for message interception
4. **Event Observation**: Monitor for dynamically added elements

## Testing with Playwright

Playwright provides an ideal framework for testing the JavaScript injection pattern:

### 1. Injection Testing Framework

```javascript
// Example Playwright test for JavaScript injection
const { test, expect } = require('@playwright/test');

test.describe('WebOTR JavaScript Injection', () => {
  test.beforeEach(async ({ page }) => {
    // Start the chat simulator
    await page.goto('/');
    
    // Clear localStorage
    await page.evaluate(() => localStorage.clear());
    
    // Inject the WebOTR script
    await page.addScriptTag({ path: '../../src/webOTR-inject.js' });
    
    // Verify injection
    const isInjected = await page.evaluate(() => {
      return typeof window.webOTR !== 'undefined';
    });
    
    expect(isInjected).toBeTruthy();
  });

  test('hooks into send buttons', async ({ page }) => {
    // Verify the forms have been hooked
    const hookedForms = await page.evaluate(() => {
      // This would be a function exposed by webOTR-inject.js for testing
      return window.webOTR.getHookedElementsCount();
    });
    
    expect(hookedForms).toBe(2); // Alice and Bob's forms
  });

  test('encrypts outgoing messages', async ({ page }) => {
    // Type a message in Alice's input
    await page.locator('.chat-panel-alice .message-input-form input').fill('Hello from Alice');
    
    // Click send
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Check that the message was encrypted in network traffic
    const networkMessage = await page.locator('.network-message-content').first();
    const messageText = await networkMessage.textContent();
    
    expect(messageText).toContain('🔒');
    expect(messageText).toContain('alice→bob');
  });
  
  test('decrypts incoming messages', async ({ page }) => {
    // Type and send a message from Alice
    await page.locator('.chat-panel-alice .message-input-form input').fill('Secret message');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Wait for delivery to Bob
    await page.waitForSelector('.chat-panel-bob .message-content');
    
    // Check that Bob's panel shows the decrypted message
    const bobMessage = await page.locator('.chat-panel-bob .message-content').first();
    const bobMessageText = await bobMessage.textContent();
    
    expect(bobMessageText).toBe('Secret message');
    
    // Check for the decryption indicator class
    const hasDecryptedClass = await page.locator('.chat-panel-bob .message').first().evaluate(
      el => el.classList.contains('webOTR-decrypted')
    );
    
    expect(hasDecryptedClass).toBeTruthy();
  });
});
```

### 2. Testing Edge Cases

- **Concurrent Messages**: Test handling of rapid message exchanges
- **Connection Disruption**: Verify behavior when network is interrupted
- **Large Messages**: Test performance with large messages
- **Special Characters**: Ensure proper handling of emojis, Unicode, etc.
- **DOM Changes**: Test injection with dynamic UI updates

## Browser Extension Framework

The ultimate goal is to package this injection logic as a browser extension:

1. **Background Script**: Manages encryption state and key storage
2. **Content Script**: Injects the hook logic into web pages
3. **Extension UI**: Provides key management and configuration
4. **Platform Adapters**: Customizes injection for different chat platforms

## Security Considerations

### 1. DOM-Based Security Issues

- Risk of Cross-Site Scripting (XSS) from injected content
- Client-side storage of sensitive key material
- Exposure of encryption metadata in DOM

### 2. Mitigation Strategies

- Sanitize all injected content
- Use secure browser storage APIs
- Implement Content Security Policy (CSP) compatibility
- Minimize exposure of key material in memory

## Implementation Roadmap

1. **Basic Injection**: Simple script that hooks into test-chat-sim (current)
2. **WebOTR Integration**: Connect with actual encryption/decryption APIs
3. **Dynamic Detection**: Create selectors that work across platforms
4. **Extension Framework**: Package as browser extension
5. **Security Hardening**: Implement security best practices 