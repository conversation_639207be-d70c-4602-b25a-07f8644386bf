# Changelog

This document tracks significant changes to the WebOTR project, with the most recent changes listed first.

## v0.3.0 (In Progress)

### Features
- Completed Socialist Millionaire Protocol (SMP) implementation
- Fixed SMP test suite and resolved all pending test issues
- Added comprehensive documentation of SMP workflow with diagrams
- Updated README with detailed SMP explanations

### Development Roadmap
- Prioritized development of 5 Product Requirements Documents (PRDs):
  1. User Experience PRD
  2. Forward Secrecy Implementation PRD
  3. Browser Extension PRD
  4. Multi-Device Support PRD
  5. Platform Integration PRD

### Technical Debt & Improvements
- Improved error handling in SMP implementation
- Enhanced callback and state management in protocol implementation
- Performance optimization for large key exchanges
- Memory usage improvements for browser environments

## v0.2.0 (2023-03-22)

### Features
- Test chat simulator implementation with Alice and Bob interfaces
- Network traffic visualization panel
- End-to-end test suite using Playwright
  - Basic chat functionality tests
  - Performance test suite
  - Accessibility testing with axe-core
  - Concurrent action testing
- Local storage for message persistence
- Dark/light mode theme support

### Bug Fixes
- Fixed localStorage mock issues in unit tests
- Added proper React act() wrappers for async state updates
- Resolved UI inconsistencies in message display
- Fixed scrolling behavior for new messages

### Technical Improvements
- Comprehensive test coverage for chat simulator components
- Improved error handling for network simulation
- Enhanced accessibility for keyboard navigation
- Responsive design for various screen sizes

## v0.1.0 (2023-03-12)

### Features
- Initial OTR protocol implementation
- Basic SMP (Socialist Millionaire Protocol) integration
- Core cryptographic primitives
- Key generation and management utilities

### Technical Foundations
- Project structure and build system
- Basic test framework
- Documentation framework
- CI/CD pipeline setup 