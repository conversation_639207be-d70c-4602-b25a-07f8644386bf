# Git Feature Branch Workflow

This document outlines the Git workflow for feature development in an AI-assisted environment.

## Branch Naming Convention

- Feature branches: `feature/short-feature-description`
- Bugfix branches: `bugfix/issue-description`
- Hotfix branches: `hotfix/critical-issue-description`
- Release branches: `release/version-number`

## Feature Development Process

### 1. Starting a New Feature

Always start from an up-to-date main branch:

```bash
git checkout main
git pull origin main
git checkout -b feature/feature-name
```

Set up the remote tracking immediately:

```bash
git push -u origin feature/feature-name
```

### 2. Making Commits

Follow these guidelines for commits:

- Use semantic commit messages:
  - `feat: add user authentication`
  - `fix: resolve login form validation issue`
  - `docs: update API documentation`
  - `style: format code according to style guide`
  - `refactor: simplify authentication logic`
  - `test: add unit tests for user service`
  - `chore: update dependencies`

- Keep commits focused on single logical changes
- Reference issue numbers when applicable: `feat: implement login form (#123)`
- Commit frequently with clear messages

### 3. Pushing Changes

Push changes to the remote branch frequently:

```bash
git push
```

Always push before ending a development session to ensure work is backed up and visible to the team.

### 4. Staying Up-to-Date with Main

Regularly incorporate changes from main to avoid significant divergence:

```bash
git checkout main
git pull origin main
git checkout feature/feature-name
git merge main
```

Alternatively, use rebase for a cleaner history:

```bash
git checkout feature/feature-name
git fetch origin
git rebase origin/main
```

If using rebase and the branch is already pushed:

```bash
git push --force-with-lease
```

### 5. Completing a Feature

Before finalizing a feature:

1. Ensure all tests pass
2. Update documentation
3. Rebase on latest main if needed
4. Make a final push

```bash
git push
```

### 6. Creating a Pull Request

Create a pull request with:

- Clear title describing the feature
- Detailed description of changes
- Reference to related issue(s)
- Steps to test the feature
- Screenshots or videos if applicable

### 7. After Merge

Once the pull request is merged:

```bash
git checkout main
git pull origin main
git branch -d feature/feature-name  # Delete local branch
git push origin --delete feature/feature-name  # Delete remote branch
```

## Handling Merge Conflicts

If conflicts occur during merge or rebase:

1. Identify conflicting files:
   ```bash
   git status
   ```

2. Edit files to resolve conflicts (look for `<<<<<<< HEAD`, `=======`, and `>>>>>>> branch-name` markers)

3. Add resolved files:
   ```bash
   git add <resolved-files>
   ```

4. Continue the merge or rebase:
   ```bash
   # For merge
   git merge --continue
   
   # For rebase
   git rebase --continue
   ```

## Best Practices for AI-Assisted Development

1. Always verify the branch you're on before making changes:
   ```bash
   git branch
   ```

2. Pull before starting work each session:
   ```bash
   git pull
   ```

3. Keep commits atomic and focused:
   - One logical change per commit
   - Clear, descriptive commit messages
   - Reference related issues

4. Regular synchronization with main:
   - Pull changes frequently
   - Resolve conflicts early
   - Keep feature branches short-lived

5. Documentation updates:
   - Update relevant documentation with each feature
   - Include changes in commit messages
   - Reference documentation updates in PR descriptions

6. Testing:
   - Run tests before committing
   - Include test updates in commits
   - Document test coverage changes

7. Code review preparation:
   - Clean up commit history if needed
   - Update documentation
   - Ensure all tests pass
   - Prepare clear PR description

## Common Git Commands

### Branch Management
```bash
# Create and switch to new branch
git checkout -b feature/new-feature

# Switch to existing branch
git checkout feature/existing-feature

# List all branches
git branch -a

# Delete local branch
git branch -d feature/old-feature

# Delete remote branch
git push origin --delete feature/old-feature
```

### Working with Changes
```bash
# Stage all changes
git add .

# Stage specific files
git add file1.txt file2.txt

# Commit changes
git commit -m "feat: add new feature"

# Push changes
git push

# Pull changes
git pull
```

### Synchronization
```bash
# Fetch all remote changes
git fetch origin

# Merge main into current branch
git merge origin/main

# Rebase current branch on main
git rebase origin/main

# Force push after rebase
git push --force-with-lease
```

### Conflict Resolution
```bash
# Check status
git status

# View conflicts
git diff

# Add resolved files
git add resolved-file.txt

# Continue merge
git merge --continue

# Continue rebase
git rebase --continue
```

## Troubleshooting

### 1. Lost Changes

If you've lost changes:

```bash
# View reflog
git reflog

# Recover specific commit
git checkout HEAD@{1}

# Create new branch from recovered state
git checkout -b feature/recovered-feature
```

### 2. Wrong Branch

If you've committed to the wrong branch:

```bash
# Stash changes
git stash

# Switch to correct branch
git checkout correct-branch

# Apply stashed changes
git stash pop
```

### 3. Revert Changes

To revert changes:

```bash
# Revert last commit
git reset --soft HEAD~1

# Revert specific commit
git revert <commit-hash>

# Revert multiple commits
git revert HEAD~3..HEAD
```

## CI/CD Integration

### 1. Pre-commit Hooks

Set up pre-commit hooks for:

- Code formatting
- Linting
- Type checking
- Test running

### 2. Pull Request Checks

Configure PR checks for:

- Build verification
- Test execution
- Code coverage
- Security scanning
- Dependency updates

### 3. Deployment Pipeline

Set up deployment stages:

1. Build and test
2. Security scanning
3. Performance testing
4. Staging deployment
5. Production deployment

## Version Control Best Practices

1. **Keep Branches Short-lived**
   - Create branches for specific features
   - Merge or delete after completion
   - Regular synchronization with main

2. **Maintain Clean History**
   - Use semantic commit messages
   - Keep commits focused
   - Clean up before merging

3. **Documentation**
   - Update docs with features
   - Include in commit messages
   - Reference in PR descriptions

4. **Testing**
   - Include tests with features
   - Maintain test coverage
   - Document test changes

5. **Security**
   - Review security implications
   - Update security docs
   - Follow security guidelines 