# AI Prompting Guide

This document provides guidance on how to effectively prompt <PERSON> for assistance with this project. It includes examples of well-structured prompts for common tasks.

## Purpose

Effective prompting helps <PERSON> provide more accurate, relevant, and useful responses. This guide helps team members craft prompts that clearly communicate their needs and context to <PERSON>.

## General Prompting Principles

1. **Be Specific**: Clearly state what you need
2. **Provide Context**: Include relevant background information
3. **Set Expectations**: Specify the format or level of detail you want
4. **Use Examples**: Show examples of desired output when possible
5. **Break Down Complex Tasks**: Split complex requests into smaller steps
6. **Iterate**: Refine your prompt based on <PERSON>'s responses

## Prompt Structure

A well-structured prompt typically includes:

1. **Task Description**: What you want <PERSON> to do
2. **Context**: Relevant background information
3. **Constraints**: Any limitations or requirements
4. **Format**: How you want the response structured
5. **Examples**: Sample outputs (if applicable)

## Example Prompts by Task

### Code Implementation

```
Task: Implement a function to [specific functionality].

Context: This is part of the [component/module] which handles [purpose]. It needs to integrate with [related components].

Requirements:
- The function should [specific behavior]
- It needs to handle [edge cases]
- Follow our [specific coding standards]
- Include error handling for [specific scenarios]

Example usage:
```python
# How the function would be called
result = my_function(param1, param2)
```

Please include unit tests and documentation.
```

### Code Review

```
Task: Review this code for issues and improvements.

Code:
```python
def calculate_total(items):
    total = 0
    for item in items:
        total += item.price * item.quantity
    return total
```

Focus areas:
- Performance considerations
- Error handling
- Edge cases
- Adherence to our coding standards
- Potential bugs

Please provide specific suggestions for improvements with code examples.
```

### Debugging

```
Task: Help debug an issue with [specific functionality].

Error message:
```
[Paste exact error message]
```

Context:
- This occurs when [specific conditions]
- The expected behavior is [description]
- I've already tried [previous attempts]

Relevant code:
```python
# Paste the relevant code snippet
```

What might be causing this issue and how can I fix it?
```

### Architecture Design

```
Task: Help design the architecture for [specific feature/component].

Requirements:
- The feature needs to [specific functionality]
- It will interact with [existing components]
- Performance considerations include [specific requirements]
- Security considerations include [specific requirements]

Current architecture:
[Brief description or diagram of current architecture]

Please suggest an architecture approach with:
- Component diagram (using Mermaid)
- Key interfaces
- Data flow
- Considerations for scalability and maintainability
```

### Database Query Optimization

```
Task: Optimize this database query for performance.

Current query:
```sql
SELECT * FROM users 
JOIN orders ON users.id = orders.user_id
WHERE orders.status = 'completed'
```

Context:
- The users table has approximately [number] rows
- The orders table has approximately [number] rows
- This query is used in [specific context]
- Current execution time is [time]

Please suggest optimizations with explanations of why they would improve performance.
```

### Test Case Generation

```
Task: Generate test cases for [specific functionality].

Functionality description:
[Description of what the code does]

Code to test:
```python
# Paste the code to be tested
```

Please provide:
- Unit test cases
- Integration test cases
- Edge cases to consider
- Test data examples
```

### Documentation

```
Task: Help create documentation for [specific component/feature].

Component/Feature:
[Brief description]

Target audience:
[Who will read this documentation]

Current documentation:
[Any existing documentation]

Please provide:
- Overview section
- Installation/Setup instructions
- Usage examples
- API documentation (if applicable)
- Troubleshooting guide
```

### Performance Optimization

```
Task: Help optimize the performance of [specific component/feature].

Current performance metrics:
- Response time: [time]
- Resource usage: [metrics]
- Bottlenecks: [identified issues]

Code to optimize:
```python
# Paste the relevant code
```

Please suggest optimizations with:
- Code changes
- Configuration changes
- Architecture improvements
- Monitoring recommendations
```

### Security Review

```
Task: Review security aspects of [specific component/feature].

Component/Feature:
[Brief description]

Current security measures:
[List of existing security measures]

Please review:
- Authentication mechanisms
- Authorization controls
- Data protection
- Input validation
- Error handling
- Logging practices
```

## Best Practices for Prompting

1. **Start with Context**
   - Provide relevant background information
   - Reference specific files or components
   - Include any relevant error messages or logs

2. **Be Specific About Requirements**
   - List specific features or behaviors needed
   - Include performance requirements
   - Specify security considerations

3. **Include Examples**
   - Show current code or behavior
   - Provide example inputs and expected outputs
   - Include error messages or logs

4. **Request Structured Responses**
   - Ask for specific sections in the response
   - Request code examples where appropriate
   - Ask for explanations of suggested changes

5. **Iterate and Refine**
   - Start with a basic prompt and refine based on responses
   - Ask follow-up questions for clarification
   - Request specific aspects to be expanded upon

## Common Pitfalls to Avoid

1. **Vague Requests**
   - ❌ "Help me with the code"
   - ✅ "Help me implement user authentication in the login component"

2. **Missing Context**
   - ❌ "Fix this bug"
   - ✅ "Fix the null pointer exception in the user service when handling deleted users"

3. **Unclear Format Requirements**
   - ❌ "Write some tests"
   - ✅ "Write unit tests for the calculateTotal function with edge cases for empty cart and negative prices"

4. **Incomplete Information**
   - ❌ "The code isn't working"
   - ✅ "The login endpoint returns 500 error when username contains special characters"

## Using Mermaid Diagrams in Prompts

When requesting architecture or flow diagrams, specify:

```
Task: Create a [diagram type] for [specific component/flow]

Requirements:
- Show [specific elements]
- Include [specific relationships]
- Highlight [specific aspects]

Please use Mermaid syntax and include:
- Component relationships
- Data flow
- State transitions
- Sequence of operations
```

## Prompt Templates

### Feature Implementation

```
Task: Implement [feature name]

Context:
- Part of [larger component/system]
- Integrates with [related components]
- Follows [specific patterns/standards]

Requirements:
1. [Specific requirement 1]
2. [Specific requirement 2]
3. [Specific requirement 3]

Technical considerations:
- Performance: [requirements]
- Security: [requirements]
- Scalability: [requirements]

Please provide:
1. Architecture diagram
2. Implementation approach
3. Code examples
4. Test cases
5. Documentation updates
```

### Bug Fix

```
Task: Fix bug in [component/feature]

Bug description:
[Detailed description of the issue]

Current behavior:
[What happens now]

Expected behavior:
[What should happen]

Steps to reproduce:
1. [Step 1]
2. [Step 2]
3. [Step 3]

Relevant code:
```python
# Paste code
```

Error messages/logs:
[Paste error messages or logs]

Please provide:
1. Root cause analysis
2. Proposed fix
3. Test cases to verify
4. Documentation updates
```

### Code Review Request

```
Task: Review [component/feature] implementation

Code location:
[File path or component name]

Changes made:
[Brief description of changes]

Focus areas:
1. [Specific area 1]
2. [Specific area 2]
3. [Specific area 3]

Please review:
1. Code quality
2. Performance
3. Security
4. Test coverage
5. Documentation
```

## Maintaining Context

When working on complex tasks that require multiple interactions:

1. **Reference Previous Responses**
   ```
   Following up on our previous discussion about [topic]...
   ```

2. **Summarize Progress**
   ```
   We've completed [previous steps] and now need to [next steps]...
   ```

3. **Provide Updated Context**
   ```
   Since our last interaction, [changes made]...
   ```

4. **Specify Current Focus**
   ```
   For this interaction, let's focus on [specific aspect]...
   ```

## Getting the Most from Claude

1. **Ask for Explanations**
   - Request explanations of suggested changes
   - Ask for rationale behind architectural decisions
   - Request clarification on complex concepts

2. **Request Alternatives**
   - Ask for different implementation approaches
   - Request alternative architectural patterns
   - Ask for different optimization strategies

3. **Seek Best Practices**
   - Ask about industry standards
   - Request recommendations for tools/libraries
   - Ask about security best practices

4. **Get Validation**
   - Ask for validation of proposed solutions
   - Request feedback on architectural decisions
   - Ask for review of security considerations
``` 