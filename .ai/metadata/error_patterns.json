{"version": "1.0", "errorPatterns": [{"id": "ERR001", "pattern": "TypeError: Cannot read property '(.*)' of undefined", "description": "Attempting to access a property on an undefined object", "components": ["all"], "solutions": [{"description": "Add null/undefined check before accessing the property", "codePattern": "if (obj && obj.$1) { /* use obj.$1 */ }", "explanation": "Check if the object exists before accessing its properties"}, {"description": "Use optional chaining", "codePattern": "const value = obj?.$1", "explanation": "Use the optional chaining operator to safely access nested properties"}]}, {"id": "ERR002", "pattern": "ReferenceError: (.*) is not defined", "description": "Using a variable that hasn't been defined", "components": ["all"], "solutions": [{"description": "Import or define the missing variable", "codePattern": "import { $1 } from './module';\n// or\nconst $1 = /* appropriate value */;", "explanation": "Ensure the variable is properly imported or defined before use"}]}, {"id": "ERR003", "pattern": "Cannot find module '(.*)'", "description": "Module import error", "components": ["all"], "solutions": [{"description": "Install the missing package", "codePattern": "npm install $1", "explanation": "Install the required dependency"}, {"description": "Fix the import path", "codePattern": "import { something } from './correct/path'", "explanation": "Ensure the import path is correct relative to the file"}]}, {"id": "ERR004", "pattern": "TypeError: (.*) is not a function", "description": "Attempting to call something that is not a function", "components": ["all"], "solutions": [{"description": "Check the type before calling", "codePattern": "if (typeof obj.$1 === 'function') { obj.$1(); }", "explanation": "Verify that the object is a function before attempting to call it"}, {"description": "Ensure proper import/definition", "codePattern": "import { $1 } from './module'; // Make sure $1 is exported as a function", "explanation": "Ensure the function is properly imported or defined"}]}]}