# .ai Directory Structure

This document provides an overview of the `.ai` directory structure, which integrates the best elements from the `.ai` and `.claude` patterns into a unified system for AI-assisted development.

## Overview

The `.ai` directory is organized to optimize AI-assisted development workflows and provide comprehensive documentation and guidance for both human developers and AI assistants.

```
.ai/
├── README.md                    # Overview of the .ai directory
├── STRUCTURE.md                 # This document, explaining the structure
├── .cursorrules                 # Cursor IDE configuration
└── docs/
    ├── 0-ai-config/             # AI tool configurations
    │   ├── claude/              # Claude-specific configuration
    │   │   └── prompting.md     # Guidance for prompting Claude
    │   ├── cursor/              # Cursor IDE configuration
    │   │   └── rules/           # Cursor-specific rules
    │   ├── common/              # Shared AI configuration
    │   └── TOKEN_OPTIMIZATION.md # Token usage optimization
    ├── 1-context/               # High-level project documentation
    │   ├── architecture.md      # System architecture documentation
    │   ├── standards/           # Documentation and coding standards
    │   │   └── documentation_standards.md # Standards for docs
    │   ├── glossary.md          # Project terminology
    │   └── decisions.md         # Key technical decisions
    ├── 2-technical-design/      # Requirements and specifications
    │   ├── workflows/           # Development process documentation
    │   │   └── feature_branch_workflow.md # Branch workflow
    │   ├── requirements/        # Project requirements
    │   │   └── api_requirements.md # API specifications 
    │   └── features/            # Feature specifications
    │       └── _template/       # Standardized feature template
    ├── 3-development/           # Development documentation
    │   ├── code_index/          # Code navigation helpers
    │   ├── patterns/            # Common code patterns
    │   │   └── error_handling/  # Error handling patterns
    │   ├── cheatsheets/         # Quick reference guides
    │   ├── troubleshooting.md   # Common issues and solutions
    │   ├── onboarding.md        # Developer onboarding guide
    │   └── lifecycle/           # Development lifecycle tracking
    │       ├── roadmap.md       # Development roadmap
    │       ├── sprints/         # Sprint planning and retrospectives
    │       ├── daily/           # Daily progress summaries
    │       └── deltas/          # Version change documentation
    └── 4-acceptance/            # Validation and compliance
        ├── security/            # Security compliance
        │   └── security_checklist.md # Security requirements
        ├── performance/         # Performance validation
        │   └── guidelines.md    # Performance optimization guidelines
        ├── testing/             # Testing strategy
        │   └── testing_strategy.md # Testing approach and standards
        └── code_quality/        # Code quality validation
            └── code_review_guidelines.md # Code review standards
```

## Directory Purposes

### 0-ai-config/

Configuration and guidance for AI tools used in the development workflow:

- **claude/**: Configuration specific to Claude AI assistant
  - **prompting.md**: Detailed guidelines for effectively prompting Claude
- **cursor/**: Configuration for the Cursor IDE
  - **rules/**: Rules and guidelines for Cursor AI assistance
- **common/**: Shared configuration applicable to all AI tools
- **TOKEN_OPTIMIZATION.md**: Strategies for optimizing token usage with AI assistants

The AI configuration is separated by tool to allow for tool-specific optimizations while maintaining shared patterns through the common directory.

### 1-context/

High-level project documentation that provides essential context:

- **architecture.md**: Comprehensive system architecture documentation
- **standards/**: Documentation and coding standards for the project
  - **documentation_standards.md**: Standards for creating and maintaining documentation
- **glossary.md**: Definitions of project-specific terminology
- **decisions.md**: Record of key technical decisions with context and rationale

This section focuses on providing AI assistants and new team members with the essential context needed to understand the project's architecture, standards, and terminology.

### 2-technical-design/

Technical specifications and requirements:

- **workflows/**: Documentation of development processes
  - **feature_branch_workflow.md**: Guidelines for feature branch development
- **requirements/**: Project requirements documentation
  - **api_requirements.md**: API specifications and requirements
- **features/**: Detailed specifications for individual features
  - **_template/**: Standardized template for feature documentation

This section bridges high-level context with implementation details, ensuring that all development follows a consistent approach aligned with project requirements.

### 3-development/

Resources to assist during the development process:

- **code_index/**: Indexes of code relationships and patterns for easier navigation
- **patterns/**: Reusable implementation patterns
  - **error_handling/**: Standard patterns for error handling
- **cheatsheets/**: Quick reference guides for common tasks
- **troubleshooting.md**: Common issues and their solutions
- **onboarding.md**: Guide for onboarding new developers
- **lifecycle/**: Tracking of the development lifecycle
  - **roadmap.md**: Future development plans
  - **sprints/**: Sprint planning and tracking
  - **daily/**: Daily development summaries
  - **deltas/**: Documentation of significant changes

This section provides practical development resources that both human developers and AI assistants can reference to maintain consistency and productivity.

### 4-acceptance/

Validation criteria and compliance documentation:

- **security/**: Security requirements and validation
  - **security_checklist.md**: Checklist for security validation
- **performance/**: Performance requirements and testing
  - **guidelines.md**: Guidelines for performance optimization
- **testing/**: Testing approach and methodology
  - **testing_strategy.md**: Comprehensive testing strategy
- **code_quality/**: Code quality standards and validation
  - **code_review_guidelines.md**: Standards for code review

This section ensures that all development meets the project's quality, security, and performance requirements before being accepted.

## Integration with AI Tools

This structure is designed for optimal integration with AI assistants:

1. **Claude**: Through explicit documentation and context organization
2. **Cursor**: Through `.cursorrules` and structured documentation
3. **Other AI Tools**: Through the standardized documentation structure

The structure follows these principles for AI integration:

1. **Context-first approach**: Providing clear architectural and project context
2. **Pattern recognition**: Documenting standard patterns that AI can follow
3. **Validation frameworks**: Clear criteria for evaluating AI-generated code
4. **Development history**: Tracking changes to help AI understand project evolution
5. **Tool-specific optimization**: Configurations tailored to each AI assistant

## Collaborative Workflows

The `.ai` directory supports collaborative workflows between humans and AI by:

1. **Shared context**: Both humans and AI work from the same documentation
2. **Standardized processes**: Clear workflows for features, bugs, and enhancements
3. **Quality standards**: Consistent validation criteria
4. **Knowledge retention**: Documentation of decisions and rationale
5. **Continuous improvement**: Process for updating documentation as the project evolves

## Best Practices

1. **Keep documentation up-to-date**: Update documentation as code evolves
2. **Use standardized templates**: Follow templates for consistency
3. **Reference specific paths**: When working with AI assistants, reference specific paths
4. **Document decisions**: Record the context and rationale for key decisions
5. **Follow validation guidelines**: Ensure all code meets security, performance, and quality standards
6. **Update prompting guidelines**: Refine AI prompting guidance based on experience

## Transition from .claude

If transitioning from a `.claude` structure:

1. Migrate key documents to their corresponding locations in the `.ai` structure
2. Update references to documentation paths in your prompts
3. Review and consolidate duplicated information
4. Update CI/CD and development scripts to reference the new structure

## Version Control

The `.ai` directory should be version controlled along with the codebase to ensure:

1. Documentation evolves alongside the code
2. Changes to standards and patterns are tracked
3. Historic context is preserved
4. Multiple contributors can collaborate on documentation 