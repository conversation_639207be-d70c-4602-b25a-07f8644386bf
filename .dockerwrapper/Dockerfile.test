FROM node:20-slim

# Install system dependencies for Playwright
RUN apt-get update && apt-get install -y \
    libicu-dev \
    libxml2 \
    libsqlite3-0 \
    libxslt1.1 \
    liblcms2-2 \
    libevent-2.1-7 \
    libopus0 \
    libwebp-dev \
    libharfbuzz-icu0 \
    libepoxy0 \
    libjpeg62-turbo \
    libpng16-16 \
    libenchant-2-2 \
    libtasn1-6 \
    libhyphen0 \
    libpcre2-8-0 \
    libpsl5 \
    libnghttp2-14 \
    libgudev-1.0-0 \
    libffi8 \
    libevdev2 \
    libjson-glib-1.0-0 \
    libgnutls30 \
    libx264-dev \
    libnss3 \
    libnspr4 \
    libdbus-1-3 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libxkbcommon0 \
    libpango-1.0-0 \
    libcairo2 \
    libasound2 \
    libatspi2.0-0 \
    libx11-xcb1 \
    libxcursor1 \
    libgtk-3-0 \
    libgdk-pixbuf-2.0-0 \
    libgstreamer1.0-0 \
    libgstreamer-plugins-base1.0-0 \
    libgstreamer-plugins-bad1.0-0 \
    libgtk-4-1 \
    libgraphene-1.0-0 \
    libatomic1 \
    libwoff1 \
    libvpx7 \
    libsecret-1-0 \
    libmanette-0.2-0 \
    libgles2 \
    flite1-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy root package files
COPY package*.json ./
RUN npm install -g serve
RUN npm install -D @playwright/test

# Copy test-chat-sim package files and install dependencies
COPY test-chat-sim/package*.json ./test-chat-sim/
RUN cd test-chat-sim && npm install && npm install -D @playwright/test

# Install Playwright browsers
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
RUN cd test-chat-sim && npx playwright install chromium --with-deps
RUN cd test-chat-sim && npx playwright install firefox --with-deps
RUN cd test-chat-sim && npx playwright install webkit --with-deps

# Copy the rest of the files
COPY . .

CMD ["npm", "test"] 