version: '3'

services:
  test:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.test
    volumes:
      - ../test-results:/app/test-results
    environment:
      - NODE_ENV=test
      - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
      - PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
      - PLAYWRIGHT_SKIP_BROWSER_GC=1
      - DEBUG=pw:api
    ports:
      - "3004:3004"
    command: >
      bash -c "
        echo 'Creating test results directory...' &&
        mkdir -p /app/test-results &&
        chmod 777 /app/test-results &&
        echo 'Changing to test-chat-sim directory...' &&
        cd /app/test-chat-sim &&
        echo 'Building the application...' &&
        npm run build &&
        echo 'Starting server...' &&
        serve -s build -l 3004 &
        SERVER_PID=\$! &&
        echo 'Server started with PID: '\$SERVER_PID &&
        echo 'Waiting for server to be ready...' &&
        for i in \$$(seq 1 30); do
          if curl -s http://localhost:3004 > /dev/null; then
            echo 'Server is ready!'
            break
          fi
          echo 'Waiting for server... (attempt '\$i'/30)'
          sleep 1
        done &&
        echo 'Running Playwright tests...' &&
        cd /app/test-chat-sim &&
        PLAYWRIGHT_TEST_BASE_URL=http://localhost:3004 npm run test:e2e &&
        echo 'Tests completed.' &&
        kill \$SERVER_PID || true" 