version: '3.8'

services:
  test:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.test
    volumes:
      - ../test-results:/app/test-results
      - ../playwright-report:/app/playwright-report
      - ../test-chat-sim:/app/test-chat-sim
    environment:
      - CI=true
      - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
    command: >
      bash -c "
        mkdir -p /app/test-results /app/playwright-report &&
        chmod -R 777 /app/test-results /app/playwright-report &&
        cd /app/test-chat-sim &&
        npm install &&
        npx playwright install &&
        npx playwright test e2e/visual-tests.spec.js --reporter=html,list
      "
    networks:
      - test-network

  app:
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile
    volumes:
      - ../test-chat-sim:/app/test-chat-sim
    command: npm start
    ports:
      - "3000:3000"
    networks:
      - test-network

  db:
    image: postgres:14-alpine
    container_name: ${PROJECT_NAME:-app}-db
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-postgres}
      - POSTGRES_DB=${DB_NAME:-app}
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - app-network

  redis:
    image: redis:alpine
    container_name: ${PROJECT_NAME:-app}-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: ${PROJECT_NAME:-app}-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
    volumes:
      - ../:/app
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - app
    networks:
      - app-network

networks:
  test-network:
    driver: bridge
  app-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data: 