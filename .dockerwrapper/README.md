# Docker Setup

This directory contains Docker configuration files and utility scripts for containerizing, building, and running the application.

## Directory Structure

- `scripts/` - Utility scripts for Docker operations
- `Dockerfile` - Main application Dockerfile
- `docker-compose.yml` - Docker Compose configuration for local development
- `.dockerignore` - Specifies files to exclude from Docker builds

## Quick Start

```bash
# Build and start all containers
cd .dockerwrapper
docker-compose up -d

# View container logs
docker-compose logs -f

# Stop all containers
docker-compose down
```

## Environment Configuration

The Docker setup uses environment variables for configuration. These can be provided in the following ways:

1. Environment variables in your shell
2. `.env` file in the project root
3. Docker Compose environment configuration

Required environment variables:

- `DB_HOST` - Database hostname
- `DB_PORT` - Database port
- `DB_NAME` - Database name
- `DB_USER` - Database username
- `DB_PASSWORD` - Database password
- `APP_SECRET_KEY` - Application secret key
- `APP_DEBUG` - Debug mode (true/false)

## Container Architecture

The application is composed of the following containers:

1. **app** - Main application container
2. **db** - Database container (PostgreSQL)
3. **redis** - Cache and session store
4. **nginx** - Web server and reverse proxy

```
┌─────────────┐     ┌─────────────┐
│    nginx    │────▶│     app     │
└─────────────┘     └──────┬──────┘
                           │
                ┌──────────┴──────────┐
                │                     │
         ┌──────▼──────┐      ┌──────▼──────┐
         │     db      │      │    redis    │
         └─────────────┘      └─────────────┘
```

## Development Workflow

1. Make code changes in your local environment
2. The changes will be reflected in the containers due to volume mounts
3. For dependency changes, rebuild the containers:
   ```bash
   docker-compose build app
   docker-compose up -d
   ```

## Utility Scripts

- `scripts/rebuild.sh` - Rebuild containers and restart
- `scripts/reset-db.sh` - Reset database to a clean state
- `scripts/backup-db.sh` - Create a database backup
- `scripts/restore-db.sh` - Restore from a database backup
- `scripts/enter-container.sh` - Enter a running container shell

## Production Deployment

For production environments:

1. Use the production Docker Compose file:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

2. The production configuration includes:
   - No volume mounts for code
   - Optimized build with production dependencies only
   - Appropriate scaling configuration
   - Security hardening

## Troubleshooting

### Common Issues

1. **Container fails to start**
   - Check logs: `docker-compose logs app`
   - Verify environment variables
   - Check disk space and Docker resource limits

2. **Database connection issues**
   - Ensure the database container is running
   - Verify database credentials
   - Check network connectivity between containers

3. **Performance problems**
   - Review Docker resource allocation
   - Check for resource-intensive processes
   - Consider optimizing Docker configuration

## Extending Docker Configuration

To add new services to the Docker setup:

1. Add the service definition to `docker-compose.yml`
2. Create a Dockerfile if needed
3. Update environment variables for integration
4. Rebuild and restart the stack

## Security Considerations

- Do not commit sensitive environment variables
- Regularly update base images for security patches
- Use non-root users in containers
- Implement proper network segmentation
- Regularly audit Docker configuration for security best practices

---

Last Updated: [Date] 