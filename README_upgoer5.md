# WebOTR

A way to talk to friends on the computer without other people seeing what you say.

## What This Is

WebOTR helps you talk in a safe way on places like Teams, Discord, and Slack. When you use this, only you and your friend can see what you both say:

- No one else can read your words
- You can prove you're talking to the right person
- No one can show others what you said
- Even if someone gets your keys later, they can't read old talks

## What It Can Do

- **Hide your words** from others on the computer
- **Make sure only you and your friend can say "this talk was real"**
- **Keep old talks safe** even if someone gets your keys later
- **Check if your friend is really who they say they are**
- **Set up safe talking** without meeting first
- **Works on many talk places** like <PERSON>, Discord, Slack
- **Easy to add** to your computer
- **Uses your computer's built-in help** to do the hard work fast
- **Can talk to other safe talk tools**
- **Helps when you use many computers**

## How To Start

### Add It To Your Computer

1. Get it from the place where you get computer adds
2. Put it on your computer
3. Go to a place where you talk to friends (<PERSON>, Discord, Slack)
4. Click the WebOTR picture to turn it on

### How To Use It

1. Start talking with another friend who has WebOTR
2. Click the button to begin safe talking
3. Check it's really your friend by both typing a word only you two know
4. Now you can talk safely

## What's Inside

The safe talking works in steps:

1. **First Talk Step**: You make a key, hide it, and send it with a mark that says "this is from my hidden key"
2. **Friend's Answer**: Your friend makes their own key and sends it back
3. **Show and Sign**: You show how to see your hidden key and sign the talk
4. **Friend Checks**: Your friend makes sure it's really you and signs back

This way, only you two can read what you say, and no one else can prove who said what.

## Checking It's Your Friend

The friend-check part lets you both make sure you're talking to the right person by checking you both know a word, without saying that word where others can see:

1. **Safe Check**: You check each other by seeing if you know the same word
2. **Word Never Shown**: The word is never sent where someone could take it
3. **Can Ask Questions**: You can ask "what was the name of my first pet?" to help
4. **Clear Yes/No**: You'll see clear "Yes, it's them!" or "No, it's not them!"
5. **Works With Safe Talk**: Fits right in with the rest of the safe talking

To use this:
1. First set up a safe talk
2. Start the check with `tell me our shared word` and maybe ask "What did we eat last week?"
3. The other person answers with their word
4. You both find out if you had the same word

This is like when you and a friend have a secret word that only you two know, and you both say it at the same time to check it's really your friend. 