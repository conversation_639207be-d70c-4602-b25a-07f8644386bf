const path = require('path');
const CopyPlugin = require('copy-webpack-plugin');

module.exports = {
  mode: 'development',
  devtool: 'cheap-module-source-map',
  entry: {
    'service-worker': path.resolve(__dirname, '../extension/background/service-worker.js'),
    'content-script': path.resolve(__dirname, '../extension/content/content-script.js'),
    'platform-detector': path.resolve(__dirname, '../extension/content/platform-detector.js'),
    'message-interceptor': path.resolve(__dirname, '../extension/content/message-interceptor.js'),
    'ui-injector': path.resolve(__dirname, '../extension/content/ui-injector.js'),
    popup: path.resolve(__dirname, '../extension/popup/popup.js')
  },
  output: {
    path: path.resolve(__dirname, '../dist'),
    filename: '[name].js',
    clean: true
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env'],
            plugins: ['@babel/plugin-proposal-class-properties']
          }
        }
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      }
    ]
  },
  plugins: [
    new CopyPlugin({
      patterns: [
        { from: path.resolve(__dirname, '../extension/manifest.json'), to: 'manifest.json' },
        { from: path.resolve(__dirname, '../extension/popup/popup.html'), to: 'popup.html' },
        { from: path.resolve(__dirname, '../extension/content/*.css'), to: '[name][ext]', noErrorOnMissing: true },
        { from: path.resolve(__dirname, '../src/ui/icons'), to: 'icons', noErrorOnMissing: true }
      ],
    }),
  ],
  resolve: {
    extensions: ['.js', '.jsx'],
    alias: {
      '@': path.resolve(__dirname, '../src'),
      '@extension': path.resolve(__dirname, '../extension'),
      '@core': path.resolve(__dirname, '../src/core'),
      '@ui': path.resolve(__dirname, '../src/ui'),
      '@platforms': path.resolve(__dirname, '../src/platforms')
    }
  },
  devServer: {
    static: {
      directory: path.join(__dirname, '../dist'),
    },
    compress: true,
    port: 3000,
    hot: true,
    open: false
  }
};
