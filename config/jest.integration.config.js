module.exports = {
  // Base test config
  testEnvironment: 'jsdom',
  transform: {
    '^.+\\.jsx?$': 'babel-jest',
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  
  // Integration test specific config
  testMatch: ['**/?(*.)+(integration).[jt]s?(x)'],
  testPathIgnorePatterns: ['/node_modules/'],
  setupFilesAfterEnv: ['<rootDir>/tests/integration/setup.js'],
  
  // Other options
  verbose: true,
  collectCoverage: false,
  testTimeout: 10000,
}; 