const { defineConfig, devices } = require('@playwright/test');

/**
 * Playwright Configuration for WebOTR E2E Testing
 *
 * Comprehensive cross-browser testing for extension functionality
 * across Chrome, Firefox, Safari, and Edge
 */

module.exports = defineConfig({
  testDir: '../tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }],
    ['list']
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure'
  },

  projects: [
    // Desktop Browsers - Extension Testing
    {
      name: 'chromium-extension',
      use: {
        ...devices['Desktop Chrome'],
        launchOptions: {
          args: [
            '--disable-extensions-except=../dist',
            '--load-extension=../dist',
            '--disable-web-security',
            '--no-sandbox',
            '--disable-setuid-sandbox'
          ]
        }
      },
    },
    {
      name: 'firefox-extension',
      use: {
        ...devices['Desktop Firefox'],
        launchOptions: {
          firefoxUserPrefs: {
            'extensions.autoDisableScopes': 0,
            'extensions.enabledScopes': 15,
            'xpinstall.signatures.required': false
          }
        }
      },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },

    // Web Application Testing
    {
      name: 'chromium-web',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox-web',
      use: { ...devices['Desktop Firefox'] },
    },

    // Mobile Testing
    {
      name: 'mobile-chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'mobile-safari',
      use: { ...devices['iPhone 12'] },
    },

    // Accessibility Testing
    {
      name: 'accessibility',
      use: {
        ...devices['Desktop Chrome'],
        colorScheme: 'dark',
        reducedMotion: 'reduce'
      },
    }
  ],

  webServer: {
    command: 'npm run serve',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },

  timeout: 30 * 1000,
  expect: {
    timeout: 5 * 1000
  },

  outputDir: '../test-results/',

  // Global setup and teardown
  globalSetup: require.resolve('../tests/e2e/global-setup.js'),
  globalTeardown: require.resolve('../tests/e2e/global-teardown.js')
});