const path = require('path');
const CopyPlugin = require('copy-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  mode: 'production',
  entry: {
    'background/service-worker': path.resolve(__dirname, '../extension/background/service-worker.js'),
    'content/content-script': path.resolve(__dirname, '../extension/content/content-script.js'),
    'content/platform-detector': path.resolve(__dirname, '../extension/content/platform-detector.js'),
    'content/message-interceptor': path.resolve(__dirname, '../extension/content/message-interceptor.js'),
    'content/ui-injector': path.resolve(__dirname, '../extension/content/ui-injector.js'),
    'popup/popup': path.resolve(__dirname, '../extension/popup/popup.js')
  },
  output: {
    path: path.resolve(__dirname, '../dist'),
    filename: '[name].js',
    clean: true
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: false, // Keep console for extension debugging
            drop_debugger: true
          },
          mangle: {
            safari10: true
          },
          output: {
            comments: false,
            ascii_only: true
          }
        },
        extractComments: false
      }),
      new CssMinimizerPlugin()
    ]
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', {
                targets: {
                  chrome: '88' // Minimum Chrome version for Manifest V3
                }
              }]
            ],
            plugins: ['@babel/plugin-proposal-class-properties']
          }
        }
      },
      {
        test: /\.css$/,
        use: [MiniCssExtractPlugin.loader, 'css-loader']
      }
    ]
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: '[name].css'
    }),
    new CopyPlugin({
      patterns: [
        { from: path.resolve(__dirname, '../extension/manifest.json'), to: 'manifest.json' },
        { from: path.resolve(__dirname, '../extension/popup/popup.html'), to: 'popup/popup.html' },
        { from: path.resolve(__dirname, '../extension/content/*.css'), to: 'content/[name][ext]', noErrorOnMissing: true },
        { from: path.resolve(__dirname, '../src/ui/icons'), to: 'icons', noErrorOnMissing: true },
        {
          from: path.resolve(__dirname, '../extension/options'),
          to: 'options',
          noErrorOnMissing: true
        }
      ],
    }),
  ],
  resolve: {
    extensions: ['.js', '.jsx'],
    alias: {
      '@': path.resolve(__dirname, '../src'),
      '@extension': path.resolve(__dirname, '../extension'),
      '@core': path.resolve(__dirname, '../src/core'),
      '@ui': path.resolve(__dirname, '../src/ui'),
      '@platforms': path.resolve(__dirname, '../src/platforms')
    }
  }
};
