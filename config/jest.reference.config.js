/**
 * Jest Configuration for libOTR Reference Implementation Tests
 * 
 * Specialized configuration for testing compatibility with libOTR
 * and protocol compliance validation.
 */

module.exports = {
  displayName: {
    name: 'libOTR Reference Tests',
    color: 'blue'
  },

  // Set root directory to project root
  rootDir: '../',

  // Test file patterns
  testMatch: [
    '<rootDir>/tests/reference/**/*.test.js',
    '<rootDir>/tests/reference/**/*.spec.js'
  ],

  // Test environment
  testEnvironment: 'node',

  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/tests/reference/setup.js'
  ],

  // Module paths
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@tests/(.*)$': '<rootDir>/tests/$1',
    '^@reference/(.*)$': '<rootDir>/tests/reference/$1'
  },
  
  // Coverage configuration
  collectCoverageFrom: [
    'src/core/protocol/**/*.js',
    'src/core/crypto/**/*.js',
    'src/core/session/**/*.js',
    'tests/reference/**/*.js',
    '!tests/reference/**/*.test.js',
    '!tests/reference/**/*.spec.js',
    '!tests/reference/setup.js'
  ],
  
  coverageDirectory: 'coverage/reference',
  
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json'
  ],
  
  // Coverage thresholds for reference implementation compatibility
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    },
    // Stricter requirements for protocol implementation
    'src/core/protocol/': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    },
    // Stricter requirements for crypto implementation
    'src/core/crypto/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },
  
  // Test timeout (longer for compatibility tests)
  testTimeout: 30000,
  
  // Verbose output for debugging
  verbose: true,
  
  // Transform configuration
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  
  // Module file extensions
  moduleFileExtensions: [
    'js',
    'json',
    'node'
  ],
  
  // Test result processor (commented out until we create it)
  // testResultsProcessor: '<rootDir>/tests/reference/results-processor.js',
  
  // Custom reporters (simplified for now)
  reporters: [
    'default'
  ],
  
  // Global setup and teardown (commented out until we create them)
  // globalSetup: '<rootDir>/tests/reference/global-setup.js',
  // globalTeardown: '<rootDir>/tests/reference/global-teardown.js',
  
  // Test environment options
  testEnvironmentOptions: {
    // Node.js specific options
    node: {
      // Enable experimental features if needed
      experimentalModules: false
    }
  },
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Bail configuration (stop on first failure for CI)
  bail: process.env.CI ? 1 : 0,
  
  // Cache configuration
  cache: true,
  cacheDirectory: '<rootDir>/node_modules/.cache/jest/reference',
  
  // Watch mode configuration
  watchman: true,
  watchPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/coverage/',
    '<rootDir>/lib/libotr/'
  ],
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/coverage/',
    '<rootDir>/lib/libotr/'
  ],
  
  // Module path ignore patterns
  modulePathIgnorePatterns: [
    '<rootDir>/coverage/',
    '<rootDir>/lib/libotr/'
  ],
  
  // Force exit after tests complete
  forceExit: false,
  
  // Detect open handles
  detectOpenHandles: true,
  
  // Detect leaked timers
  detectLeaks: false,
  
  // Maximum worker processes
  maxWorkers: process.env.CI ? 2 : '50%',
  
  // Test sequencer (run tests in specific order if needed)
  // testSequencer: '<rootDir>/tests/reference/test-sequencer.js'
};
