module.exports = {
  // Base test config
  testEnvironment: 'jsdom',
  transform: {
    '^.+\\.jsx?$': 'babel-jest',
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  
  // E2E test specific config
  testMatch: ['**/?(*.)+(e2e).[jt]s?(x)'],
  testPathIgnorePatterns: ['/node_modules/'],
  setupFilesAfterEnv: ['<rootDir>/tests/e2e/setup.js'],
  
  // Other options
  verbose: true,
  collectCoverage: false,
  testTimeout: 30000,
}; 