const path = require('path');

module.exports = {
  rootDir: path.resolve(__dirname, '..'),
  testEnvironment: 'node',
  testMatch: ['**/tests/**/*.test.js'],
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.js',
    'extension/**/*.js',
    '!src/ui/**',
    '!src/platforms/**',
    '!extension/__tests__/**',
  ],
  coverageDirectory: 'coverage',
  transform: {
    '^.+\\.js$': 'babel-jest',
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  moduleNameMapper: {
    // Map src/core/crypto modules to our mocks for deterministic testing
    // Note: DH tests need real randomness, so they import directly
    '^src/core/crypto/random': '<rootDir>/tests/mocks/crypto.js',
    '^src/core/crypto/hmac': '<rootDir>/tests/mocks/crypto.js',
    // Add alias mappings for cleaner imports
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@extension/(.*)$': '<rootDir>/extension/$1',
    '^@core/(.*)$': '<rootDir>/src/core/$1',
    '^@ui/(.*)$': '<rootDir>/src/ui/$1',
    '^@platforms/(.*)$': '<rootDir>/src/platforms/$1',
  },
  testTimeout: 30000, // Increased timeout for crypto operations
  fakeTimers: {
    enableGlobally: false // Disabled to prevent issues with async crypto operations
  },
  testEnvironmentOptions: {
    url: 'http://localhost'
  }
};
