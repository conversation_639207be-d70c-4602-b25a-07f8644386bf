module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/tests/**/*.test.js'],
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/extension/**',
    '!src/ui/**',
    '!src/platforms/**',
  ],
  coverageDirectory: 'coverage',

  // ES6 Module Support
  transform: {
    '^.+\\.js$': 'babel-jest',
  },
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$))'
  ],

  // Setup file to configure test environment
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],

  // Mock modules with proper ES6 support
  moduleNameMapper: {
    // Map src/core/crypto modules to our mocks for deterministic testing
    '^src/core/crypto/random$': '<rootDir>/tests/mocks/crypto.js',
    '^src/core/crypto/hmac$': '<rootDir>/tests/mocks/crypto.js',
    // Support ES6 imports from src
    '^src/(.*)$': '<rootDir>/src/$1',
  },

  // Configure test timeouts
  testTimeout: 10000,

  // Configure fake timers
  fakeTimers: {
    enableGlobally: true
  },

  // Configure test environment variables
  testEnvironmentOptions: {
    url: 'http://localhost'
  }
};
