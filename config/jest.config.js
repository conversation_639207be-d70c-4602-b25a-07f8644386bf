const path = require('path');

module.exports = {
  rootDir: path.resolve(__dirname, '..'),
  testEnvironment: 'node',
  testMatch: ['**/tests/**/*.test.js'],
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.js',
    'extension/**/*.js',
    '!src/ui/**',
    '!src/platforms/**',
    '!extension/__tests__/**',
  ],
  coverageDirectory: 'coverage',

  // ES6 Module Support
  transform: {
    '^.+\\.js$': 'babel-jest',
  },
<<<<<<< HEAD:config/jest.config.js
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  moduleNameMapper: {
    // Map src/core/crypto modules to our mocks for deterministic testing
    // Note: DH tests need real randomness, so they import directly
    '^src/core/crypto/random': '<rootDir>/tests/mocks/crypto.js',
    '^src/core/crypto/hmac': '<rootDir>/tests/mocks/crypto.js',
    // Add alias mappings for cleaner imports
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@extension/(.*)$': '<rootDir>/extension/$1',
    '^@core/(.*)$': '<rootDir>/src/core/$1',
    '^@ui/(.*)$': '<rootDir>/src/ui/$1',
    '^@platforms/(.*)$': '<rootDir>/src/platforms/$1',
  },
  testTimeout: 30000, // Increased timeout for crypto operations
=======
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$))'
  ],

  // Setup file to configure test environment
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],

  // Mock modules with proper ES6 support
  moduleNameMapper: {
    // Map src/core/crypto modules to our mocks for deterministic testing
    '^src/core/crypto/random$': '<rootDir>/tests/mocks/crypto.js',
    '^src/core/crypto/hmac$': '<rootDir>/tests/mocks/crypto.js',
    // Support ES6 imports from src
    '^src/(.*)$': '<rootDir>/src/$1',
  },

  // Configure test timeouts
  testTimeout: 10000,

  // Configure fake timers
>>>>>>> origin/feature/browser-extension:jest.config.js
  fakeTimers: {
    enableGlobally: false // Disabled to prevent issues with async crypto operations
  },
<<<<<<< HEAD:config/jest.config.js
=======

  // Configure test environment variables
>>>>>>> origin/feature/browser-extension:jest.config.js
  testEnvironmentOptions: {
    url: 'http://localhost'
  }
};
