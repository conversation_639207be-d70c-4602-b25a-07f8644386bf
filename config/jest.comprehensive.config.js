/**
 * Comprehensive Jest Configuration
 * 
 * Enhanced test configuration for expanded test coverage:
 * - Forward Secrecy system tests
 * - User Experience system tests
 * - Browser Extension system tests
 * - Integration and end-to-end tests
 */

module.exports = {
  // Test environment
  testEnvironment: 'jsdom',
  
  // Test file patterns
  testMatch: [
    '<rootDir>/tests/**/*.test.js',
    '<rootDir>/src/**/__tests__/**/*.test.js',
    '<rootDir>/extension/**/__tests__/**/*.test.js',
    '<rootDir>/test-chat-sim/tests/**/*.test.js'
  ],
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/',
    '/playwright-report/'
  ],
  
  // Module paths
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@extension/(.*)$': '<rootDir>/extension/$1',
    '^@tests/(.*)$': '<rootDir>/tests/$1'
  },
  
  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/tests/setup.js'
  ],
  
  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'clover',
    'json'
  ],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 75,
      lines: 80,
      statements: 80
    },
    './src/core/forward-secrecy/': {
      branches: 85,
      functions: 90,
      lines: 90,
      statements: 90
    },
    './src/ui/user-experience/': {
      branches: 80,
      functions: 85,
      lines: 85,
      statements: 85
    },
    './extension/': {
      branches: 75,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  
  // Files to collect coverage from
  collectCoverageFrom: [
    'src/**/*.js',
    'extension/**/*.js',
    '!src/**/*.test.js',
    '!extension/**/*.test.js',
    '!src/**/index.js',
    '!extension/**/index.js',
    '!**/node_modules/**',
    '!**/dist/**',
    '!**/build/**',
    '!**/coverage/**'
  ],
  
  // Transform configuration
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  
  // Module file extensions
  moduleFileExtensions: [
    'js',
    'json',
    'jsx',
    'ts',
    'tsx'
  ],
  
  // Test timeout
  testTimeout: 30000,
  
  // Verbose output
  verbose: true,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Test suites configuration
  projects: [
    {
      displayName: 'Core OTR Tests',
      testMatch: ['<rootDir>/tests/core/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/tests/setup.js']
    },
    {
      displayName: 'Forward Secrecy Tests',
      testMatch: ['<rootDir>/src/core/forward-secrecy/**/__tests__/**/*.test.js'],
      setupFilesAfterEnv: [
        '<rootDir>/tests/setup.js',
        '<rootDir>/tests/mocks/crypto.js'
      ]
    },
    {
      displayName: 'User Experience Tests',
      testMatch: ['<rootDir>/src/ui/user-experience/**/__tests__/**/*.test.js'],
      setupFilesAfterEnv: [
        '<rootDir>/tests/setup.js',
        '<rootDir>/tests/mocks/dom.js'
      ]
    },
    {
      displayName: 'Browser Extension Tests',
      testMatch: ['<rootDir>/extension/**/__tests__/**/*.test.js'],
      setupFilesAfterEnv: [
        '<rootDir>/tests/setup.js',
        '<rootDir>/tests/mocks/extension.js'
      ]
    },
    {
      displayName: 'Integration Tests',
      testMatch: ['<rootDir>/tests/integration/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/tests/setup.js']
    },
    {
      displayName: 'Platform Tests',
      testMatch: ['<rootDir>/tests/platforms/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/tests/setup.js']
    }
  ],
  
  // Reporters
  reporters: [
    'default'
  ],
  
  // Watch plugins
  watchPlugins: [],
  
  // Snapshot serializers
  snapshotSerializers: [],
  
  // Global setup and teardown
  globalSetup: '<rootDir>/tests/globalSetup.js',
  globalTeardown: '<rootDir>/tests/globalTeardown.js'
};
