# 🔐 WebOTR - Secure Messaging for Everyone

<div align="center">

[![Build Status](https://github.com/forkrul/webOTteR/workflows/CI/badge.svg)](https://github.com/forkrul/webOTteR/actions)
[![Coverage Status](https://codecov.io/gh/forkrul/webOTteR/branch/main/graph/badge.svg)](https://codecov.io/gh/forkrul/webOTteR)
[![Accessibility](https://img.shields.io/badge/accessibility-WCAG%202.1%20AA-green.svg)](https://www.w3.org/WAI/WCAG21/quickref/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

**WebOTR** transforms Off-The-Record (OTR) messaging into an intuitive, accessible, and secure communication tool for everyone. With a revolutionary user experience that makes cryptographic security as simple as sending a text message.

</div>

**A JavaScript implementation of the Off-The-Record (OTR) messaging protocol for web chat platforms**

[![Security](https://img.shields.io/badge/Security-OTRv3%20Compliant-green.svg)](https://otr.cypherpunks.ca/)
[![Encryption](https://img.shields.io/badge/Encryption-End--to--End-blue.svg)](https://en.wikipedia.org/wiki/End-to-end_encryption)
[![Forward Secrecy](https://img.shields.io/badge/Forward%20Secrecy-Perfect-brightgreen.svg)](https://en.wikipedia.org/wiki/Forward_secrecy)
[![Deniability](https://img.shields.io/badge/Deniability-Cryptographic-orange.svg)](https://en.wikipedia.org/wiki/Deniable_authentication)

*"The right to whisper in a digital world"*

</div>

---

## 🌟 Overview

**WebOTR** brings the battle-tested security of Off-The-Record messaging to the modern web. Born from the need for privacy in an age of surveillance, WebOTR enables secure, private messaging over non-E2E encrypted communication channels like Microsoft Teams, Discord, Slack, and other web chat platforms.

### 🛡️ The Four Pillars of OTR Security

- **🔒 End-to-end encryption** - Your messages, your eyes only
- **🔑 Authentication** - Know who you're really talking to
- **🕶️ Deniability** - Plausible deniability for sensitive conversations
- **⚡ Perfect forward secrecy** - Past conversations stay buried, even if keys are compromised

<<<<<<< HEAD
## ✨ Features
=======
- Node.js 20+ (recommended)
- npm or yarn
- Web browser (Chrome, Firefox, Edge)
- Docker (optional, for DevContainer development)
>>>>>>> origin/feature/browser-extension

<table>
<tr>
<td width="50%">

<<<<<<< HEAD
### 🔐 **Cryptographic Excellence**
- **🛡️ End-to-end encryption** for web chat platforms
- **🔑 Deniable authentication** - conversations that never happened
- **⚡ Perfect forward secrecy** - protecting your digital past
- **🤝 Socialist Millionaire Protocol** - zero-knowledge authentication
- **🔄 Authenticated Key Exchange (AKE)** - secure session establishment
=======
#### Option 1: DevContainer Development (Recommended)

For a consistent, containerized development environment:

1. Open the project in VS Code
2. Install the "Dev Containers" extension
3. Click "Reopen in Container" when prompted
4. The DevContainer will automatically set up Node.js 20, dependencies, and testing tools

#### Option 2: Local Development

To set up the development environment locally:
>>>>>>> origin/feature/browser-extension

</td>
<td width="50%">

### 🌐 **Platform Mastery**
- **💼 Microsoft Teams** - enterprise-grade privacy
- **🎮 Discord** - secure gaming communications
- **💬 Slack** - workplace conversations, truly private
- **📱 WhatsApp Web** - media encryption and secure file sharing
- **✈️ Telegram Web** - enhanced bot integration and command encryption
- **🌐 Element Matrix** - federation security and cross-server encryption
- **🔧 Generic adapter** - works everywhere else too
- **🧩 Browser extension** - production-ready with advanced features

</td>
</tr>
<tr>
<td width="50%">

### ⚡ **Technical Prowess**
- **🚀 Web Crypto API** - hardware-accelerated when available
- **🔄 Pure JS fallbacks** - works in any environment
- **📡 Protocol negotiation** - OTRv2 and OTRv3 support
- **🏷️ Instance tags** - multi-client harmony

</td>
<td width="50%">

### 🎯 **User Experience**
- **🖱️ One-click activation** - privacy shouldn't be hard
- **🔔 Rich notification system** - interactive alerts with customizable themes
- **❓ Question-based auth** - verify with shared secrets
- **🎨 Customizable themes** - 5 built-in themes plus custom creation
- **⚙️ Advanced settings** - comprehensive configuration options
- **🌐 Cross-platform integration** - feels native to each platform

</td>
</tr>
</table>

---

## 🏛️ An Ode to libOTR: Standing on the Shoulders of Giants

<div align="center">

*In the beginning, there was surveillance...*
*And the cypherpunks said: "Let there be privacy."*
*And there was **libOTR**.*

</div>

**WebOTR** is a love letter to the legendary **libOTR** - the C library that changed the world of digital privacy forever. Like a digital Prometheus, libOTR brought the fire of cryptographic privacy to the masses, transforming the theoretical into the practical, the academic into the accessible.

### 🎭 The Legacy Lives On

Born in the hallowed halls of academia by **Nikita Borisov**, **Ian Goldberg**, and **Eric Brewer**, the OTR protocol wasn't just code - it was a manifesto. A declaration that privacy isn't a privilege, but a fundamental right. libOTR took this vision and made it real, powering secure conversations across the globe for over two decades.

### 🌉 Bridging Worlds

Where libOTR conquered the desktop with C's raw power, **WebOTR** carries the torch into the web's infinite expanse. We stand in reverence of the original implementation, studying its every line, learning from its battle-tested wisdom, and adapting its genius for the JavaScript age.

<div align="center">

```
📚 lib/libotr/ - The Sacred Texts
```
*Our reference implementation submodule - because some wisdom is timeless*

</div>

### 🔥 The Fire Continues

Every function in WebOTR echoes the careful craftsmanship of libOTR. Every security decision honors the paranoid perfectionism of its creators. We don't just implement OTR - we **celebrate** it, **extend** it, and **evolve** it for a new generation of privacy-conscious users.

**libOTR taught us that privacy isn't just about hiding - it's about freedom.**
**WebOTR ensures that freedom reaches every corner of the web.**

---

## 🚀 Getting Started

### 📦 Installation

<div align="center">

**Choose Your Path to Privacy**

</div>

#### 🌐 Browser Extension (Recommended)
```bash
<<<<<<< HEAD
# Ready for browser stores! 🎉
✅ Chrome Web Store - Package Ready
✅ Firefox Add-ons - Package Ready
✅ Safari Extensions - Package Ready
✅ Edge Add-ons - Package Ready
```

**🚀 Production Ready Features:**
- **Advanced Platform Integration** - WhatsApp media encryption, Telegram bot integration, Element Matrix federation
- **Enhanced UI Components** - Rich notifications, customizable themes, advanced settings
- **Cross-Browser Compatibility** - Seamless operation across all major browsers
- **Store Submission Ready** - All packages prepared and compliance verified

#### 🛠️ Developer Installation
```bash
git clone https://github.com/forkrul/webOTteR.git
cd webOTteR
npm install
npm start
```

### 🎯 Usage

<div align="center">
=======
# Install dependencies
npm install

# Install test-chat-sim dependencies
cd test-chat-sim && npm install && cd ..

# Start development server
npm start
```

### Development Environment Features

- **Comprehensive Test Suite**: 155+ tests with 100% passing core functionality
- **DevContainer Support**: Consistent development environment with Docker
- **Hot Reloading**: Webpack development server with file watching
- **Browser Testing**: Playwright integration for end-to-end testing
- **Code Quality**: ESLint, Jest, and comprehensive testing infrastructure
>>>>>>> origin/feature/browser-extension

**Privacy in 4 Simple Steps**

<<<<<<< HEAD
</div>
=======
- `npm start` - Sets up the environment and runs the development server
- `npm run dev` - Runs webpack in development mode with file watching
- `npm run build` - Creates a production build
- `npm test` - Runs unit tests (155+ comprehensive tests)
- `npm run test:integration` - Runs integration tests
- `npm run test:e2e` - Runs end-to-end tests with Playwright
- `npm run lint` - Runs ESLint on source files
- `./scripts/test-devcontainer.sh` - Validates DevContainer environment

### Testing

WebOTR now includes a comprehensive test suite with:

- **Core Session Tests** (22 tests) - Complete OTR session management
- **Message Utilities Tests** (17 tests) - Message parsing and creation
- **Protocol Tests** (15+ tests) - AKE and SMP protocol functions
- **Platform Adapter Tests** (25+ tests) - Teams, Discord, Slack integration
- **Integration Tests** (10+ tests) - End-to-end protocol flows
- **DevContainer Tests** - Environment validation and setup verification

All core functionality tests achieve 100% pass rate with comprehensive API coverage.
>>>>>>> origin/feature/browser-extension

1. **🚀 Launch** - Start a conversation on any supported platform
2. **🔐 Activate** - Click the WebOTR icon to initiate secure messaging
3. **🤝 Verify** - Use the Socialist Millionaire Protocol to verify your partner
4. **💬 Communicate** - Exchange messages with complete privacy and deniability

<<<<<<< HEAD
<div align="center">

*Your conversations, your rules, your privacy.*

</div>

---

## 🛠️ Development

<div align="center">

**Join the Privacy Revolution**

*Building the future of secure communications, one commit at a time*

</div>

### 🔧 Prerequisites

<table>
<tr>
<td width="33%">

**🟢 Node.js 16+**
*The JavaScript runtime that powers our privacy engine*

</td>
<td width="33%">

**📦 npm/yarn**
*Package management for the modern web*

</td>
<td width="33%">

**🌐 Modern Browser**
*Chrome, Firefox, Edge - we support them all*

</td>
</tr>
</table>

### 🚀 Quick Start

```bash
# Clone the repository of freedom
git clone https://github.com/forkrul/webOTteR.git
cd webOTteR

# Install dependencies
npm install

# Launch the privacy engine
npm start
```

**What happens when you run `npm start`:**
1. 🔧 **Environment Setup** - Creates necessary UI component stubs
2. 📦 **Webpack Magic** - Starts the development server in watch mode
3. 🔄 **Live Reload** - Your changes appear instantly
4. 🛡️ **Privacy Ready** - Start building secure communications

### ⚡ Available Scripts

<div align="center">

| Command | Purpose | Description |
|---------|---------|-------------|
| `npm start` | 🚀 **Development** | Full environment setup + dev server |
| `npm run dev` | 🔄 **Watch Mode** | Webpack development with live reload |
| `npm run build` | 📦 **Production** | Optimized build for deployment |
| `npm test` | 🧪 **Unit Tests** | Comprehensive test suite |
| `npm run test:integration` | 🔗 **Integration** | Cross-component testing |
| `npm run test:e2e` | 🎯 **End-to-End** | Full workflow validation |
| `npm run lint` | ✨ **Code Quality** | ESLint for clean code |

</div>

---

## 🏗️ Project Architecture

<div align="center">

**A Cathedral of Code, Built for Privacy**

</div>

```
webOTteR/
├── 🔐 src/core/                    # The Heart of Privacy
│   ├── 🧮 crypto/                  # Cryptographic Primitives
│   │   ├── dh.js                   # Diffie-Hellman Key Exchange
│   │   ├── aes.js                  # AES Encryption/Decryption
│   │   ├── hmac.js                 # Message Authentication
│   │   ├── dsa.js                  # Digital Signature Algorithm
│   │   └── smp.js                  # Socialist Millionaire Magic
│   ├── 📡 protocol/                # OTR Protocol Engine
│   │   ├── ⚡ ake.js               # Authenticated Key Exchange
│   │   ├── 🤝 smp.js               # Socialist Millionaire Protocol
│   │   ├── 🔄 state.js             # Protocol State Machine
│   │   ├── 📨 message.js           # Message Formatting & Parsing
│   │   └── 📋 index.js             # Protocol Exports
│   └── 🎭 session/                 # Session Management
│       └── index.js                # OTR Session Orchestration
├── 🌐 src/platforms/               # Platform Adapters
│   ├── 💼 teams/                   # Microsoft Teams Integration
│   ├── 🎮 discord/                 # Discord Integration
│   ├── 💬 slack/                   # Slack Integration
│   └── 🔧 generic/                 # Universal Platform Support
├── 🎨 src/ui/                      # User Interface Components
├── 🧩 src/extension/               # Browser Extension Magic
└── 📚 lib/libotr/                  # The Sacred Reference (C Library)
```

<div align="center">

*Each directory tells a story of privacy, security, and digital freedom*

</div>
=======
- `src/core/` - Core OTR protocol implementation
  - `crypto/` - Cryptographic primitives (DH, AES, HMAC, DSA, etc.)
  - `protocol/` - OTR protocol state machine and message handling
    - `ake.js` - Authenticated Key Exchange implementation
    - `smp.js` - Socialist Millionaire Protocol implementation
    - `state.js` - OTR state machine
    - `message.js` - Message parsing and formatting
    - `index.js` - Protocol exports
  - `session/` - OTR session management
- `src/platforms/` - Platform-specific adapters
  - `teams/` - Microsoft Teams adapter
  - `discord/` - Discord adapter
  - `slack/` - Slack adapter
  - `generic/` - Generic adapter for other platforms
- `src/ui/` - User interface components
- `src/extension/` - Browser extension code
- `tests/` - Comprehensive test suite (155+ tests)
  - `core/` - Core functionality tests (session, protocol, crypto, utils)
  - `platforms/` - Platform adapter tests
  - `integration/` - End-to-end integration tests
  - `framework/` - Framework integration tests
- `test-chat-sim/` - Test chat simulator for development and testing
- `.devcontainer/` - DevContainer configuration for consistent development
- `scripts/` - Development and deployment scripts
- `docs/` - Sphinx documentation and GitHub Pages
- `lib/libotr/` - Reference implementation of the OTR protocol (C library submodule)
>>>>>>> origin/feature/browser-extension

## Security

WebOTR is designed with security as a top priority. However, it is important to understand the limitations and threats to your security when using any encrypted messaging system.

- WebOTR only protects the content of your messages, not metadata
- Both parties must be using WebOTR for the conversation to be secure
- WebOTR cannot protect against compromised devices or browsers

## Technical Details

### Authenticated Key Exchange (AKE)

The AKE protocol in WebOTR follows the OTRv3 specification and consists of the following steps:

1. **DH Commit Message**: The initiator generates a DH key pair, encrypts their public key, and sends it along with a hash of the encrypted key.
2. **DH Key Message**: The responder generates their own DH key pair and sends their public key.
3. **Reveal Signature Message**: The initiator reveals the encryption key, allowing the responder to verify the hash, and sends a signature of the DH exchange.
4. **Signature Message**: The responder verifies the signature and sends their own signature.

This process establishes a secure, authenticated channel for communication with perfect forward secrecy.

### Socialist Millionaire Protocol (SMP)

The SMP implementation allows users to verify each other's identities by confirming they both know the same secret, without revealing the secret itself. Key features include:

1. **Secure Authentication**: Users can verify each other's identities based on shared knowledge
2. **Zero-Knowledge Proof**: The verification process never reveals the shared secret
3. **Custom Questions**: Users can include custom questions to prompt for the shared secret
4. **User Notifications**: Clear feedback on verification success or failure
5. **Integration with OTR**: Seamless operation within existing OTR sessions

To use SMP:
1. First establish an encrypted OTR session
2. Initiate SMP verification with `otrSession.initiateSMP("shared-secret", "Optional question?")`
3. The other party responds with `otrSession.respondToSMP("shared-secret")`
4. Both parties are notified of the verification result

#### SMP In-Depth

The Socialist Millionaire Protocol provides an essential layer of security by allowing two users to verify they share the same knowledge (such as a passphrase or answer to a question) without revealing that information, even to each other. This is based on a variant of the socialist millionaires' problem: two millionaires want to determine if they have the same amount of money without revealing their actual wealth.

**Protocol Flow:**

1. **Initiation (SMP1)**: The initiator generates random exponents, computes generators based on these values, creates zero-knowledge proofs to verify their validity, and sends this data to the responder. They can optionally include a question prompt.

2. **Response (SMP2)**: The responder generates their own random values, computes corresponding generators, creates verification parameters, and sends these with zero-knowledge proofs back to the initiator.

3. **Verification Part 1 (SMP3)**: The initiator verifies the responder's proofs, computes verification values based on their secret, and sends these values with additional proofs to the responder.

4. **Verification Part 2 (SMP4)**: The responder performs final verification calculations, determines if the secrets match, and sends confirmation data to the initiator.

5. **Final Verification**: The initiator performs their own final verification to independently confirm if the secrets match.

**Security Importance:**

SMP is crucial for OTR security because it addresses the man-in-the-middle vulnerability that can exist even with encrypted communications. By verifying shared knowledge out-of-band (like a previously agreed upon phrase or answer to a personal question), users can confirm they're communicating with the intended party and not an impostor.

The protocol provides:

- **Cryptographic Verification**: Mathematical proof of shared knowledge without revealing the knowledge itself
- **Man-in-the-Middle Detection**: Ability to detect if someone is intercepting and relaying messages
- **Trust Establishment**: A mechanism to establish trusted communication channels
- **Forward Secrecy Protection**: Verification that doesn't compromise the security of past or future communications

The WebOTR implementation of SMP uses the secure MODP Group from RFC 3526 with a 1536-bit prime modulus and follows the cryptographic principles defined in the OTRv3 specification, ensuring compatibility with other OTR implementations while maintaining the highest security standards.

<<<<<<< HEAD
## 📚 Documentation
=======
## Contributing
>>>>>>> origin/feature/browser-extension

<div align="center">

**Comprehensive Guides for Privacy Pioneers**

</div>

### 📖 Available Documentation

- **[API Documentation](docs/API.md)** - Complete API reference with examples
- **[Security Policy](docs/SECURITY.md)** - Security guidelines and vulnerability reporting
- **[Architecture Guide](docs/ARCHITECTURE.md)** - Detailed system architecture overview
- **[Browser Extension Guide](docs/BROWSER_EXTENSION_PRD_STATUS.md)** - Complete browser extension implementation
- **[Store Submission Guide](docs/STORE_SUBMISSION_GUIDE.md)** - Browser store deployment instructions
- **[Contributing Guide](CONTRIBUTING.md)** - How to contribute to WebOTR
- **[Branch Merge PRD](docs/BRANCH_MERGE_PRD.md)** - Development process documentation

### 🔧 API Quick Reference

```javascript
import { OtrSession, SMPHandler, SMP_RESULT } from 'webotr';

// Create and start OTR session
const session = new OtrSession({ versions: [3, 4] });
await session.startOtr();

// Encrypt/decrypt messages
const encrypted = await session.encryptMessage('Hello, secure world!');
const decrypted = await session.processIncoming(encryptedMessage);

// SMP Authentication
const smp = new SMPHandler();
smp.onSMPResult((result) => {
  if (result.result === SMP_RESULT.SUCCESS) {
    console.log('✅ Authentication successful!');
  }
});

const smp1 = await smp.initiateSMP('shared-secret', 'What is our secret?');
```

### 🛡️ Security Features

- **HKDF Key Derivation** - Modern key derivation using HMAC-based KDF
- **Secure Memory Management** - Automatic clearing of sensitive data
- **Constant-Time Operations** - Protection against timing attacks
- **Comprehensive Input Validation** - Defense against malformed inputs
- **Zero-Knowledge Proofs** - SMP authentication without revealing secrets

---

## 🤝 Contributing

<div align="center">

**Join the Privacy Revolution**

*Every commit is a step toward a more private world*

</div>

We welcome contributions from privacy advocates, cryptography enthusiasts, and developers who believe in digital freedom. Whether you're fixing bugs, adding features, or improving documentation, you're helping build a more secure internet.

**Ways to Contribute:**
- 🐛 **Bug Reports** - Help us squash privacy vulnerabilities
- ✨ **Feature Requests** - Suggest new ways to enhance privacy
- 📝 **Documentation** - Make privacy accessible to everyone
- 🔧 **Code Contributions** - Build the future of secure messaging
- 🧪 **Testing** - Ensure our security is bulletproof

See our [Contributing Guidelines](CONTRIBUTING.md) for detailed information.

---

## 📜 License

<div align="center">

**MIT License - Freedom to Innovate**

*This project is licensed under the [MIT License](LICENSE)*

*Because privacy tools should be free for everyone*

</div>

---

## 🙏 Acknowledgments

<div align="center">

**Standing on the Shoulders of Giants**

</div>

### 🏛️ The Founding Fathers of OTR
- **Nikita Borisov** - Co-creator of the OTR protocol
- **Ian Goldberg** - Cryptographic genius and OTR co-creator
- **Eric Brewer** - Co-creator and privacy pioneer

### 🔥 The libOTR Legacy
- **The Off-the-Record Messaging Project** - For the reference implementation that changed everything
- **The Cypherpunk Community** - For never giving up on digital privacy
- **Every OTR User** - For choosing privacy over convenience

### 💝 Special Thanks
- **The Web Crypto API Team** - For bringing cryptography to the browser
- **The Open Source Community** - For proving that privacy can be collaborative
- **You** - For caring enough about privacy to read this far

<div align="center">

*"Privacy is not something that I'm merely entitled to, it's an absolute prerequisite."*
**- Marlon Brando**

**WebOTR: Where Privacy Meets the Web** 🔐✨

</div>