# WebOTR

A JavaScript implementation of the Off-The-Record (OTR) messaging protocol for web chat platforms.

## Overview

WebOTR enables secure, private messaging over non-E2E encrypted communication channels like Microsoft Teams, Discord, Slack, and other web chat platforms. It provides the security properties of OTR messaging:

- End-to-end encryption
- Authentication
- Deniability
- Perfect forward secrecy

## Features

- **End-to-end encryption** for web chat platforms
- **Deniable authentication** to prevent message authentication outside the conversation
- **Perfect forward secrecy** to protect past communications if keys are compromised
- **Socialist Millionaire Protocol** for secure authentication
- **Authenticated Key Exchange (AKE)** for secure session establishment
- **Cross-platform compatibility** with Microsoft Teams, Discord, Slack, and more
- **Browser extension** for easy installation and use
- **Web Crypto API** for hardware-accelerated cryptography with pure JS fallbacks
- **Protocol version negotiation** supporting OTRv2 and OTRv3
- **Instance tags** for multi-client support

## Getting Started

### Installation

1. Download the browser extension from the Chrome Web Store or Firefox Add-ons
2. Install the extension in your browser
3. Navigate to a supported web chat platform (Teams, Discord, Slack)
4. Click the WebOTR icon to enable OTR messaging

### Usage

1. Start a conversation with another WebOTR user
2. Click the OTR button to initiate an OTR session
3. Verify your conversation partner using the Socialist Millionaire Protocol
4. Exchange messages securely

## Development

### Prerequisites

- Node.js 20+ (recommended)
- npm or yarn
- Web browser (Chrome, Firefox, Edge)
- Docker (optional, for DevContainer development)

### Getting Started

#### Option 1: DevContainer Development (Recommended)

For a consistent, containerized development environment:

1. Open the project in VS Code
2. Install the "Dev Containers" extension
3. Click "Reopen in Container" when prompted
4. The DevContainer will automatically set up Node.js 20, dependencies, and testing tools

#### Option 2: Local Development

To set up the development environment locally:

```bash
# Install dependencies
npm install

# Install test-chat-sim dependencies
cd test-chat-sim && npm install && cd ..

# Start development server
npm start
```

### Development Environment Features

- **Comprehensive Test Suite**: 155+ tests with 100% passing core functionality
- **DevContainer Support**: Consistent development environment with Docker
- **Hot Reloading**: Webpack development server with file watching
- **Browser Testing**: Playwright integration for end-to-end testing
- **Code Quality**: ESLint, Jest, and comprehensive testing infrastructure

### Available Scripts

- `npm start` - Sets up the environment and runs the development server
- `npm run dev` - Runs webpack in development mode with file watching
- `npm run build` - Creates a production build
- `npm test` - Runs unit tests (155+ comprehensive tests)
- `npm run test:integration` - Runs integration tests
- `npm run test:e2e` - Runs end-to-end tests with Playwright
- `npm run lint` - Runs ESLint on source files
- `./scripts/test-devcontainer.sh` - Validates DevContainer environment

### Testing

WebOTR now includes a comprehensive test suite with:

- **Core Session Tests** (22 tests) - Complete OTR session management
- **Message Utilities Tests** (17 tests) - Message parsing and creation
- **Protocol Tests** (15+ tests) - AKE and SMP protocol functions
- **Platform Adapter Tests** (25+ tests) - Teams, Discord, Slack integration
- **Integration Tests** (10+ tests) - End-to-end protocol flows
- **DevContainer Tests** - Environment validation and setup verification

All core functionality tests achieve 100% pass rate with comprehensive API coverage.

## Project Structure

- `src/core/` - Core OTR protocol implementation
  - `crypto/` - Cryptographic primitives (DH, AES, HMAC, DSA, etc.)
  - `protocol/` - OTR protocol state machine and message handling
    - `ake.js` - Authenticated Key Exchange implementation
    - `smp.js` - Socialist Millionaire Protocol implementation
    - `state.js` - OTR state machine
    - `message.js` - Message parsing and formatting
    - `index.js` - Protocol exports
  - `session/` - OTR session management
- `src/platforms/` - Platform-specific adapters
  - `teams/` - Microsoft Teams adapter
  - `discord/` - Discord adapter
  - `slack/` - Slack adapter
  - `generic/` - Generic adapter for other platforms
- `src/ui/` - User interface components
- `src/extension/` - Browser extension code
- `tests/` - Comprehensive test suite (155+ tests)
  - `core/` - Core functionality tests (session, protocol, crypto, utils)
  - `platforms/` - Platform adapter tests
  - `integration/` - End-to-end integration tests
  - `framework/` - Framework integration tests
- `test-chat-sim/` - Test chat simulator for development and testing
- `.devcontainer/` - DevContainer configuration for consistent development
- `scripts/` - Development and deployment scripts
- `docs/` - Sphinx documentation and GitHub Pages
- `lib/libotr/` - Reference implementation of the OTR protocol (C library submodule)

## Security

WebOTR is designed with security as a top priority. However, it is important to understand the limitations and threats to your security when using any encrypted messaging system.

- WebOTR only protects the content of your messages, not metadata
- Both parties must be using WebOTR for the conversation to be secure
- WebOTR cannot protect against compromised devices or browsers

## Technical Details

### Authenticated Key Exchange (AKE)

The AKE protocol in WebOTR follows the OTRv3 specification and consists of the following steps:

1. **DH Commit Message**: The initiator generates a DH key pair, encrypts their public key, and sends it along with a hash of the encrypted key.
2. **DH Key Message**: The responder generates their own DH key pair and sends their public key.
3. **Reveal Signature Message**: The initiator reveals the encryption key, allowing the responder to verify the hash, and sends a signature of the DH exchange.
4. **Signature Message**: The responder verifies the signature and sends their own signature.

This process establishes a secure, authenticated channel for communication with perfect forward secrecy.

### Socialist Millionaire Protocol (SMP)

The SMP implementation allows users to verify each other's identities by confirming they both know the same secret, without revealing the secret itself. Key features include:

1. **Secure Authentication**: Users can verify each other's identities based on shared knowledge
2. **Zero-Knowledge Proof**: The verification process never reveals the shared secret
3. **Custom Questions**: Users can include custom questions to prompt for the shared secret
4. **User Notifications**: Clear feedback on verification success or failure
5. **Integration with OTR**: Seamless operation within existing OTR sessions

To use SMP:
1. First establish an encrypted OTR session
2. Initiate SMP verification with `otrSession.initiateSMP("shared-secret", "Optional question?")`
3. The other party responds with `otrSession.respondToSMP("shared-secret")`
4. Both parties are notified of the verification result

#### SMP In-Depth

The Socialist Millionaire Protocol provides an essential layer of security by allowing two users to verify they share the same knowledge (such as a passphrase or answer to a question) without revealing that information, even to each other. This is based on a variant of the socialist millionaires' problem: two millionaires want to determine if they have the same amount of money without revealing their actual wealth.

**Protocol Flow:**

1. **Initiation (SMP1)**: The initiator generates random exponents, computes generators based on these values, creates zero-knowledge proofs to verify their validity, and sends this data to the responder. They can optionally include a question prompt.

2. **Response (SMP2)**: The responder generates their own random values, computes corresponding generators, creates verification parameters, and sends these with zero-knowledge proofs back to the initiator.

3. **Verification Part 1 (SMP3)**: The initiator verifies the responder's proofs, computes verification values based on their secret, and sends these values with additional proofs to the responder.

4. **Verification Part 2 (SMP4)**: The responder performs final verification calculations, determines if the secrets match, and sends confirmation data to the initiator.

5. **Final Verification**: The initiator performs their own final verification to independently confirm if the secrets match.

**Security Importance:**

SMP is crucial for OTR security because it addresses the man-in-the-middle vulnerability that can exist even with encrypted communications. By verifying shared knowledge out-of-band (like a previously agreed upon phrase or answer to a personal question), users can confirm they're communicating with the intended party and not an impostor.

The protocol provides:

- **Cryptographic Verification**: Mathematical proof of shared knowledge without revealing the knowledge itself
- **Man-in-the-Middle Detection**: Ability to detect if someone is intercepting and relaying messages
- **Trust Establishment**: A mechanism to establish trusted communication channels
- **Forward Secrecy Protection**: Verification that doesn't compromise the security of past or future communications

The WebOTR implementation of SMP uses the secure MODP Group from RFC 3526 with a 1536-bit prime modulus and follows the cryptographic principles defined in the OTRv3 specification, ensuring compatibility with other OTR implementations while maintaining the highest security standards.

## Contributing

Contributions are welcome! Please see our [Contributing Guidelines](CONTRIBUTING.md) for more information.

## License

This project is licensed under the [MIT License](LICENSE).

## Acknowledgments

- The original OTR protocol by Nikita Borisov, Ian Goldberg, and Eric Brewer
- The libotr implementation by the Off-the-Record Messaging project 