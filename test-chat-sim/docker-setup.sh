#!/bin/bash

# WebOTR Test App Docker Development Setup Script
# Sets up Docker environment with Traefik integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Detect Docker Compose command
detect_docker_compose() {
    if command_exists docker-compose; then
        DOCKER_COMPOSE="docker-compose"
    elif docker compose version >/dev/null 2>&1; then
        DOCKER_COMPOSE="docker compose"
    else
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."

    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    # Detect Docker Compose
    detect_docker_compose
    print_status "Using Docker Compose command: $DOCKER_COMPOSE"

    # Check if Docker daemon is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi

    print_success "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p .docker-cache
    mkdir -p logs
    
    print_success "Directories created"
}

# Create or check Traefik network
setup_traefik_network() {
    print_status "Setting up Traefik network..."
    
    if ! docker network ls | grep -q "traefik_network"; then
        print_status "Creating traefik_network..."
        docker network create traefik_network
        print_success "Traefik network created"
    else
        print_success "Traefik network already exists"
    fi
}

# Create environment file
create_env_file() {
    print_status "Creating environment configuration..."
    
    if [ ! -f .env.docker ]; then
        cat > .env.docker << EOF
# WebOTR Test App Docker Environment Configuration

# Node.js Environment
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# React Development Settings
CHOKIDAR_USEPOLLING=true
WATCHPACK_POLLING=true
FAST_REFRESH=true
BROWSER=none
GENERATE_SOURCEMAP=true

# WebOTR Specific Settings
REACT_APP_ENV=development
REACT_APP_API_URL=http://webotter.docker.localhost

# Docker Settings
COMPOSE_PROJECT_NAME=webotter
COMPOSE_FILE=docker-compose.dev.yml

# Traefik Settings
TRAEFIK_DOMAIN=docker.localhost
WEBOTTER_DOMAIN=webotter.docker.localhost
EOF
        print_success "Environment file created: .env.docker"
    else
        print_success "Environment file already exists: .env.docker"
    fi
}

# Build and start services
start_services() {
    print_status "Building and starting WebOTR Test App..."
    
    # Load environment variables
    if [ -f .env.docker ]; then
        export $(cat .env.docker | grep -v '^#' | xargs)
    fi
    
    # Build and start services
    $DOCKER_COMPOSE -f docker-compose.dev.yml up --build -d
    
    print_success "Services started successfully"
}

# Wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for Traefik
    print_status "Waiting for Traefik to be ready..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -s http://localhost:8080/ping >/dev/null 2>&1; then
            print_success "Traefik is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_warning "Traefik health check timeout, but continuing..."
    fi
    
    # Wait for WebOTR Test App
    print_status "Waiting for WebOTR Test App to be ready..."
    timeout=120  # Give more time for npm install
    while [ $timeout -gt 0 ]; do
        if curl -s http://webotter.docker.localhost >/dev/null 2>&1; then
            print_success "WebOTR Test App is ready"
            break
        fi
        sleep 5
        timeout=$((timeout - 5))
    done
    
    if [ $timeout -le 0 ]; then
        print_warning "WebOTR Test App health check timeout, but it might still be starting..."
    fi
}

# Display access information
show_access_info() {
    print_success "🎉 WebOTR Test App Docker Environment Setup Complete!"
    echo
    echo "📋 Access Information:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "🌐 WebOTR Test App:     http://webotter.docker.localhost"
    echo "🔧 Traefik Dashboard:   http://traefik.docker.localhost"
    echo "🐳 Direct Access:       http://localhost:3000"
    echo "📊 Traefik API:         http://localhost:8080"
    echo
    echo "📁 Volume Mounts:"
    echo "   • Source Code:        $(pwd) → /app"
    echo "   • Node Modules:       Anonymous volume (preserved)"
    echo "   • Cache:              .docker-cache → /app/.cache"
    echo
    echo "🔧 Development Features:"
    echo "   • ✅ Hot Reload Enabled"
    echo "   • ✅ Source Maps Enabled"
    echo "   • ✅ Fast Refresh Enabled"
    echo "   • ✅ File Watching (Polling)"
    echo
    echo "📝 Useful Commands:"
    echo "   • View logs:          docker-compose -f docker-compose.dev.yml logs -f"
    echo "   • Restart app:        docker-compose -f docker-compose.dev.yml restart webotter-testapp"
    echo "   • Stop services:      docker-compose -f docker-compose.dev.yml down"
    echo "   • Rebuild:            docker-compose -f docker-compose.dev.yml up --build -d"
    echo "   • Shell access:       docker exec -it webotter-testapp-dev sh"
    echo
    echo "🧪 Testing:"
    echo "   • Unit Tests:         docker exec -it webotter-testapp-dev npm test"
    echo "   • E2E Tests:          docker exec -it webotter-testapp-dev npx playwright test"
    echo
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# Show logs
show_logs() {
    print_status "Showing service logs (Ctrl+C to exit)..."
    $DOCKER_COMPOSE -f docker-compose.dev.yml logs -f
}

# Main execution
main() {
    echo "🐳 WebOTR Test App Docker Development Setup"
    echo "==========================================="
    echo
    
    case "${1:-setup}" in
        "setup")
            check_prerequisites
            create_directories
            setup_traefik_network
            create_env_file
            start_services
            wait_for_services
            show_access_info
            ;;
        "start")
            detect_docker_compose
            print_status "Starting existing services..."
            $DOCKER_COMPOSE -f docker-compose.dev.yml up -d
            wait_for_services
            show_access_info
            ;;
        "stop")
            detect_docker_compose
            print_status "Stopping services..."
            $DOCKER_COMPOSE -f docker-compose.dev.yml down
            print_success "Services stopped"
            ;;
        "restart")
            detect_docker_compose
            print_status "Restarting services..."
            $DOCKER_COMPOSE -f docker-compose.dev.yml restart
            wait_for_services
            show_access_info
            ;;
        "rebuild")
            detect_docker_compose
            print_status "Rebuilding and restarting services..."
            $DOCKER_COMPOSE -f docker-compose.dev.yml down
            $DOCKER_COMPOSE -f docker-compose.dev.yml up --build -d
            wait_for_services
            show_access_info
            ;;
        "logs")
            detect_docker_compose
            show_logs
            ;;
        "status")
            detect_docker_compose
            print_status "Service status:"
            $DOCKER_COMPOSE -f docker-compose.dev.yml ps
            ;;
        "clean")
            detect_docker_compose
            print_status "Cleaning up Docker resources..."
            $DOCKER_COMPOSE -f docker-compose.dev.yml down -v
            docker system prune -f
            print_success "Cleanup complete"
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [command]"
            echo
            echo "Commands:"
            echo "  setup     - Initial setup (default)"
            echo "  start     - Start existing services"
            echo "  stop      - Stop services"
            echo "  restart   - Restart services"
            echo "  rebuild   - Rebuild and restart services"
            echo "  logs      - Show service logs"
            echo "  status    - Show service status"
            echo "  clean     - Clean up Docker resources"
            echo "  help      - Show this help message"
            ;;
        *)
            print_error "Unknown command: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
