# WebOTR Integration Notes and Troubleshooting

This document provides important information about integrating the WebOTR framework with the test chat application and how to handle common issues.

## Important Create React App Limitation

Create React App (CRA) has a built-in restriction that prevents importing files from outside the `src/` directory. This is primarily for security reasons and to ensure proper build optimization. This limitation affects our project in the following ways:

1. The test chat app cannot directly import from the main WebOTR project
2. Test files that need to import the actual WebOTR implementation cannot be run in the standard CRA test environment

## Solution Options

We've implemented several solutions to address this limitation:

### 1. Mock Implementation for UI Testing

For the test chat application, we've created a mock implementation of the OtrSession class:
```javascript
// In test-chat-sim/src/utils/otrMock.js
import { OtrSession } from './utils/otrMock';
```

This mock implementation simulates the behavior of the real WebOTR framework, allowing us to test the UI integration without running into CRA's import restrictions.

### 2. Separate Framework Tests

For validating the actual WebOTR framework functionality, we've created separate test files that are designed to be run outside the CRA environment:

- `OtrProtocol.test.js`
- `OtrCrypto.test.js`
- `OtrNegotiation.test.js`

These tests can be run using our `run-otr-tests.sh` script, which copies them to the main project directory and runs them from there.

### 3. Symlink Setup (Alternative Approach)

As an alternative approach, we've provided a script (`setup-symlink.sh`) that creates symbolic links in the test-chat-sim's node_modules directory, pointing to the main WebOTR framework. 

After running this script, you can import WebOTR components using:
```javascript
import { OtrSession } from 'webotr/src/core/session';
```

## How to Run the Tests

1. **UI Integration Tests**:
   ```
   cd test-chat-sim
   npm test
   ```

2. **WebOTR Framework Tests**:
   ```
   ./run-otr-tests.sh
   ```

3. **To use the symlink approach**:
   ```
   ./setup-symlink.sh
   ```
   Then edit `test-chat-sim/src/App.js` to use:
   ```javascript
   import { OtrSession } from 'webotr/src/core/session';
   ```

## Common Issues and Solutions

### Error: "Module not found: Error: You attempted to import ../../src/core/session"

This error occurs when trying to import files from outside the src/ directory in a CRA application.

**Solution**: Use one of the approaches described above:
1. Use the mock implementation for UI testing
2. Run the real tests in the main project environment
3. Set up symlinks and import from the 'webotr' package

### Error: "Cannot find module 'webotr/src/core/session'"

This occurs if you're trying to use the symlink approach but haven't run the `setup-symlink.sh` script.

**Solution**: Run `./setup-symlink.sh` first, then restart your development server.

### Test files fail to run with Jest

If your test files cannot find the WebOTR modules, ensure you're using the correct approach:

**Solution**: Use the `run-otr-tests.sh` script which properly handles copying the tests to the main project directory and fixing the import paths.

## Future Improvements

For a more integrated development experience, consider these options:

1. **Custom Webpack Configuration**: Eject from CRA and customize the webpack configuration to allow imports from outside the src directory.

2. **Move WebOTR into the test app**: Create a copy of the core WebOTR components inside the test app's src directory (not recommended for long-term development).

3. **WebOTR as npm package**: Package WebOTR as a proper npm module that can be installed as a dependency.

4. **Monorepo setup**: Use a tool like Yarn Workspaces or Lerna to manage a monorepo structure where both the library and test app can reference each other more easily. 