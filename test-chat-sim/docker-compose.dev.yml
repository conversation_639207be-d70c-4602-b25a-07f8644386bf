# WebOTR Test App Development Docker Compose Configuration
# Includes Traefik integration for webotter.docker.localhost

version: '3.8'

services:
  # WebOTR Test App Development Container
  webotter-testapp:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: webotter-testapp-dev
    restart: unless-stopped
    
    # Volume mounting for live development
    volumes:
      - .:/app:cached
      - /app/node_modules  # Anonymous volume for node_modules
      - webotter-dev-cache:/app/.cache
      - webotter-dev-build:/app/build
    
    # Environment variables for development
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true  # For file watching in Docker
      - WATCHPACK_POLLING=true
      - FAST_REFRESH=true
      - PORT=3000
      - HOST=0.0.0.0
      - BROWSER=none  # Disable automatic browser opening
      - GENERATE_SOURCEMAP=true
      - REACT_APP_ENV=development
    
    # Port mapping (for direct access if needed)
    ports:
      - "3000:3000"
    
    # Traefik labels for reverse proxy
    labels:
      - "traefik.enable=true"
      
      # HTTP router
      - "traefik.http.routers.webotter-testapp.rule=Host(`webotter.docker.localhost`)"
      - "traefik.http.routers.webotter-testapp.entrypoints=web"
      - "traefik.http.routers.webotter-testapp.service=webotter-testapp"
      
      # Service configuration
      - "traefik.http.services.webotter-testapp.loadbalancer.server.port=3000"
      
      # Optional: HTTPS router (if Traefik has TLS configured)
      - "traefik.http.routers.webotter-testapp-secure.rule=Host(`webotter.docker.localhost`)"
      - "traefik.http.routers.webotter-testapp-secure.entrypoints=websecure"
      - "traefik.http.routers.webotter-testapp-secure.tls=true"
      - "traefik.http.routers.webotter-testapp-secure.service=webotter-testapp"
      
      # Development middleware
      - "traefik.http.routers.webotter-testapp.middlewares=webotter-dev-headers"
      - "traefik.http.middlewares.webotter-dev-headers.headers.customrequestheaders.X-Forwarded-Proto=http"
      - "traefik.http.middlewares.webotter-dev-headers.headers.accesscontrolalloworigin=*"
      - "traefik.http.middlewares.webotter-dev-headers.headers.accesscontrolallowmethods=GET,POST,PUT,DELETE,OPTIONS"
      - "traefik.http.middlewares.webotter-dev-headers.headers.accesscontrolallowheaders=*"
    
    # Networks
    networks:
      - traefik_network
      - webotter_internal
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 90s  # Give more time for npm install
    
    # Dependencies
    depends_on:
      - traefik
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=webotter-testapp"

  # Traefik reverse proxy (if not already running externally)
  traefik:
    image: traefik:v2.10
    container_name: webotter-traefik
    restart: unless-stopped
    
    # Traefik configuration
    command:
      - "--api.dashboard=true"
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.network=traefik_network"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--log.level=INFO"
      - "--accesslog=true"
      - "--ping=true"
    
    # Port mapping
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    
    # Volume for Docker socket
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-data:/data
    
    # Labels for Traefik dashboard
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik-dashboard.rule=Host(`traefik.docker.localhost`)"
      - "traefik.http.routers.traefik-dashboard.entrypoints=web"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"
    
    # Networks
    networks:
      - traefik_network
    
    # Health check
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    # Logging
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

# Networks
networks:
  # External Traefik network (create with: docker network create traefik_network)
  traefik_network:
    external: true
    name: traefik_network
  
  # Internal network for WebOTR services
  webotter_internal:
    driver: bridge
    internal: false
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  webotter-dev-cache:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/.docker-cache
  
  webotter-dev-build:
    driver: local
  
  traefik-data:
    driver: local
