{"name": "chat-simulator", "version": "1.0.0", "description": "A chat simulator between <PERSON> and <PERSON> with network visualization", "main": "src/index.js", "scripts": {"start": "react-scripts start", "start:9999": "PORT=9999 react-scripts start", "start:coverage": "PORT=3001 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage", "eject": "react-scripts eject", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:codegen": "playwright codegen http://localhost:3001", "test:e2e:report": "playwright show-report", "test:e2e:coverage": "playwright test --reporter=html,dot", "test:a11y": "playwright test e2e/accessibility.spec.js", "test:a11y:report": "playwright test e2e/accessibility.spec.js --reporter=html", "test:fix-all": "npm test -- --watchAll=false && npm run test:a11y"}, "dependencies": {"@giphy/js-fetch-api": "^5.6.0", "@giphy/react-components": "^10.0.1", "emoji-picker-react": "^4.12.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1"}, "devDependencies": {"@axe-core/playwright": "^4.7.3", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/index.js"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}}