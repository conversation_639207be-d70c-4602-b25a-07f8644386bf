# WebOTR Test App Docker Ignore File
# Excludes unnecessary files from Docker build context

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
build
dist

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.docker

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
README.md

# Docker files (except the one being used)
Dockerfile.dev
docker-compose*.yml
.dockerignore

# Cache and temporary files
.cache
.docker-cache
.npm
.eslintcache
*.tgz
*.tar.gz

# Test files and reports
coverage
.nyc_output
test-results
playwright-report
.playwright

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Backup files
*.backup
*.bak
*.tmp

# Documentation
docs
*.md
!README.md

# CI/CD
.github
.gitlab-ci.yml
.travis.yml
.circleci

# Local development
.local
.docker-data
docker-data
