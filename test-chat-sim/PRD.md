# Chat Simulator PRD (Product Requirements Document)

## Project Overview

The Chat Simulator is a React-based web application that simulates a chat between two users (<PERSON> and <PERSON>) in a single browser window. The application will visually demonstrate how messages travel across a network, with all functionality contained within the client side (no actual network communication). Messages will be stored in localStorage to persist between sessions.

## User Requirements

1. **User Interface Layout**:
   - Left panel: Alice's chat interface
   - Right panel: Bob's chat interface
   - Bottom panel: Network simulation panel showing message transmission

2. **Functionality**:
   - Users can send messages from either <PERSON> or <PERSON>'s panel
   - Messages appear in the sender's panel immediately
   - Messages appear in the recipient's panel after a simulated network delay
   - Message transmission is visually represented in the bottom panel
   - Messages are persisted in localStorage
   - Users can clear conversation history

3. **User Experience**:
   - Simple, intuitive UI
   - Visual indication of message status (sent, in transit, received)
   - Timestamps for all messages

## Technical Requirements

1. **Technology Stack**:
   - React.js for UI components
   - localStorage for message persistence
   - React Testing Library and Jest for tests
   - No external dependencies beyond React ecosystem

2. **Component Structure**:
   - App (main container)
   - ChatPanel (reusable for both <PERSON> and <PERSON>)
   - MessageList (to display messages)
   - MessageInput (to compose messages)
   - NetworkPanel (to visualize message transmission)
   - Message (data model for messages)

3. **Data Flow**:
   - Messages stored in App state
   - Messages passed to appropriate components via props
   - Messages persisted in localStorage

4. **Testing Requirements**:
   - Unit tests for all components
   - Integration tests for message flow
   - Testing using React Testing Library and Jest

## Design Mockup

```
+------------------------------+------------------------------+
|                              |                              |
|                              |                              |
|                              |                              |
|                              |                              |
|         ALICE PANEL          |          BOB PANEL           |
|                              |                              |
|                              |                              |
|                              |                              |
|                              |                              |
+------------------------------+------------------------------+
|                                                             |
|                      NETWORK PANEL                          |
|                                                             |
+-------------------------------------------------------------+
```

## Implementation Plan

1. **Phase 1: Project Setup**
   - Initialize React application
   - Set up folder structure
   - Configure testing environment
   - Create basic styling

2. **Phase 2: Core Components**
   - Implement ChatPanel component
   - Implement MessageList component
   - Implement MessageInput component
   - Implement basic styling

3. **Phase 3: Message Flow & Network Simulation**
   - Implement message passing between panels
   - Add network simulation visualization
   - Add delay mechanism for message delivery

4. **Phase 4: Data Persistence**
   - Implement localStorage integration
   - Add message history loading/saving

5. **Phase 5: Testing**
   - Write unit tests for components
   - Write integration tests for message flow
   - Test localStorage persistence

## Success Criteria

1. Users can send messages between Alice and Bob
2. Message transmission is visually represented in the network panel
3. Messages persist between page refreshes
4. All tests pass
5. UI is clean and intuitive
6. Code is well-structured, maintainable, and follows React best practices 