const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

test.describe('Chat Interface Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('http://localhost:3004');
  });

  test('should capture app screenshot', async ({ page }) => {
    // Wait for the app to be fully loaded
    await page.waitForSelector('.chat-panel.alice');
    await page.waitForSelector('.chat-panel.bob');
    
    // Take a screenshot of the entire page
    await page.screenshot();
  });

  test('should display both chat panels', async ({ page }) => {
    // Check if both chat panels are visible
    await expect(page.locator('.chat-panel.alice')).toBeVisible();
    await expect(page.locator('.chat-panel.bob')).toBeVisible();
    
    // Check if headers are correct
    await expect(page.locator('.chat-panel.alice h2')).toHaveText('Alice');
    await expect(page.locator('.chat-panel.bob h2')).toHaveText('Bob');
  });

  test('should allow sending messages', async ({ page }) => {
    // Send a message from Alice
    await page.locator('.chat-panel.alice input').fill('Hello Bob!');
    await page.locator('.chat-panel.alice button[type="submit"]').click();

    // Verify message appears in both panels
    await expect(page.locator('.chat-panel.alice .message-content')).toHaveText('Hello Bob!');
    await expect(page.locator('.chat-panel.bob .message-content')).toHaveText('Hello Bob!');

    // Send a message from Bob
    await page.locator('.chat-panel.bob input').fill('Hi Alice!');
    await page.locator('.chat-panel.bob button[type="submit"]').click();

    // Verify Bob's message appears in both panels
    await expect(page.locator('.chat-panel.alice .message-content').nth(1)).toHaveText('Hi Alice!');
    await expect(page.locator('.chat-panel.bob .message-content').nth(1)).toHaveText('Hi Alice!');
  });

  test('should clear messages', async ({ page }) => {
    // Send a message first
    await page.locator('.chat-panel.alice input').fill('Test message');
    await page.locator('.chat-panel.alice button[type="submit"]').click();

    // Clear messages
    await page.locator('.chat-panel.alice .clear-btn').click();

    // Verify messages are cleared
    await expect(page.locator('.chat-panel.alice .no-messages')).toBeVisible();
    await expect(page.locator('.chat-panel.bob .no-messages')).toBeVisible();
  });

  test('should toggle dark mode', async ({ page }) => {
    // Click theme toggle
    await page.locator('.theme-toggle').click();

    // Verify dark mode is applied
    await expect(page.locator('.App')).toHaveClass(/dark-mode/);

    // Click again to toggle back
    await page.locator('.theme-toggle').click();

    // Verify dark mode is removed
    await expect(page.locator('.App')).not.toHaveClass(/dark-mode/);
  });

  test('should handle message history', async ({ page }) => {
    // Send multiple messages
    const messages = ['First message', 'Second message', 'Third message'];
    for (const msg of messages) {
      await page.locator('.chat-panel.alice input').fill(msg);
      await page.locator('.chat-panel.alice button[type="submit"]').click();
    }

    // Verify all messages are displayed in order
    for (let i = 0; i < messages.length; i++) {
      await expect(page.locator('.chat-panel.alice .message-content').nth(i)).toHaveText(messages[i]);
      await expect(page.locator('.chat-panel.bob .message-content').nth(i)).toHaveText(messages[i]);
    }
  });

  test('should handle long messages', async ({ page }) => {
    const longMessage = 'This is a very long message that should wrap properly in the chat interface. '.repeat(5);
    await page.locator('.chat-panel.alice input').fill(longMessage);
    await page.locator('.chat-panel.alice button[type="submit"]').click();

    // Verify message is displayed properly
    await expect(page.locator('.chat-panel.alice .message-content')).toHaveText(longMessage);
    await expect(page.locator('.chat-panel.bob .message-content')).toHaveText(longMessage);
  });

  test('should handle special characters in messages', async ({ page }) => {
    const specialMessage = '!@#$%^&*()_+{}[]|\\:;"<>,.?/~`';
    await page.locator('.chat-panel.alice input').fill(specialMessage);
    await page.locator('.chat-panel.alice button[type="submit"]').click();

    // Verify special characters are displayed correctly
    await expect(page.locator('.chat-panel.alice .message-content')).toHaveText(specialMessage);
    await expect(page.locator('.chat-panel.bob .message-content')).toHaveText(specialMessage);
  });

  test('should handle empty messages', async ({ page }) => {
    // Try to send an empty message
    await page.locator('.chat-panel.alice button[type="submit"]').click();

    // Verify no message was sent
    await expect(page.locator('.chat-panel.alice .message-content')).not.toBeVisible();
    await expect(page.locator('.chat-panel.bob .message-content')).not.toBeVisible();
  });
}); 