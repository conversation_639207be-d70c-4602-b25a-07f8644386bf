FROM mcr.microsoft.com/playwright:v1.40.0-jammy

WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies
RUN npm install

# Copy application code
COPY . .

# Install Playwright browsers
RUN npx playwright install --with-deps chromium

# Build React app
RUN npm run build

# Expose the port for the React app
EXPOSE 3001

# Set environment variable for port
ENV PORT=3001

# Command to start the app and run tests
CMD ["sh", "-c", "npm start & npx playwright test"] 