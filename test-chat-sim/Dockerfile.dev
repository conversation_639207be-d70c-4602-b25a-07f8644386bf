# WebOTR Test App Development Container
# Optimized for development with volume mounting and hot reload

FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install development tools
RUN apk add --no-cache \
    git \
    bash \
    curl \
    vim \
    nano \
    && npm install -g nodemon

# Create app user for development
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# Set up proper permissions for node_modules
RUN mkdir -p /app/node_modules && \
    chown -R appuser:appgroup /app

# Switch to app user
USER appuser

# Copy package files (these will be overridden by volume mount)
COPY --chown=appuser:appgroup package*.json ./

# Install dependencies (will be run again in CMD for volume mounts)
RUN npm install

# Set up volume mount point for source code
VOLUME ["/app"]

# Expose port for React development server
EXPOSE 3000

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# Development command with volume support and dependency installation
CMD ["sh", "-c", "npm install && npm start"]
