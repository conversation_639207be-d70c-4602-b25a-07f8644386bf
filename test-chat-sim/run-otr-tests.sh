#!/bin/bash

# Script to run tests for the WebOTR framework

echo "===== WebOTR Framework Tests ====="

# Get the current directory and the parent (main project) directory
CURRENT_DIR="$(cd "$(dirname "$0")" && pwd)"
MAIN_PROJECT_DIR="$(cd "$CURRENT_DIR/.." && pwd)"

echo "Current directory: $CURRENT_DIR"
echo "Main project directory: $MAIN_PROJECT_DIR"

# Check if the main project has tests directory
if [ ! -d "$MAIN_PROJECT_DIR/tests" ]; then
    echo "Error: Main project tests directory not found. Make sure you're running this script from the test-chat-sim directory."
    exit 1
fi

# Create framework tests directory in the main project if it doesn't exist
mkdir -p "$MAIN_PROJECT_DIR/tests/framework"

# Copy the tests to the main project's tests directory
echo "Copying framework tests to main project directory..."
cp "$CURRENT_DIR/src/__tests__/OtrProtocol.test.js" "$MAIN_PROJECT_DIR/tests/framework/"
cp "$CURRENT_DIR/src/__tests__/OtrCrypto.test.js" "$MAIN_PROJECT_DIR/tests/framework/"
cp "$CURRENT_DIR/src/__tests__/OtrNegotiation.test.js" "$MAIN_PROJECT_DIR/tests/framework/"

# Change to the main project directory
cd "$MAIN_PROJECT_DIR"

# Update imports in the test files to use the correct paths
for testfile in "$MAIN_PROJECT_DIR/tests/framework"/*.test.js; do
    echo "Updating imports in $testfile..."
    # Fix the imports in each test file
    sed -i 's/\.\.\/\.\.\/\.\.\/src\/core\/session/\.\.\/\.\.\/src\/core\/session/g' "$testfile"
    sed -i 's/\.\.\/\.\.\/\.\.\/src\/core\/crypto/\.\.\/\.\.\/src\/core\/crypto/g' "$testfile"
    sed -i 's/\.\.\/\.\.\/\.\.\/src\/core\/protocol/\.\.\/\.\.\/src\/core\/protocol/g' "$testfile"
done

# Run the tests
echo "Running framework tests from main project directory..."

echo "===== OTR Protocol Tests ====="
npx jest tests/framework/OtrProtocol.test.js

echo ""
echo "===== OTR Cryptography Tests ====="
npx jest tests/framework/OtrCrypto.test.js

echo ""
echo "===== OTR Negotiation Tests ====="
npx jest tests/framework/OtrNegotiation.test.js

echo ""
echo "===== Testing complete =====" 