* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --background-color: #f5f5f5;
  --card-background: #ffffff;
  --card-background-rgb: 255, 255, 255;
  --text-color: #333333;
  --text-secondary: #666666;
  --text-muted: #999999;
  --border-color: #dddddd;
  --alice-color: #3498db;
  --bob-color: #e74c3c;
  --message-sent-bg: #e1f5fe;
  --message-sent-border: #b3e5fc;
  --message-received-bg: #f1f1f1;
  --message-received-border: #e0e0e0;
  --network-bg: #f0f0f0;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --input-bg: #ffffff;
}

[data-theme="dark"] {
  --background-color: #121212;
  --card-background: #1e1e1e;
  --card-background-rgb: 30, 30, 30;
  --text-color: #e0e0e0;
  --text-secondary: #b0b0b0;
  --text-muted: #777777;
  --border-color: #333333;
  --alice-color: #2980b9;
  --bob-color: #c0392b;
  --message-sent-bg: #1c3b50;
  --message-sent-border: #2c5c7c;
  --message-received-bg: #2a2a2a;
  --message-received-border: #3d3d3d;
  --network-bg: #1a1a1a;
  --shadow-color: rgba(0, 0, 0, 0.4);
  --input-bg: #2a2a2a;
}

html, body {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: all 0.3s ease;
}

#root {
  height: 100%;
  width: 100%;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
}

.theme-toggle {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 100;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  background-color: var(--text-color);
  color: var(--background-color);
}

.chat-panels {
  display: flex;
  flex: 1;
  min-height: 400px; /* Ensure a minimum height */
  max-height: calc(100vh - 220px); /* Allow room for network panel */
  gap: 16px; /* Add spacing between chat panels */
}

.network-panel {
  height: 150px;
  min-height: 150px;
  background-color: var(--network-bg);
  border: 1px solid var(--border-color);
  padding: 10px;
  overflow-y: auto;
  margin-top: 16px;
  border-radius: 5px;
}

/* App controls */
.app-controls {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 10px;
}

/* OTR toggle button */
.otr-toggle {
  background-color: #f1f1f1;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.3s;
}

.otr-toggle.otr-enabled {
  background-color: #d4edda;
  color: #155724;
}

[data-theme='dark'] .otr-toggle {
  background-color: #333;
  color: #f1f1f1;
}

[data-theme='dark'] .otr-toggle.otr-enabled {
  background-color: #1e4620;
  color: #d4edda;
}

/* Header controls */
.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* OTR status indicator */
.otr-status {
  font-size: 1.2rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

/* Encrypted message styling */
.message-content.encrypted {
  position: relative;
  background-color: #e8f5e9;
  border-radius: 4px;
}

[data-theme='dark'] .message-content.encrypted {
  background-color: #1b3a1c;
}

.encrypted-indicator {
  margin-right: 4px;
  font-size: 0.9rem;
}

.App {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px var(--shadow-color);
  z-index: 10;
}

.app-header h1 {
  font-size: 1.5rem;
  margin: 0;
}

.header-controls {
  display: flex;
  gap: 20px;
}

.toggle-switch {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.toggle-switch span {
  margin-right: 8px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  background-color: #ccc;
  border-radius: 20px;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  border-radius: 50%;
  transition: .4s;
}

input:checked + .slider {
  background-color: var(--alice-color);
}

input:checked + .slider:before {
  transform: translateX(20px);
}

.otr-toggle input:checked + .slider {
  background-color: #4CAF50;
}

.dark-mode-toggle input:checked + .slider {
  background-color: #555;
}

.chat-layout {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.chats-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.chat-panel {
  flex: 1;
  min-width: 0;
  margin: 10px;
  height: calc(70vh - 80px);
}

.network-container {
  height: 30vh;
  padding: 0 10px 10px 10px;
}

.dark-mode {
  background-color: var(--background-color);
  color: var(--text-color);
} 