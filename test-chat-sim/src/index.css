* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  /* Light theme variables */
  --bg-color: #ffffff;
  --text-color: #333333;
  --text-muted: #666666;
  --border-color: #e0e0e0;
  --header-bg: #f5f5f5;
  --input-bg: #ffffff;
  --button-bg: #e0e0e0;
  --button-text: #333333;
  --button-hover: #d0d0d0;
  --button-disabled: #cccccc;
  --primary-color: #3498db;
  --primary-hover: #2980b9;
  --message-sent-bg: #e3f2fd;
  --message-sent-text: #1976d2;
  --message-received-bg: #f5f5f5;
  --message-received-text: #333333;
  --alice-color: #3498db;
  --bob-color: #2ecc71;
}

/* Dark theme variables */
[data-theme='dark'] {
  --bg-color: #1a1a1a;
  --text-color: #ffffff;
  --text-muted: #b0b0b0;
  --border-color: #333333;
  --header-bg: #2d2d2d;
  --input-bg: #333333;
  --button-bg: #404040;
  --button-text: #ffffff;
  --button-hover: #505050;
  --button-disabled: #404040;
  --primary-color: #3498db;
  --primary-hover: #2980b9;
  --message-sent-bg: #1e3a5f;
  --message-sent-text: #64b5f6;
  --message-received-bg: #2d2d2d;
  --message-received-text: #ffffff;
  --alice-color: #3498db;
  --bob-color: #2ecc71;
}

html, body {
  height: 100%;
  width: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.5;
}

#root {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.App {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow: hidden;
}

.chat-layout {
  display: flex;
  flex: 1;
  gap: 20px;
  overflow: hidden;
}

.chats-container {
  display: flex;
  flex: 1;
  gap: 20px;
  overflow: hidden;
}

.chat-panel {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.network-container {
  width: 300px;
  min-height: 200px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-color);
  overflow: hidden;
}

.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  padding: 8px 16px;
  background-color: var(--button-bg);
  color: var(--button-text);
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.theme-toggle:hover {
  background-color: var(--button-hover);
}

.chat-panels {
  display: flex;
  flex: 1;
  min-height: 400px; /* Ensure a minimum height */
  max-height: calc(100vh - 220px); /* Allow room for network panel */
  gap: 16px; /* Add spacing between chat panels */
}

.network-panel {
  height: 150px;
  min-height: 150px;
  background-color: var(--network-bg);
  border: 1px solid var(--border-color);
  padding: 10px;
  overflow-y: auto;
  margin-top: 16px;
  border-radius: 5px;
}

/* App controls */
.app-controls {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 10px;
}

/* OTR toggle button */
.otr-toggle {
  background-color: #f1f1f1;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.3s;
}

.otr-toggle.otr-enabled {
  background-color: #d4edda;
  color: #155724;
}

[data-theme='dark'] .otr-toggle {
  background-color: #333;
  color: #f1f1f1;
}

[data-theme='dark'] .otr-toggle.otr-enabled {
  background-color: #1e4620;
  color: #d4edda;
}

/* Header controls */
.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* OTR status indicator */
.otr-status {
  font-size: 1.2rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

/* Encrypted message styling */
.message-content.encrypted {
  position: relative;
  background-color: #e8f5e9;
  border-radius: 4px;
}

[data-theme='dark'] .message-content.encrypted {
  background-color: #1b3a1c;
}

.encrypted-indicator {
  margin-right: 4px;
  font-size: 0.9rem;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px var(--shadow-color);
  z-index: 10;
}

.app-header h1 {
  font-size: 1.5rem;
  margin: 0;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* Tab controls */
.tab-controls {
  display: flex;
  gap: 10px;
}

.tab-button {
  padding: 8px 16px;
  border: 2px solid #007bff;
  background: transparent;
  color: #007bff;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tab-button:hover {
  background: #007bff;
  color: white;
}

.tab-button.active {
  background: #007bff;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

[data-theme='dark'] .tab-button {
  border-color: #4dabf7;
  color: #4dabf7;
}

[data-theme='dark'] .tab-button:hover {
  background: #4dabf7;
  color: #121212;
}

[data-theme='dark'] .tab-button.active {
  background: #4dabf7;
  color: #121212;
  box-shadow: 0 2px 8px rgba(77, 171, 247, 0.3);
}

/* Steganography layout */
.steganography-layout {
  flex: 1;
  overflow: auto;
  background: var(--background-color);
  padding: 20px;
}

/* Handshake layout */
.handshake-layout {
  flex: 1;
  overflow: auto;
  background: var(--background-color);
  padding: 20px;
}

.toggle-switch {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.toggle-switch span {
  margin-right: 8px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  background-color: #ccc;
  border-radius: 20px;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  border-radius: 50%;
  transition: .4s;
}

input:checked + .slider {
  background-color: var(--alice-color);
}

input:checked + .slider:before {
  transform: translateX(20px);
}

.otr-toggle input:checked + .slider {
  background-color: #4CAF50;
}

.dark-mode-toggle input:checked + .slider {
  background-color: #555;
}

.dark-mode {
  background-color: var(--background-color);
  color: var(--text-color);
} 