/**
 * A mock OTR session class for testing OTR functionality in the test chat app
 * This simulates the behavior of the real OtrSession class from the WebOTR library
 */

/**
 * Enum for OTR states
 */
const STATE = {
  PLAINTEXT: 0,
  ENCRYPTED: 1,
  FINISHED: 2
};

/**
 * Simulated OTR session
 */
class OtrSession {
  /**
   * Create a new mock OTR session
   * @param {string} peer - Peer identifier
   * @param {Object} options - Session options
   */
  constructor(peer, options = {}) {
    this.peer = peer;
    this.state = STATE.PLAINTEXT;
    this.sendMessage = options.sendMessage || (() => {});
    this.initializing = false;
    this.negotiating = false;
    this.ready = false;
  }

  /**
   * Initialize the session
   * @returns {Promise<boolean>} Success indicator
   */
  async init() {
    this.initializing = true;
    // Simulate key generation delay
    await new Promise(resolve => setTimeout(resolve, 500));
    this.initializing = false;
    return true;
  }

  /**
   * Start OTR negotiation
   * @returns {Promise<boolean>} Success indicator
   */
  async startOtr() {
    if (this.state === STATE.ENCRYPTED) {
      return true; // Already encrypted
    }

    this.negotiating = true;

    // Send a fake commit message
    this.sendMessage('?OTRv3?');

    // Simulate negotiation delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Set state to encrypted
    this.state = STATE.ENCRYPTED;
    this.negotiating = false;
    this.ready = true;

    return true;
  }

  /**
   * End the OTR session
   * @returns {Promise<boolean>} Success indicator
   */
  async endOtr() {
    if (this.state !== STATE.ENCRYPTED) {
      return false;
    }

    // Send a fake end message
    this.sendMessage('?OTR END?');

    // Set state back to plaintext
    this.state = STATE.PLAINTEXT;
    this.ready = false;

    return true;
  }

  /**
   * Process outgoing message
   * @param {string} message - Message to encrypt
   * @returns {Promise<string>} Encrypted message
   */
  async processOutgoing(message) {
    try {
      if (this.state !== STATE.ENCRYPTED) {
        // If not encrypted, try to start OTR
        if (message === '?OTR?') {
          await this.startOtr();
          return null;
        }
        return message;
      }

      // In encrypted state, simulate encryption
      const encryptedMessage = `?OTR:${btoa(message)}?`;
      return encryptedMessage;
    } catch (error) {
      console.error('[OTR Mock] Error processing outgoing message:', error);
      // Return the original message if encryption fails
      return message;
    }
  }

  /**
   * Process incoming message
   * @param {string} message - Message to decrypt
   * @returns {Promise<Object>} Processing result
   */
  async processIncoming(message) {
    try {
      // Check if this is an OTR related message
      if (message === '?OTRv3?') {
        // This is a commit message - normally would trigger a response
        // Send a fake response
        setTimeout(() => {
          try {
            this.sendMessage('?OTR:KEY?');
            
            // Auto-advance the state
            setTimeout(() => {
              this.state = STATE.ENCRYPTED;
              this.ready = true;
            }, 500);
          } catch (error) {
            console.error('[OTR Mock] Error sending response to commit message:', error);
          }
        }, 500);
        
        return {
          message: null,
          encrypted: false,
          internal: true
        };
      }
      
      if (message === '?OTR:KEY?') {
        // This is a key message
        setTimeout(() => {
          try {
            this.sendMessage('?OTR:SIG?');
          } catch (error) {
            console.error('[OTR Mock] Error sending response to key message:', error);
          }
        }, 500);
        
        return {
          message: null,
          encrypted: false,
          internal: true
        };
      }
      
      if (message === '?OTR:SIG?') {
        // This is a signature message
        this.state = STATE.ENCRYPTED;
        this.ready = true;
        
        return {
          message: "Secure OTR session established",
          encrypted: true,
          internal: false
        };
      }
      
      if (message === '?OTR END?') {
        // End of OTR session
        this.state = STATE.PLAINTEXT;
        this.ready = false;
        
        return {
          message: "OTR session ended",
          encrypted: false,
          internal: false
        };
      }
      
      // Check if this is an encrypted message
      if (message.startsWith('?OTR:') && message.endsWith('?')) {
        // Simple "decryption"
        const encryptedPart = message.substring(5, message.length - 1);
        try {
          const decryptedMessage = atob(encryptedPart);
          
          return {
            message: decryptedMessage,
            encrypted: true,
            internal: false
          };
        } catch (e) {
          console.error('[OTR Mock] Error decrypting message:', e);
          return {
            message: "Failed to decrypt message",
            encrypted: false,
            internal: false
          };
        }
      }
      
      // Regular message, pass through
      return {
        message: message,
        encrypted: false,
        internal: false
      };
    } catch (error) {
      console.error('[OTR Mock] Error processing incoming message:', error);
      // Return a fail-safe result
      return {
        message: "Error processing message",
        encrypted: false,
        internal: false
      };
    }
  }
}

// Export the class
export { OtrSession }; 