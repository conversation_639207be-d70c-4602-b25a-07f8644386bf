/**
 * A mock OTR session class for testing OTR functionality in the test chat app
 * This simulates the behavior of the real OtrSession class from the WebOTR library
 */

/**
 * Custom logging function that writes to both console and container logs
 * @param {string} level - Log level (warn, error, log)
 * @param {string} message - Message to log
 */
const log = (level, message) => {
  // Log to browser console
  console[level](message);
  
  // Log to container (using fetch to send to a logging endpoint)
  fetch('/api/log', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      level,
      message,
      timestamp: new Date().toISOString()
    })
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('Log sent successfully:', data);
  })
  .catch(error => {
    // Log the error to console
    console.error('Failed to send log to container:', error);
    // Also log to container using console.log which will be captured by the server
    console.log(`[${new Date().toISOString()}] LOG_ERROR: Failed to send log - ${error.message}`);
  });
};

/**
 * Enum for OTR states
 */
const STATE = {
  PLAINTEXT: 0,
  ENCRYPTED: 1,
  FINISHED: 2
};

/**
 * Simulated OTR session
 */
class OtrSession {
  /**
   * Create a new mock OTR session
   * @param {string} peer - Peer identifier
   * @param {Object} options - Session options
   */
  constructor(peer, options = {}) {
    log('warn', `[OTR Mock] Creating new session for peer: ${peer}`);
    this.peer = peer;
    this.state = STATE.PLAINTEXT;
    this.sendMessage = options.sendMessage || (() => {});
    this.initializing = false;
    this.negotiating = false;
    this.ready = false;
    this.smpCallback = null;
    log('warn', `[OTR Mock] Session created with state: ${this.state}`);
  }

  /**
   * Initialize the session
   * @returns {Promise<boolean>} Success indicator
   */
  async init() {
    log('warn', `[OTR Mock] Starting initialization for peer: ${this.peer}`);
    this.initializing = true;
    // Simulate key generation delay
    await new Promise(resolve => setTimeout(resolve, 500));
    this.initializing = false;
    log('warn', `[OTR Mock] Initialization complete for peer: ${this.peer}`);
    return true;
  }

  /**
   * Register SMP callback
   * @param {Function} callback - Callback function for SMP events
   */
  registerSMPCallback(callback) {
    log('warn', `[OTR Mock] Registering SMP callback for peer: ${this.peer}`);
    this.smpCallback = callback;
    log('warn', '[OTR Mock] SMP callback registered');
  }

  /**
   * Start OTR negotiation
   * @returns {Promise<boolean>} Success indicator
   */
  async startOtr() {
    log('warn', `[OTR Mock] Starting OTR negotiation for peer: ${this.peer}`);
    if (this.state === STATE.ENCRYPTED) {
      log('warn', `[OTR Mock] Already encrypted for peer: ${this.peer}`);
      return true; // Already encrypted
    }

    this.negotiating = true;

    // Send a fake commit message
    log('warn', `[OTR Mock] Sending commit message for peer: ${this.peer}`);
    this.sendMessage('?OTRv3?');

    // Simulate negotiation delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Set state to encrypted
    this.state = STATE.ENCRYPTED;
    this.negotiating = false;
    this.ready = true;
    log('warn', `[OTR Mock] OTR negotiation complete for peer: ${this.peer}`);

    return true;
  }

  /**
   * End the OTR session
   * @returns {Promise<boolean>} Success indicator
   */
  async endOtr() {
    if (this.state !== STATE.ENCRYPTED) {
      return false;
    }

    // Send a fake end message
    this.sendMessage('?OTR END?');

    // Set state back to plaintext
    this.state = STATE.PLAINTEXT;
    this.ready = false;

    return true;
  }

  /**
   * Process outgoing message
   * @param {string} message - Message to encrypt
   * @returns {Promise<string>} Encrypted message
   */
  async processOutgoing(message) {
    try {
      log('warn', `[OTR Mock] Processing outgoing message for peer: ${this.peer}, Message: ${message}`);
      if (this.state !== STATE.ENCRYPTED) {
        // If not encrypted, try to start OTR
        if (message === '?OTR?') {
          log('warn', '[OTR Mock] Starting OTR for outgoing message');
          await this.startOtr();
          return null;
        }
        log('warn', '[OTR Mock] Message not encrypted, sending plaintext');
        return message;
      }

      // In encrypted state, simulate encryption
      const encryptedMessage = `?OTR:${btoa(message)}?`;
      log('warn', '[OTR Mock] Message encrypted');
      return encryptedMessage;
    } catch (error) {
      log('error', `[OTR Mock] Error processing outgoing message: ${error}`);
      // Return the original message if encryption fails
      return message;
    }
  }

  /**
   * Process incoming message
   * @param {string} message - Message to decrypt
   * @returns {Promise<Object>} Processing result
   */
  async processIncoming(message) {
    try {
      log('warn', `[OTR Mock] Processing incoming message for peer: ${this.peer}, Message: ${message}`);
      // Check if this is an OTR related message
      if (message === '?OTRv3?') {
        log('warn', '[OTR Mock] Received commit message');
        // This is a commit message - normally would trigger a response
        // Send a fake response
        setTimeout(() => {
          try {
            log('warn', '[OTR Mock] Sending key message in response to commit');
            this.sendMessage('?OTR:KEY?');
            
            // Auto-advance the state
            setTimeout(() => {
              log('warn', '[OTR Mock] Advancing state to encrypted');
              this.state = STATE.ENCRYPTED;
              this.ready = true;
            }, 500);
          } catch (error) {
            log('error', `[OTR Mock] Error sending response to commit message: ${error}`);
          }
        }, 500);
        
        return {
          message: null,
          encrypted: false,
          internal: true
        };
      }
      
      if (message === '?OTR:KEY?') {
        log('warn', '[OTR Mock] Received key message');
        // This is a key message
        setTimeout(() => {
          try {
            log('warn', '[OTR Mock] Sending signature message in response to key');
            this.sendMessage('?OTR:SIG?');
          } catch (error) {
            log('error', `[OTR Mock] Error sending response to key message: ${error}`);
          }
        }, 500);
        
        return {
          message: null,
          encrypted: false,
          internal: true
        };
      }
      
      if (message === '?OTR:SIG?') {
        log('warn', '[OTR Mock] Received signature message');
        // This is a signature message
        this.state = STATE.ENCRYPTED;
        this.ready = true;
        
        return {
          message: "Secure OTR session established",
          encrypted: true,
          internal: false
        };
      }
      
      if (message === '?OTR END?') {
        log('warn', '[OTR Mock] Received end message');
        // End of OTR session
        this.state = STATE.PLAINTEXT;
        this.ready = false;
        
        return {
          message: "OTR session ended",
          encrypted: false,
          internal: false
        };
      }
      
      // Check if this is an encrypted message
      if (message.startsWith('?OTR:') && message.endsWith('?')) {
        log('warn', '[OTR Mock] Processing encrypted message');
        // Simple "decryption"
        const encryptedPart = message.substring(5, message.length - 1);
        try {
          const decryptedMessage = atob(encryptedPart);
          log('warn', '[OTR Mock] Message decrypted successfully');
          
          return {
            message: decryptedMessage,
            encrypted: true,
            internal: false
          };
        } catch (e) {
          log('error', `[OTR Mock] Error decrypting message: ${e}`);
          return {
            message: "Failed to decrypt message",
            encrypted: false,
            internal: false
          };
        }
      }
      
      // Regular message, pass through
      log('warn', '[OTR Mock] Processing regular message');
      return {
        message: message,
        encrypted: false,
        internal: false
      };
    } catch (error) {
      log('error', `[OTR Mock] Error processing incoming message: ${error}`);
      // Return a fail-safe result
      return {
        message: "Error processing message",
        encrypted: false,
        internal: false
      };
    }
  }
}

// Export the class
export default OtrSession; 