/**
 * Steganography Integration for Test Chat Simulator
 * Integrates WebOTR steganography functionality into the test environment
 */

// Import steganography modules from the main project
// Note: In a real environment, these would be proper imports
// For testing, we'll create mock implementations that simulate the real functionality

// Global shared storage for hidden data (simulates actual steganography)
const globalHiddenDataStore = new Map();

/**
 * Mock SteganographyOTR implementation for test environment
 */
export class SteganographyOTR {
  constructor(options = {}) {
    this.options = {
      quality: options.quality || 0.95,
      password: options.password || null,
      compression: options.compression !== false,
      ...options
    };

    // Use global storage for hidden data
    this._hiddenDataStore = globalHiddenDataStore;
  }

  /**
   * Hide message in image
   * @param {ImageData|Object} image - Cover image
   * @param {string} message - Message to hide
   * @param {Object} options - Steganography options
   * @returns {Promise<Object>} Stego image
   */
  async hideMessage(image, message, options = {}) {
    // Simulate processing time
    await this._simulateProcessingDelay();
    
    // Generate unique key for this stego image
    const stegoKey = this._generateStegoKey(image, message);
    
    // Store the hidden message
    this._hiddenDataStore.set(stegoKey, {
      message,
      timestamp: Date.now(),
      options,
      originalImage: image
    });
    
    // Return stego image (copy of original with hidden data marker)
    return {
      ...image,
      _stegoKey: stegoKey,
      _hasHiddenData: true,
      _stegoTimestamp: Date.now()
    };
  }

  /**
   * Reveal message from stego image
   * @param {Object} stegoImage - Stego image
   * @param {Object} options - Extraction options
   * @returns {Promise<string|null>} Hidden message
   */
  async revealMessage(stegoImage, options = {}) {
    // Simulate processing time
    await this._simulateProcessingDelay();
    
    if (!stegoImage._stegoKey || !stegoImage._hasHiddenData) {
      return null;
    }
    
    const hiddenData = this._hiddenDataStore.get(stegoImage._stegoKey);
    return hiddenData ? hiddenData.message : null;
  }

  /**
   * Detect if image contains hidden message
   * @param {Object} image - Image to analyze
   * @returns {Promise<boolean>} True if hidden message detected
   */
  async detectMessage(image) {
    await this._simulateProcessingDelay(50); // Faster detection
    return image._hasHiddenData || false;
  }

  /**
   * Calculate image capacity for message hiding
   * @param {Object} image - Image to analyze
   * @returns {Promise<number>} Capacity in bytes
   */
  async calculateCapacity(image) {
    if (!image.width || !image.height) {
      return 0;
    }
    
    // Simulate capacity calculation (roughly 10% of pixels)
    return Math.floor(image.width * image.height * 0.1);
  }

  /**
   * Generate cover image
   * @param {number} width - Image width
   * @param {number} height - Image height
   * @param {string} style - Image style
   * @returns {Promise<Object>} Generated cover image
   */
  async generateCover(width, height, style = 'noise') {
    await this._simulateProcessingDelay();
    
    const data = new Uint8ClampedArray(width * height * 4);
    
    // Generate different patterns based on style
    switch (style) {
      case 'gradient':
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const i = (y * width + x) * 4;
            data[i] = (x / width) * 255;     // Red gradient
            data[i + 1] = (y / height) * 255; // Green gradient
            data[i + 2] = 128;               // Blue constant
            data[i + 3] = 255;               // Alpha
          }
        }
        break;
        
      case 'pattern':
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const i = (y * width + x) * 4;
            const checker = ((Math.floor(x / 16) + Math.floor(y / 16)) % 2) * 255;
            data[i] = checker;
            data[i + 1] = checker * 0.8;
            data[i + 2] = checker * 0.6;
            data[i + 3] = 255;
          }
        }
        break;
        
      default: // noise
        for (let i = 0; i < data.length; i += 4) {
          data[i] = Math.floor(Math.random() * 256);
          data[i + 1] = Math.floor(Math.random() * 256);
          data[i + 2] = Math.floor(Math.random() * 256);
          data[i + 3] = 255;
        }
    }
    
    return {
      data,
      width,
      height,
      _generated: true,
      _style: style
    };
  }

  // Private methods
  _simulateProcessingDelay(baseMs = 100) {
    const delay = baseMs + Math.random() * 50; // Add some randomness
    return new Promise(resolve => setTimeout(resolve, delay));
  }

  _generateStegoKey(image, message) {
    const imageHash = this._simpleHash(JSON.stringify({
      width: image.width,
      height: image.height,
      timestamp: Date.now()
    }));
    const messageHash = this._simpleHash(message);
    return `stego_${imageHash}_${messageHash}`;
  }

  _simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}

/**
 * Mock OTRSteganographySession for test environment
 */
export class OTRSteganographySession {
  constructor(options = {}) {
    this.options = {
      testing: true,
      steganographyEnabled: options.steganographyEnabled || false,
      stegoPassword: options.stegoPassword || null,
      clientName: options.clientName || 'TestClient',
      ...options
    };
    
    this.instanceTag = Math.floor(Math.random() * 0xFFFFFFFF);
    this.stego = new SteganographyOTR({
      password: this.options.stegoPassword,
      quality: 0.95
    });
    
    this.stegoState = {
      enabled: this.options.steganographyEnabled,
      autoDetect: true,
      coverImages: new Map(),
      pendingImages: new Map()
    };
    
    this.callbacks = {};
  }

  /**
   * Send message via steganography
   * @param {string} plaintext - Message to send
   * @param {Object} coverImage - Cover image
   * @param {Object} options - Options
   * @returns {Promise<Object>} Stego image
   */
  async sendStegoMessage(plaintext, coverImage, options = {}) {
    if (!this.stegoState.enabled) {
      throw new Error('Steganography not enabled');
    }
    
    // Simulate OTR encryption
    const encryptedMessage = `[ENCRYPTED:${this.options.clientName}]${plaintext}[/ENCRYPTED]`;
    
    // Hide in image
    const stegoImage = await this.stego.hideMessage(coverImage, encryptedMessage, {
      type: 'OTR_MESSAGE',
      sessionId: this.instanceTag,
      ...options
    });
    
    // Store for potential retransmission
    this._storeStegoImage(encryptedMessage, stegoImage);
    
    return stegoImage;
  }

  /**
   * Process incoming stego image
   * @param {Object} stegoImage - Stego image
   * @param {Object} options - Options
   * @returns {Promise<string|null>} Decrypted message
   */
  async processStegoImage(stegoImage, options = {}) {
    if (!this.stegoState.enabled) {
      return null;
    }

    // Extract encrypted message
    const encryptedMessage = await this.stego.revealMessage(stegoImage, options);

    if (!encryptedMessage) {
      return null;
    }

    // Simulate OTR decryption
    if (encryptedMessage.startsWith('[ENCRYPTED:') && encryptedMessage.endsWith('[/ENCRYPTED]')) {
      const startMarker = encryptedMessage.indexOf(']') + 1;
      const endMarker = encryptedMessage.lastIndexOf('[');
      return encryptedMessage.substring(startMarker, endMarker);
    }

    return encryptedMessage;
  }

  /**
   * Auto-detect and process images
   * @param {Object} image - Image to check
   * @returns {Promise<Object|null>} Processing result
   */
  async autoProcessImage(image) {
    if (!this.stegoState.autoDetect) {
      return null;
    }
    
    const hasHiddenData = await this.stego.detectMessage(image);
    if (!hasHiddenData) {
      return null;
    }
    
    const result = await this.processStegoImage(image);
    if (result) {
      return {
        type: 'message',
        content: result,
        image: image
      };
    }
    
    return null;
  }

  /**
   * Generate cover image
   * @param {number} messageSize - Message size
   * @param {string} style - Image style
   * @returns {Promise<Object>} Cover image
   */
  async generateCoverImage(messageSize, style = 'noise') {
    const minPixels = messageSize * 8;
    const dimension = Math.ceil(Math.sqrt(minPixels));
    const size = Math.max(dimension, 512);
    
    return await this.stego.generateCover(size, size, style);
  }

  /**
   * Get steganography statistics
   * @returns {Object} Statistics
   */
  getStegoStats() {
    return {
      enabled: this.stegoState.enabled,
      autoDetect: this.stegoState.autoDetect,
      cachedImages: this.stegoState.coverImages.size,
      pendingImages: this.stegoState.pendingImages.size,
      quality: this.stego.options.quality,
      hasPassword: !!this.stego.options.password
    };
  }

  /**
   * Enable steganography
   */
  enableSteganography() {
    this.stegoState.enabled = true;
  }

  /**
   * Disable steganography
   */
  disableSteganography() {
    this.stegoState.enabled = false;
  }

  /**
   * Clear cache
   */
  clearStegoCache() {
    this.stegoState.coverImages.clear();
    this.stegoState.pendingImages.clear();
  }

  /**
   * Destroy session
   */
  destroy() {
    this.clearStegoCache();
    this.stegoState = null;
    this.stego = null;
  }

  /**
   * Register state change callback
   * @param {Function} callback - Callback function
   */
  onStateChange(callback) {
    this.callbacks.status = callback;
  }

  // Private methods
  _storeStegoImage(messageId, stegoImage) {
    this.stegoState.coverImages.set(messageId, {
      image: stegoImage,
      timestamp: Date.now()
    });
    
    // Cleanup old images (keep last 10)
    if (this.stegoState.coverImages.size > 10) {
      const entries = Array.from(this.stegoState.coverImages.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      for (let i = 0; i < entries.length - 10; i++) {
        this.stegoState.coverImages.delete(entries[i][0]);
      }
    }
  }
}

/**
 * Steganography test utilities for the test environment
 */
export const SteganographyTestUtils = {
  /**
   * Create test image data
   * @param {number} width - Image width
   * @param {number} height - Image height
   * @param {string} pattern - Pattern type
   * @returns {Object} Test image data
   */
  createTestImage(width, height, pattern = 'random') {
    const data = new Uint8ClampedArray(width * height * 4);
    
    switch (pattern) {
      case 'solid':
        data.fill(128);
        for (let i = 3; i < data.length; i += 4) {
          data[i] = 255; // Alpha
        }
        break;
        
      case 'gradient':
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const i = (y * width + x) * 4;
            data[i] = (x / width) * 255;
            data[i + 1] = (y / height) * 255;
            data[i + 2] = 128;
            data[i + 3] = 255;
          }
        }
        break;
        
      default: // random
        for (let i = 0; i < data.length; i += 4) {
          data[i] = Math.floor(Math.random() * 256);
          data[i + 1] = Math.floor(Math.random() * 256);
          data[i + 2] = Math.floor(Math.random() * 256);
          data[i + 3] = 255;
        }
    }
    
    return { data, width, height };
  },

  /**
   * Convert image data to data URL
   * @param {Object} imageData - Image data
   * @returns {string} Data URL
   */
  imageDataToDataURL(imageData) {
    // For test environment, create a mock data URL
    // In a real environment, this would use canvas
    try {
      if (typeof document !== 'undefined' && document.createElement) {
        const canvas = document.createElement('canvas');
        canvas.width = imageData.width;
        canvas.height = imageData.height;

        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.putImageData(new ImageData(imageData.data, imageData.width, imageData.height), 0, 0);
          return canvas.toDataURL('image/png');
        }
      }
    } catch (error) {
      // Fall back to mock data URL for testing
    }

    // Mock data URL for test environment
    const mockImageData = btoa(`mock-image-${imageData.width}x${imageData.height}-${Date.now()}`);
    return `data:image/png;base64,${mockImageData}`;
  },

  /**
   * Create data URL from test image
   * @param {number} width - Image width
   * @param {number} height - Image height
   * @param {string} pattern - Pattern type
   * @returns {string} Data URL
   */
  createTestImageDataURL(width, height, pattern = 'random') {
    const imageData = this.createTestImage(width, height, pattern);
    return this.imageDataToDataURL(imageData);
  }
};
