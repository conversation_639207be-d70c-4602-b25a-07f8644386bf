const STORAGE_KEY = 'chat_simulator_messages';

/**
 * Save messages to localStorage
 * @param {Array} messages - Array of message objects to save
 * @returns {boolean} - Success status
 */
export const saveMessages = (messages) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(messages));
    return true;
  } catch (error) {
    console.error('Error saving messages to localStorage:', error);
    return false;
  }
};

/**
 * Load messages from localStorage
 * @returns {Array} Array of message objects or empty array if none found
 */
export const loadMessages = () => {
  try {
    const savedMessages = localStorage.getItem(STORAGE_KEY);
    return savedMessages ? JSON.parse(savedMessages) : [];
  } catch (error) {
    console.error('Error loading messages from localStorage:', error);
    return [];
  }
};

/**
 * Clear all saved messages from localStorage
 * @returns {boolean} Success status
 */
export const clearMessages = () => {
  try {
    localStorage.removeItem(STORAGE_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing messages from localStorage:', error);
    return false;
  }
}; 