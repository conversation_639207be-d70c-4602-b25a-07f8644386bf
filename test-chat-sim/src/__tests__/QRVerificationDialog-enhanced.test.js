import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import QRVerificationDialog from '../components/QRVerificationDialog';

describe('QRVerificationDialog Component - Enhanced Coverage', () => {
  const mockProps = {
    isOpen: true,
    onClose: jest.fn(),
    user: 'alice',
    localFingerprint: '5A2F8E7B1D94C65B304218B209A587E2C17F33',
    partnerFingerprint: '7E1DC849A3B6F592D0783E5C9A1FB432D6502C91',
    partnerName: 'Bob',
    onVerificationComplete: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('handles complete tab switching workflow', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Start with QR Code tab
    expect(screen.getByText('QR Code')).toBeInTheDocument();

    // Switch to Fingerprint tab
    const fingerprintTab = screen.getByText('Fingerprint');
    await user.click(fingerprintTab);
    expect(screen.getByText('Compare these fingerprints')).toBeInTheDocument();

    // Switch to Secret Q&A tab
    const secretTab = screen.getByText('Secret Q&A');
    await user.click(secretTab);
    expect(screen.getByText('This tab would contain the Secret Q&A')).toBeInTheDocument();

    // Switch back to QR Code tab
    const qrTab = screen.getByText('QR Code');
    await user.click(qrTab);
    expect(screen.getByText('Generating QR code...')).toBeInTheDocument();
  });

  test('handles fingerprint display with various formats', () => {
    const testCases = [
      {
        local: 'ABCD1234EFGH5678',
        partner: '9876FEDC5432BA10',
        description: 'short fingerprints'
      },
      {
        local: 'A'.repeat(40),
        partner: 'B'.repeat(40),
        description: 'long fingerprints'
      },
      {
        local: '',
        partner: '',
        description: 'empty fingerprints'
      }
    ];

    testCases.forEach(({ local, partner, description }) => {
      const customProps = {
        ...mockProps,
        localFingerprint: local,
        partnerFingerprint: partner
      };

      const { unmount } = render(<QRVerificationDialog {...customProps} />);
      
      // Switch to fingerprint tab
      const fingerprintTab = screen.getByText('Fingerprint');
      fireEvent.click(fingerprintTab);

      // Should handle various fingerprint formats
      expect(screen.getByText('Compare these fingerprints')).toBeInTheDocument();
      
      unmount();
    });
  });

  test('handles dialog state management across renders', () => {
    const { rerender } = render(<QRVerificationDialog {...mockProps} />);

    // Initially open
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();

    // Close dialog
    rerender(<QRVerificationDialog {...mockProps} isOpen={false} />);
    expect(screen.queryByText('Verify Your Conversation Partner')).not.toBeInTheDocument();

    // Reopen dialog
    rerender(<QRVerificationDialog {...mockProps} isOpen={true} />);
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
  });

  test('handles user prop variations', () => {
    const userVariations = ['alice', 'bob', 'charlie', ''];

    userVariations.forEach(user => {
      const customProps = {
        ...mockProps,
        user,
        partnerName: user === 'alice' ? 'Bob' : 'Alice'
      };

      const { unmount } = render(<QRVerificationDialog {...customProps} />);
      
      // Should render regardless of user
      expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
      
      unmount();
    });
  });

  test('handles missing optional props gracefully', () => {
    const minimalProps = {
      isOpen: true,
      onClose: jest.fn(),
      user: 'alice'
      // Missing: localFingerprint, partnerFingerprint, partnerName, onVerificationComplete
    };

    expect(() => {
      render(<QRVerificationDialog {...minimalProps} />);
    }).not.toThrow();

    // Should still render basic structure
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
  });

  test('handles dialog overlay click behavior', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Find the dialog overlay
    const dialog = screen.getByRole('dialog');
    const overlay = dialog.parentElement;

    // Click on overlay (outside dialog content)
    fireEvent.mouseDown(overlay);
    fireEvent.mouseUp(overlay);

    // Should trigger onClose
    expect(mockProps.onClose).toHaveBeenCalled();
  });

  test('handles dialog content click behavior', async () => {
    render(<QRVerificationDialog {...mockProps} />);

    // Click on dialog content (should not close)
    const dialogContent = screen.getByRole('dialog');
    fireEvent.mouseDown(dialogContent);
    fireEvent.mouseUp(dialogContent);

    // Should NOT trigger onClose
    expect(mockProps.onClose).not.toHaveBeenCalled();
  });

  test('handles keyboard navigation and focus management', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Test tab navigation through dialog elements
    await user.tab(); // Should focus first interactive element
    await user.tab(); // Should focus next element
    await user.tab(); // Should focus next element

    // Test escape key
    await user.keyboard('{Escape}');
    expect(mockProps.onClose).toHaveBeenCalled();
  });

  test('handles QR code loading states', async () => {
    render(<QRVerificationDialog {...mockProps} />);

    // Should show loading initially
    expect(screen.getByText('Generating QR code...')).toBeInTheDocument();

    // Wait for any async operations
    await waitFor(() => {
      // Loading state should be present
      expect(screen.getByText('QR Code')).toBeInTheDocument();
    }, { timeout: 1000 });
  });

  test('handles button interactions in different states', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Wait for any initial loading
    await waitFor(() => {
      expect(screen.getByText('QR Code')).toBeInTheDocument();
    }, { timeout: 1000 });

    // Try to interact with buttons (if they exist)
    const scanButton = screen.queryByText('📷 Scan Partner\'s Code');
    const manualButton = screen.queryByText('✎ Enter Manually');

    if (scanButton && !scanButton.disabled) {
      await user.click(scanButton);
      // Should not crash
    }

    if (manualButton && !manualButton.disabled) {
      await user.click(manualButton);
      // Should not crash
    }
  });

  test('handles verification workflow completion', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Switch to fingerprint tab for verification
    const fingerprintTab = screen.getByText('Fingerprint');
    await user.click(fingerprintTab);

    // Should show fingerprint comparison
    expect(screen.getByText('Compare these fingerprints')).toBeInTheDocument();
    expect(screen.getByText(mockProps.localFingerprint)).toBeInTheDocument();
    expect(screen.getByText(mockProps.partnerFingerprint)).toBeInTheDocument();
  });

  test('handles responsive design considerations', () => {
    // Mock different screen sizes
    const screenSizes = [
      { width: 320, height: 568 }, // Mobile
      { width: 768, height: 1024 }, // Tablet
      { width: 1920, height: 1080 } // Desktop
    ];

    screenSizes.forEach(({ width, height }) => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: width,
      });
      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: height,
      });

      const { unmount } = render(<QRVerificationDialog {...mockProps} />);
      
      // Should render on all screen sizes
      expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
      
      unmount();
    });
  });

  test('handles error states gracefully', async () => {
    // Mock console.error to capture any errors
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(<QRVerificationDialog {...mockProps} />);

    // Should render without errors
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();

    // Wait for any async operations that might error
    await waitFor(() => {
      expect(screen.getByText('QR Code')).toBeInTheDocument();
    }, { timeout: 1000 });

    consoleSpy.mockRestore();
  });

  test('handles prop updates during component lifecycle', () => {
    const { rerender } = render(<QRVerificationDialog {...mockProps} />);

    // Update props
    const updatedProps = {
      ...mockProps,
      partnerName: 'Charlie',
      localFingerprint: 'NEW123',
      partnerFingerprint: 'NEW456'
    };

    rerender(<QRVerificationDialog {...updatedProps} />);

    // Should handle prop updates
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
  });

  test('handles accessibility features', () => {
    render(<QRVerificationDialog {...mockProps} />);

    // Should have proper ARIA attributes
    const dialog = screen.getByRole('dialog');
    expect(dialog).toBeInTheDocument();

    // Should have accessible close button
    const closeButton = screen.getByText('✕');
    expect(closeButton).toBeInTheDocument();

    // Should have proper tab structure
    expect(screen.getByText('QR Code')).toBeInTheDocument();
    expect(screen.getByText('Fingerprint')).toBeInTheDocument();
    expect(screen.getByText('Secret Q&A')).toBeInTheDocument();
  });

  test('handles component unmounting cleanup', () => {
    const { unmount } = render(<QRVerificationDialog {...mockProps} />);

    // Should unmount without errors
    expect(() => unmount()).not.toThrow();
  });

  test('handles edge cases in fingerprint comparison', async () => {
    const user = userEvent.setup();
    
    const edgeCaseProps = {
      ...mockProps,
      localFingerprint: null,
      partnerFingerprint: undefined
    };

    render(<QRVerificationDialog {...edgeCaseProps} />);

    // Switch to fingerprint tab
    const fingerprintTab = screen.getByText('Fingerprint');
    await user.click(fingerprintTab);

    // Should handle null/undefined fingerprints gracefully
    expect(screen.getByText('Compare these fingerprints')).toBeInTheDocument();
  });
});
