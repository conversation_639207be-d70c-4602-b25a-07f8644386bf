import { OtrSession } from '../utils/otrMock';

describe('OtrSession Mock', () => {
  let session;
  let mockSendMessage;

  beforeEach(() => {
    mockSendMessage = jest.fn();
    session = new OtrSession('bob', {
      sendMessage: mockSendMessage
    });
  });

  test('creates OTR session with correct parameters', () => {
    expect(session.peer).toBe('bob');
    expect(session.sendMessage).toBe(mockSendMessage);
    expect(session.state).toBe(0); // PLAINTEXT
  });

  test('initializes session successfully', async () => {
    const result = await session.init();
    expect(result).toBe(true);
    expect(session.initializing).toBe(false);
  });

  test('starts OTR session', async () => {
    await session.init();
    const result = await session.startOtr();
    expect(result).toBe(true);
    expect(session.state).toBe(1); // ENCRYPTED
    expect(session.ready).toBe(true);
    expect(mockSendMessage).toHaveBeenCalledWith('?OTRv3?');
  });

  test('ends OTR session', async () => {
    await session.init();
    await session.startOtr();
    const result = await session.endOtr();
    expect(result).toBe(true);
    expect(session.state).toBe(0); // PLAINTEXT
    expect(session.ready).toBe(false);
    expect(mockSendMessage).toHaveBeenCalledWith('?OTR END?');
  });

  test('processes outgoing messages when OTR is active', async () => {
    await session.init();
    await session.startOtr();

    const result = await session.processOutgoing('Hello, this is a test message');
    expect(result).toBe('?OTR:SGVsbG8sIHRoaXMgaXMgYSB0ZXN0IG1lc3NhZ2U=?');
  });

  test('processes outgoing messages when OTR is not active', async () => {
    await session.init();

    const result = await session.processOutgoing('Hello, this is a test message');
    expect(result).toBe('Hello, this is a test message');
  });

  test('processes incoming OTR commit messages', async () => {
    await session.init();

    const commitMessage = '?OTRv3?';
    const result = await session.processIncoming(commitMessage);

    expect(result).toEqual({
      message: null,
      internal: true,
      encrypted: false
    });

    // Should send a response
    await new Promise(resolve => setTimeout(resolve, 600));
    expect(mockSendMessage).toHaveBeenCalledWith('?OTR:KEY?');
  });

  test('processes incoming plaintext messages', async () => {
    await session.init();

    const result = await session.processIncoming('Hello from the other side');

    expect(result).toEqual({
      message: 'Hello from the other side',
      internal: false,
      encrypted: false
    });
  });

  test('processes incoming encrypted messages', async () => {
    await session.init();
    await session.startOtr();

    const encryptedMessage = '?OTR:SGVsbG8gZnJvbSB0aGUgb3RoZXIgc2lkZQ==?';
    const result = await session.processIncoming(encryptedMessage);

    expect(result).toEqual({
      message: 'Hello from the other side',
      internal: false,
      encrypted: true
    });
  });

  test('handles OTR key exchange messages', async () => {
    await session.init();

    const keyMessage = '?OTR:KEY?';
    const result = await session.processIncoming(keyMessage);

    expect(result).toEqual({
      message: null,
      internal: true,
      encrypted: false
    });

    // Should send a signature response
    await new Promise(resolve => setTimeout(resolve, 600));
    expect(mockSendMessage).toHaveBeenCalledWith('?OTR:SIG?');
  });

  test('handles OTR signature messages', async () => {
    await session.init();

    const sigMessage = '?OTR:SIG?';
    const result = await session.processIncoming(sigMessage);

    expect(result).toEqual({
      message: 'Secure OTR session established',
      internal: false,
      encrypted: true
    });

    expect(session.state).toBe(1); // ENCRYPTED
    expect(session.ready).toBe(true);
  });

  test('handles OTR end messages', async () => {
    await session.init();
    await session.startOtr();

    const endMessage = '?OTR END?';
    const result = await session.processIncoming(endMessage);

    expect(result).toEqual({
      message: 'OTR session ended',
      internal: false,
      encrypted: false
    });

    expect(session.state).toBe(0); // PLAINTEXT
    expect(session.ready).toBe(false);
  });

  test('handles errors gracefully in processOutgoing', async () => {
    // Mock sendMessage to throw an error
    mockSendMessage.mockImplementation(() => {
      throw new Error('Network error');
    });

    await session.init();

    // Should not throw, but return the original message
    const result = await session.processOutgoing('test message');
    expect(result).toBe('test message');
  });

  test('handles different session peers', () => {
    const session1 = new OtrSession('alice', { sendMessage: jest.fn() });
    const session2 = new OtrSession('bob', { sendMessage: jest.fn() });

    expect(session1.peer).toBe('alice');
    expect(session2.peer).toBe('bob');
  });

  test('handles session state transitions', async () => {
    expect(session.state).toBe(0); // PLAINTEXT
    expect(session.ready).toBe(false);

    await session.init();
    expect(session.initializing).toBe(false);

    await session.startOtr();
    expect(session.state).toBe(1); // ENCRYPTED
    expect(session.ready).toBe(true);

    await session.endOtr();
    expect(session.state).toBe(0); // PLAINTEXT
    expect(session.ready).toBe(false);
  });

  test('handles concurrent operations', async () => {
    await session.init();

    // Start multiple operations concurrently
    const promises = [
      session.startOtr(),
      session.processOutgoing('message1'),
      session.processOutgoing('message2')
    ];

    const results = await Promise.all(promises);
    expect(results[0]).toBe(true); // startOtr
    expect(typeof results[1]).toBe('string'); // processOutgoing returns message
    expect(typeof results[2]).toBe('string'); // processOutgoing returns message
  });

  test('simulates realistic OTR conversation flow', async () => {
    // Create two sessions for Alice and Bob
    const aliceSession = new OtrSession('bob', { sendMessage: jest.fn() });
    const bobSession = new OtrSession('alice', { sendMessage: jest.fn() });

    // Initialize both sessions
    await aliceSession.init();
    await bobSession.init();

    // Alice starts OTR
    await aliceSession.startOtr();

    // Bob responds (simulated)
    await bobSession.startOtr();

    // Alice sends a message
    const encryptedMessage = await aliceSession.processOutgoing('Hello Bob!');

    // Bob processes the encrypted message
    const result = await bobSession.processIncoming(encryptedMessage);
    expect(result.encrypted).toBe(true);
    expect(result.message).toBe('Hello Bob!');

    // Verify both sessions are active
    expect(aliceSession.state).toBe(1); // ENCRYPTED
    expect(bobSession.state).toBe(1); // ENCRYPTED
  });

  test('handles edge cases in message processing', async () => {
    await session.init();
    await session.startOtr();

    // Test empty message
    const result1 = await session.processOutgoing('');
    expect(result1).toBe('?OTR:?');

    // Test very long message
    const longMessage = 'A'.repeat(100);
    const result2 = await session.processOutgoing(longMessage);
    expect(result2).toContain('?OTR:');

    // Test message with special characters (this may fail due to btoa limitations)
    const specialMessage = 'Special chars: test';
    const result3 = await session.processOutgoing(specialMessage);
    expect(result3).toContain('?OTR:');
  });

  test('handles malformed encrypted messages', async () => {
    await session.init();

    // Test malformed encrypted message
    const malformedMessage = '?OTR:invalid_base64!@#$%?';
    const result = await session.processIncoming(malformedMessage);

    expect(result.message).toBe('Failed to decrypt message');
    expect(result.encrypted).toBe(false);
  });

  test('handles error in processIncoming', async () => {
    await session.init();

    // Mock console.error to avoid noise in test output
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    // Force an error by passing null
    const result = await session.processIncoming(null);

    expect(result.message).toBe('Error processing message');
    expect(result.encrypted).toBe(false);
    expect(result.internal).toBe(false);

    consoleSpy.mockRestore();
  });

  test('maintains session isolation', () => {
    const session1 = new OtrSession('alice', { sendMessage: jest.fn() });
    const session2 = new OtrSession('bob', { sendMessage: jest.fn() });

    // Sessions should have different peers
    expect(session1.peer).toBe('alice');
    expect(session2.peer).toBe('bob');

    // State changes in one shouldn't affect the other
    session1.state = 1;
    expect(session2.state).toBe(0);
  });

  test('handles OTR query message processing', async () => {
    await session.init();

    const queryResult = await session.processOutgoing('?OTR?');
    expect(queryResult).toBeNull();
    expect(session.state).toBe(1); // Should start OTR
  });
});
