import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import App from '../App';
import ChatPanel from '../components/ChatPanel';
import NetworkPanel from '../components/NetworkPanel';
import { loadMessages, saveMessages } from '../utils/storage';

// Mock the storage functions
jest.mock('../utils/storage', () => ({
  loadMessages: jest.fn(),
  saveMessages: jest.fn().mockReturnValue(true),
  clearMessages: jest.fn().mockReturnValue(true),
}));

describe('Edge Cases and Error Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    loadMessages.mockReturnValue([]);
    Element.prototype.scrollIntoView = jest.fn();
    global.alert = jest.fn();
  });

  test('handles extremely long messages', async () => {
    const user = userEvent.setup();
    const longMessage = 'A'.repeat(10000);
    
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn()
    };

    render(<ChatPanel {...mockProps} />);

    const input = screen.getByPlaceholderText(/Type a message/i);
    await user.type(input, longMessage);

    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    expect(mockProps.onSendMessage).toHaveBeenCalledWith('alice', longMessage, 'text');
  });

  test('handles special characters and emojis in messages', async () => {
    const user = userEvent.setup();
    const specialMessage = '🔒💬🚀 Special: <>&"\' \n\t\r';
    
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn()
    };

    render(<ChatPanel {...mockProps} />);

    const input = screen.getByPlaceholderText(/Type a message/i);
    await user.type(input, specialMessage);

    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    expect(mockProps.onSendMessage).toHaveBeenCalledWith('alice', specialMessage, 'text');
  });

  test('handles rapid message sending', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

    // Send multiple messages rapidly
    for (let i = 0; i < 5; i++) {
      await act(async () => {
        await user.type(aliceInput, `Rapid message ${i}`);
        await user.click(aliceSendButton);
      });
    }

    // Should handle all messages
    await waitFor(() => {
      expect(screen.getByText(/ALICE → BOB/i)).toBeInTheDocument();
    }, { timeout: 1000 });
  });

  test('handles corrupted message data', () => {
    const corruptedMessages = [
      { id: null, sender: 'alice', content: 'No ID' },
      { id: 'valid', sender: null, content: 'No sender' },
      { id: 'valid2', sender: 'alice', content: null },
      { id: 'valid3', sender: 'alice', content: 'Valid', timestamp: 'invalid-date' }
    ];

    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: corruptedMessages,
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn()
    };

    // Should not crash with corrupted data
    expect(() => {
      render(<ChatPanel {...mockProps} />);
    }).not.toThrow();
  });

  test('handles network panel with malformed messages', () => {
    const malformedMessages = [
      { id: 'msg1' }, // Missing required fields
      { id: 'msg2', sender: 'alice' }, // Missing recipient
      { id: 'msg3', sender: 'alice', recipient: 'bob' }, // Missing content
      { 
        id: 'msg4', 
        sender: 'alice', 
        recipient: 'bob', 
        content: 'Valid message',
        timestamp: 'invalid-timestamp',
        status: 'unknown-status'
      }
    ];

    // Should not crash with malformed data
    expect(() => {
      render(<NetworkPanel messages={malformedMessages} />);
    }).not.toThrow();
  });

  test('handles file upload errors', async () => {
    const user = userEvent.setup();
    
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn()
    };

    render(<ChatPanel {...mockProps} />);

    const dropArea = screen.getByPlaceholderText(/Type a message/i).closest('.message-input-container');
    
    // Mock FileReader to throw an error
    global.FileReader = jest.fn().mockImplementation(() => {
      throw new Error('FileReader error');
    });

    const file = new File(['test'], 'test.png', { type: 'image/png' });

    // Should handle FileReader error gracefully
    expect(() => {
      fireEvent.drop(dropArea, {
        dataTransfer: { files: [file] }
      });
    }).not.toThrow();
  });

  test('handles localStorage quota exceeded', async () => {
    const user = userEvent.setup();
    
    // Mock saveMessages to throw quota exceeded error
    saveMessages.mockImplementation(() => {
      throw new Error('QuotaExceededError');
    });

    await act(async () => {
      render(<App />);
    });

    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

    await act(async () => {
      await user.type(aliceInput, 'Test message');
      await user.click(aliceSendButton);
    });

    // Should handle storage error gracefully
    expect(saveMessages).toHaveBeenCalled();
  });

  test('handles window resize events', async () => {
    await act(async () => {
      render(<App />);
    });

    // Simulate window resize
    global.innerWidth = 500;
    global.innerHeight = 600;
    fireEvent(window, new Event('resize'));

    // Should not crash on resize
    expect(screen.getByText('Alice')).toBeInTheDocument();
  });

  test('handles focus and blur events', async () => {
    const user = userEvent.setup();
    
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn()
    };

    render(<ChatPanel {...mockProps} />);

    const input = screen.getByPlaceholderText(/Type a message/i);
    
    // Focus and blur the input
    await user.click(input);
    fireEvent.blur(input);
    await user.click(input);

    // Should handle focus events without issues
    expect(input).toBeInTheDocument();
  });

  test('handles keyboard shortcuts and special keys', async () => {
    const user = userEvent.setup();
    
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn()
    };

    render(<ChatPanel {...mockProps} />);

    const input = screen.getByPlaceholderText(/Type a message/i);
    
    // Test various keyboard combinations
    await user.type(input, 'Test message');
    await user.keyboard('{Control>}a{/Control}'); // Ctrl+A
    await user.keyboard('{Control>}c{/Control}'); // Ctrl+C
    await user.keyboard('{Control>}v{/Control}'); // Ctrl+V
    await user.keyboard('{Escape}'); // Escape
    await user.keyboard('{Tab}'); // Tab

    // Should handle keyboard events gracefully
    expect(input).toBeInTheDocument();
  });

  test('handles concurrent user interactions', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const bobInput = screen.getAllByPlaceholderText(/Type a message/i)[1];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];
    const bobSendButton = screen.getAllByRole('button', { name: /send/i })[1];

    // Simulate concurrent interactions
    await act(async () => {
      await Promise.all([
        user.type(aliceInput, 'Alice message'),
        user.type(bobInput, 'Bob message')
      ]);
    });

    await act(async () => {
      await Promise.all([
        user.click(aliceSendButton),
        user.click(bobSendButton)
      ]);
    });

    // Should handle concurrent interactions
    await waitFor(() => {
      expect(screen.getByText(/ALICE → BOB/i)).toBeInTheDocument();
    }, { timeout: 1000 });
  });

  test('handles memory pressure scenarios', async () => {
    // Create a large number of messages to simulate memory pressure
    const manyMessages = Array.from({ length: 1000 }, (_, i) => ({
      id: `msg_${i}`,
      sender: i % 2 === 0 ? 'alice' : 'bob',
      recipient: i % 2 === 0 ? 'bob' : 'alice',
      content: `Message ${i} with some content to use memory`,
      timestamp: new Date(Date.now() - i * 1000).toISOString(),
      status: 'delivered',
      type: 'text'
    }));

    loadMessages.mockReturnValue(manyMessages);

    // Should handle large datasets without crashing
    await act(async () => {
      render(<App />);
    });

    expect(screen.getByText('Alice')).toBeInTheDocument();
    expect(screen.getByText('Bob')).toBeInTheDocument();
  });
});
