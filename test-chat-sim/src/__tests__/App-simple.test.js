import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import App from '../App';
import { loadMessages, saveMessages } from '../utils/storage';

// Mock the storage functions
jest.mock('../utils/storage', () => ({
  loadMessages: jest.fn(),
  saveMessages: jest.fn().mockReturnValue(true),
  clearMessages: jest.fn().mockReturnValue(true),
}));

describe('App Component - Simple Coverage Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    loadMessages.mockReturnValue([]);
    Element.prototype.scrollIntoView = jest.fn();
    global.alert = jest.fn();
    localStorage.clear();
    document.documentElement.removeAttribute('data-theme');
  });

  test('renders the main app components', async () => {
    await act(async () => {
      render(<App />);
    });

    // Check for main app elements
    expect(screen.getByText('WebOTR Test Chat Simulator')).toBeInTheDocument();
    expect(screen.getByText('Alice')).toBeInTheDocument();
    expect(screen.getByText('Bob')).toBeInTheDocument();
    expect(screen.getByText('Network Traffic')).toBeInTheDocument();
  });

  test('handles dark mode toggle', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    const darkModeToggle = screen.getByRole('checkbox', { name: /dark mode/i });
    
    // Toggle dark mode on
    await act(async () => {
      await user.click(darkModeToggle);
    });

    expect(document.documentElement.getAttribute('data-theme')).toBe('dark');

    // Toggle dark mode off
    await act(async () => {
      await user.click(darkModeToggle);
    });

    expect(document.documentElement.getAttribute('data-theme')).toBeNull();
  });

  test('handles message sending between users', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    // Send message from Alice
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

    await act(async () => {
      await user.type(aliceInput, 'Hello from Alice!');
      await user.click(aliceSendButton);
    });

    // Message should appear in chat
    expect(screen.getByText('Hello from Alice!')).toBeInTheDocument();

    // Should save messages
    expect(saveMessages).toHaveBeenCalled();
  });

  test('handles message clearing', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    // Send a message first
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

    await act(async () => {
      await user.type(aliceInput, 'Test message');
      await user.click(aliceSendButton);
    });

    // Clear messages
    const aliceClearButton = screen.getAllByTitle('Clear Messages')[0];
    await act(async () => {
      await user.click(aliceClearButton);
    });

    // Message should be gone
    expect(screen.queryByText('Test message')).not.toBeInTheDocument();
  });

  test('loads existing messages on startup', async () => {
    const existingMessages = [
      {
        id: 'test-1',
        sender: 'alice',
        recipient: 'bob',
        content: 'Existing message',
        timestamp: new Date().toISOString(),
        status: 'delivered',
        type: 'text'
      }
    ];
    
    loadMessages.mockReturnValue(existingMessages);

    await act(async () => {
      render(<App />);
    });

    // Should load and display existing messages (appears in both Alice and Bob panels)
    const messageElements = screen.getAllByText('Existing message');
    expect(messageElements.length).toBeGreaterThan(0);
  });

  test('handles OTR toggle availability', async () => {
    await act(async () => {
      render(<App />);
    });

    // OTR toggle should be present
    const otrToggle = screen.getByRole('checkbox', { name: /otr/i });
    expect(otrToggle).toBeInTheDocument();
    
    // Initially might be disabled while sessions initialize
    // This is expected behavior
  });

  test('handles theme persistence setup', async () => {
    // This test verifies the theme persistence mechanism works
    // by checking that the component can handle theme changes
    await act(async () => {
      render(<App />);
    });

    // Verify the app renders correctly
    expect(screen.getByText('WebOTR Test Chat Simulator')).toBeInTheDocument();

    // Verify dark mode toggle exists and can be interacted with
    const darkModeToggle = screen.getByRole('checkbox', { name: /dark mode/i });
    expect(darkModeToggle).toBeInTheDocument();
  });

  test('handles system theme preference', async () => {
    // Mock matchMedia for system preference
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    await act(async () => {
      render(<App />);
    });

    // Should use system preference when no saved theme
    expect(document.documentElement.getAttribute('data-theme')).toBe('dark');
  });

  test('handles matchMedia error gracefully', async () => {
    // Mock matchMedia to throw error
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(() => {
        throw new Error('matchMedia error');
      }),
    });

    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

    await act(async () => {
      render(<App />);
    });

    // Should default to light mode and log error
    expect(document.documentElement.getAttribute('data-theme')).toBeNull();
    expect(consoleSpy).toHaveBeenCalledWith('matchMedia not available, defaulting to light mode');
    
    consoleSpy.mockRestore();
  });

  test('handles message loading errors', async () => {
    // Mock loadMessages to throw error
    loadMessages.mockImplementation(() => {
      throw new Error('Loading failed');
    });

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    await act(async () => {
      render(<App />);
    });

    // Should handle error gracefully
    expect(consoleSpy).toHaveBeenCalledWith('Error loading messages:', expect.any(Error));
    
    consoleSpy.mockRestore();
  });

  test('handles network message display', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    // Send a message to generate network traffic
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

    await act(async () => {
      await user.type(aliceInput, 'Network test message');
      await user.click(aliceSendButton);
    });

    // Should show in network panel
    await waitFor(() => {
      expect(screen.getByText(/ALICE → BOB/i)).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  test('handles multiple message types', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    // Send text message
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

    await act(async () => {
      await user.type(aliceInput, 'Text message');
      await user.click(aliceSendButton);
    });

    // Send message from Bob
    const bobInput = screen.getAllByPlaceholderText(/Type a message/i)[1];
    const bobSendButton = screen.getAllByRole('button', { name: /send/i })[1];

    await act(async () => {
      await user.type(bobInput, 'Bob response');
      await user.click(bobSendButton);
    });

    // Both messages should appear
    expect(screen.getByText('Text message')).toBeInTheDocument();
    expect(screen.getByText('Bob response')).toBeInTheDocument();
  });

  test('handles component unmounting', async () => {
    const { unmount } = render(<App />);

    // Should unmount without errors
    expect(() => unmount()).not.toThrow();
  });
});
