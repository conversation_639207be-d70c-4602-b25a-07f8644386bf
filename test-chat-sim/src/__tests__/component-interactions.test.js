import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import ChatPanel from '../components/ChatPanel';
import NetworkPanel from '../components/NetworkPanel';
import { QRCodeVerifier } from '../ui/components/verification/QRCodeVerifier';

describe('Component Interactions and Advanced Scenarios', () => {
  beforeEach(() => {
    Element.prototype.scrollIntoView = jest.fn();
    global.alert = jest.fn();
  });

  describe('ChatPanel Advanced Interactions', () => {
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn(),
      darkMode: false,
      onEditMessage: jest.fn(),
      otrEnabled: false,
      onInitiateSMP: jest.fn(),
      onRespondSMP: jest.fn(),
      onVerificationComplete: jest.fn(),
      smpState: 'none'
    };

    test('handles complex message input scenarios', async () => {
      const user = userEvent.setup();
      render(<ChatPanel {...mockProps} />);

      const input = screen.getByPlaceholderText(/Type a message/i);

      // Test various input scenarios
      await user.type(input, 'Hello');
      await user.keyboard('{Control>}a{/Control}'); // Select all
      await user.type(input, 'Replaced text');
      
      const sendButton = screen.getByRole('button', { name: /send/i });
      await user.click(sendButton);

      expect(mockProps.onSendMessage).toHaveBeenCalledWith('alice', 'Replaced text', 'text');
    });

    test('handles emoji picker interactions', async () => {
      const user = userEvent.setup();
      render(<ChatPanel {...mockProps} />);

      const emojiButton = screen.queryByLabelText('Open emoji picker');
      if (emojiButton) {
        await user.click(emojiButton);
        
        // Should show emoji picker
        const emojiPicker = screen.queryByClassName('emoji-picker');
        if (emojiPicker) {
          expect(emojiPicker).toBeInTheDocument();
        }
      }
    });

    test('handles GIF picker interactions', async () => {
      const user = userEvent.setup();
      render(<ChatPanel {...mockProps} />);

      const gifButton = screen.queryByLabelText('Open GIF picker');
      if (gifButton) {
        await user.click(gifButton);
        
        // Should show GIF picker
        const gifPicker = screen.queryByPlaceholderText('Search for GIFs...');
        if (gifPicker) {
          expect(gifPicker).toBeInTheDocument();
          
          // Test GIF search
          await user.type(gifPicker, 'happy');
          // GIF results would appear here in real implementation
        }
      }
    });

    test('handles drag and drop with multiple files', async () => {
      render(<ChatPanel {...mockProps} />);

      const inputContainer = screen.getByPlaceholderText(/Type a message/i).closest('.message-input-container');
      
      // Create multiple files
      const files = [
        new File(['image1'], 'image1.png', { type: 'image/png' }),
        new File(['image2'], 'image2.jpg', { type: 'image/jpeg' }),
        new File(['doc'], 'document.pdf', { type: 'application/pdf' })
      ];

      // Mock FileReader for each file
      const mockFileReaders = files.map((file, index) => ({
        readAsDataURL: jest.fn(),
        onload: null,
        result: `data:${file.type};base64,mockdata${index}`
      }));

      let readerIndex = 0;
      global.FileReader = jest.fn(() => mockFileReaders[readerIndex++]);

      // Simulate drop with multiple files
      fireEvent.drop(inputContainer, {
        dataTransfer: { files }
      });

      // Only image files should be processed
      expect(global.FileReader).toHaveBeenCalledTimes(2); // Only for PNG and JPG
    });

    test('handles SMP verification workflow', async () => {
      const user = userEvent.setup();
      const smpProps = {
        ...mockProps,
        otrEnabled: true,
        smpState: 'pending',
        smpQuestion: 'What is our shared secret?'
      };

      render(<ChatPanel {...smpProps} />);

      // Should show SMP pending state
      expect(screen.getByTitle('Verification Pending')).toBeInTheDocument();

      // Look for SMP response input
      const smpInput = screen.queryByPlaceholderText(/Enter your answer/i);
      if (smpInput) {
        await user.type(smpInput, 'shared secret answer');
        
        const respondButton = screen.queryByText('Respond');
        if (respondButton) {
          await user.click(respondButton);
          expect(smpProps.onRespondSMP).toHaveBeenCalledWith('alice', 'shared secret answer');
        }
      }
    });

    test('handles message context menu interactions', async () => {
      const user = userEvent.setup();
      const messagesProps = {
        ...mockProps,
        messages: [
          {
            id: 'msg1',
            sender: 'alice',
            content: 'Test message',
            timestamp: new Date().toISOString(),
            type: 'text'
          }
        ]
      };

      render(<ChatPanel {...messagesProps} />);

      const messageElement = screen.getByText('Test message');
      fireEvent.contextMenu(messageElement);

      // Look for context menu options
      const editOption = screen.queryByText('Edit message');
      const deleteOption = screen.queryByText('Delete message');
      const copyOption = screen.queryByText('Copy message');

      if (editOption) {
        await user.click(editOption);
        // Edit functionality would be triggered
      }
    });
  });

  describe('NetworkPanel Advanced Interactions', () => {
    const networkMessages = [
      {
        id: 'net1',
        sender: 'alice',
        recipient: 'bob',
        content: 'Network message 1',
        timestamp: '2025-03-22T10:30:00.000Z',
        status: 'delivered',
        type: 'text',
        networkTimestamp: '2025-03-22T10:30:00.000Z',
        deliveredTimestamp: '2025-03-22T10:30:01.000Z'
      },
      {
        id: 'net2',
        sender: 'bob',
        recipient: 'alice',
        content: 'data:image/png;base64,fakedata',
        timestamp: '2025-03-22T10:31:00.000Z',
        status: 'transmitting',
        type: 'image',
        networkTimestamp: '2025-03-22T10:31:00.000Z'
      }
    ];

    test('handles message popout interactions', async () => {
      const user = userEvent.setup();
      render(<NetworkPanel messages={networkMessages} />);

      // Click on first message
      const firstMessage = screen.getByText('Network message 1');
      await user.click(firstMessage);

      // Should show popout
      expect(screen.getByText('Message Transmission Details')).toBeInTheDocument();

      // Test popout content
      expect(screen.getByText('ALICE')).toBeInTheDocument();
      expect(screen.getByText('BOB')).toBeInTheDocument();

      // Close popout
      const closeButton = screen.getByText('×');
      await user.click(closeButton);

      expect(screen.queryByText('Message Transmission Details')).not.toBeInTheDocument();
    });

    test('handles different message types in popout', async () => {
      const user = userEvent.setup();
      render(<NetworkPanel messages={networkMessages} />);

      // Click on image message
      const imageMessage = screen.getByText(/\[Image\]/i);
      await user.click(imageMessage);

      // Should show image in popout
      expect(screen.getByText('Message Transmission Details')).toBeInTheDocument();
      expect(screen.getByAltText('Image transmission')).toBeInTheDocument();
    });

    test('handles network message filtering and search', async () => {
      const user = userEvent.setup();
      render(<NetworkPanel messages={networkMessages} />);

      // Look for filter controls (if implemented)
      const filterInput = screen.queryByPlaceholderText(/Filter messages/i);
      if (filterInput) {
        await user.type(filterInput, 'alice');
        // Should filter to show only Alice's messages
      }

      // Look for status filter
      const statusFilter = screen.queryByLabelText(/Filter by status/i);
      if (statusFilter) {
        await user.selectOptions(statusFilter, 'delivered');
        // Should show only delivered messages
      }
    });

    test('handles real-time message updates', async () => {
      const { rerender } = render(<NetworkPanel messages={[networkMessages[0]]} />);

      // Initially shows one message
      expect(screen.getByText('Network message 1')).toBeInTheDocument();

      // Add more messages
      rerender(<NetworkPanel messages={networkMessages} />);

      // Should show both messages
      expect(screen.getByText('Network message 1')).toBeInTheDocument();
      expect(screen.getByText(/\[Image\]/i)).toBeInTheDocument();
    });
  });

  describe('QRCodeVerifier Component Integration', () => {
    let container;

    beforeEach(() => {
      container = document.createElement('div');
      document.body.appendChild(container);
    });

    afterEach(() => {
      if (container && container.parentNode) {
        container.parentNode.removeChild(container);
      }
    });

    test('integrates with verification dialog workflow', () => {
      const onScan = jest.fn();
      const onVerificationResult = jest.fn();
      
      const verifier = new QRCodeVerifier(container, {
        onScan,
        onVerificationResult
      });

      // Set fingerprint data
      verifier.setData('alice-fingerprint-123');

      // Simulate QR code scan
      verifier._onQRCodeDetected('bob-fingerprint-456');
      expect(onScan).toHaveBeenCalledWith('bob-fingerprint-456');

      // Verify fingerprints
      const result = verifier.verifyFingerprints('alice-fingerprint-123', 'alice-fingerprint-123');
      expect(result).toBe(true);
      expect(onVerificationResult).toHaveBeenCalledWith(true);

      verifier.destroy();
    });

    test('handles camera permissions and errors', () => {
      const verifier = new QRCodeVerifier(container);

      // Mock getUserMedia to simulate permission denied
      global.navigator.mediaDevices = {
        getUserMedia: jest.fn().mockRejectedValue(new Error('Permission denied'))
      };

      // Should handle camera errors gracefully
      expect(() => verifier.startScanning()).not.toThrow();

      verifier.destroy();
    });

    test('handles QR code generation errors', async () => {
      const verifier = new QRCodeVerifier(container);

      // Should handle generation errors gracefully
      const result = await verifier.generateQRCode();
      expect(result).toBeDefined();

      verifier.destroy();
    });
  });

  describe('Cross-Component Communication', () => {
    test('message flow from ChatPanel to NetworkPanel', async () => {
      const user = userEvent.setup();
      const messages = [];
      const onSendMessage = jest.fn((sender, content, type) => {
        const newMessage = {
          id: `msg-${Date.now()}`,
          sender,
          recipient: sender === 'alice' ? 'bob' : 'alice',
          content,
          type,
          timestamp: new Date().toISOString(),
          status: 'transmitting',
          networkTimestamp: new Date().toISOString()
        };
        messages.push(newMessage);
      });

      const chatProps = {
        user: 'alice',
        displayName: 'Alice',
        messages: [],
        onSendMessage,
        onClearMessages: jest.fn(),
        darkMode: false,
        onEditMessage: jest.fn(),
        otrEnabled: false,
        onInitiateSMP: jest.fn(),
        onRespondSMP: jest.fn(),
        onVerificationComplete: jest.fn(),
        smpState: 'none'
      };

      const { rerender } = render(
        <div>
          <ChatPanel {...chatProps} />
          <NetworkPanel messages={messages} />
        </div>
      );

      // Send message from chat
      const input = screen.getByPlaceholderText(/Type a message/i);
      const sendButton = screen.getByRole('button', { name: /send/i });

      await user.type(input, 'Cross-component test');
      await user.click(sendButton);

      // Update NetworkPanel with new message
      rerender(
        <div>
          <ChatPanel {...chatProps} />
          <NetworkPanel messages={messages} />
        </div>
      );

      // Should show message in network panel
      expect(screen.getByText(/ALICE → BOB/i)).toBeInTheDocument();
    });

    test('theme consistency across components', () => {
      const chatProps = {
        user: 'alice',
        displayName: 'Alice',
        messages: [],
        onSendMessage: jest.fn(),
        onClearMessages: jest.fn(),
        darkMode: true,
        onEditMessage: jest.fn(),
        otrEnabled: false,
        onInitiateSMP: jest.fn(),
        onRespondSMP: jest.fn(),
        onVerificationComplete: jest.fn(),
        smpState: 'none'
      };

      render(
        <div>
          <ChatPanel {...chatProps} />
          <NetworkPanel messages={[]} darkMode={true} />
        </div>
      );

      // Both components should have dark mode classes
      const chatPanel = screen.getByTestId('alice-panel');
      expect(chatPanel).toHaveClass('dark-mode');
    });

    test('OTR state synchronization', () => {
      const chatProps = {
        user: 'alice',
        displayName: 'Alice',
        messages: [],
        onSendMessage: jest.fn(),
        onClearMessages: jest.fn(),
        darkMode: false,
        onEditMessage: jest.fn(),
        otrEnabled: true,
        onInitiateSMP: jest.fn(),
        onRespondSMP: jest.fn(),
        onVerificationComplete: jest.fn(),
        smpState: 'verified'
      };

      render(<ChatPanel {...chatProps} />);

      // Should show OTR indicators
      expect(screen.getByTitle('OTR Encryption Enabled')).toBeInTheDocument();
      expect(screen.getByTitle('Identity Verified')).toBeInTheDocument();
    });
  });

  describe('Error Boundary and Recovery', () => {
    test('handles component errors gracefully', () => {
      const ErrorComponent = () => {
        throw new Error('Component error');
      };

      // Should not crash the entire app
      expect(() => {
        render(<ErrorComponent />);
      }).toThrow('Component error');
    });

    test('handles prop validation errors', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      // Pass invalid props
      render(<ChatPanel user={null} displayName="" messages={null} />);

      // Should handle gracefully
      expect(screen.getByText('Send')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    test('handles missing callback functions', async () => {
      const user = userEvent.setup();
      
      // Render with missing callbacks
      render(
        <ChatPanel
          user="alice"
          displayName="Alice"
          messages={[]}
          onSendMessage={undefined}
          onClearMessages={undefined}
          darkMode={false}
        />
      );

      const input = screen.getByPlaceholderText(/Type a message/i);
      const sendButton = screen.getByRole('button', { name: /send/i });

      // Should not crash when clicking send
      await user.type(input, 'Test');
      expect(() => user.click(sendButton)).not.toThrow();
    });
  });
});
