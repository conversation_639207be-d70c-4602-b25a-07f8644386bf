/**
 * Tests for OTR protocol negotiation sequence
 * 
 * NOTE: These tests import from outside the src/ directory and 
 * cannot be run directly with CRA's test environment.
 * They need to be run in a proper Node.js environment with direct 
 * access to the WebOTR library.
 */
import { OtrSession } from '../../../src/core/session';
import { STATE, OtrState } from '../../../src/core/protocol';

describe('OTR Protocol Negotiation', () => {
  let aliceSession;
  let bobSession;
  let aliceMessages = [];
  let bobMessages = [];
  
  // Track all messages sent to verify the sequence
  const aliceSendMessage = jest.fn(message => {
    bobMessages.push(message);
  });
  
  const bobSendMessage = jest.fn(message => {
    aliceMessages.push(message);
  });
  
  beforeEach(async () => {
    // Reset messages
    aliceMessages = [];
    bobMessages = [];
    
    // Create sessions
    aliceSession = new OtrSession('bob', {
      sendMessage: aliceSendMessage,
      version: 3
    });
    
    bobSession = new OtrSession('alice', {
      sendMessage: bobSendMessage,
      version: 3
    });
    
    // Initialize both sessions
    await Promise.all([
      aliceSession.init(),
      bobSession.init()
    ]);
  });
  
  afterEach(() => {
    // Clean up
    aliceSession = null;
    bobSession = null;
  });
  
  test('OTR negotiation should follow correct sequence', async () => {
    // Initial state should be PLAINTEXT
    expect(aliceSession.state.getState()).toBe(STATE.PLAINTEXT);
    expect(bobSession.state.getState()).toBe(STATE.PLAINTEXT);
    
    // Step 1: Alice initiates OTR
    await aliceSession.startOtr();
    
    // Alice should have sent a DH Commit message
    expect(aliceSendMessage).toHaveBeenCalled();
    expect(bobMessages.length).toBe(1);
    
    // Alice's state should be AWAITING_DHKEY
    // (Check against actual state in OtrState implementation)
    
    // Step 2: Bob receives DH Commit and responds with DH Key
    const bobResponse1 = await bobSession.processIncoming(bobMessages[0]);
    
    // Bob should have responded with a DH Key message
    expect(bobSendMessage).toHaveBeenCalled();
    expect(aliceMessages.length).toBe(1);
    
    // Bob's state should be AWAITING_REVEALSIG
    
    // Step 3: Alice receives DH Key and responds with Reveal Signature
    const aliceResponse1 = await aliceSession.processIncoming(aliceMessages[0]);
    
    // Alice should have sent a Reveal Signature message
    expect(aliceSendMessage).toHaveBeenCalledTimes(2);
    expect(bobMessages.length).toBe(2);
    
    // Step 4: Bob receives Reveal Signature and responds with Signature
    const bobResponse2 = await bobSession.processIncoming(bobMessages[1]);
    
    // Bob should have sent a Signature message
    expect(bobSendMessage).toHaveBeenCalledTimes(2);
    expect(aliceMessages.length).toBe(2);
    
    // Bob should now be in ENCRYPTED state
    expect(bobSession.state.getState()).toBe(STATE.ENCRYPTED);
    
    // Step 5: Alice receives Signature
    const aliceResponse2 = await aliceSession.processIncoming(aliceMessages[1]);
    
    // Alice should now be in ENCRYPTED state
    expect(aliceSession.state.getState()).toBe(STATE.ENCRYPTED);
    
    // Both parties should now be able to exchange encrypted messages
    const testMessage = "Hello, encrypted world!";
    
    // Alice sends message to Bob
    await aliceSession.processOutgoing(testMessage);
    
    // Message should be encrypted (not plaintext)
    expect(bobMessages[2]).not.toEqual(testMessage);
    
    // Bob should be able to decrypt
    const decryptedResult = await bobSession.processIncoming(bobMessages[2]);
    
    // Decrypted message should match original
    expect(decryptedResult.message).toEqual(testMessage);
    expect(decryptedResult.encrypted).toBeTruthy();
  });
  
  test('Protocol should handle version negotiation', async () => {
    // Create sessions with different versions
    const aliceV3 = new OtrSession('bob', {
      sendMessage: aliceSendMessage,
      version: 3
    });
    
    const bobV2 = new OtrSession('alice', {
      sendMessage: bobSendMessage,
      version: 2
    });
    
    await Promise.all([
      aliceV3.init(),
      bobV2.init()
    ]);
    
    // Clear previous messages
    aliceMessages = [];
    bobMessages = [];
    
    // Alice initiates with V3
    await aliceV3.startOtr();
    
    // Should negotiate down to V2 if that's all Bob supports
    // Complete the protocol exchange
    await bobV2.processIncoming(bobMessages[0]);
    await aliceV3.processIncoming(aliceMessages[0]);
    await bobV2.processIncoming(bobMessages[1]);
    await aliceV3.processIncoming(aliceMessages[1]);
    
    // Check if both ended up encrypted
    expect(aliceV3.state.getState()).toBe(STATE.ENCRYPTED);
    expect(bobV2.state.getState()).toBe(STATE.ENCRYPTED);
    
    // The negotiated version should be V2
    expect(aliceV3.state.getVersion()).toBe(2);
    expect(bobV2.state.getVersion()).toBe(2);
  });
}); 