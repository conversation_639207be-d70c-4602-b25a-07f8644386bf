/**
 * Tests for OTR protocol messages
 * 
 * NOTE: These tests import from outside the src/ directory and 
 * cannot be run directly with CRA's test environment.
 * They need to be run in a proper Node.js environment with direct 
 * access to the WebOTR library.
 */
import { OtrSession } from '../../../src/core/session';

describe('OTR Protocol Messages', () => {
  let aliceSession;
  let bobSession;
  let aliceMessages = [];
  let bobMessages = [];

  // Create send message functions that capture messages for testing
  const aliceSendMessage = jest.fn((message) => {
    bobMessages.push(message);
  });

  const bobSendMessage = jest.fn((message) => {
    aliceMessages.push(message);
  });

  beforeEach(async () => {
    // Reset captured messages
    aliceMessages = [];
    bobMessages = [];
    
    // Set up sessions
    aliceSession = new OtrSession('bob', {
      sendMessage: aliceSendMessage,
      version: 3
    });
    
    bobSession = new OtrSession('alice', {
      sendMessage: bobSendMessage,
      version: 3
    });
    
    // Initialize both sessions
    await aliceSession.init();
    await bobSession.init();
  });

  afterEach(() => {
    aliceSession = null;
    bobSession = null;
  });

  test('Query message should be recognized', async () => {
    // Alice sends ?OTR? query message
    await aliceSession.processOutgoing('?OTR?');
    
    // Should start OTR and generate a commit message
    expect(aliceSendMessage).toHaveBeenCalled();
    
    // The sent message should follow OTR format
    const message = aliceSendMessage.mock.calls[0][0];
    expect(message).toMatch(/^\?OTR/);
  });

  test('Full OTR handshake should establish encrypted session', async () => {
    // Start OTR session from Alice
    await aliceSession.startOtr();
    
    // Alice should send the DH commit message
    expect(aliceSendMessage).toHaveBeenCalled();
    
    // Process Alice's message in Bob's session
    for (const message of bobMessages) {
      await bobSession.processIncoming(message);
    }
    
    // Bob should respond with a DH key message
    expect(bobSendMessage).toHaveBeenCalled();
    
    // Now Bob's message should be processed by Alice
    for (const message of aliceMessages) {
      await aliceSession.processIncoming(message);
    }
    
    // This should continue until both are in encrypted state
    // (This is a simplified check - in reality we'd need to pass multiple messages back and forth)
    
    // Reset the arrays to track only new messages
    aliceMessages = [];
    bobMessages = [];
    
    // Try to send an encrypted message from Alice to Bob
    const testMessage = "This is a secret message";
    await aliceSession.processOutgoing(testMessage);
    
    // The message should be encrypted
    expect(bobMessages[0]).not.toEqual(testMessage);
    
    // Bob should be able to decrypt it
    const result = await bobSession.processIncoming(bobMessages[0]);
    
    // Verify the decrypted message matches the original
    expect(result.message).toEqual(testMessage);
    expect(result.encrypted).toBeTruthy();
  });
  
  test('OTR session can be properly ended', async () => {
    // First establish an OTR session
    await aliceSession.startOtr();
    
    // Process all messages to establish the session
    // This is simplified - real tests would need to process all messages
    for (const message of bobMessages) {
      await bobSession.processIncoming(message);
    }
    
    for (const message of aliceMessages) {
      await aliceSession.processIncoming(message);
    }
    
    // Reset message arrays
    aliceMessages = [];
    bobMessages = [];
    
    // Now end the session
    await aliceSession.endOtr();
    
    // Alice should send a message indicating end of session
    expect(aliceSendMessage).toHaveBeenCalled();
    
    // Process the end message in Bob's session
    const result = await bobSession.processIncoming(bobMessages[0]);
    
    // Bob's session should also end
    expect(bobSession.state.getState()).not.toEqual('ENCRYPTED');
  });
}); 