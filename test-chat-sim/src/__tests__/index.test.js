import React from 'react';
import ReactDOM from 'react-dom/client';
import App from '../App';

// Mock ReactDOM.createRoot and render
jest.mock('react-dom/client', () => ({
  createRoot: jest.fn(() => ({
    render: jest.fn()
  }))
}));

// Mock the App component
jest.mock('../App', () => {
  const mockReact = require('react');
  return function MockApp() {
    return mockReact.createElement('div', { 'data-testid': 'mock-app' }, 'Mock App');
  };
});

describe('Application Entry Point', () => {
  let mockRoot;
  let mockCreateRoot;
  let mockRender;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup mock implementations
    mockRender = jest.fn();
    mockRoot = { render: mockRender };
    mockCreateRoot = jest.fn(() => mockRoot);
    ReactDOM.createRoot = mockCreateRoot;

    // Mock document.getElementById
    const mockRootElement = document.createElement('div');
    mockRootElement.id = 'root';
    jest.spyOn(document, 'getElementById').mockReturnValue(mockRootElement);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('creates React root and renders App component', () => {
    // Import the index file to trigger the initialization
    require('../index.js');

    // Verify createRoot was called with the root element
    expect(mockCreateRoot).toHaveBeenCalledWith(
      expect.objectContaining({ id: 'root' })
    );

    // Verify render was called with App wrapped in StrictMode
    expect(mockRender).toHaveBeenCalledWith(
      expect.objectContaining({
        type: React.StrictMode,
        props: expect.objectContaining({
          children: expect.objectContaining({
            type: App
          })
        })
      })
    );
  });

  test('handles missing root element gracefully', () => {
    // Mock getElementById to return null
    document.getElementById.mockReturnValue(null);

    // This should not throw an error in a real scenario
    // but createRoot will be called with null
    require('../index.js');

    expect(mockCreateRoot).toHaveBeenCalledWith(null);
  });

  test('uses React.StrictMode wrapper', () => {
    require('../index.js');

    const renderCall = mockRender.mock.calls[0][0];
    
    // Check that the rendered element is StrictMode
    expect(renderCall.type).toBe(React.StrictMode);
    
    // Check that App is the child of StrictMode
    expect(renderCall.props.children.type).toBe(App);
  });

  test('renders only once on import', () => {
    // Import multiple times
    require('../index.js');
    require('../index.js');
    require('../index.js');

    // Should only render once (due to module caching)
    expect(mockRender).toHaveBeenCalledTimes(1);
  });

  test('handles createRoot errors gracefully', () => {
    // Mock createRoot to throw an error
    mockCreateRoot.mockImplementation(() => {
      throw new Error('Failed to create root');
    });

    // This should throw in a real scenario, but we're testing error handling
    expect(() => {
      require('../index.js');
    }).toThrow('Failed to create root');
  });

  test('handles render errors gracefully', () => {
    // Mock render to throw an error
    mockRender.mockImplementation(() => {
      throw new Error('Failed to render');
    });

    // This should throw in a real scenario
    expect(() => {
      require('../index.js');
    }).toThrow('Failed to render');
  });
});

describe('CSS Import', () => {
  test('imports index.css', () => {
    // This test ensures the CSS import doesn't cause errors
    // The actual CSS loading is handled by the build system
    expect(() => {
      require('../index.js');
    }).not.toThrow();
  });
});

describe('Module Dependencies', () => {
  test('imports required React modules', () => {
    // Verify that React and ReactDOM are properly imported
    const React = require('react');
    const ReactDOM = require('react-dom/client');
    
    expect(React).toBeDefined();
    expect(ReactDOM).toBeDefined();
    expect(ReactDOM.createRoot).toBeDefined();
  });

  test('imports App component', () => {
    const App = require('../App').default;
    expect(App).toBeDefined();
    expect(typeof App).toBe('function');
  });
});

describe('DOM Integration', () => {
  test('targets correct DOM element', () => {
    const mockElement = document.createElement('div');
    mockElement.id = 'root';
    document.getElementById.mockReturnValue(mockElement);

    require('../index.js');

    expect(document.getElementById).toHaveBeenCalledWith('root');
    expect(mockCreateRoot).toHaveBeenCalledWith(mockElement);
  });

  test('works with existing DOM structure', () => {
    // Create a more realistic DOM structure
    const mockBody = document.createElement('body');
    const mockRoot = document.createElement('div');
    mockRoot.id = 'root';
    mockBody.appendChild(mockRoot);
    
    document.getElementById.mockReturnValue(mockRoot);

    require('../index.js');

    expect(mockCreateRoot).toHaveBeenCalledWith(mockRoot);
  });
});

describe('Error Boundaries', () => {
  test('handles App component errors through StrictMode', () => {
    // Mock App to throw an error
    const ErrorApp = () => {
      throw new Error('App component error');
    };
    
    jest.doMock('../App', () => ErrorApp);

    // The error should be caught by React's error handling
    expect(() => {
      require('../index.js');
    }).not.toThrow();
  });
});

describe('Performance Considerations', () => {
  test('uses React 18 createRoot API', () => {
    require('../index.js');

    // Verify we're using the modern createRoot API, not legacy render
    expect(mockCreateRoot).toHaveBeenCalled();
    expect(ReactDOM.render).toBeUndefined();
  });

  test('enables StrictMode for development benefits', () => {
    require('../index.js');

    const renderCall = mockRender.mock.calls[0][0];
    expect(renderCall.type).toBe(React.StrictMode);
  });
});

describe('Browser Compatibility', () => {
  test('works in environments with document object', () => {
    // Ensure document is available (JSDOM provides this in tests)
    expect(document).toBeDefined();
    expect(document.getElementById).toBeDefined();

    expect(() => {
      require('../index.js');
    }).not.toThrow();
  });

  test('handles modern React features', () => {
    // Verify React 18 features are available
    expect(React.StrictMode).toBeDefined();
    expect(ReactDOM.createRoot).toBeDefined();

    require('../index.js');

    expect(mockCreateRoot).toHaveBeenCalled();
  });
});
