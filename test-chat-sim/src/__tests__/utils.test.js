import { loadMessages, saveMessages, clearMessages } from '../utils/storage';
import { localStorageMock } from '../__mocks__/localStorageMock';

// Get the actual storage key used in the implementation
const STORAGE_KEY = 'chat_simulator_messages';

describe('Storage Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  // Helper to mock localStorage return value for a specific test
  const setupStorageReturn = (returnValue) => {
    localStorageMock.getItem.mockReturnValueOnce(returnValue);
  };

  test('loadMessages returns empty array when no messages exist', () => {
    setupStorageReturn(null);
    const messages = loadMessages();
    expect(messages).toEqual([]);
    expect(localStorageMock.getItem).toHaveBeenCalledWith(STORAGE_KEY);
  });

  test('loadMessages parses JSON from localStorage', () => {
    // Prepare test data
    const testMessages = [
      { id: 'msg1', content: 'Test message 1' },
      { id: 'msg2', content: 'Test message 2' }
    ];
    setupStorageReturn(JSON.stringify(testMessages));
    
    const messages = loadMessages();
    
    expect(messages).toEqual(testMessages);
    expect(localStorageMock.getItem).toHaveBeenCalledWith(STORAGE_KEY);
  });

  test('loadMessages handles invalid JSON gracefully', () => {
    // Set invalid JSON in localStorage
    setupStorageReturn('not valid json');
    
    const messages = loadMessages();
    
    // Should return empty array when JSON is invalid
    expect(messages).toEqual([]);
    expect(localStorageMock.getItem).toHaveBeenCalledWith(STORAGE_KEY);
  });

  test('saveMessages stores messages as JSON in localStorage', () => {
    const testMessages = [
      { id: 'msg1', content: 'Test message 1' },
      { id: 'msg2', content: 'Test message 2' }
    ];
    
    saveMessages(testMessages);
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      STORAGE_KEY, 
      JSON.stringify(testMessages)
    );
  });

  test('clearMessages removes messages from localStorage', () => {
    clearMessages();
    
    expect(localStorageMock.removeItem).toHaveBeenCalledWith(STORAGE_KEY);
  });

  test('saveMessages handles circular references gracefully', () => {
    // Create an object with circular reference
    const circularObj = { name: 'circular' };
    circularObj.self = circularObj;
    
    const testMessagesWithCircular = [
      { id: 'msg1', content: 'Test message 1', metaData: circularObj }
    ];
    
    // The function should catch the JSON.stringify error and return false
    const result = saveMessages(testMessagesWithCircular);
    expect(result).toBe(false);
    
    // Verify that setItem wasn't called since stringification failed
    expect(localStorageMock.setItem).not.toHaveBeenCalled();
  });

  test('saveMessages can store large messages', () => {
    // Create a very large message to test storage limits
    const largeContent = 'A'.repeat(10000);
    const largeMessages = [
      { id: 'large_msg', content: largeContent }
    ];
    
    saveMessages(largeMessages);
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      STORAGE_KEY, 
      JSON.stringify(largeMessages)
    );
  });

  test('loadMessages handles very large stored data efficiently', () => {
    // Create a large dataset with 1000 messages
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      id: `msg_${i}`,
      content: `Message content ${i}`,
      timestamp: new Date().toISOString()
    }));
    
    setupStorageReturn(JSON.stringify(largeDataset));
    
    const startTime = performance.now();
    const messages = loadMessages();
    const endTime = performance.now();
    
    expect(messages.length).toBe(1000);
    
    // Performance check - should parse 1000 messages quickly
    // This is a soft assertion as performance varies by environment
    const parseTime = endTime - startTime;
    console.log(`Time to parse 1000 messages: ${parseTime}ms`);
    
    // Most modern browsers should parse this in under 500ms
    expect(parseTime).toBeLessThan(500);
  });

  test('saveMessages with strange/emoji characters', () => {
    const messagesWithSpecialChars = [
      { id: 'emoji_msg', content: '🚀 🎉 Testing with emojis! 👍 😊' },
      { id: 'unicode_msg', content: 'Unicode characters: ñáéíóúü Ñ Ü ß' },
      { id: 'symbols_msg', content: '∑ π ∫ ∂ √ ∞ ∆ Ω ≈ ≠ ≤ ≥ ± ÷ × ∈ ∉' }
    ];
    
    saveMessages(messagesWithSpecialChars);
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      STORAGE_KEY, 
      JSON.stringify(messagesWithSpecialChars)
    );
    
    // Now test loading these messages back
    setupStorageReturn(JSON.stringify(messagesWithSpecialChars));
    
    const loadedMessages = loadMessages();
    
    expect(loadedMessages).toEqual(messagesWithSpecialChars);
    expect(loadedMessages[0].content).toBe('🚀 🎉 Testing with emojis! 👍 😊');
  });
}); 