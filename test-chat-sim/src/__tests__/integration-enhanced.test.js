import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import App from '../App';
import { loadMessages, saveMessages } from '../utils/storage';

// Mock the storage functions
jest.mock('../utils/storage', () => ({
  loadMessages: jest.fn(),
  saveMessages: jest.fn().mockReturnValue(true),
  clearMessages: jest.fn().mockReturnValue(true),
}));

describe('Enhanced Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    loadMessages.mockReturnValue([]);
    Element.prototype.scrollIntoView = jest.fn();
    global.alert = jest.fn();
    localStorage.clear();
    document.documentElement.removeAttribute('data-theme');
  });

  test('complete conversation workflow with theme switching', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    // Start conversation in light mode
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const bobInput = screen.getAllByPlaceholderText(/Type a message/i)[1];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];
    const bobSendButton = screen.getAllByRole('button', { name: /send/i })[1];

    await act(async () => {
      await user.type(aliceInput, 'Starting conversation in light mode');
      await user.click(aliceSendButton);
    });

    // Switch to dark mode mid-conversation
    const darkModeToggle = screen.getByRole('checkbox', { name: /dark mode/i });
    await act(async () => {
      await user.click(darkModeToggle);
    });

    // Continue conversation in dark mode
    await act(async () => {
      await user.type(bobInput, 'Responding in dark mode');
      await user.click(bobSendButton);
    });

    // Verify both messages exist
    expect(screen.getByText('Starting conversation in light mode')).toBeInTheDocument();
    expect(screen.getByText('Responding in dark mode')).toBeInTheDocument();

    // Verify network traffic
    await waitFor(() => {
      expect(screen.getByText(/ALICE → BOB/i)).toBeInTheDocument();
      expect(screen.getByText(/BOB → ALICE/i)).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  test('message persistence across app restarts', async () => {
    const user = userEvent.setup();
    
    // First session - send messages
    const persistentMessages = [
      {
        id: 'persist-1',
        sender: 'alice',
        recipient: 'bob',
        content: 'Persistent message 1',
        timestamp: new Date().toISOString(),
        status: 'delivered',
        type: 'text'
      },
      {
        id: 'persist-2',
        sender: 'bob',
        recipient: 'alice',
        content: 'Persistent message 2',
        timestamp: new Date().toISOString(),
        status: 'delivered',
        type: 'text'
      }
    ];

    loadMessages.mockReturnValue(persistentMessages);

    await act(async () => {
      render(<App />);
    });

    // Messages should be loaded from storage
    expect(screen.getByText('Persistent message 1')).toBeInTheDocument();
    expect(screen.getByText('Persistent message 2')).toBeInTheDocument();

    // Send new message
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

    await act(async () => {
      await user.type(aliceInput, 'New session message');
      await user.click(aliceSendButton);
    });

    // Should save messages
    expect(saveMessages).toHaveBeenCalled();
  });

  test('complex file handling workflow', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    const alicePanel = screen.getByTestId('alice-panel');
    const inputContainer = alicePanel.querySelector('.message-input-container');

    // Test multiple file types
    const files = [
      new File(['image data'], 'test.png', { type: 'image/png' }),
      new File(['image data'], 'test.jpg', { type: 'image/jpeg' }),
      new File(['gif data'], 'test.gif', { type: 'image/gif' })
    ];

    // Mock FileReader for each file
    const mockFileReaders = files.map((file, index) => ({
      readAsDataURL: jest.fn(),
      onload: null,
      result: `data:${file.type};base64,mockdata${index}`
    }));

    let readerIndex = 0;
    global.FileReader = jest.fn(() => mockFileReaders[readerIndex++]);

    // Process each file
    for (let i = 0; i < files.length; i++) {
      fireEvent.drop(inputContainer, {
        dataTransfer: { files: [files[i]] }
      });

      // Trigger FileReader onload
      if (mockFileReaders[i].onload) {
        mockFileReaders[i].onload({ 
          target: { result: mockFileReaders[i].result } 
        });
      }
    }

    // Should process all images
    await waitFor(() => {
      const networkMessages = screen.getAllByText(/ALICE → BOB/i);
      expect(networkMessages.length).toBeGreaterThan(0);
    }, { timeout: 2000 });
  });

  test('stress test with rapid message sending', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const bobInput = screen.getAllByPlaceholderText(/Type a message/i)[1];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];
    const bobSendButton = screen.getAllByRole('button', { name: /send/i })[1];

    // Send multiple messages rapidly
    for (let i = 0; i < 5; i++) {
      await act(async () => {
        await user.type(aliceInput, `Alice message ${i}`);
        await user.click(aliceSendButton);
      });

      await act(async () => {
        await user.type(bobInput, `Bob message ${i}`);
        await user.click(bobSendButton);
      });
    }

    // Should handle all messages
    expect(screen.getByText('Alice message 4')).toBeInTheDocument();
    expect(screen.getByText('Bob message 4')).toBeInTheDocument();
  });

  test('network panel interaction workflow', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    // Send a message to generate network traffic
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

    await act(async () => {
      await user.type(aliceInput, 'Network interaction test');
      await user.click(aliceSendButton);
    });

    // Wait for network message to appear
    await waitFor(() => {
      expect(screen.getByText(/ALICE → BOB/i)).toBeInTheDocument();
    }, { timeout: 2000 });

    // Click on network message to open popout
    const networkMessage = screen.getByText('Network interaction test');
    await user.click(networkMessage);

    // Should show message details popout
    expect(screen.getByText('Message Transmission Details')).toBeInTheDocument();

    // Close popout
    const closeButton = screen.getByText('×');
    await user.click(closeButton);

    // Popout should be closed
    expect(screen.queryByText('Message Transmission Details')).not.toBeInTheDocument();
  });

  test('error recovery during message transmission', async () => {
    const user = userEvent.setup();
    
    // Mock saveMessages to fail intermittently
    let callCount = 0;
    saveMessages.mockImplementation(() => {
      callCount++;
      if (callCount % 2 === 0) {
        throw new Error('Intermittent storage error');
      }
      return true;
    });

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    await act(async () => {
      render(<App />);
    });

    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

    // Send multiple messages (some will fail to save)
    for (let i = 0; i < 4; i++) {
      await act(async () => {
        await user.type(aliceInput, `Recovery test ${i}`);
        await user.click(aliceSendButton);
      });
    }

    // Should handle errors gracefully and continue working
    expect(screen.getByText('Recovery test 3')).toBeInTheDocument();
    
    consoleSpy.mockRestore();
  });

  test('accessibility workflow with keyboard navigation', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    // Test tab navigation through the interface
    await user.tab(); // Dark mode toggle
    await user.tab(); // OTR toggle
    await user.tab(); // Alice input
    await user.tab(); // Alice send button
    await user.tab(); // Alice clear button
    await user.tab(); // Bob input

    // Test keyboard message sending
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    await user.click(aliceInput);
    await user.type(aliceInput, 'Keyboard accessibility test');
    await user.keyboard('{Enter}');

    // Message should be sent
    expect(screen.getByText('Keyboard accessibility test')).toBeInTheDocument();
  });

  test('responsive behavior across different viewport sizes', async () => {
    const viewports = [
      { width: 320, height: 568, name: 'mobile' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 1200, height: 800, name: 'desktop' }
    ];

    for (const viewport of viewports) {
      // Set viewport size
      global.innerWidth = viewport.width;
      global.innerHeight = viewport.height;
      fireEvent(window, new Event('resize'));

      const { unmount } = render(<App />);

      // Should render correctly on all viewport sizes
      expect(screen.getByText('WebOTR Test Chat Simulator')).toBeInTheDocument();
      expect(screen.getByText('Alice')).toBeInTheDocument();
      expect(screen.getByText('Bob')).toBeInTheDocument();

      unmount();
    }
  });

  test('memory management with large message history', async () => {
    const user = userEvent.setup();
    
    // Create large message history
    const largeMessageHistory = Array.from({ length: 50 }, (_, i) => ({
      id: `large-${i}`,
      sender: i % 2 === 0 ? 'alice' : 'bob',
      recipient: i % 2 === 0 ? 'bob' : 'alice',
      content: `Large history message ${i}`,
      timestamp: new Date(Date.now() - i * 1000).toISOString(),
      status: 'delivered',
      type: 'text'
    }));

    loadMessages.mockReturnValue(largeMessageHistory);

    await act(async () => {
      render(<App />);
    });

    // Should handle large message history
    expect(screen.getByText('Large history message 49')).toBeInTheDocument();

    // Add new message
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

    await act(async () => {
      await user.type(aliceInput, 'New message with large history');
      await user.click(aliceSendButton);
    });

    // Should handle new message with large history
    expect(screen.getByText('New message with large history')).toBeInTheDocument();
  });

  test('concurrent user interactions', async () => {
    const user = userEvent.setup();
    
    await act(async () => {
      render(<App />);
    });

    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const bobInput = screen.getAllByPlaceholderText(/Type a message/i)[1];
    const darkModeToggle = screen.getByRole('checkbox', { name: /dark mode/i });

    // Simulate concurrent interactions
    await act(async () => {
      // Start typing in both inputs simultaneously
      user.type(aliceInput, 'Alice concurrent');
      user.type(bobInput, 'Bob concurrent');
      user.click(darkModeToggle);
    });

    // Should handle concurrent interactions gracefully
    expect(screen.getByText('Alice')).toBeInTheDocument();
    expect(screen.getByText('Bob')).toBeInTheDocument();
  });
});
