import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import App from '../App';
import { loadMessages, saveMessages } from '../utils/storage';

// Mock the storage functions
jest.mock('../utils/storage', () => ({
  loadMessages: jest.fn(),
  saveMessages: jest.fn().mockReturnValue(true),
  clearMessages: jest.fn().mockReturnValue(true),
}));

describe('Comprehensive Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    loadMessages.mockReturnValue([]);
    Element.prototype.scrollIntoView = jest.fn();
    global.alert = jest.fn();
    localStorage.clear();
    document.documentElement.removeAttribute('data-theme');
  });

  describe('Complete User Workflows', () => {
    test('complete conversation workflow with <PERSON>TR', async () => {
      const user = userEvent.setup();
      
      await act(async () => {
        render(<App />);
      });

      // Wait for OTR initialization
      await waitFor(() => {
        const otrToggle = screen.getByRole('checkbox', { name: /otr/i });
        expect(otrToggle).not.toBeDisabled();
      }, { timeout: 3000 });

      // Enable OTR
      const otrToggle = screen.getByRole('checkbox', { name: /otr/i });
      await act(async () => {
        await user.click(otrToggle);
      });

      // Send messages from both users
      const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
      const bobInput = screen.getAllByPlaceholderText(/Type a message/i)[1];
      const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];
      const bobSendButton = screen.getAllByRole('button', { name: /send/i })[1];

      // Alice sends first message
      await act(async () => {
        await user.type(aliceInput, 'Hello Bob, OTR is enabled!');
        await user.click(aliceSendButton);
      });

      // Bob responds
      await act(async () => {
        await user.type(bobInput, 'Hi Alice, I can see the encryption!');
        await user.click(bobSendButton);
      });

      // Verify messages appear in chat
      expect(screen.getByText('Hello Bob, OTR is enabled!')).toBeInTheDocument();
      expect(screen.getByText('Hi Alice, I can see the encryption!')).toBeInTheDocument();

      // Verify network traffic
      await waitFor(() => {
        expect(screen.getByText(/ALICE → BOB/i)).toBeInTheDocument();
        expect(screen.getByText(/BOB → ALICE/i)).toBeInTheDocument();
      }, { timeout: 2000 });
    });

    test('theme switching during active conversation', async () => {
      const user = userEvent.setup();
      
      await act(async () => {
        render(<App />);
      });

      // Send a message first
      const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
      const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

      await act(async () => {
        await user.type(aliceInput, 'Testing theme switch');
        await user.click(aliceSendButton);
      });

      // Switch to dark mode
      const darkModeToggle = screen.getByRole('checkbox', { name: /dark mode/i });
      await act(async () => {
        await user.click(darkModeToggle);
      });

      // Verify theme applied
      expect(document.documentElement.getAttribute('data-theme')).toBe('dark');

      // Send another message
      await act(async () => {
        await user.type(aliceInput, 'Dark mode message');
        await user.click(aliceSendButton);
      });

      // Switch back to light mode
      await act(async () => {
        await user.click(darkModeToggle);
      });

      expect(document.documentElement.getAttribute('data-theme')).toBeNull();
    });

    test('message editing workflow', async () => {
      const user = userEvent.setup();
      
      // Start with existing messages
      const existingMessages = [
        {
          id: 'edit-test-1',
          sender: 'alice',
          recipient: 'bob',
          content: 'Original message',
          timestamp: new Date().toISOString(),
          status: 'delivered',
          type: 'text'
        }
      ];
      
      loadMessages.mockReturnValue(existingMessages);

      await act(async () => {
        render(<App />);
      });

      // Find the message and right-click to edit
      const messageElement = screen.getByText('Original message');
      fireEvent.contextMenu(messageElement);

      // Look for edit option (if implemented)
      const editButton = screen.queryByText('Edit message');
      if (editButton) {
        await user.click(editButton);

        // Edit the message
        const editInput = screen.getByDisplayValue('Original message');
        await user.clear(editInput);
        await user.type(editInput, 'Edited message content');

        // Save the edit
        const saveButton = screen.getByText('Save');
        await user.click(saveButton);

        expect(saveMessages).toHaveBeenCalled();
      }
    });

    test('file upload and image handling workflow', async () => {
      const user = userEvent.setup();
      
      await act(async () => {
        render(<App />);
      });

      // Get Alice's input area
      const alicePanel = screen.getByTestId('alice-panel');
      const inputContainer = alicePanel.querySelector('.message-input-container');

      // Create mock file
      const file = new File(['test image data'], 'test.png', { type: 'image/png' });

      // Mock FileReader
      const mockFileReader = {
        readAsDataURL: jest.fn(),
        onload: null,
        result: 'data:image/png;base64,testdata'
      };
      global.FileReader = jest.fn(() => mockFileReader);

      // Simulate file drop
      fireEvent.drop(inputContainer, {
        dataTransfer: { files: [file] }
      });

      // Trigger FileReader onload
      if (mockFileReader.onload) {
        mockFileReader.onload({ target: { result: 'data:image/png;base64,testdata' } });
      }

      // Verify image handling
      await waitFor(() => {
        expect(screen.getByText(/ALICE → BOB/i)).toBeInTheDocument();
      }, { timeout: 2000 });
    });
  });

  describe('Error Recovery Scenarios', () => {
    test('recovers from storage errors during conversation', async () => {
      const user = userEvent.setup();
      
      await act(async () => {
        render(<App />);
      });

      // Start normal conversation
      const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
      const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

      await act(async () => {
        await user.type(aliceInput, 'First message');
        await user.click(aliceSendButton);
      });

      // Simulate storage error
      saveMessages.mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      // Try to send another message
      await act(async () => {
        await user.type(aliceInput, 'Second message');
        await user.click(aliceSendButton);
      });

      // Should handle error gracefully
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    test('handles network simulation errors', async () => {
      const user = userEvent.setup();
      
      await act(async () => {
        render(<App />);
      });

      // Send multiple messages rapidly to stress test
      const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
      const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

      for (let i = 0; i < 10; i++) {
        await act(async () => {
          await user.type(aliceInput, `Stress test message ${i}`);
          await user.click(aliceSendButton);
        });
      }

      // Should handle rapid messages without crashing
      expect(screen.getByText('Alice')).toBeInTheDocument();
    });

    test('handles OTR session errors gracefully', async () => {
      const user = userEvent.setup();
      
      // Mock console to capture errors
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      await act(async () => {
        render(<App />);
      });

      // Try to enable OTR (might fail in some scenarios)
      await waitFor(() => {
        const otrToggle = screen.getByRole('checkbox', { name: /otr/i });
        expect(otrToggle).toBeInTheDocument();
      }, { timeout: 2000 });

      const otrToggle = screen.getByRole('checkbox', { name: /otr/i });
      
      if (!otrToggle.disabled) {
        await act(async () => {
          await user.click(otrToggle);
        });
      }

      // Should not crash the app
      expect(screen.getByText('Alice')).toBeInTheDocument();
      
      consoleSpy.mockRestore();
    });
  });

  describe('Performance and Stress Testing', () => {
    test('handles large number of messages', async () => {
      const user = userEvent.setup();
      
      // Create many messages
      const manyMessages = Array.from({ length: 100 }, (_, i) => ({
        id: `stress-${i}`,
        sender: i % 2 === 0 ? 'alice' : 'bob',
        recipient: i % 2 === 0 ? 'bob' : 'alice',
        content: `Stress test message ${i}`,
        timestamp: new Date(Date.now() - i * 1000).toISOString(),
        status: 'delivered',
        type: 'text'
      }));

      loadMessages.mockReturnValue(manyMessages);

      await act(async () => {
        render(<App />);
      });

      // Should render without performance issues
      expect(screen.getByText('Alice')).toBeInTheDocument();
      expect(screen.getByText('Bob')).toBeInTheDocument();
    });

    test('handles rapid user interactions', async () => {
      const user = userEvent.setup();
      
      await act(async () => {
        render(<App />);
      });

      const darkModeToggle = screen.getByRole('checkbox', { name: /dark mode/i });
      const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];

      // Rapid interactions
      for (let i = 0; i < 5; i++) {
        await act(async () => {
          await user.click(darkModeToggle);
          await user.type(aliceInput, `${i}`);
          await user.keyboard('{Backspace}');
        });
      }

      // Should handle rapid interactions
      expect(screen.getByText('Alice')).toBeInTheDocument();
    });

    test('memory usage with message cleanup', async () => {
      const user = userEvent.setup();
      
      await act(async () => {
        render(<App />);
      });

      // Send messages and clear them multiple times
      const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
      const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];
      const aliceClearButton = screen.getAllByTitle('Clear Messages')[0];

      for (let cycle = 0; cycle < 3; cycle++) {
        // Send messages
        for (let i = 0; i < 5; i++) {
          await act(async () => {
            await user.type(aliceInput, `Cycle ${cycle} Message ${i}`);
            await user.click(aliceSendButton);
          });
        }

        // Clear messages
        await act(async () => {
          await user.click(aliceClearButton);
        });
      }

      // Should handle memory cleanup
      expect(screen.getByText('Alice')).toBeInTheDocument();
    });
  });

  describe('Accessibility and Usability', () => {
    test('keyboard navigation workflow', async () => {
      const user = userEvent.setup();
      
      await act(async () => {
        render(<App />);
      });

      // Test tab navigation
      await user.tab();
      await user.tab();
      await user.tab();

      // Test keyboard shortcuts
      const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
      await user.click(aliceInput);
      
      await user.type(aliceInput, 'Keyboard test');
      await user.keyboard('{Enter}');

      // Should handle keyboard interactions
      expect(screen.getByText('Keyboard test')).toBeInTheDocument();
    });

    test('screen reader compatibility', async () => {
      await act(async () => {
        render(<App />);
      });

      // Check for proper ARIA labels and roles
      expect(screen.getByRole('checkbox', { name: /otr/i })).toBeInTheDocument();
      expect(screen.getByRole('checkbox', { name: /dark mode/i })).toBeInTheDocument();
      
      const sendButtons = screen.getAllByRole('button', { name: /send/i });
      expect(sendButtons).toHaveLength(2);
    });

    test('responsive behavior simulation', async () => {
      await act(async () => {
        render(<App />);
      });

      // Simulate different viewport sizes
      global.innerWidth = 768;
      global.innerHeight = 1024;
      fireEvent(window, new Event('resize'));

      global.innerWidth = 320;
      global.innerHeight = 568;
      fireEvent(window, new Event('resize'));

      // Should handle responsive changes
      expect(screen.getByText('Alice')).toBeInTheDocument();
    });
  });

  describe('Data Persistence and State Management', () => {
    test('state persistence across component updates', async () => {
      const user = userEvent.setup();
      
      const { rerender } = render(<App />);

      // Change state
      const darkModeToggle = screen.getByRole('checkbox', { name: /dark mode/i });
      await act(async () => {
        await user.click(darkModeToggle);
      });

      // Rerender component
      rerender(<App />);

      // State should persist
      expect(document.documentElement.getAttribute('data-theme')).toBe('dark');
    });

    test('message persistence with localStorage', async () => {
      const user = userEvent.setup();
      
      await act(async () => {
        render(<App />);
      });

      // Send a message
      const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
      const aliceSendButton = screen.getAllByRole('button', { name: /send/i })[0];

      await act(async () => {
        await user.type(aliceInput, 'Persistent message');
        await user.click(aliceSendButton);
      });

      // Verify saveMessages was called
      expect(saveMessages).toHaveBeenCalled();
    });
  });
});
