import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import App from '../App';
import { loadMessages, saveMessages } from '../utils/storage';

// Mock the storage functionality
jest.mock('../utils/storage', () => ({
  loadMessages: jest.fn(),
  saveMessages: jest.fn(),
  clearMessages: jest.fn(),
}));

describe('Chat Application Edge Cases', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    loadMessages.mockReturnValue([]);
    
    // Mock scrollIntoView
    Element.prototype.scrollIntoView = jest.fn();
    
    // Set up user event
    global.user = userEvent.setup();
  });

  test('handles empty messages (whitespace only)', async () => {
    render(<App />);
    
    // Type only whitespace in Alice's input
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    await global.user.type(aliceInput, '   ');
    
    // Send button should remain disabled
    const sendButton = screen.getAllByRole('button', { name: /send/i })[0];
    expect(sendButton).toBeDisabled();
    
    // Try with a space and backspace to test edge case
    await global.user.clear(aliceInput);
    await global.user.type(aliceInput, ' ');
    await global.user.keyboard('{Backspace}');
    expect(sendButton).toBeDisabled();
  });

  test('handles very long messages appropriately', async () => {
    render(<App />);
    
    // Create a very long message
    const longMessage = 'A'.repeat(1000);
    
    // Type the long message in Alice's input
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    await global.user.type(aliceInput, longMessage);
    
    // Send the message
    const sendButton = screen.getAllByRole('button', { name: /send/i })[0];
    await global.user.click(sendButton);
    
    // Message should appear in Alice's panel
    const messageElement = await screen.findByText(longMessage);
    expect(messageElement).toBeInTheDocument();
    
    // Message should be delivered to Bob after delay
    await waitFor(() => {
      expect(screen.getByText('Delivered')).toBeInTheDocument();
    }, { timeout: 2000 });
    
    // saveMessages should be called with the long message
    expect(saveMessages).toHaveBeenCalled();
    const savedMessagesArg = saveMessages.mock.calls[0][0];
    expect(savedMessagesArg[0].content).toBe(longMessage);
  });

  test('handles special characters in messages', async () => {
    render(<App />);
    
    // Create a message with special characters
    const specialCharsMessage = '!@#$%^&*()_+{}:"<>?[];\',./-=\\|~`';
    
    // Type the message in Bob's input
    const bobInput = screen.getAllByPlaceholderText(/Type a message/i)[1];
    await global.user.type(bobInput, specialCharsMessage);
    
    // Send the message
    const sendButton = screen.getAllByRole('button', { name: /send/i })[1];
    await global.user.click(sendButton);
    
    // Message should appear in Bob's panel
    expect(await screen.findByText(specialCharsMessage)).toBeInTheDocument();
    
    // Message should be delivered to Alice after delay
    await waitFor(() => {
      expect(screen.getByText('Delivered')).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  test('handles HTML-like content in messages safely', async () => {
    render(<App />);
    
    // Create a message with HTML-like content
    const htmlMessage = '<script>alert("XSS")</script><img src="x" onerror="alert(1)">';
    
    // Type the message in Alice's input
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    await global.user.type(aliceInput, htmlMessage);
    
    // Send the message
    const sendButton = screen.getAllByRole('button', { name: /send/i })[0];
    await global.user.click(sendButton);
    
    // The content should be rendered as text, not executed as HTML
    const messageElement = await screen.findByText(htmlMessage);
    expect(messageElement).toBeInTheDocument();
    
    // Make sure no script execution happened
    expect(document.querySelector('script')).toBeNull();
  });

  test('handles rapid message sending', async () => {
    render(<App />);
    
    // Send multiple messages rapidly from Alice
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    const sendButton = screen.getAllByRole('button', { name: /send/i })[0];
    
    // Send 5 messages in quick succession
    for (let i = 1; i <= 5; i++) {
      await global.user.clear(aliceInput);
      await global.user.type(aliceInput, `Message ${i}`);
      await global.user.click(sendButton);
    }
    
    // Check that all messages were sent and displayed
    for (let i = 1; i <= 5; i++) {
      expect(screen.getByText(`Message ${i}`)).toBeInTheDocument();
    }
    
    // Check network panel shows all messages
    expect(screen.getAllByText(/ALICE → BOB/i)).toHaveLength(5);
    
    // Wait for all messages to be delivered
    await waitFor(() => {
      const deliveredStatuses = screen.getAllByText('Delivered');
      expect(deliveredStatuses).toHaveLength(5);
    }, { timeout: 5000 });
  });

  test('messages are in chronological order', async () => {
    // Mock saved messages in reverse chronological order to test sorting
    const mockMessages = [
      {
        id: 'msg2',
        sender: 'bob',
        recipient: 'alice',
        content: 'Second message',
        timestamp: '2023-03-22T10:01:00.000Z',
        status: 'delivered'
      },
      {
        id: 'msg1',
        sender: 'alice',
        recipient: 'bob',
        content: 'First message',
        timestamp: '2023-03-22T10:00:00.000Z',
        status: 'delivered'
      }
    ];
    
    loadMessages.mockReturnValue(mockMessages);
    
    render(<App />);
    
    // Verify messages are rendered in chronological order
    const messageElements = screen.getAllByText(/First message|Second message/);
    const messageContents = messageElements.map(el => el.textContent);
    
    // First message should come before second message
    const firstIndex = messageContents.findIndex(content => content === 'First message');
    const secondIndex = messageContents.findIndex(content => content === 'Second message');
    
    expect(firstIndex).toBeLessThan(secondIndex);
  });

  test('handles emoji characters correctly', async () => {
    render(<App />);
    
    // Create a message with emoji characters
    const emojiMessage = '😀 🎉 👍 🚀 🌈';
    
    // Type the message in Alice's input
    const aliceInput = screen.getAllByPlaceholderText(/Type a message/i)[0];
    await global.user.type(aliceInput, emojiMessage);
    
    // Send the message
    const sendButton = screen.getAllByRole('button', { name: /send/i })[0];
    await global.user.click(sendButton);
    
    // Message should appear in Alice's panel
    const messageElement = await screen.findByText(emojiMessage);
    expect(messageElement).toBeInTheDocument();
    
    // Message should be delivered to Bob after delay
    await waitFor(() => {
      expect(screen.getByText('Delivered')).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  test('handles messages with newlines and tabs', async () => {
    render(<App />);
    
    // Create a message with newlines and tabs
    const formattedMessage = 'Line 1\nLine 2\tTabbed';
    
    // Type the message in Bob's input
    const bobInput = screen.getAllByPlaceholderText(/Type a message/i)[1];
    await global.user.clear(bobInput);
    await global.user.paste(formattedMessage);
    
    // Send the message
    const sendButton = screen.getAllByRole('button', { name: /send/i })[1];
    await global.user.click(sendButton);
    
    // Check that the message preserves formatting
    // Since newlines may be rendered differently, we check that the content exists
    expect(screen.getByText(/Line 1/)).toBeInTheDocument();
    expect(screen.getByText(/Line 2/)).toBeInTheDocument();
    expect(screen.getByText(/Tabbed/)).toBeInTheDocument();
  });

  test('handles very high message volume', async () => {
    // Mock a large number of saved messages
    const highVolumeMessages = Array.from({ length: 100 }, (_, i) => ({
      id: `msg${i}`,
      sender: i % 2 === 0 ? 'alice' : 'bob',
      recipient: i % 2 === 0 ? 'bob' : 'alice',
      content: `Test message ${i}`,
      timestamp: new Date(2023, 2, 22, 10, 0, i).toISOString(),
      status: 'delivered'
    }));
    
    loadMessages.mockReturnValue(highVolumeMessages);
    
    render(<App />);
    
    // Verify that the app doesn't crash with high volume
    expect(screen.getByText('Test message 0')).toBeInTheDocument();
    expect(screen.getByText('Test message 99')).toBeInTheDocument();
    
    // Verify messages are properly displayed in the network panel
    const networkMessages = screen.getAllByTestId('network-message');
    expect(networkMessages.length).toBe(100);
  });

  test('handles browser refresh with persistent state', async () => {
    // Set up initial messages
    const initialMessages = [
      {
        id: 'msg1',
        sender: 'alice',
        recipient: 'bob',
        content: 'Persistent message',
        timestamp: new Date(2023, 2, 22, 10, 0, 0).toISOString(),
        status: 'delivered'
      }
    ];
    
    loadMessages.mockReturnValue(initialMessages);
    
    const { unmount } = render(<App />);
    
    // Verify initial message is displayed
    expect(screen.getByText('Persistent message')).toBeInTheDocument();
    
    // Unmount and remount to simulate page refresh
    unmount();
    
    // Load the same messages on "refresh"
    render(<App />);
    
    // Verify message persists after "refresh"
    expect(screen.getByText('Persistent message')).toBeInTheDocument();
  });
}); 