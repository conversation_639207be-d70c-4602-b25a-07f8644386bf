import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ChatPanel from '../components/ChatPanel';

// Mock the emoji-picker-react component
jest.mock('emoji-picker-react', () => {
  return function MockEmojiPicker({ onEmojiClick }) {
    return (
      <div data-testid="emoji-picker">
        <button
          data-testid="emoji-select"
          onClick={() => onEmojiClick({ emoji: '😀' })}
        >
          Select 😀
        </button>
      </div>
    );
  };
});

describe('ChatPanel Component', () => {
  const mockMessages = [
    {
      id: 'msg1',
      sender: 'alice',
      content: 'Hello there!',
      timestamp: '2025-03-22T10:30:00.000Z',
      status: 'delivered'
    },
    {
      id: 'msg2',
      sender: 'bob',
      content: 'Hi Alice!',
      timestamp: '2025-03-22T10:31:00.000Z',
      status: 'delivered'
    }
  ];

  const mockProps = {
    user: 'alice',
    displayName: 'Alice',
    messages: mockMessages,
    onSendMessage: jest.fn(),
    onClearMessages: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock scrollIntoView to avoid errors
    Element.prototype.scrollIntoView = jest.fn();
  });

  test('renders the chat panel with correct user name', () => {
    render(<ChatPanel {...mockProps} />);
    expect(screen.getByText('Alice')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /clear/i })).toBeInTheDocument();
  });

  test('renders messages correctly', () => {
    render(<ChatPanel {...mockProps} />);
    expect(screen.getByText('Hello there!')).toBeInTheDocument();
    expect(screen.getByText('Hi Alice!')).toBeInTheDocument();
  });

  test('sends a message when form is submitted', async () => {
    const user = userEvent.setup();
    render(<ChatPanel {...mockProps} />);
    
    const input = screen.getByPlaceholderText(/type a message/i);
    await user.type(input, 'New message');
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);
    
    expect(mockProps.onSendMessage).toHaveBeenCalledWith('alice', 'New message', 'text');
    expect(input).toHaveValue(''); // Input should be cleared
  });

  test('sends a message when Enter key is pressed', async () => {
    render(<ChatPanel {...mockProps} />);
    
    const input = screen.getByPlaceholderText(/type a message/i);
    fireEvent.change(input, { target: { value: 'Message with Enter' } });
    const form = screen.getByRole('button', { name: /send/i }).closest('form');
    fireEvent.submit(form);
    
    expect(mockProps.onSendMessage).toHaveBeenCalledWith('alice', 'Message with Enter', 'text');
  });

  test('clears messages when clear button is clicked', async () => {
    const user = userEvent.setup();
    render(<ChatPanel {...mockProps} />);
    
    const clearButton = screen.getByRole('button', { name: /clear/i });
    await user.click(clearButton);
    
    expect(mockProps.onClearMessages).toHaveBeenCalled();
  });

  test('disable send button when input is empty', async () => {
    const user = userEvent.setup();
    render(<ChatPanel {...mockProps} />);
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    expect(sendButton).toBeDisabled();
    
    const input = screen.getByPlaceholderText(/type a message/i);
    await user.type(input, 'A');
    expect(sendButton).not.toBeDisabled();
    
    await user.clear(input);
    expect(sendButton).toBeDisabled();
  });

  test('displays "No messages yet" when no messages are provided', () => {
    render(<ChatPanel {...mockProps} messages={[]} />);
    expect(screen.getByText('No messages yet')).toBeInTheDocument();
  });

  test('displays different styling for sent vs received messages', () => {
    render(<ChatPanel {...mockProps} />);
    
    // First message should have 'sent' class (from alice)
    const messages = screen.getAllByText(/Hello|Hi/);
    
    // Find the parent elements that have the 'message' class
    const message1 = messages[0].closest('.message');
    const message2 = messages[1].closest('.message');
    
    expect(message1).toHaveClass('sent');
    expect(message2).toHaveClass('received');
  });

  test('displays message status indicators correctly', () => {
    const messagesWithStatus = [
      {
        id: 'msg3',
        sender: 'alice',
        content: 'Message being sent',
        timestamp: '2025-03-22T10:32:00.000Z',
        status: 'sent'
      },
      {
        id: 'msg4',
        sender: 'alice',
        content: 'Message delivered',
        timestamp: '2025-03-22T10:33:00.000Z',
        status: 'delivered'
      }
    ];
    
    render(<ChatPanel {...mockProps} messages={messagesWithStatus} />);
    
    expect(screen.getByText('Sending...')).toBeInTheDocument();
    expect(screen.getByText('Delivered')).toBeInTheDocument();
  });

  test('formats timestamps correctly', () => {
    const mockToLocaleTimeString = jest.fn().mockReturnValue('2:30 PM');

    // Mock Date constructor to return an object with our mocked method
    const originalDate = global.Date;
    global.Date = jest.fn().mockImplementation((timestamp) => {
      const date = new originalDate(timestamp);
      date.toLocaleTimeString = mockToLocaleTimeString;
      return date;
    });

    const messagesWithTimestamp = [
      {
        id: 'msg5',
        sender: 'alice',
        content: 'Message with timestamp',
        timestamp: '2025-03-22T14:30:00.000Z',
        status: 'delivered'
      }
    ];

    render(<ChatPanel {...mockProps} messages={messagesWithTimestamp} />);

    expect(mockToLocaleTimeString).toHaveBeenCalled();

    // Restore original Date
    global.Date = originalDate;
  });

  test('emoji picker button opens picker and selecting emoji adds it to input', async () => {
    const onSendMessage = jest.fn();
    const props = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage,
      onClearMessages: jest.fn()
    };
    
    const user = userEvent.setup();
    render(<ChatPanel {...props} />);
    
    // Open emoji picker
    const emojiToggleButton = screen.getByLabelText('Open emoji picker');
    await user.click(emojiToggleButton);
    
    // Verify emoji picker is visible
    const emojiPicker = screen.getByTestId('emoji-picker');
    expect(emojiPicker).toBeInTheDocument();
    
    // Select an emoji
    const emojiSelectButton = screen.getByTestId('emoji-select');
    await user.click(emojiSelectButton);
    
    // Verify emoji was added to input
    const inputField = screen.getByLabelText('Message input for Alice');
    expect(inputField).toHaveValue('😀');
    
    // Type additional text and send
    await user.type(inputField, ' Hello with emoji');
    
    const sendButton = screen.getByRole('button', { name: 'Send message' });
    await user.click(sendButton);
    
    // Verify the message with emoji was sent
    expect(onSendMessage).toHaveBeenCalledWith('alice', '😀 Hello with emoji', 'text');
  });

  test('handles drag and drop image upload', async () => {
    const user = userEvent.setup();
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn()
    };

    render(<ChatPanel {...mockProps} />);

    const dropArea = screen.getByPlaceholderText(/type a message/i).closest('.message-input-container');

    // Create a mock file
    const file = new File(['test'], 'test.png', { type: 'image/png' });

    // Mock FileReader
    const mockFileReader = {
      readAsDataURL: jest.fn(),
      onload: null,
      result: 'data:image/png;base64,testdata'
    };

    global.FileReader = jest.fn(() => mockFileReader);

    // Simulate drag enter
    fireEvent.dragEnter(dropArea, {
      dataTransfer: {
        files: [file]
      }
    });

    // Simulate drop
    fireEvent.drop(dropArea, {
      dataTransfer: {
        files: [file]
      }
    });

    // Trigger the FileReader onload
    mockFileReader.onload({ target: { result: 'data:image/png;base64,testdata' } });

    expect(mockProps.onSendMessage).toHaveBeenCalledWith('alice', 'data:image/png;base64,testdata', 'image');
  });

  test('handles drag and drop with non-image file', async () => {
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn()
    };

    // Mock alert
    global.alert = jest.fn();

    render(<ChatPanel {...mockProps} />);

    const dropArea = screen.getByPlaceholderText(/type a message/i).closest('.message-input-container');

    // Create a mock non-image file
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });

    // Simulate drop with non-image file
    fireEvent.drop(dropArea, {
      dataTransfer: {
        files: [file]
      }
    });

    expect(global.alert).toHaveBeenCalledWith('Only image files are supported');
    expect(mockProps.onSendMessage).not.toHaveBeenCalled();
  });

  test('handles message editing', async () => {
    const user = userEvent.setup();
    const mockEditMessage = jest.fn();
    const messages = [
      {
        id: 'msg1',
        sender: 'alice',
        content: 'Original message',
        timestamp: '2025-03-22T10:30:00.000Z',
        status: 'delivered',
        type: 'text'
      }
    ];

    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages,
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn(),
      onEditMessage: mockEditMessage
    };

    render(<ChatPanel {...mockProps} />);

    // Right-click on the message to open context menu
    const messageElement = screen.getByText('Original message');
    fireEvent.contextMenu(messageElement);

    // Click edit option
    const editButton = screen.getByText('Edit message');
    await user.click(editButton);

    // Edit the message
    const editInput = screen.getByDisplayValue('Original message');
    await user.clear(editInput);
    await user.type(editInput, 'Edited message');

    // Save the edit
    const saveButton = screen.getByText('Save');
    await user.click(saveButton);

    expect(mockEditMessage).toHaveBeenCalledWith('msg1', 'Edited message');
  });

  test('cancels message editing', async () => {
    const user = userEvent.setup();
    const mockEditMessage = jest.fn();
    const messages = [
      {
        id: 'msg1',
        sender: 'alice',
        content: 'Original message',
        timestamp: '2025-03-22T10:30:00.000Z',
        status: 'delivered',
        type: 'text'
      }
    ];

    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages,
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn(),
      onEditMessage: mockEditMessage
    };

    render(<ChatPanel {...mockProps} />);

    // Right-click on the message to open context menu
    const messageElement = screen.getByText('Original message');
    fireEvent.contextMenu(messageElement);

    // Click edit option
    const editButton = screen.getByText('Edit message');
    await user.click(editButton);

    // Cancel the edit
    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    expect(mockEditMessage).not.toHaveBeenCalled();
    expect(screen.getByText('Original message')).toBeInTheDocument();
  });

  test('renders GIF messages correctly', () => {
    const gifMessage = {
      id: 'gif1',
      sender: 'alice',
      content: 'https://media.giphy.com/media/test/giphy.gif',
      timestamp: '2025-03-22T10:30:00.000Z',
      status: 'delivered',
      type: 'gif'
    };

    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [gifMessage],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn()
    };

    render(<ChatPanel {...mockProps} />);

    const gifElement = screen.getByAltText('GIF sent by alice');
    expect(gifElement).toBeInTheDocument();
    expect(gifElement).toHaveClass('message-gif');
  });

  test('opens and closes Giphy picker', async () => {
    const user = userEvent.setup();
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn()
    };

    render(<ChatPanel {...mockProps} />);

    // Open Giphy picker
    const giphyButton = screen.getByLabelText('Open GIF picker');
    await user.click(giphyButton);

    // Check if Giphy picker is visible
    const giphyPicker = screen.getByPlaceholderText('Search for GIFs...');
    expect(giphyPicker).toBeInTheDocument();

    // Close by clicking the button again
    await user.click(giphyButton);
  });

  test('handles encrypted messages display', () => {
    const encryptedMessage = {
      id: 'enc1',
      sender: 'alice',
      content: 'Secret message',
      timestamp: '2025-03-22T10:30:00.000Z',
      status: 'delivered',
      type: 'text',
      encrypted: true
    };

    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [encryptedMessage],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn()
    };

    render(<ChatPanel {...mockProps} />);

    expect(screen.getByText('🔒')).toBeInTheDocument();
    expect(screen.getByText('Secret message')).toBeInTheDocument();
  });

  test('shows OTR and SMP indicators when enabled', () => {
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn(),
      otrEnabled: true,
      smpState: 'verified'
    };

    render(<ChatPanel {...mockProps} />);

    expect(screen.getByTitle('OTR Encryption Enabled')).toBeInTheDocument();
    expect(screen.getByTitle('Identity Verified')).toBeInTheDocument();
  });

  test('shows SMP failed indicator', () => {
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn(),
      otrEnabled: true,
      smpState: 'failed'
    };

    render(<ChatPanel {...mockProps} />);

    expect(screen.getByTitle('Verification Failed')).toBeInTheDocument();
  });

  test('shows SMP pending indicator', () => {
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn(),
      otrEnabled: true,
      smpState: 'pending'
    };

    render(<ChatPanel {...mockProps} />);

    expect(screen.getByTitle('Verification Pending')).toBeInTheDocument();
  });

  test('handles SMP initiation', async () => {
    const user = userEvent.setup();
    const mockInitiateSMP = jest.fn();
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn(),
      otrEnabled: true,
      onInitiateSMP: mockInitiateSMP
    };

    render(<ChatPanel {...mockProps} />);

    // Click the dropdown button
    const dropdownButton = screen.getByText('▼');
    await user.click(dropdownButton);

    // Click Secret Q&A option
    const secretQAButton = screen.getByText('Secret Q&A');
    await user.click(secretQAButton);

    // Enter secret
    const secretInput = screen.getByPlaceholderText('Shared secret');
    await user.type(secretInput, 'test-secret');

    // Click Verify button in the dialog
    const verifyButtons = screen.getAllByText('Verify');
    const dialogVerifyButton = verifyButtons.find(button =>
      button.closest('.smp-dialog-buttons')
    );
    await user.click(dialogVerifyButton);

    expect(mockInitiateSMP).toHaveBeenCalledWith('alice', 'test-secret');
  });
});