import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import ChatPanel from '../components/ChatPanel';
import NetworkPanel from '../components/NetworkPanel';

describe('Image Message Display', () => {
  test('ChatPanel renders image messages correctly', () => {
    // Mock an image message
    const imageMessage = {
      id: 'img1',
      sender: 'alice',
      recipient: 'bob',
      content: 'data:image/png;base64,fakeImageData',
      timestamp: new Date().toISOString(),
      status: 'delivered',
      type: 'image'
    };

    // Mock props for ChatPanel
    const mockProps = {
      user: 'alice',
      displayName: 'Alice',
      messages: [imageMessage],
      onSendMessage: jest.fn(),
      onClearMessages: jest.fn()
    };

    // Render the ChatPanel with the image message
    render(<ChatPanel {...mockProps} />);

    // Check for image message
    const imageElement = screen.getByAltText('Image sent by alice');
    expect(imageElement).toBeInTheDocument();
    expect(imageElement).toHaveClass('message-image');
  });
  
  test('NetworkPanel shows emoji for image messages', () => {
    // Create a mock image message for the network panel
    const networkImageMessage = {
      id: 'net_img1',
      sender: 'alice',
      recipient: 'bob',
      content: 'data:image/png;base64,fakeImageData',
      timestamp: new Date().toISOString(),
      status: 'delivered',
      networkTimestamp: new Date().toISOString(),
      deliveredTimestamp: new Date().toISOString(),
      type: 'image'
    };
    
    // Render the NetworkPanel with the image message
    render(<NetworkPanel messages={[networkImageMessage]} />);
    
    // Check for image message indicator and emoji in the network panel
    const imageText = screen.getByText(/\[Image\]/i);
    expect(imageText).toBeInTheDocument();
    
    const emoji = screen.getByLabelText('Image');
    expect(emoji).toBeInTheDocument();
    expect(emoji.textContent).toBe('🖼️');
  });
  
  test('NetworkPanel shows text emoji for text messages', () => {
    // Create a mock text message for the network panel
    const networkTextMessage = {
      id: 'net_txt1',
      sender: 'bob',
      recipient: 'alice',
      content: 'Hello, this is a test message',
      timestamp: new Date().toISOString(),
      status: 'delivered',
      networkTimestamp: new Date().toISOString(),
      deliveredTimestamp: new Date().toISOString(),
      type: 'text'
    };
    
    // Render the NetworkPanel with the text message
    render(<NetworkPanel messages={[networkTextMessage]} />);
    
    // Check for text emoji in the network panel
    const textContent = screen.getByText(/Hello, this is a test message/i);
    expect(textContent).toBeInTheDocument();
    
    const emoji = screen.getByLabelText('Text');
    expect(emoji).toBeInTheDocument();
    expect(emoji.textContent).toBe('💬');
  });
}); 