import { QRCodeVerifier } from '../ui/components/verification/QRCodeVerifier';

// Mock console methods to avoid noise in tests
const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

describe('QRCodeVerifier', () => {
  let container;
  let verifier;

  beforeEach(() => {
    // Create a container element for each test
    container = document.createElement('div');
    document.body.appendChild(container);
    
    // Clear console spy
    consoleSpy.mockClear();
  });

  afterEach(() => {
    // Clean up
    if (verifier) {
      verifier.destroy();
    }
    if (container && container.parentNode) {
      container.parentNode.removeChild(container);
    }
    consoleSpy.mockClear();
  });

  afterAll(() => {
    consoleSpy.mockRestore();
  });

  describe('Constructor', () => {
    test('creates verifier with container', () => {
      verifier = new QRCodeVerifier(container);
      
      expect(verifier.container).toBe(container);
      expect(verifier.options.size).toBe(200);
      expect(verifier.options.darkColor).toBe('#000000');
      expect(verifier.options.lightColor).toBe('#FFFFFF');
    });

    test('throws error without container', () => {
      expect(() => {
        new QRCodeVerifier(null);
      }).toThrow('Container element is required');
    });

    test('accepts custom options', () => {
      const options = {
        size: 300,
        darkColor: '#FF0000',
        lightColor: '#00FF00',
        onScan: jest.fn(),
        onVerificationResult: jest.fn()
      };

      verifier = new QRCodeVerifier(container, options);

      expect(verifier.options.size).toBe(300);
      expect(verifier.options.darkColor).toBe('#FF0000');
      expect(verifier.options.lightColor).toBe('#00FF00');
      expect(verifier.options.onScan).toBe(options.onScan);
      expect(verifier.options.onVerificationResult).toBe(options.onVerificationResult);
    });
  });

  describe('DOM Element Creation', () => {
    test('creates required DOM elements', () => {
      verifier = new QRCodeVerifier(container);

      expect(container.children.length).toBe(3);
      
      const generateElement = container.querySelector('.qr-generate');
      const scanElement = container.querySelector('.qr-scan');
      const resultElement = container.querySelector('.qr-result');

      expect(generateElement).toBeTruthy();
      expect(scanElement).toBeTruthy();
      expect(resultElement).toBeTruthy();
      expect(scanElement.classList.contains('hidden')).toBe(true);
    });

    test('stores element references', () => {
      verifier = new QRCodeVerifier(container);

      expect(verifier.generateElement).toBeTruthy();
      expect(verifier.scanElement).toBeTruthy();
      expect(verifier.resultElement).toBeTruthy();
      expect(verifier.generateElement.className).toBe('qr-generate');
      expect(verifier.scanElement.className).toBe('qr-scan hidden');
      expect(verifier.resultElement.className).toBe('qr-result');
    });
  });

  describe('QR Code Generation', () => {
    test('sets data and generates QR code', () => {
      verifier = new QRCodeVerifier(container);
      const testData = 'test-fingerprint-data';

      verifier.setData(testData);

      expect(verifier.currentData).toBe(testData);
      expect(consoleSpy).toHaveBeenCalledWith('Generating QR code for:', testData);
    });

    test('does not regenerate for same data', () => {
      verifier = new QRCodeVerifier(container);
      const testData = 'test-fingerprint-data';

      verifier.setData(testData);
      consoleSpy.mockClear();
      verifier.setData(testData); // Same data

      expect(consoleSpy).not.toHaveBeenCalled();
    });

    test('regenerates for different data', () => {
      verifier = new QRCodeVerifier(container);
      
      verifier.setData('data1');
      consoleSpy.mockClear();
      verifier.setData('data2'); // Different data

      expect(consoleSpy).toHaveBeenCalledWith('Generating QR code for:', 'data2');
    });

    test('generateQRCode returns mock data URL', async () => {
      verifier = new QRCodeVerifier(container);
      
      const result = await verifier.generateQRCode();
      
      expect(result).toBe('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==');
    });
  });

  describe('Camera and Scanning', () => {
    test('starts scanning', () => {
      verifier = new QRCodeVerifier(container);

      verifier.startScanning();

      expect(consoleSpy).toHaveBeenCalledWith('Setting up camera');
      expect(consoleSpy).toHaveBeenCalledWith('Starting QR code scanning');
    });

    test('stops scanning with video element', () => {
      verifier = new QRCodeVerifier(container);
      
      // Mock video element with tracks
      const mockTrack = { stop: jest.fn() };
      const mockStream = { getTracks: jest.fn().mockReturnValue([mockTrack]) };
      verifier.videoElement = { srcObject: mockStream };

      verifier.stopScanning();

      expect(mockStream.getTracks).toHaveBeenCalled();
      expect(mockTrack.stop).toHaveBeenCalled();
    });

    test('stops scanning without video element', () => {
      verifier = new QRCodeVerifier(container);
      
      // Should not throw error
      expect(() => verifier.stopScanning()).not.toThrow();
    });

    test('handles QR code detection with callback', () => {
      const onScan = jest.fn();
      verifier = new QRCodeVerifier(container, { onScan });
      const testData = 'detected-qr-data';

      verifier._onQRCodeDetected(testData);

      expect(onScan).toHaveBeenCalledWith(testData);
    });

    test('handles QR code detection without callback', () => {
      verifier = new QRCodeVerifier(container);
      
      // Should not throw error
      expect(() => verifier._onQRCodeDetected('test-data')).not.toThrow();
    });
  });

  describe('Fingerprint Verification', () => {
    test('verifies matching fingerprints', () => {
      const onVerificationResult = jest.fn();
      verifier = new QRCodeVerifier(container, { onVerificationResult });
      
      const result = verifier.verifyFingerprints('abc123', 'abc123');

      expect(result).toBe(true);
      expect(onVerificationResult).toHaveBeenCalledWith(true);
    });

    test('verifies non-matching fingerprints', () => {
      const onVerificationResult = jest.fn();
      verifier = new QRCodeVerifier(container, { onVerificationResult });
      
      const result = verifier.verifyFingerprints('abc123', 'def456');

      expect(result).toBe(false);
      expect(onVerificationResult).toHaveBeenCalledWith(false);
    });

    test('verifies fingerprints without callback', () => {
      verifier = new QRCodeVerifier(container);
      
      const result = verifier.verifyFingerprints('abc123', 'abc123');

      expect(result).toBe(true);
    });
  });

  describe('UI State Management', () => {
    test('shows verification success result', () => {
      verifier = new QRCodeVerifier(container);

      verifier.showVerificationResult(true);

      expect(verifier.resultElement.classList.contains('success')).toBe(true);
      expect(verifier.resultElement.classList.contains('error')).toBe(false);
    });

    test('shows verification error result', () => {
      verifier = new QRCodeVerifier(container);

      verifier.showVerificationResult(false);

      expect(verifier.resultElement.classList.contains('error')).toBe(true);
      expect(verifier.resultElement.classList.contains('success')).toBe(false);
    });

    test('shows scan mode', () => {
      verifier = new QRCodeVerifier(container);

      verifier.showScanMode();

      expect(verifier.generateElement.classList.contains('hidden')).toBe(true);
      expect(verifier.scanElement.classList.contains('hidden')).toBe(false);
    });

    test('shows generate mode', () => {
      verifier = new QRCodeVerifier(container);
      
      // First set to scan mode
      verifier.showScanMode();
      
      // Then back to generate mode
      verifier.showGenerateMode();

      expect(verifier.generateElement.classList.contains('hidden')).toBe(false);
      expect(verifier.scanElement.classList.contains('hidden')).toBe(true);
    });
  });

  describe('Cleanup and Destruction', () => {
    test('destroys component and cleans up DOM', () => {
      verifier = new QRCodeVerifier(container);
      
      // Mock video element for cleanup testing
      const mockTrack = { stop: jest.fn() };
      const mockStream = { getTracks: jest.fn().mockReturnValue([mockTrack]) };
      verifier.videoElement = { srcObject: mockStream };

      verifier.destroy();

      expect(container.children.length).toBe(0);
      expect(mockTrack.stop).toHaveBeenCalled();
    });

    test('handles destruction with missing container.contains method', () => {
      verifier = new QRCodeVerifier(container);
      
      // Remove contains method to test fallback
      delete container.contains;

      expect(() => verifier.destroy()).not.toThrow();
      expect(container.children.length).toBe(0);
    });

    test('handles destruction with elements not in container', () => {
      verifier = new QRCodeVerifier(container);
      
      // Move elements to different parent
      const otherContainer = document.createElement('div');
      otherContainer.appendChild(verifier.generateElement);
      otherContainer.appendChild(verifier.scanElement);
      otherContainer.appendChild(verifier.resultElement);

      expect(() => verifier.destroy()).not.toThrow();
    });
  });

  describe('Edge Cases', () => {
    test('handles missing result element in showVerificationResult', () => {
      verifier = new QRCodeVerifier(container);
      verifier.resultElement = null;

      expect(() => verifier.showVerificationResult(true)).not.toThrow();
    });

    test('handles missing elements in mode switching', () => {
      verifier = new QRCodeVerifier(container);
      verifier.generateElement = null;
      verifier.scanElement = null;

      expect(() => verifier.showScanMode()).not.toThrow();
      expect(() => verifier.showGenerateMode()).not.toThrow();
    });

    test('handles empty and null data', () => {
      verifier = new QRCodeVerifier(container);

      verifier.setData('');
      expect(verifier.currentData).toBe('');

      verifier.setData(null);
      expect(verifier.currentData).toBe(null);
    });

    test('handles special characters in fingerprints', () => {
      verifier = new QRCodeVerifier(container);
      
      const special1 = 'abc!@#$%^&*()123';
      const special2 = 'abc!@#$%^&*()123';
      
      const result = verifier.verifyFingerprints(special1, special2);
      expect(result).toBe(true);
    });
  });
});
