import { saveMessages, loadMessages, clearMessages } from '../utils/storage';

describe('Storage Utilities', () => {
  // Mock localStorage
  const localStorageMock = (function() {
    let store = {};
    return {
      getItem: jest.fn(key => store[key] || null),
      setItem: jest.fn((key, value) => {
        store[key] = value.toString();
      }),
      removeItem: jest.fn(key => {
        delete store[key];
      }),
      clear: jest.fn(() => {
        store = {};
      })
    };
  })();
  
  // Spy on console.error
  const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
  
  beforeEach(() => {
    // Set up the localStorage mock
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock
    });
    
    // Clear mocks before each test
    jest.clearAllMocks();
    localStorageMock.clear();
  });
  
  afterEach(() => {
    consoleErrorSpy.mockClear();
  });
  
  afterAll(() => {
    consoleErrorSpy.mockRestore();
  });

  test('saveMessages saves messages to localStorage', () => {
    const messages = [
      { id: 'msg1', content: 'Test message' },
      { id: 'msg2', content: 'Another test message' }
    ];
    
    const result = saveMessages(messages);
    
    expect(result).toBe(true);
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'chat_simulator_messages',
      JSON.stringify(messages)
    );
  });
  
  test('saveMessages handles errors gracefully', () => {
    // Make setItem throw an error
    localStorageMock.setItem.mockImplementationOnce(() => {
      throw new Error('Storage error');
    });
    
    const messages = [{ id: 'msg1', content: 'Test message' }];
    
    const result = saveMessages(messages);
    
    expect(result).toBe(false);
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'Error saving messages to localStorage:',
      expect.any(Error)
    );
  });
  
  test('loadMessages loads messages from localStorage', () => {
    const messages = [
      { id: 'msg1', content: 'Test message' },
      { id: 'msg2', content: 'Another test message' }
    ];
    
    // Set up localStorage to return our test messages
    localStorageMock.getItem.mockReturnValueOnce(JSON.stringify(messages));
    
    const result = loadMessages();
    
    expect(result).toEqual(messages);
    expect(localStorageMock.getItem).toHaveBeenCalledWith('chat_simulator_messages');
  });
  
  test('loadMessages returns empty array when no messages are found', () => {
    // Return null to simulate no messages in localStorage
    localStorageMock.getItem.mockReturnValueOnce(null);
    
    const result = loadMessages();
    
    expect(result).toEqual([]);
    expect(localStorageMock.getItem).toHaveBeenCalledWith('chat_simulator_messages');
  });
  
  test('loadMessages handles JSON parse errors', () => {
    // Return invalid JSON
    localStorageMock.getItem.mockReturnValueOnce('{invalid-json');
    
    const result = loadMessages();
    
    expect(result).toEqual([]);
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'Error loading messages from localStorage:',
      expect.any(Error)
    );
  });
  
  test('clearMessages removes messages from localStorage', () => {
    const result = clearMessages();
    
    expect(result).toBe(true);
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('chat_simulator_messages');
  });
  
  test('clearMessages handles errors gracefully', () => {
    // Make removeItem throw an error
    localStorageMock.removeItem.mockImplementationOnce(() => {
      throw new Error('Storage error');
    });
    
    const result = clearMessages();
    
    expect(result).toBe(false);
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'Error clearing messages from localStorage:',
      expect.any(Error)
    );
  });
}); 