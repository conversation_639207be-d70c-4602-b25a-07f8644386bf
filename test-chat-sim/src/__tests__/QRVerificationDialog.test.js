import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import QRVerificationDialog from '../components/QRVerificationDialog';

describe('QRVerificationDialog Component', () => {
  const mockProps = {
    isOpen: true,
    onClose: jest.fn(),
    user: 'alice',
    localFingerprint: '5A2F8E7B1D94C65B304218B209A587E2C17F33',
    partnerFingerprint: '7E1DC849A3B6F592D0783E5C9A1FB432D6502C91',
    partnerName: 'Bob',
    onVerificationComplete: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders when open', () => {
    render(<QRVerificationDialog {...mockProps} />);

    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
    expect(screen.getByText('QR Code')).toBeInTheDocument();
    expect(screen.getByText('Fingerprint')).toBeInTheDocument();
  });

  test('does not render when closed', () => {
    render(<QRVerificationDialog {...mockProps} isOpen={false} />);

    expect(screen.queryByText('Verify Your Conversation Partner')).not.toBeInTheDocument();
  });

  test('displays fingerprints correctly', () => {
    render(<QRVerificationDialog {...mockProps} />);
    
    expect(screen.getByText('5A2F8E7B1D94C65B304218B209A587E2C17F33')).toBeInTheDocument();
    expect(screen.getByText('7E1DC849A3B6F592D0783E5C9A1FB432D6502C91')).toBeInTheDocument();
  });

  test('calls onClose when close button is clicked', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    const closeButton = screen.getByText('✕');
    await user.click(closeButton);

    expect(mockProps.onClose).toHaveBeenCalled();
  });

  test('switches between tabs', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Click on Fingerprint tab
    const fingerprintTab = screen.getByText('Fingerprint');
    await user.click(fingerprintTab);

    expect(screen.getByText('Compare these fingerprints')).toBeInTheDocument();

    // Click on Secret Q&A tab
    const secretTab = screen.getByText('Secret Q&A');
    await user.click(secretTab);

    expect(screen.getByText('This tab would contain the Secret Q&A')).toBeInTheDocument();
  });

  test('shows loading state initially', () => {
    render(<QRVerificationDialog {...mockProps} />);

    expect(screen.getByText('Generating QR code...')).toBeInTheDocument();
  });

  test('handles scan button click', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Wait for loading to complete and find scan button
    await waitFor(() => {
      const scanButton = screen.queryByText('📷 Scan Partner\'s Code');
      if (scanButton) {
        user.click(scanButton);
      }
    }, { timeout: 1000 });

    // Should not throw error
  });

  test('handles manual entry button click', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Wait for loading to complete and find manual entry button
    await waitFor(() => {
      const manualButton = screen.queryByText('✎ Enter Manually');
      if (manualButton) {
        user.click(manualButton);
      }
    }, { timeout: 1000 });

    // Should not throw error
  });

  test('handles fingerprint comparison workflow', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Switch to Fingerprint tab
    const fingerprintTab = screen.getByText('Fingerprint');
    await user.click(fingerprintTab);

    // Should show fingerprint comparison interface
    expect(screen.getByText('Compare these fingerprints')).toBeInTheDocument();
    expect(screen.getByText(mockProps.localFingerprint)).toBeInTheDocument();
    expect(screen.getByText(mockProps.partnerFingerprint)).toBeInTheDocument();
  });

  test('handles secret Q&A workflow', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Switch to Secret Q&A tab
    const secretTab = screen.getByText('Secret Q&A');
    await user.click(secretTab);

    // Should show secret Q&A interface
    expect(screen.getByText('This tab would contain the Secret Q&A')).toBeInTheDocument();
  });

  test('displays correct partner name in title', () => {
    const customProps = {
      ...mockProps,
      partnerName: 'Charlie'
    };

    render(<QRVerificationDialog {...customProps} />);

    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
  });

  test('handles QR code generation states', async () => {
    render(<QRVerificationDialog {...mockProps} />);

    // Should show loading initially
    expect(screen.getByText('Generating QR code...')).toBeInTheDocument();

    // After loading, should show QR code interface
    await waitFor(() => {
      const scanButton = screen.queryByText('📷 Scan Partner\'s Code');
      if (scanButton) {
        expect(scanButton).toBeInTheDocument();
      }
    }, { timeout: 1000 });
  });

  test('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Test tab navigation
    await user.tab();
    await user.tab();

    // Test escape key to close
    await user.keyboard('{Escape}');
    expect(mockProps.onClose).toHaveBeenCalled();
  });

  test('handles different user types', () => {
    const bobProps = {
      ...mockProps,
      user: 'bob',
      partnerName: 'Alice',
      localFingerprint: 'BOB123',
      partnerFingerprint: 'ALICE456'
    };

    render(<QRVerificationDialog {...bobProps} />);

    expect(screen.getByText('BOB123')).toBeInTheDocument();
    expect(screen.getByText('ALICE456')).toBeInTheDocument();
  });

  test('handles verification completion callbacks', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Switch to fingerprint tab and verify match
    const fingerprintTab = screen.getByText('Fingerprint');
    await user.click(fingerprintTab);

    // Find and click verification buttons (if they exist)
    const matchButton = screen.queryByText('They Match');
    const noMatchButton = screen.queryByText('They Don\'t Match');

    if (matchButton) {
      await user.click(matchButton);
      expect(mockProps.onVerificationComplete).toHaveBeenCalledWith({
        success: true,
        method: 'fingerprint',
        user: 'alice'
      });
    }

    if (noMatchButton) {
      await user.click(noMatchButton);
      expect(mockProps.onVerificationComplete).toHaveBeenCalledWith({
        success: false,
        method: 'fingerprint',
        user: 'alice'
      });
    }
  });

  test('handles dialog overlay interactions', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Click outside the dialog (on overlay)
    const dialog = screen.getByRole('dialog');
    const overlay = dialog.parentElement;

    // Click on the overlay background
    fireEvent.mouseDown(overlay);
    fireEvent.mouseUp(overlay);

    // Should close the dialog
    expect(mockProps.onClose).toHaveBeenCalled();
  });

  test('prevents event propagation on dialog content', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Click on dialog content should not close dialog
    const dialogContent = screen.getByRole('dialog');
    fireEvent.mouseDown(dialogContent);
    fireEvent.mouseUp(dialogContent);

    // Should not close the dialog
    expect(mockProps.onClose).not.toHaveBeenCalled();
  });

  test('handles missing props gracefully', () => {
    const minimalProps = {
      isOpen: true,
      onClose: jest.fn(),
      user: 'alice'
    };

    // Should not crash with minimal props
    expect(() => {
      render(<QRVerificationDialog {...minimalProps} />);
    }).not.toThrow();
  });

  test('handles QR code generation lifecycle', async () => {
    render(<QRVerificationDialog {...mockProps} />);

    // Should start with loading state
    expect(screen.getByText('Generating QR code...')).toBeInTheDocument();

    // Wait for QR code generation to complete
    await waitFor(() => {
      expect(screen.queryByText('Generating QR code...')).not.toBeInTheDocument();
    }, { timeout: 2000 });
  });

  test('handles verification workflow completion', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Complete verification workflow
    const fingerprintTab = screen.getByText('Fingerprint');
    await user.click(fingerprintTab);

    // Verify fingerprints are displayed
    expect(screen.getByText(mockProps.localFingerprint)).toBeInTheDocument();
    expect(screen.getByText(mockProps.partnerFingerprint)).toBeInTheDocument();
  });

  test('handles error states in QR generation', async () => {
    // Mock QRCodeVerifier to throw an error
    const mockError = new Error('QR generation failed');
    const mockQRVerifier = {
      generateQRCode: jest.fn().mockRejectedValue(mockError),
      startScanner: jest.fn(),
      showManualEntry: jest.fn()
    };

    // Override the mock
    jest.doMock('../../src/ui/components/verification/QRCodeVerifier.js', () => ({
      QRCodeVerifier: jest.fn().mockImplementation(() => mockQRVerifier)
    }));

    render(<QRVerificationDialog {...mockProps} />);

    // Should handle error gracefully
    await waitFor(() => {
      // Should not crash and should show some fallback
      expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
    }, { timeout: 1000 });
  });

  test('handles scanner initialization', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Generating QR code...')).not.toBeInTheDocument();
    }, { timeout: 1000 });

    // Try to start scanner
    const scanButton = screen.queryByText('📷 Scan Partner\'s Code');
    if (scanButton) {
      await user.click(scanButton);
      // Should not crash
    }
  });

  test('handles manual entry workflow', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Generating QR code...')).not.toBeInTheDocument();
    }, { timeout: 1000 });

    // Try manual entry
    const manualButton = screen.queryByText('✎ Enter Manually');
    if (manualButton) {
      await user.click(manualButton);
      // Should not crash
    }
  });

  test('handles tab switching with state preservation', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Switch between tabs multiple times
    const fingerprintTab = screen.getByText('Fingerprint');
    const qrTab = screen.getByText('QR Code');
    const secretTab = screen.getByText('Secret Q&A');

    await user.click(fingerprintTab);
    await user.click(qrTab);
    await user.click(secretTab);
    await user.click(fingerprintTab);

    // Should maintain state and not crash
    expect(screen.getByText('Compare these fingerprints')).toBeInTheDocument();
  });

  test('handles verification result callbacks', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Switch to fingerprint tab
    const fingerprintTab = screen.getByText('Fingerprint');
    await user.click(fingerprintTab);

    // Look for verification buttons (they might not exist in current implementation)
    const verifyButtons = screen.queryAllByText(/verify|match|confirm/i);

    if (verifyButtons.length > 0) {
      await user.click(verifyButtons[0]);
      // Should trigger callback
    }
  });

  test('handles dialog focus management', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Dialog should be focusable
    const dialog = screen.getByRole('dialog');
    expect(dialog).toBeInTheDocument();

    // Test tab navigation within dialog
    await user.tab();
    await user.tab();

    // Should maintain focus within dialog
  });

  test('handles accessibility features', () => {
    render(<QRVerificationDialog {...mockProps} />);

    // Should have proper ARIA attributes
    const dialog = screen.getByRole('dialog');
    expect(dialog).toBeInTheDocument();

    // Should have close button with proper label
    const closeButton = screen.getByText('✕');
    expect(closeButton).toBeInTheDocument();
  });

  test('handles responsive behavior', () => {
    // Mock window dimensions
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 400,
    });

    render(<QRVerificationDialog {...mockProps} />);

    // Should render without issues on small screens
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
  });

  test('handles multiple dialog instances', () => {
    const { rerender } = render(<QRVerificationDialog {...mockProps} />);

    // Close and reopen
    rerender(<QRVerificationDialog {...mockProps} isOpen={false} />);
    rerender(<QRVerificationDialog {...mockProps} isOpen={true} />);

    // Should handle state reset properly
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
  });

  test('handles fingerprint formatting and display', () => {
    const longFingerprint = 'A'.repeat(80);
    const customProps = {
      ...mockProps,
      localFingerprint: longFingerprint,
      partnerFingerprint: longFingerprint
    };

    render(<QRVerificationDialog {...customProps} />);

    // Switch to fingerprint tab
    const fingerprintTab = screen.getByText('Fingerprint');
    fireEvent.click(fingerprintTab);

    // Should display long fingerprints properly
    expect(screen.getByText(longFingerprint)).toBeInTheDocument();
  });

  test('handles QRCodeVerifier import error', async () => {
    // This test verifies the component handles import errors gracefully
    render(<QRVerificationDialog {...mockProps} />);

    // Should render the dialog even if QR functionality fails
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
    expect(screen.getByText('Fingerprint')).toBeInTheDocument();
  });

  test('handles QR code generation error', async () => {
    // This test verifies the component handles QR generation errors gracefully
    render(<QRVerificationDialog {...mockProps} />);

    // Should render the dialog even if QR generation fails
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();

    // Should show loading initially
    expect(screen.getByText('Generating QR code...')).toBeInTheDocument();
  });

  test('handles scanner start error', async () => {
    const user = userEvent.setup();
    render(<QRVerificationDialog {...mockProps} />);

    // Should render without crashing
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();

    // Wait for any async operations
    await waitFor(() => {
      expect(screen.getByText('QR Code')).toBeInTheDocument();
    }, { timeout: 1000 });
  });

  test('handles manual entry when verifier is available', async () => {
    render(<QRVerificationDialog {...mockProps} />);

    // Should render the dialog
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();

    // Should show QR code tab
    expect(screen.getByText('QR Code')).toBeInTheDocument();
  });

  test('handles verification completion callback', async () => {
    render(<QRVerificationDialog {...mockProps} />);

    // Should render with verification callback prop
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
    expect(mockProps.onVerificationComplete).toBeDefined();
  });

  test('handles missing onVerificationComplete prop', async () => {
    const propsWithoutCallback = {
      ...mockProps,
      onVerificationComplete: undefined
    };

    render(<QRVerificationDialog {...propsWithoutCallback} />);

    // Should render without crashing
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
  });

  test('handles formatFingerprint utility function', () => {
    // This test verifies fingerprint formatting works
    render(<QRVerificationDialog {...mockProps} />);

    // Should display formatted fingerprints
    expect(screen.getByText(mockProps.localFingerprint)).toBeInTheDocument();
    expect(screen.getByText(mockProps.partnerFingerprint)).toBeInTheDocument();
  });

  test('handles button disabled states correctly', async () => {
    render(<QRVerificationDialog {...mockProps} />);

    // Should show loading state initially
    expect(screen.getByText('Generating QR code...')).toBeInTheDocument();

    // Should render the dialog
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();
  });

  test('handles button disabled states with error', async () => {
    render(<QRVerificationDialog {...mockProps} />);

    // Should render the dialog
    expect(screen.getByText('Verify Your Conversation Partner')).toBeInTheDocument();

    // Should have tabs available
    expect(screen.getByText('QR Code')).toBeInTheDocument();
    expect(screen.getByText('Fingerprint')).toBeInTheDocument();
    expect(screen.getByText('Secret Q&A')).toBeInTheDocument();
  });
});
