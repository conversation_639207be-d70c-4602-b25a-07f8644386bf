/**
 * Steganography Integration Tests for Test Chat Simulator
 * Comprehensive testing of steganography functionality in the simulated environment
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { 
  SteganographyOTR, 
  OTRSteganographySession, 
  SteganographyTestUtils 
} from '../utils/steganographyIntegration';

describe('Steganography Integration Tests', () => {
  let stego;
  let testImage;

  beforeEach(() => {
    stego = new SteganographyOTR({
      quality: 0.95,
      password: 'test-password-123'
    });
    
    testImage = SteganographyTestUtils.createTestImage(512, 512, 'gradient');
  });

  describe('Core Steganography Functionality', () => {
    test('should hide and reveal messages in test environment', async () => {
      const message = 'Hello from test environment!';
      
      const stegoImage = await stego.hideMessage(testImage, message);
      expect(stegoImage).toBeDefined();
      expect(stegoImage._hasHiddenData).toBe(true);
      
      const revealedMessage = await stego.revealMessage(stegoImage);
      expect(revealedMessage).toBe(message);
    });

    test('should detect hidden messages', async () => {
      const message = 'Detection test message';
      
      // Clean image should not have hidden data
      const hasHiddenBefore = await stego.detectMessage(testImage);
      expect(hasHiddenBefore).toBe(false);
      
      // Stego image should have hidden data
      const stegoImage = await stego.hideMessage(testImage, message);
      const hasHiddenAfter = await stego.detectMessage(stegoImage);
      expect(hasHiddenAfter).toBe(true);
    });

    test('should calculate image capacity', async () => {
      const capacity = await stego.calculateCapacity(testImage);
      expect(capacity).toBeGreaterThan(0);
      expect(capacity).toBe(Math.floor(testImage.width * testImage.height * 0.1));
    });

    test('should generate cover images', async () => {
      const styles = ['noise', 'gradient', 'pattern'];
      
      for (const style of styles) {
        const coverImage = await stego.generateCover(256, 256, style);
        expect(coverImage).toBeDefined();
        expect(coverImage.width).toBe(256);
        expect(coverImage.height).toBe(256);
        expect(coverImage._generated).toBe(true);
        expect(coverImage._style).toBe(style);
      }
    });
  });

  describe('OTR Steganography Session Integration', () => {
    let alice, bob;

    beforeEach(() => {
      alice = new OTRSteganographySession({
        steganographyEnabled: true,
        stegoPassword: 'shared-secret',
        clientName: 'Alice'
      });
      
      bob = new OTRSteganographySession({
        steganographyEnabled: true,
        stegoPassword: 'shared-secret',
        clientName: 'Bob'
      });
    });

    afterEach(() => {
      alice.destroy();
      bob.destroy();
    });

    test('should enable and disable steganography', () => {
      expect(alice.stegoState.enabled).toBe(true);
      
      alice.disableSteganography();
      expect(alice.stegoState.enabled).toBe(false);
      
      alice.enableSteganography();
      expect(alice.stegoState.enabled).toBe(true);
    });

    test('should send and receive steganographic messages', async () => {
      const message = 'Secret message from Alice to Bob';
      const coverImage = await alice.generateCoverImage(1000, 'gradient');
      
      // Alice sends message
      const stegoImage = await alice.sendStegoMessage(message, coverImage);
      expect(stegoImage).toBeDefined();
      expect(stegoImage._hasHiddenData).toBe(true);
      
      // Bob receives message
      const receivedMessage = await bob.processStegoImage(stegoImage);
      expect(receivedMessage).toBe(message);
    });

    test('should handle auto-detection', async () => {
      const message = 'Auto-detection test message';
      const coverImage = await alice.generateCoverImage(500, 'noise');
      
      // Create stego image
      const stegoImage = await alice.sendStegoMessage(message, coverImage);
      
      // Auto-detect should find the message
      const autoResult = await bob.autoProcessImage(stegoImage);
      expect(autoResult).toBeDefined();
      expect(autoResult.type).toBe('message');
      expect(autoResult.content).toBe(message);
    });

    test('should provide steganography statistics', () => {
      const stats = alice.getStegoStats();
      expect(stats).toBeDefined();
      expect(stats.enabled).toBe(true);
      expect(stats.autoDetect).toBe(true);
      expect(typeof stats.cachedImages).toBe('number');
      expect(typeof stats.quality).toBe('number');
    });

    test('should handle cache management', async () => {
      const message = 'Cache test message';
      const coverImage = await alice.generateCoverImage(500, 'pattern');
      
      // Send message to populate cache
      await alice.sendStegoMessage(message, coverImage);
      expect(alice.stegoState.coverImages.size).toBe(1);
      
      // Clear cache
      alice.clearStegoCache();
      expect(alice.stegoState.coverImages.size).toBe(0);
    });
  });

  describe('Test Environment Utilities', () => {
    test('should create test images with different patterns', () => {
      const patterns = ['random', 'solid', 'gradient'];
      
      for (const pattern of patterns) {
        const image = SteganographyTestUtils.createTestImage(100, 100, pattern);
        expect(image).toBeDefined();
        expect(image.width).toBe(100);
        expect(image.height).toBe(100);
        expect(image.data).toBeInstanceOf(Uint8ClampedArray);
        expect(image.data.length).toBe(100 * 100 * 4);
      }
    });

    test('should convert image data to data URL', () => {
      const image = SteganographyTestUtils.createTestImage(50, 50, 'solid');
      const dataURL = SteganographyTestUtils.imageDataToDataURL(image);
      
      expect(dataURL).toBeDefined();
      expect(dataURL.startsWith('data:image/png;base64,')).toBe(true);
    });

    test('should create test image data URLs', () => {
      const dataURL = SteganographyTestUtils.createTestImageDataURL(64, 64, 'gradient');
      
      expect(dataURL).toBeDefined();
      expect(dataURL.startsWith('data:image/png;base64,')).toBe(true);
    });
  });

  describe('Real-World Simulation Scenarios', () => {
    test('should simulate journalist-source communication', async () => {
      const journalist = new OTRSteganographySession({
        steganographyEnabled: true,
        stegoPassword: 'press-freedom-2024',
        clientName: 'Journalist'
      });
      
      const source = new OTRSteganographySession({
        steganographyEnabled: true,
        stegoPassword: 'press-freedom-2024',
        clientName: 'Source'
      });
      
      // Source sends sensitive information
      const sensitiveInfo = JSON.stringify({
        type: 'leak',
        subject: 'Government Contract Irregularities',
        meetingLocation: 'Central Park, Bethesda Fountain',
        meetingTime: '2024-01-15T14:00:00Z',
        safetyCode: 'blue-bird-sings'
      });
      
      const newsPhoto = await source.generateCoverImage(1000, 'noise');
      const stegoImage = await source.sendStegoMessage(sensitiveInfo, newsPhoto);
      const receivedInfo = await journalist.processStegoImage(stegoImage);
      
      expect(receivedInfo).toBe(sensitiveInfo);
      
      const parsedInfo = JSON.parse(receivedInfo);
      expect(parsedInfo.type).toBe('leak');
      expect(parsedInfo.safetyCode).toBe('blue-bird-sings');
      
      journalist.destroy();
      source.destroy();
    });

    test('should simulate social media image sharing', async () => {
      const alice = new OTRSteganographySession({
        steganographyEnabled: true,
        stegoPassword: 'social-media-secret',
        clientName: 'Alice'
      });
      
      const bob = new OTRSteganographySession({
        steganographyEnabled: true,
        stegoPassword: 'social-media-secret',
        clientName: 'Bob'
      });
      
      // Simulate different social media image formats
      const platforms = [
        { name: 'Instagram', size: [1080, 1080] },
        { name: 'Facebook', size: [1200, 630] },
        { name: 'Twitter', size: [1024, 512] }
      ];
      
      for (const platform of platforms) {
        const message = `Secret message via ${platform.name}`;
        const socialImage = await alice.generateCoverImage(
          Math.max(...platform.size), 
          'gradient'
        );
        
        const stegoImage = await alice.sendStegoMessage(message, socialImage);
        const receivedMessage = await bob.processStegoImage(stegoImage);
        
        expect(receivedMessage).toBe(message);
      }
      
      alice.destroy();
      bob.destroy();
    });

    test('should handle multiple concurrent conversations', async () => {
      const participants = [];
      
      // Create multiple participants
      for (let i = 0; i < 5; i++) {
        participants.push(new OTRSteganographySession({
          steganographyEnabled: true,
          stegoPassword: 'group-secret',
          clientName: `User${i}`
        }));
      }
      
      const messages = [];
      const coverImage = await participants[0].generateCoverImage(800, 'pattern');
      
      // Each participant sends a message
      for (let i = 0; i < participants.length; i++) {
        const message = `Message from User${i}: This is a test of concurrent steganography`;
        const stegoImage = await participants[i].sendStegoMessage(message, coverImage);
        messages.push({ sender: i, message, stegoImage });
      }
      
      // Each participant tries to read all messages
      for (let receiver = 0; receiver < participants.length; receiver++) {
        for (const { sender, message, stegoImage } of messages) {
          const receivedMessage = await participants[receiver].processStegoImage(stegoImage);
          expect(receivedMessage).toBe(message);
        }
      }
      
      // Cleanup
      participants.forEach(p => p.destroy());
    });
  });

  describe('Performance and Stress Testing', () => {
    test('should handle rapid message processing', async () => {
      const alice = new OTRSteganographySession({
        steganographyEnabled: true,
        stegoPassword: 'performance-test',
        clientName: 'Alice'
      });

      const bob = new OTRSteganographySession({
        steganographyEnabled: true,
        stegoPassword: 'performance-test',
        clientName: 'Bob'
      });

      const messageCount = 10; // Reduced for faster testing
      const results = [];
      
      for (let i = 0; i < messageCount; i++) {
        const message = `Rapid test message ${i}`;
        const coverImage = await alice.generateCoverImage(256, 'noise');
        
        const startTime = Date.now();
        const stegoImage = await alice.sendStegoMessage(message, coverImage);
        const receivedMessage = await bob.processStegoImage(stegoImage);
        const endTime = Date.now();
        
        results.push({
          success: receivedMessage === message,
          processingTime: endTime - startTime
        });
      }
      
      // All messages should be processed successfully
      const successCount = results.filter(r => r.success).length;
      expect(successCount).toBe(messageCount);
      
      // Average processing time should be reasonable
      const avgTime = results.reduce((sum, r) => sum + r.processingTime, 0) / results.length;
      expect(avgTime).toBeLessThan(1000); // Less than 1 second average
      
      alice.destroy();
      bob.destroy();
    }, 10000); // 10 second timeout

    test('should handle various message sizes', async () => {
      const alice = new OTRSteganographySession({
        steganographyEnabled: true,
        stegoPassword: 'size-test',
        clientName: 'Alice'
      });
      
      const bob = new OTRSteganographySession({
        steganographyEnabled: true,
        stegoPassword: 'size-test',
        clientName: 'Bob'
      });
      
      const messageSizes = [10, 100, 500, 1000, 5000];
      
      for (const size of messageSizes) {
        const message = 'X'.repeat(size);
        const coverImage = await alice.generateCoverImage(1024, 'gradient');
        
        const stegoImage = await alice.sendStegoMessage(message, coverImage);
        const receivedMessage = await bob.processStegoImage(stegoImage);
        
        expect(receivedMessage).toBe(message);
        expect(receivedMessage.length).toBe(size);
      }
      
      alice.destroy();
      bob.destroy();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle disabled steganography gracefully', async () => {
      const alice = new OTRSteganographySession({
        steganographyEnabled: false,
        clientName: 'Alice'
      });
      
      const coverImage = SteganographyTestUtils.createTestImage(256, 256);
      
      await expect(alice.sendStegoMessage('test', coverImage))
        .rejects.toThrow('Steganography not enabled');
      
      alice.destroy();
    });

    test('should handle invalid images gracefully', async () => {
      const alice = new OTRSteganographySession({
        steganographyEnabled: true,
        clientName: 'Alice'
      });
      
      const invalidImage = { invalid: 'data' };
      
      const result = await alice.processStegoImage(invalidImage);
      expect(result).toBeNull();
      
      alice.destroy();
    });

    test('should handle auto-detection with clean images', async () => {
      const bob = new OTRSteganographySession({
        steganographyEnabled: true,
        clientName: 'Bob'
      });
      
      const cleanImage = SteganographyTestUtils.createTestImage(256, 256);
      const result = await bob.autoProcessImage(cleanImage);
      
      expect(result).toBeNull();
      
      bob.destroy();
    });
  });
});
