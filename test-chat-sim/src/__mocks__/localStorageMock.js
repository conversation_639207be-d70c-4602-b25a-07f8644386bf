// Mock localStorage
const createLocalStorageMock = () => {
  let store = {};
  return {
    getItem: jest.fn(key => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn(key => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    })
  };
};

// Create the mock
const localStorageMock = createLocalStorageMock();

// Set up localStorage mock
const setupLocalStorageMock = () => {
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true
  });
};

// Reset all mock functions and clear storage
const resetLocalStorageMock = () => {
  localStorageMock.getItem.mockClear();
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();
  localStorageMock.clear();
};

export { localStorageMock, setupLocalStorageMock, resetLocalStorageMock }; 