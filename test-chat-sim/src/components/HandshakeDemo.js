/**
 * Handshake Demo Component
 * Interactive demonstration of OTR handshake functionality in the test app
 */

import React, { useState, useEffect, useRef } from 'react';

// Mock OTR Session for handshake demonstration
class MockOTRSession {
  constructor(options = {}) {
    this.options = {
      testing: true,
      clientName: options.clientName || 'TestClient',
      ...options
    };
    
    this.instanceTag = Math.floor(Math.random() * 0xFFFFFFFF);
    this.state = {
      state: 0, // PLAINTEXT
      authState: null,
      instanceTag: this.instanceTag
    };
    
    this.handshakeState = {
      initiated: false,
      dhCommitSent: false,
      dhKeySent: false,
      revealSignatureSent: false,
      signatureSent: false,
      completed: false,
      encrypted: false
    };
    
    this.callbacks = {};
    this.messageQueue = [];
    this.handshakeMessages = [];
  }

  async startOtr() {
    this.handshakeState.initiated = true;
    this.state.state = 1; // AWAITING_DHKEY
    
    const dhCommitMessage = `?OTR:DH_COMMIT:${this.instanceTag}:${Date.now()}:${Math.random().toString(36)}`;
    this.handshakeState.dhCommitSent = true;
    this.handshakeMessages.push({
      type: 'DH_COMMIT',
      message: dhCommitMessage,
      timestamp: Date.now(),
      direction: 'sent'
    });
    
    this._notifyStateChange();
    this._notifyHandshakeProgress('DH_COMMIT_SENT');
    return true;
  }

  async performHandshakeWith(otherSession, progressCallback) {
    const steps = [];
    
    // Step 1: Alice sends DH Commit
    await this.startOtr();
    steps.push({ step: 'DH_COMMIT_SENT', timestamp: Date.now(), actor: this.options.clientName });
    if (progressCallback) progressCallback(steps[steps.length - 1]);
    
    await this._delay(200);
    
    // Step 2: Bob responds with DH Key
    const dhKeyResponse = await otherSession._respondToDHCommit();
    steps.push({ step: 'DH_KEY_RECEIVED', timestamp: Date.now(), actor: otherSession.options.clientName });
    if (progressCallback) progressCallback(steps[steps.length - 1]);
    
    await this._delay(150);
    
    // Step 3: Alice sends Reveal Signature
    const revealSigResponse = await this._sendRevealSignature();
    steps.push({ step: 'REVEAL_SIGNATURE_SENT', timestamp: Date.now(), actor: this.options.clientName });
    if (progressCallback) progressCallback(steps[steps.length - 1]);
    
    await this._delay(100);
    
    // Step 4: Bob sends Signature
    const signatureResponse = await otherSession._sendSignature();
    steps.push({ step: 'SIGNATURE_RECEIVED', timestamp: Date.now(), actor: otherSession.options.clientName });
    if (progressCallback) progressCallback(steps[steps.length - 1]);
    
    await this._delay(100);
    
    // Step 5: Complete handshake
    this.handshakeState.completed = true;
    this.handshakeState.encrypted = true;
    this.state.state = 4; // ENCRYPTED
    
    otherSession.handshakeState.completed = true;
    otherSession.handshakeState.encrypted = true;
    otherSession.state.state = 4; // ENCRYPTED
    
    steps.push({ step: 'HANDSHAKE_COMPLETED', timestamp: Date.now(), actor: 'BOTH' });
    if (progressCallback) progressCallback(steps[steps.length - 1]);
    
    return {
      success: true,
      steps,
      duration: Date.now() - this.handshakeMessages[0].timestamp,
      messagesExchanged: this.handshakeMessages.length + otherSession.handshakeMessages.length
    };
  }

  getHandshakeStatus() {
    return {
      ...this.handshakeState,
      currentState: this._getStateName(this.state.state),
      messagesExchanged: this.handshakeMessages.length,
      timeElapsed: this.handshakeMessages.length > 0 ? 
        Date.now() - this.handshakeMessages[0].timestamp : 0,
      instanceTag: this.instanceTag
    };
  }

  onStateChange(callback) {
    this.callbacks.stateChange = callback;
  }

  onHandshakeProgress(callback) {
    this.callbacks.handshakeProgress = callback;
  }

  destroy() {
    this.callbacks = {};
    this.messageQueue = [];
    this.handshakeMessages = [];
  }

  // Private methods
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  _respondToDHCommit() {
    this.state.state = 2; // AWAITING_REVEALSIG
    this.handshakeState.dhKeySent = true;
    
    const dhKeyMessage = `?OTR:DH_KEY:${this.instanceTag}:${Date.now()}:${Math.random().toString(36)}`;
    this.handshakeMessages.push({
      type: 'DH_KEY',
      message: dhKeyMessage,
      timestamp: Date.now(),
      direction: 'sent'
    });
    
    this._notifyHandshakeProgress('DH_KEY_SENT');
    return dhKeyMessage;
  }

  _sendRevealSignature() {
    this.handshakeState.revealSignatureSent = true;
    const revealSigMessage = `?OTR:REVEAL_SIGNATURE:${this.instanceTag}:${Date.now()}:${Math.random().toString(36)}`;
    this.handshakeMessages.push({
      type: 'REVEAL_SIGNATURE',
      message: revealSigMessage,
      timestamp: Date.now(),
      direction: 'sent'
    });
    
    this._notifyHandshakeProgress('REVEAL_SIGNATURE_SENT');
    return revealSigMessage;
  }

  _sendSignature() {
    this.handshakeState.signatureSent = true;
    const signatureMessage = `?OTR:SIGNATURE:${this.instanceTag}:${Date.now()}:${Math.random().toString(36)}`;
    this.handshakeMessages.push({
      type: 'SIGNATURE',
      message: signatureMessage,
      timestamp: Date.now(),
      direction: 'sent'
    });
    
    this._notifyHandshakeProgress('SIGNATURE_SENT');
    return signatureMessage;
  }

  _getStateName(state) {
    const states = {
      0: 'PLAINTEXT',
      1: 'AWAITING_DHKEY',
      2: 'AWAITING_REVEALSIG',
      3: 'AWAITING_SIG',
      4: 'ENCRYPTED',
      5: 'FINISHED'
    };
    return states[state] || 'UNKNOWN';
  }

  _notifyStateChange() {
    if (this.callbacks.stateChange) {
      this.callbacks.stateChange(this.state);
    }
  }

  _notifyHandshakeProgress(step) {
    if (this.callbacks.handshakeProgress) {
      this.callbacks.handshakeProgress({
        step,
        status: this.getHandshakeStatus(),
        timestamp: Date.now()
      });
    }
  }
}

const HandshakeDemo = () => {
  const [alice, setAlice] = useState(null);
  const [bob, setBob] = useState(null);
  const [handshakeInProgress, setHandshakeInProgress] = useState(false);
  const [handshakeSteps, setHandshakeSteps] = useState([]);
  const [handshakeResult, setHandshakeResult] = useState(null);
  const [logs, setLogs] = useState([]);
  const [aliceStatus, setAliceStatus] = useState(null);
  const [bobStatus, setBobStatus] = useState(null);
  const [performanceMetrics, setPerformanceMetrics] = useState(null);

  useEffect(() => {
    // Initialize sessions
    const aliceSession = new MockOTRSession({ clientName: 'Alice' });
    const bobSession = new MockOTRSession({ clientName: 'Bob' });
    
    setAlice(aliceSession);
    setBob(bobSession);
    
    addLog('Handshake demo initialized');
    addLog(`Alice instance tag: ${aliceSession.instanceTag.toString(16).toUpperCase()}`);
    addLog(`Bob instance tag: ${bobSession.instanceTag.toString(16).toUpperCase()}`);
    
    updateStatus();
    
    return () => {
      if (aliceSession) aliceSession.destroy();
      if (bobSession) bobSession.destroy();
    };
  }, []);

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-9), `[${timestamp}] ${message}`]);
  };

  const updateStatus = () => {
    if (alice && bob) {
      setAliceStatus(alice.getHandshakeStatus());
      setBobStatus(bob.getHandshakeStatus());
    }
  };

  const startHandshake = async () => {
    if (!alice || !bob || handshakeInProgress) return;
    
    setHandshakeInProgress(true);
    setHandshakeSteps([]);
    setHandshakeResult(null);
    setPerformanceMetrics(null);
    
    addLog('Starting OTR handshake...');
    
    const startTime = Date.now();
    
    try {
      const result = await alice.performHandshakeWith(bob, (step) => {
        setHandshakeSteps(prev => [...prev, step]);
        addLog(`${step.actor}: ${step.step.replace(/_/g, ' ')}`);
        updateStatus();
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      setHandshakeResult(result);
      setPerformanceMetrics({
        duration,
        messagesPerSecond: (result.messagesExchanged / (duration / 1000)).toFixed(2),
        avgStepTime: (duration / result.steps.length).toFixed(0)
      });
      
      addLog(`Handshake completed successfully in ${duration}ms`);
      addLog(`Messages exchanged: ${result.messagesExchanged}`);
      addLog(`Average step time: ${(duration / result.steps.length).toFixed(0)}ms`);
      
    } catch (error) {
      addLog(`Handshake failed: ${error.message}`);
    } finally {
      setHandshakeInProgress(false);
      updateStatus();
    }
  };

  const resetDemo = () => {
    if (alice) alice.destroy();
    if (bob) bob.destroy();
    
    const newAlice = new MockOTRSession({ clientName: 'Alice' });
    const newBob = new MockOTRSession({ clientName: 'Bob' });
    
    setAlice(newAlice);
    setBob(newBob);
    setHandshakeSteps([]);
    setHandshakeResult(null);
    setPerformanceMetrics(null);
    setLogs([]);
    
    addLog('Demo reset');
    addLog(`Alice instance tag: ${newAlice.instanceTag.toString(16).toUpperCase()}`);
    addLog(`Bob instance tag: ${newBob.instanceTag.toString(16).toUpperCase()}`);
    
    updateStatus();
  };

  const runPerformanceTest = async () => {
    if (handshakeInProgress) return;
    
    addLog('Running handshake performance test...');
    setHandshakeInProgress(true);
    
    const testResults = [];
    const testCount = 5;
    
    for (let i = 0; i < testCount; i++) {
      const testAlice = new MockOTRSession({ clientName: `Alice-${i}` });
      const testBob = new MockOTRSession({ clientName: `Bob-${i}` });
      
      const startTime = Date.now();
      const result = await testAlice.performHandshakeWith(testBob);
      const endTime = Date.now();
      
      testResults.push({
        duration: endTime - startTime,
        messagesExchanged: result.messagesExchanged,
        success: result.success
      });
      
      testAlice.destroy();
      testBob.destroy();
    }
    
    const avgDuration = testResults.reduce((sum, r) => sum + r.duration, 0) / testCount;
    const successRate = testResults.filter(r => r.success).length / testCount * 100;
    
    addLog(`Performance test completed:`);
    addLog(`- Average duration: ${avgDuration.toFixed(1)}ms`);
    addLog(`- Success rate: ${successRate.toFixed(1)}%`);
    addLog(`- Tests completed: ${testCount}`);
    
    setHandshakeInProgress(false);
  };

  const getStepIcon = (step) => {
    const icons = {
      'DH_COMMIT_SENT': '🔑',
      'DH_KEY_RECEIVED': '🗝️',
      'REVEAL_SIGNATURE_SENT': '✍️',
      'SIGNATURE_RECEIVED': '📝',
      'HANDSHAKE_COMPLETED': '🔒'
    };
    return icons[step] || '📨';
  };

  const getStateColor = (state) => {
    const colors = {
      'PLAINTEXT': '#dc3545',
      'AWAITING_DHKEY': '#ffc107',
      'AWAITING_REVEALSIG': '#fd7e14',
      'AWAITING_SIG': '#20c997',
      'ENCRYPTED': '#28a745',
      'FINISHED': '#6c757d'
    };
    return colors[state] || '#6c757d';
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h2>🤝 OTR Handshake Demo</h2>
      
      {/* Status Panel */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: '1fr 1fr', 
        gap: '20px', 
        marginBottom: '20px' 
      }}>
        <div style={{ 
          backgroundColor: '#f8f9fa', 
          padding: '15px', 
          borderRadius: '8px',
          border: `3px solid ${aliceStatus ? getStateColor(aliceStatus.currentState) : '#ccc'}`
        }}>
          <h3>👩‍💻 Alice Status</h3>
          {aliceStatus && (
            <div>
              <p><strong>State:</strong> <span style={{ color: getStateColor(aliceStatus.currentState) }}>
                {aliceStatus.currentState}
              </span></p>
              <p><strong>Instance Tag:</strong> {aliceStatus.instanceTag.toString(16).toUpperCase()}</p>
              <p><strong>Messages:</strong> {aliceStatus.messagesExchanged}</p>
              <p><strong>Encrypted:</strong> {aliceStatus.encrypted ? '✅' : '❌'}</p>
            </div>
          )}
        </div>
        
        <div style={{ 
          backgroundColor: '#f8f9fa', 
          padding: '15px', 
          borderRadius: '8px',
          border: `3px solid ${bobStatus ? getStateColor(bobStatus.currentState) : '#ccc'}`
        }}>
          <h3>👨‍💻 Bob Status</h3>
          {bobStatus && (
            <div>
              <p><strong>State:</strong> <span style={{ color: getStateColor(bobStatus.currentState) }}>
                {bobStatus.currentState}
              </span></p>
              <p><strong>Instance Tag:</strong> {bobStatus.instanceTag.toString(16).toUpperCase()}</p>
              <p><strong>Messages:</strong> {bobStatus.messagesExchanged}</p>
              <p><strong>Encrypted:</strong> {bobStatus.encrypted ? '✅' : '❌'}</p>
            </div>
          )}
        </div>
      </div>

      {/* Controls */}
      <div style={{ 
        display: 'flex', 
        gap: '10px', 
        marginBottom: '20px',
        flexWrap: 'wrap'
      }}>
        <button 
          onClick={startHandshake}
          disabled={handshakeInProgress}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: '#007bff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: handshakeInProgress ? 'not-allowed' : 'pointer'
          }}
        >
          {handshakeInProgress ? '⏳ Handshaking...' : '🤝 Start Handshake'}
        </button>
        
        <button 
          onClick={runPerformanceTest}
          disabled={handshakeInProgress}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: '#6f42c1', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: handshakeInProgress ? 'not-allowed' : 'pointer'
          }}
        >
          📊 Performance Test
        </button>
        
        <button 
          onClick={resetDemo}
          disabled={handshakeInProgress}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: '#dc3545', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: handshakeInProgress ? 'not-allowed' : 'pointer'
          }}
        >
          🔄 Reset Demo
        </button>
      </div>

      {/* Handshake Progress */}
      {handshakeSteps.length > 0 && (
        <div style={{ marginBottom: '20px' }}>
          <h3>🔄 Handshake Progress</h3>
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column', 
            gap: '10px',
            backgroundColor: '#f8f9fa',
            padding: '15px',
            borderRadius: '8px'
          }}>
            {handshakeSteps.map((step, index) => (
              <div key={index} style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '10px',
                padding: '8px',
                backgroundColor: 'white',
                borderRadius: '4px',
                border: '1px solid #dee2e6'
              }}>
                <span style={{ fontSize: '20px' }}>{getStepIcon(step.step)}</span>
                <span style={{ fontWeight: 'bold', minWidth: '80px' }}>{step.actor}:</span>
                <span>{step.step.replace(/_/g, ' ')}</span>
                <span style={{ marginLeft: 'auto', fontSize: '12px', color: '#6c757d' }}>
                  {new Date(step.timestamp).toLocaleTimeString()}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Metrics */}
      {performanceMetrics && (
        <div style={{ marginBottom: '20px' }}>
          <h3>📈 Performance Metrics</h3>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '15px',
            backgroundColor: '#e7f3ff',
            padding: '15px',
            borderRadius: '8px'
          }}>
            <div>
              <strong>Total Duration:</strong><br />
              {performanceMetrics.duration}ms
            </div>
            <div>
              <strong>Messages/Second:</strong><br />
              {performanceMetrics.messagesPerSecond}
            </div>
            <div>
              <strong>Avg Step Time:</strong><br />
              {performanceMetrics.avgStepTime}ms
            </div>
          </div>
        </div>
      )}

      {/* Activity Log */}
      <div>
        <h3>📋 Activity Log</h3>
        <div style={{ 
          backgroundColor: '#000', 
          color: '#00ff00', 
          padding: '15px', 
          borderRadius: '4px', 
          fontFamily: 'monospace',
          fontSize: '12px',
          height: '200px',
          overflow: 'auto'
        }}>
          {logs.map((log, index) => (
            <div key={index}>{log}</div>
          ))}
          {handshakeInProgress && <div>⏳ Processing handshake...</div>}
        </div>
      </div>
    </div>
  );
};

export default HandshakeDemo;
