.chat-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
  height: 100%;
  background-color: var(--card-background);
  margin: 0;
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow-color);
  overflow: hidden;
  min-width: 300px; /* Ensure panels have minimum width */
  z-index: 1; /* Ensure proper stacking */
}

.chat-panel-alice {
  border-top: 4px solid var(--alice-color);
}

.chat-panel-bob {
  border-top: 4px solid var(--bob-color);
}

.chat-header {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--card-background);
}

.chat-header h2 {
  font-size: 1.2rem;
  margin: 0;
  color: var(--text-color);
  font-weight: 600;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  background-color: var(--card-background);
  min-height: 200px; /* Ensure messages area has height */
}

.no-messages {
  text-align: center;
  color: var(--text-muted);
  margin-top: 20px;
  font-style: italic;
}

.message {
  max-width: 80%;
  margin-bottom: 12px;
  padding: 10px 14px;
  border-radius: 12px;
  position: relative;
  word-break: break-word;
}

.message.sent {
  align-self: flex-end;
  background-color: var(--message-sent-bg);
  border: 1px solid var(--message-sent-border);
  color: var(--text-color);
}

.message.received {
  align-self: flex-start;
  background-color: var(--message-received-bg);
  border: 1px solid var(--message-received-border);
  color: var(--text-color);
}

.message-content {
  margin-bottom: 6px;
  font-size: 0.95rem;
}

.message-container {
  position: relative;
}

.message-type-indicator {
  position: absolute;
  top: -8px;
  left: -8px;
  font-size: 1.2rem;
  background: var(--card-background);
  border-radius: 50%;
  box-shadow: 0 1px 3px var(--shadow-color);
  padding: 2px;
  z-index: 2;
}

.message-image {
  max-width: 100%;
  border-radius: 6px;
  margin-bottom: 6px;
  display: block;
}

.message-timestamp {
  font-size: 0.7rem;
  color: var(--text-muted);
  text-align: right;
}

.message-status {
  font-size: 0.7rem;
  color: var(--text-secondary);
  text-align: right;
  font-style: italic;
}

.message-input-container {
  position: relative;
  border-top: 1px solid var(--border-color);
  background-color: var(--card-background);
}

.message-input-form {
  display: flex;
  padding: 12px 16px;
  background-color: var(--card-background);
  min-height: 60px; /* Ensure input area has height */
}

.message-input-form input {
  flex: 1;
  padding: 10px 14px;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  margin-right: 10px;
  outline: none;
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 0.95rem;
}

.message-input-form input:focus {
  border-color: var(--alice-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.message-form-buttons {
  display: flex;
  align-items: center;
}

.emoji-toggle {
  background-color: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px 10px;
  margin-right: 5px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  line-height: 1;
}

.giphy-toggle {
  background-color: transparent;
  border: 2px solid var(--border-color);
  font-size: 0.9rem;
  font-weight: bold;
  cursor: pointer;
  padding: 5px 10px;
  margin-right: 5px;
  border-radius: 16px;
  transition: all 0.2s ease;
  line-height: 1;
  color: var(--text-color);
}

.emoji-toggle:hover {
  background-color: var(--border-color);
}

.giphy-toggle:hover {
  background-color: var(--border-color);
  transform: scale(1.05);
}

.chat-panel-alice .giphy-toggle {
  color: var(--alice-color);
  border-color: var(--alice-color);
}

.chat-panel-bob .giphy-toggle {
  color: var(--bob-color);
  border-color: var(--bob-color);
}

.emoji-picker-container {
  position: absolute;
  bottom: 70px;
  left: 16px;
  z-index: 1000;
  box-shadow: 0 0 10px var(--shadow-color);
  border-radius: 8px;
  overflow: hidden;
  border: none !important; /* Prevent any border inheritance */
}

.giphy-picker-container {
  position: absolute;
  bottom: 70px;
  left: 16px;
  z-index: 1000;
  width: 300px;
  height: 400px;
  box-shadow: 0 0 10px var(--shadow-color);
  border-radius: 8px;
  overflow: hidden;
  border: none !important;
  background-color: var(--card-background);
  display: flex;
  flex-direction: column;
}

.giphy-search {
  padding: 10px;
  border-bottom: 1px solid var(--border-color);
}

.giphy-search-input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 20px;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--text-color);
  font-size: 0.9rem;
  outline: none;
}

.giphy-grid {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.message-gif {
  max-width: 100%;
  border-radius: 6px;
  margin-bottom: 6px;
  display: block;
}

/* Additional styles for dark/light theme */
.emoji-picker-container.dark-theme {
  --epr-search-input-bg-color: var(--input-bg);
  --epr-search-input-text-color: var(--text-color);
}

.emoji-picker-container.light-theme {
  --epr-search-input-bg-color: var(--input-bg);
  --epr-search-input-text-color: var(--text-color);
}

/* Override any chat-panel specific styling for the emoji picker */
.chat-panel-alice .emoji-picker-container,
.chat-panel-bob .emoji-picker-container,
.chat-panel-alice .giphy-picker-container,
.chat-panel-bob .giphy-picker-container {
  border: none;
  border-top: none;
  border-color: transparent;
}

/* Remove colored backgrounds from emoji items */
.chat-panel-alice .emoji-picker-container [role="button"],
.chat-panel-bob .emoji-picker-container [role="button"],
.chat-panel-alice .emoji-picker-container button,
.chat-panel-bob .emoji-picker-container button,
.chat-panel-alice .emoji-picker-container .epr-emoji-category,
.chat-panel-bob .emoji-picker-container .epr-emoji-category,
.chat-panel-alice .emoji-picker-container .epr-body button,
.chat-panel-bob .emoji-picker-container .epr-body button,
.chat-panel-alice .emoji-picker-container [class*="emojiButton"],
.chat-panel-bob .emoji-picker-container [class*="emojiButton"] {
  background-color: transparent !important;
  border-radius: 8px !important;
}

/* Fix specific hover states for emoji buttons */
.chat-panel-alice .emoji-picker-container [role="button"]:hover,
.chat-panel-bob .emoji-picker-container [role="button"]:hover,
.chat-panel-alice .emoji-picker-container button:hover,
.chat-panel-bob .emoji-picker-container button:hover {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

/* Fix hover states in dark mode */
.dark-theme .emoji-picker-container [role="button"]:hover,
.dark-theme .emoji-picker-container button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Fix emoji-picker background in dark mode to match the theme */
.emoji-picker-container [data-theme='dark'] {
  --epr-bg-color: var(--card-background);
  --epr-category-label-bg-color: var(--card-background);
}

.chat-panel-alice .emoji-toggle:hover {
  background-color: rgba(52, 152, 219, 0.1);
}

.chat-panel-bob .emoji-toggle:hover {
  background-color: rgba(231, 76, 60, 0.1);
}

.message-input-form button {
  background-color: var(--alice-color);
  color: white;
  border: none;
  padding: 10px 18px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s ease;
  min-width: 70px;
}

.chat-panel-alice .message-input-form button {
  background-color: var(--alice-color);
}

.chat-panel-bob .message-input-form button {
  background-color: var(--bob-color);
}

.message-input-form button:disabled {
  background-color: var(--text-muted);
  cursor: not-allowed;
  opacity: 0.7;
}

.clear-button {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 0.8rem;
  padding: 4px 10px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-button:hover {
  background-color: var(--border-color);
  color: var(--text-color);
}

.drag-active {
  background-color: var(--border-color);
  border: 2px dashed var(--alice-color);
  border-radius: 4px;
}

.chat-panel-bob .drag-active {
  border-color: var(--bob-color);
}

.drag-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(var(--card-background-rgb), 0.9);
  color: var(--text-color);
  font-weight: bold;
  pointer-events: none;
  border-radius: 6px;
}

/* Message editing styles */
.edit-message-form {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.edit-message-input {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: var(--input-bg);
  color: var(--text-color);
  outline: none;
  font-size: 0.95rem;
}

.edit-message-input:focus {
  border-color: var(--alice-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.chat-panel-bob .edit-message-input:focus {
  border-color: var(--bob-color);
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

.edit-buttons {
  display: flex;
  gap: 8px;
  align-self: flex-end;
}

.edit-buttons button {
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
}

.edit-save-button {
  background-color: var(--alice-color);
  color: white;
  border: none;
}

.chat-panel-bob .edit-save-button {
  background-color: var(--bob-color);
}

.edit-cancel-button {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

.edited-indicator {
  font-style: italic;
  opacity: 0.8;
  font-size: 0.7rem;
}

/* Context menu styles */
.message-context-menu {
  position: fixed;
  z-index: 1000;
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  box-shadow: 0 2px 10px var(--shadow-color);
  padding: 4px 0;
  min-width: 120px;
}

.message-context-menu button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 8px 12px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-color);
  font-size: 0.9rem;
}

.message-context-menu button:hover {
  background-color: var(--border-color);
}

/* SMP Authentication Styles */
.smp-controls {
  display: flex;
  align-items: center;
  margin-right: 10px;
  position: relative;
}

.smp-initiate-btn {
  background-color: #4c84ff;
  color: white;
  border: none;
  border-radius: 4px 0 0 4px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.smp-dropdown {
  position: relative;
  display: inline-block;
}

.smp-dropdown-btn {
  background-color: #4c84ff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 5px 8px;
  cursor: pointer;
  font-size: 0.7rem;
  transition: background-color 0.2s;
  border-left: 1px solid rgba(255, 255, 255, 0.3);
}

.smp-dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  min-width: 160px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 1;
  border-radius: 4px;
  margin-top: 2px;
}

.smp-dropdown-content button {
  color: #333;
  padding: 10px 16px;
  text-decoration: none;
  display: block;
  text-align: left;
  background: none;
  border: none;
  width: 100%;
  cursor: pointer;
  font-size: 0.9rem;
}

.smp-dropdown-content button:hover {
  background-color: #f1f1f1;
}

.smp-dropdown:hover .smp-dropdown-content {
  display: block;
}

.dark-mode .smp-dropdown-content {
  background-color: #2c3e50;
}

.dark-mode .smp-dropdown-content button {
  color: #f0f0f0;
}

.dark-mode .smp-dropdown-content button:hover {
  background-color: #34495e;
}

.smp-initiate-btn:hover,
.smp-respond-btn:hover {
  background-color: #3a75f0;
}

.smp-initiate-btn:disabled,
.smp-respond-btn:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
}

.smp-verified {
  display: inline-block;
  margin-left: 8px;
  color: #2ecc71;
  font-weight: bold;
  font-size: 1.2rem;
}

.smp-failed {
  display: inline-block;
  margin-left: 8px;
  color: #e74c3c;
  font-weight: bold;
  font-size: 1.2rem;
}

.smp-pending {
  display: inline-block;
  margin-left: 8px;
  color: #f39c12;
  font-weight: bold;
  font-size: 1.2rem;
}

.smp-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.smp-dialog {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark-mode .smp-dialog {
  background-color: #2c3e50;
  color: white;
}

.smp-dialog h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.dark-mode .smp-dialog h3 {
  color: #f0f0f0;
}

.smp-dialog p {
  margin-bottom: 15px;
  line-height: 1.5;
}

.smp-dialog input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-bottom: 15px;
  font-size: 1rem;
}

.dark-mode .smp-dialog input {
  background-color: #34495e;
  border-color: #2c3e50;
  color: white;
}

.smp-dialog-buttons {
  display: flex;
  justify-content: flex-end;
}

.smp-dialog-buttons button {
  padding: 8px 16px;
  margin-left: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.smp-dialog-buttons button:first-child {
  background-color: #e0e0e0;
  color: #333;
}

.dark-mode .smp-dialog-buttons button:first-child {
  background-color: #546e7a;
  color: white;
}

.smp-dialog-buttons button:last-child {
  background-color: #4c84ff;
  color: white;
}

.smp-dialog-buttons button:last-child:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
}

.otr-indicator {
  display: inline-block;
  margin-left: 8px;
  color: #4c84ff;
  font-size: 1.2rem;
}
