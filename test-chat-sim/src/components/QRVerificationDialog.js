import React, { useState, useEffect } from 'react';
import './QRVerificationDialog.css';

/**
 * QR Verification Dialog Component
 * 
 * This component provides a dialog for QR code verification in the chat simulator.
 * It integrates with the WebOTteR QR code verification functionality.
 */
function QRVerificationDialog({ 
  isOpen, 
  onClose, 
  user, 
  localFingerprint, 
  partnerFingerprint, 
  partnerName,
  onVerificationComplete
}) {
  const [activeTab, setActiveTab] = useState('qr-code');
  const [qrCodeImage, setQrCodeImage] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [qrVerifier, setQrVerifier] = useState(null);

  // Load the QR code verifier component
  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      setError(null);
      
      // Import the QRCodeVerifier dynamically
      import('../../src/ui/components/verification/QRCodeVerifier.js')
        .then(module => {
          const { QRCodeVerifier } = module;
          
          // Create a new QR code verifier instance
          const verifier = new QRCodeVerifier({
            localFingerprint: localFingerprint,
            expectedFingerprint: partnerFingerprint,
            partnerName: partnerName,
            onVerificationComplete: (result) => {
              console.log('QR verification result:', result);
              if (onVerificationComplete) {
                onVerificationComplete(result);
              }
            }
          });
          
          setQrVerifier(verifier);
          
          // Generate QR code
          verifier.generateQRCode()
            .then(dataUrl => {
              setQrCodeImage(dataUrl);
              setIsLoading(false);
            })
            .catch(err => {
              console.error('Error generating QR code:', err);
              setError('Failed to generate QR code');
              setIsLoading(false);
            });
        })
        .catch(err => {
          console.error('Error loading QRCodeVerifier:', err);
          setError('Failed to load QR code verification component');
          setIsLoading(false);
        });
    }
  }, [isOpen, localFingerprint, partnerFingerprint, partnerName, onVerificationComplete]);

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Handle scan button click
  const handleScanClick = () => {
    if (qrVerifier) {
      qrVerifier.startScanner()
        .catch(err => {
          console.error('Error starting scanner:', err);
          setError('Failed to start QR code scanner');
        });
    }
  };

  // Handle manual entry button click
  const handleManualEntryClick = () => {
    if (qrVerifier) {
      qrVerifier.showManualEntry();
    }
  };

  // If dialog is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="qr-verification-overlay">
      <div className="qr-verification-dialog">
        <div className="qr-verification-header">
          <h2>Verify Your Conversation Partner</h2>
          <button className="qr-verification-close" onClick={onClose}>✕</button>
        </div>
        
        <div className="qr-verification-tabs">
          <button 
            className={`qr-verification-tab ${activeTab === 'qr-code' ? 'active' : ''}`}
            onClick={() => handleTabChange('qr-code')}
          >
            QR Code
          </button>
          <button 
            className={`qr-verification-tab ${activeTab === 'secret' ? 'active' : ''}`}
            onClick={() => handleTabChange('secret')}
          >
            Secret Q&A
          </button>
          <button 
            className={`qr-verification-tab ${activeTab === 'fingerprint' ? 'active' : ''}`}
            onClick={() => handleTabChange('fingerprint')}
          >
            Fingerprint
          </button>
        </div>
        
        <div className="qr-verification-content">
          {activeTab === 'qr-code' && (
            <div className="qr-code-tab">
              <p>Show this QR code to your partner or scan their QR code to verify identities.</p>
              
              <div className="qr-code-container">
                {isLoading ? (
                  <div className="qr-loading">Generating QR code...</div>
                ) : error ? (
                  <div className="qr-error">{error}</div>
                ) : (
                  <img src={qrCodeImage} alt="QR Code" className="qr-code-image" />
                )}
              </div>
              
              <div className="qr-verification-buttons">
                <button 
                  className="qr-scan-button"
                  onClick={handleScanClick}
                  disabled={isLoading || error}
                >
                  📷 Scan Partner's Code
                </button>
                <button 
                  className="qr-manual-button"
                  onClick={handleManualEntryClick}
                  disabled={isLoading}
                >
                  ✎ Enter Manually
                </button>
              </div>
              
              <div className="qr-verification-info">
                <p>
                  <strong>Your fingerprint:</strong><br />
                  <code>{formatFingerprint(localFingerprint)}</code>
                </p>
                <p>
                  <strong>Expected partner fingerprint:</strong><br />
                  <code>{formatFingerprint(partnerFingerprint)}</code>
                </p>
              </div>
            </div>
          )}
          
          {activeTab === 'secret' && (
            <div className="secret-tab">
              <p>This tab would contain the Secret Q&A verification method.</p>
              <p>For this demo, please use the QR Code tab.</p>
            </div>
          )}
          
          {activeTab === 'fingerprint' && (
            <div className="fingerprint-tab">
              <p>Compare these fingerprints with your partner using a different communication channel.</p>
              
              <div className="fingerprint-info">
                <p>
                  <strong>Your fingerprint:</strong><br />
                  <code>{formatFingerprint(localFingerprint)}</code>
                </p>
                <p>
                  <strong>Expected partner fingerprint:</strong><br />
                  <code>{formatFingerprint(partnerFingerprint)}</code>
                </p>
              </div>
              
              <div className="fingerprint-buttons">
                <button className="fingerprint-mismatch">Fingerprints Don't Match</button>
                <button className="fingerprint-match">Fingerprints Match</button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Helper function to format fingerprint with spaces
function formatFingerprint(fingerprint) {
  if (!fingerprint) return 'Not available';
  return fingerprint.replace(/(.{4})/g, '$1 ').trim();
}

export default QRVerificationDialog;
