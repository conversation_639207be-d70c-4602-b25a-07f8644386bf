import React, { useState, useRef, useEffect } from 'react';
<<<<<<< HEAD
import EmojiPicker from 'emoji-picker-react';
import { GiphyFetch } from '@giphy/js-fetch-api';
import { Grid } from '@giphy/react-components';
import QRVerificationDialog from './QRVerificationDialog';
import './ChatPanel.css';

// Initialize Giphy API with a public beta key
// In a production app, you'd want to use an environment variable or server proxy
const giphyFetch = new GiphyFetch('sXpGFDGZs0Dv1mmNFvYaGUvYwKX0PWIh');

/**
 * Component for displaying a user's chat panel with message history and input field
 *
 * @param {Object} props
 * @param {string} props.user - User identifier ('alice' or 'bob')
 * @param {string} props.displayName - User's display name
 * @param {Array} props.messages - Array of message objects
 * @param {Function} props.onSendMessage - Function to call when sending a message
 * @param {Function} props.onClearMessages - Function to clear all messages
 * @param {boolean} props.darkMode - Whether the app is in dark mode
 * @param {Function} props.onEditMessage - Function to edit a message
 * @param {boolean} props.otrEnabled - Whether OTR encryption is enabled
 * @param {Function} props.onInitiateSMP - Function to initiate SMP authentication
 * @param {Function} props.onRespondSMP - Function to respond to SMP authentication
 * @param {Function} props.onVerificationComplete - Function to handle verification completion
 * @param {string} props.smpState - Current state of SMP authentication ('none', 'pending', 'verified', 'failed')
 * @param {string} props.smpQuestion - SMP authentication question if any
 */
function ChatPanel({
  user,
  displayName,
  messages,
  onSendMessage,
  onClearMessages,
  darkMode,
  onEditMessage,
  otrEnabled,
  onInitiateSMP,
  onRespondSMP,
  onVerificationComplete,
  smpState = 'none',
  smpQuestion
}) {
  const [messageInput, setMessageInput] = useState('');
  const [dragActive, setDragActive] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showGiphyPicker, setShowGiphyPicker] = useState(false);
  const [giphySearchTerm, setGiphySearchTerm] = useState('');
  const [editingMessage, setEditingMessage] = useState(null);
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const [selectedMessageId, setSelectedMessageId] = useState(null);

  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const dropAreaRef = useRef(null);
  const emojiPickerRef = useRef(null);
  const giphyPickerRef = useRef(null);
  const editInputRef = useRef(null);
  const contextMenuRef = useRef(null);

  // Add state for SMP input and QR verification
  const [smpSecret, setSmpSecret] = useState('');
  const [showSmpDialog, setShowSmpDialog] = useState(false);
  const [showQrDialog, setShowQrDialog] = useState(false);
  const [smpDialogType, setSmpDialogType] = useState('initiate'); // 'initiate' or 'respond'
  const smpDialogRef = useRef(null);

  // Auto-scroll to bottom of messages on new message
  useEffect(() => {
    if (messagesEndRef.current &&
        typeof messagesEndRef.current.scrollIntoView === 'function') {
=======
import './ChatPanel.css';

function ChatPanel({ user, displayName, messages, onSendMessage, onClearMessages, darkMode }) {
  const [messageInput, setMessageInput] = useState('');
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Auto-scroll to bottom of messages on new message
  useEffect(() => {
    if (messagesEndRef.current) {
>>>>>>> 69817ad (Complete Branch Merge PRD implementation with full development infrastructure)
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Focus input field when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

<<<<<<< HEAD
  // Focus edit input when editing starts
  useEffect(() => {
    if (editingMessage && editInputRef.current) {
      editInputRef.current.focus();
    }
  }, [editingMessage]);

  // Add event listener to close pickers on outside click
  useEffect(() => {
    function handleClickOutside(event) {
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target) &&
          !event.target.closest('.emoji-toggle')) {
        setShowEmojiPicker(false);
      }

      if (giphyPickerRef.current && !giphyPickerRef.current.contains(event.target) &&
          !event.target.closest('.giphy-toggle')) {
        setShowGiphyPicker(false);
      }

      // Close context menu when clicking outside
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {
        setShowContextMenu(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [emojiPickerRef, giphyPickerRef, contextMenuRef]);

  // Add effect to handle clicking outside SMP dialog
  useEffect(() => {
    function handleClickOutsideSmpDialog(event) {
      if (smpDialogRef.current && !smpDialogRef.current.contains(event.target)) {
        setShowSmpDialog(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutsideSmpDialog);
    return () => {
      document.removeEventListener('mousedown', handleClickOutsideSmpDialog);
    };
  }, [smpDialogRef]);

=======
>>>>>>> 69817ad (Complete Branch Merge PRD implementation with full development infrastructure)
  const handleSendMessage = (e) => {
    e.preventDefault();
    if (messageInput.trim()) {
      onSendMessage(user, messageInput.trim());
      setMessageInput('');
      // Re-focus input after sending
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  };

<<<<<<< HEAD
  // Handle message edit submission
  const handleEditSubmit = (e) => {
    e.preventDefault();

    if (editingMessage && editingMessage.newContent.trim()) {
      // Call parent function to update the message
      onEditMessage(editingMessage.id, editingMessage.newContent.trim());
      setEditingMessage(null);
    }
  };

=======
>>>>>>> 69817ad (Complete Branch Merge PRD implementation with full development infrastructure)
  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

<<<<<<< HEAD
  // Handle right-click on message to show edit menu
  const handleMessageContextMenu = (e, message) => {
    // Only allow editing user's own messages that are text type
    if (message.sender === user && message.type === 'text') {
      e.preventDefault(); // Prevent default context menu

      // Position the context menu
      setContextMenuPosition({ x: e.pageX, y: e.pageY });
      setShowContextMenu(true);
      setSelectedMessageId(message.id);
    }
  };

  // Handle edit option click from context menu
  const handleEditClick = () => {
    // Find the message to edit
    const messageToEdit = messages.find(m => m.id === selectedMessageId);

    if (messageToEdit) {
      setEditingMessage({
        id: messageToEdit.id,
        newContent: messageToEdit.content,
        originalContent: messageToEdit.content
      });
      setShowContextMenu(false);
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingMessage(null);
  };

  // Handle drag events
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  // Handle drop event
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];

      // Only process images
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (event) => {
          const imageDataUrl = event.target.result;
          onSendMessage(user, imageDataUrl, 'image');
        };
        reader.readAsDataURL(file);
      } else {
        // Optionally show an error for non-image files
        alert('Only image files are supported');
      }
    }
  };

  // Handle emoji selection
  const onEmojiClick = (emojiObject) => {
    const emoji = emojiObject.emoji;

    // If editing, insert emoji in edit input
    if (editingMessage) {
      const cursorPosition = editInputRef.current.selectionStart;
      const textBeforeCursor = editingMessage.newContent.slice(0, cursorPosition);
      const textAfterCursor = editingMessage.newContent.slice(cursorPosition);

      // Insert emoji at cursor position in edit input
      setEditingMessage({
        ...editingMessage,
        newContent: textBeforeCursor + emoji + textAfterCursor
      });

      // After state update, set cursor position after the inserted emoji
      setTimeout(() => {
        if (editInputRef.current) {
          const newCursorPosition = cursorPosition + emoji.length;
          editInputRef.current.focus();
          editInputRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
        }
      }, 0);
    } else {
      // Normal emoji insertion in message input
      const cursorPosition = inputRef.current.selectionStart;
      const textBeforeCursor = messageInput.slice(0, cursorPosition);
      const textAfterCursor = messageInput.slice(cursorPosition);

      // Insert emoji at cursor position
      setMessageInput(textBeforeCursor + emoji + textAfterCursor);

      // After state update, set cursor position after the inserted emoji
      setTimeout(() => {
        if (inputRef.current) {
          const newCursorPosition = cursorPosition + emoji.length;
          inputRef.current.focus();
          inputRef.current.setSelectionRange(newCursorPosition, newCursorPosition);
        }
      }, 0);
    }
  };

  // Handle GIF selection
  const onGifClick = (gif) => {
    // Send the GIF as a message
    onSendMessage(user, gif.images.original.url, 'gif');

    // Close the Giphy picker
    setShowGiphyPicker(false);
  };

  // Toggle emoji picker
  const toggleEmojiPicker = (e) => {
    e.preventDefault();
    setShowEmojiPicker(prev => !prev);
    // Close Giphy picker if open
    setShowGiphyPicker(false);
  };

  // Toggle Giphy picker
  const toggleGiphyPicker = (e) => {
    e.preventDefault();
    setShowGiphyPicker(prev => !prev);
    // Close emoji picker if open
    setShowEmojiPicker(false);
  };

  // Handle Giphy search input change
  const handleGiphySearchChange = (e) => {
    setGiphySearchTerm(e.target.value);
  };

  // Render message content based on type
  const renderMessageContent = (message) => {
    // If currently editing this message
    if (editingMessage && editingMessage.id === message.id) {
      return (
        <form className="edit-message-form" onSubmit={handleEditSubmit}>
          <input
            type="text"
            value={editingMessage.newContent}
            onChange={(e) => setEditingMessage({
              ...editingMessage,
              newContent: e.target.value
            })}
            ref={editInputRef}
            className="edit-message-input"
          />
          <div className="edit-buttons">
            <button type="submit" className="edit-save-button">Save</button>
            <button type="button" className="edit-cancel-button" onClick={handleCancelEdit}>Cancel</button>
          </div>
        </form>
      );
    }

    // Normal display
    if (message.type === 'image') {
      return (
        <div className="message-container">
          <img
            src={message.content}
            alt={`Image sent by ${message.sender}`}
            className="message-image"
          />
        </div>
      );
    }
    if (message.type === 'gif') {
      return (
        <div className="message-container">
          <img
            src={message.content}
            alt={`GIF sent by ${message.sender}`}
            className="message-gif"
          />
        </div>
      );
    }

    // Check if message is encrypted
    const isEncrypted = message.encrypted;

    return (
      <div className={`message-content ${isEncrypted ? 'encrypted' : ''}`}>
        {isEncrypted && <span className="encrypted-indicator">🔒 </span>}
        {message.content}
      </div>
    );
  };

  // Fetch GIFs for search term
  const fetchGifs = (offset) => {
    return giphySearchTerm
      ? giphyFetch.search(giphySearchTerm, { offset, limit: 10 })
      : giphyFetch.trending({ offset, limit: 10 });
  };

  // Handle initiating SMP authentication
  const handleInitiateSMP = () => {
    if (smpSecret.trim()) {
      onInitiateSMP(user, smpSecret.trim());
      setSmpSecret('');
      setShowSmpDialog(false);
    }
  };

  // Handle responding to SMP authentication
  const handleRespondSMP = () => {
    if (smpSecret.trim()) {
      onRespondSMP(user, smpSecret.trim());
      setSmpSecret('');
      setShowSmpDialog(false);
    }
  };

  // Show initiate SMP dialog
  const showInitiateSmpDialog = () => {
    setSmpDialogType('initiate');
    setShowSmpDialog(true);
    setSmpSecret('');
  };

  // Show respond SMP dialog
  const showRespondSmpDialog = () => {
    setSmpDialogType('respond');
    setShowSmpDialog(true);
    setSmpSecret('');
  };

  // Show QR verification dialog
  const showQrVerificationDialog = () => {
    setShowQrDialog(true);
  };

  // Handle QR verification result
  const handleQrVerificationComplete = (result) => {
    console.log('QR verification completed:', result);
    if (result.success) {
      // If verification was successful, update SMP state
      if (onVerificationComplete) {
        onVerificationComplete({
          success: true,
          method: 'qr-code',
          user: user
        });
      }
    } else {
      // If verification failed
      if (onVerificationComplete) {
        onVerificationComplete({
          success: false,
          method: 'qr-code',
          user: user
        });
      }
    }

    // Close the dialog
    setShowQrDialog(false);
  };

=======
>>>>>>> 69817ad (Complete Branch Merge PRD implementation with full development infrastructure)
  return (
    <div className={`chat-panel ${darkMode ? 'dark-mode' : ''} ${user}`} data-testid={`${user}-panel`}>
      <div className="chat-header">
<<<<<<< HEAD
        <h2>
          {displayName}
          {otrEnabled && <span className="otr-indicator" title="OTR Encryption Enabled">🔒</span>}
          {smpState === 'verified' && <span className="smp-verified" title="Identity Verified">✓</span>}
          {smpState === 'failed' && <span className="smp-failed" title="Verification Failed">✗</span>}
          {smpState === 'pending' && <span className="smp-pending" title="Verification Pending">⟳</span>}
        </h2>
        <div className="chat-controls">
          {/* SMP Controls */}
          {otrEnabled && (
            <div className="smp-controls">
              <button
                className="smp-initiate-btn"
                onClick={showQrVerificationDialog}
                title="Verify Identity with QR Code"
                disabled={smpState === 'pending'}
              >
                Verify
              </button>
              <div className="smp-dropdown">
                <button className="smp-dropdown-btn">▼</button>
                <div className="smp-dropdown-content">
                  <button onClick={showInitiateSmpDialog}>Secret Q&A</button>
                  {smpQuestion && (
                    <button onClick={showRespondSmpDialog}>
                      Respond to Question
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}
          <button className="clear-btn" onClick={onClearMessages} title="Clear Messages">
            Clear
          </button>
        </div>
      </div>

      {/* SMP Dialog */}
      {showSmpDialog && (
        <div className="smp-dialog-overlay">
          <div className="smp-dialog" ref={smpDialogRef}>
            <h3>
              {smpDialogType === 'initiate'
                ? 'Verify Identity'
                : `Answer Authentication Question${smpQuestion ? ': ' + smpQuestion : ''}`}
            </h3>
            <p>
              {smpDialogType === 'initiate'
                ? 'Enter a shared secret that only you and your conversation partner would know.'
                : 'Enter the secret that both of you know to verify your identity.'}
            </p>
            <input
              type="password"
              value={smpSecret}
              onChange={(e) => setSmpSecret(e.target.value)}
              placeholder="Shared secret"
              autoFocus
            />
            <div className="smp-dialog-buttons">
              <button onClick={() => setShowSmpDialog(false)}>Cancel</button>
              <button
                onClick={smpDialogType === 'initiate' ? handleInitiateSMP : handleRespondSMP}
                disabled={!smpSecret.trim()}
              >
                {smpDialogType === 'initiate' ? 'Verify' : 'Respond'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* QR Verification Dialog */}
      <QRVerificationDialog
        isOpen={showQrDialog}
        onClose={() => setShowQrDialog(false)}
        user={user}
        localFingerprint={user === 'alice' ? '5A2F8E7B1D94C65B304218B209A587E2C17F33' : '7E1DC849A3B6F592D0783E5C9A1FB432D6502C91'}
        partnerFingerprint={user === 'alice' ? '7E1DC849A3B6F592D0783E5C9A1FB432D6502C91' : '5A2F8E7B1D94C65B304218B209A587E2C17F33'}
        partnerName={user === 'alice' ? 'Bob' : 'Alice'}
        onVerificationComplete={handleQrVerificationComplete}
      />

=======
        <h2>{displayName}</h2>
        <button className="clear-btn" onClick={onClearMessages} title="Clear Messages">
          Clear
        </button>
      </div>

>>>>>>> 69817ad (Complete Branch Merge PRD implementation with full development infrastructure)
      <div className="messages-container">
        {messages.length === 0 ? (
          <div className="no-messages">No messages yet</div>
        ) : (
          messages.map(message => (
            <div
              key={message.id}
              className={`message ${message.sender === user ? 'sent' : 'received'}`}
            >
              <div className="message-content">
                {message.content}
              </div>
              <div className="message-timestamp">
                {formatTimestamp(message.timestamp)}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>
<<<<<<< HEAD

      <div
        className={`message-input-container ${dragActive ? 'drag-active' : ''}`}
        onDragEnter={handleDrag}
        onDragOver={handleDrag}
        onDragLeave={handleDrag}
        onDrop={handleDrop}
        ref={dropAreaRef}
      >
=======
      
      <div className="message-input-container">
>>>>>>> 69817ad (Complete Branch Merge PRD implementation with full development infrastructure)
        <form className="message-input-form" onSubmit={handleSendMessage}>
          <input
            type="text"
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            placeholder="Type a message..."
            aria-label={`Message input for ${displayName}`}
            ref={inputRef}
          />
<<<<<<< HEAD
          <div className="message-form-buttons">
            <button
              type="button"
              className="emoji-toggle"
              onClick={toggleEmojiPicker}
              aria-label="Open emoji picker"
            >
              😊
            </button>
            <button
              type="button"
              className="giphy-toggle"
              onClick={toggleGiphyPicker}
              aria-label="Open GIF picker"
            >
              GIF
            </button>
            <button
              type="submit"
              disabled={!messageInput.trim()}
              aria-label="Send message"
            >
              Send
            </button>
          </div>
          {showEmojiPicker && (
            <div className={`emoji-picker-container ${darkMode ? 'dark-theme' : 'light-theme'}`} ref={emojiPickerRef}>
              <EmojiPicker
                onEmojiClick={onEmojiClick}
                searchPlaceholder="Search emojis..."
                width={300}
                height={400}
                theme={darkMode ? 'dark' : 'light'}
              />
            </div>
          )}
          {showGiphyPicker && (
            <div className={`giphy-picker-container ${darkMode ? 'dark-theme' : 'light-theme'}`} ref={giphyPickerRef}>
              <div className="giphy-search">
                <input
                  type="text"
                  placeholder="Search for GIFs..."
                  value={giphySearchTerm}
                  onChange={handleGiphySearchChange}
                  className="giphy-search-input"
                />
              </div>
              <div className="giphy-grid">
                <Grid
                  key={giphySearchTerm}
                  width={300}
                  columns={2}
                  fetchGifs={fetchGifs}
                  onGifClick={onGifClick}
                  noLink={true}
                  hideAttribution={true}
                />
              </div>
            </div>
          )}
=======
          <button 
            type="submit" 
            disabled={!messageInput.trim()}
            aria-label="Send message"
          >
            Send
          </button>
>>>>>>> 69817ad (Complete Branch Merge PRD implementation with full development infrastructure)
        </form>
      </div>
<<<<<<< HEAD

      {/* Context menu for editing */}
      {showContextMenu && (
        <div
          className="message-context-menu"
          style={{ top: contextMenuPosition.y, left: contextMenuPosition.x }}
          ref={contextMenuRef}
        >
          <button onClick={handleEditClick}>Edit message</button>
        </div>
      )}
=======
>>>>>>> 69817ad (Complete Branch Merge PRD implementation with full development infrastructure)
    </div>
  );
}

export default ChatPanel;
