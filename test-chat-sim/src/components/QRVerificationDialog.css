/* QR Verification Dialog Styles */

.qr-verification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.qr-verification-dialog {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.qr-verification-header {
  padding: 16px 20px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.qr-verification-header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.qr-verification-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 0;
  margin: 0;
}

.qr-verification-close:hover {
  color: #000;
}

.qr-verification-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
}

.qr-verification-tab {
  flex: 1;
  padding: 12px 16px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.2s;
}

.qr-verification-tab:hover {
  background-color: #f9f9f9;
}

.qr-verification-tab.active {
  border-bottom-color: #0078d4;
  font-weight: bold;
}

.qr-verification-content {
  padding: 20px;
  flex-grow: 1;
  overflow-y: auto;
}

/* QR Code Tab */
.qr-code-tab p {
  margin-top: 0;
  margin-bottom: 16px;
}

.qr-code-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 220px;
  background-color: #f9f9f9;
  border: 1px dashed #ccc;
  border-radius: 4px;
  margin-bottom: 16px;
  overflow: hidden;
}

.qr-code-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.qr-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 14px;
  color: #666;
}

.qr-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 14px;
  color: #e74c3c;
  padding: 0 20px;
  text-align: center;
}

.qr-verification-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
}

.qr-scan-button, .qr-manual-button {
  flex: 1;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid #0078d4;
}

.qr-scan-button {
  background-color: #0078d4;
  color: white;
}

.qr-scan-button:hover {
  background-color: #006cbe;
}

.qr-manual-button {
  background-color: white;
  color: #0078d4;
}

.qr-manual-button:hover {
  background-color: #f0f8ff;
}

.qr-scan-button:disabled, .qr-manual-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.qr-verification-info {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
}

.qr-verification-info p {
  margin: 0 0 8px 0;
}

.qr-verification-info p:last-child {
  margin-bottom: 0;
}

.qr-verification-info code {
  display: block;
  font-family: monospace;
  word-break: break-all;
  margin-top: 4px;
  line-height: 1.5;
}

/* Fingerprint Tab */
.fingerprint-info {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

.fingerprint-info p {
  margin: 0 0 12px 0;
}

.fingerprint-info p:last-child {
  margin-bottom: 0;
}

.fingerprint-info code {
  display: block;
  font-family: monospace;
  word-break: break-all;
  margin-top: 4px;
  line-height: 1.5;
}

.fingerprint-buttons {
  display: flex;
  gap: 10px;
}

.fingerprint-match, .fingerprint-mismatch {
  flex: 1;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.fingerprint-match {
  background-color: #2ecc71;
  color: white;
  border: 1px solid #27ae60;
}

.fingerprint-match:hover {
  background-color: #27ae60;
}

.fingerprint-mismatch {
  background-color: #e74c3c;
  color: white;
  border: 1px solid #c0392b;
}

.fingerprint-mismatch:hover {
  background-color: #c0392b;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .qr-verification-dialog {
    background-color: #2d2d2d;
    color: #f0f0f0;
  }
  
  .qr-verification-header {
    background-color: #222;
    border-bottom-color: #444;
  }
  
  .qr-verification-header h2 {
    color: #f0f0f0;
  }
  
  .qr-verification-close {
    color: #aaa;
  }
  
  .qr-verification-close:hover {
    color: #fff;
  }
  
  .qr-verification-tabs {
    border-bottom-color: #444;
  }
  
  .qr-verification-tab:hover {
    background-color: #333;
  }
  
  .qr-code-container {
    background-color: #333;
    border-color: #444;
  }
  
  .qr-loading {
    color: #aaa;
  }
  
  .qr-manual-button {
    background-color: #2d2d2d;
    color: #0078d4;
  }
  
  .qr-manual-button:hover {
    background-color: #333;
  }
  
  .qr-verification-info, .fingerprint-info {
    background-color: #333;
  }
}

/* Responsive styles */
@media (max-width: 480px) {
  .qr-verification-dialog {
    width: 95%;
    max-height: 95vh;
  }
  
  .qr-verification-tabs {
    flex-direction: column;
  }
  
  .qr-verification-tab {
    border-bottom: none;
    border-left: 3px solid transparent;
  }
  
  .qr-verification-tab.active {
    border-bottom: none;
    border-left-color: #0078d4;
  }
  
  .qr-verification-buttons {
    flex-direction: column;
  }
  
  .fingerprint-buttons {
    flex-direction: column;
  }
}
