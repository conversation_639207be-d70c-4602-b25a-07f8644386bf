/**
 * Steganography Demo Component
 * Interactive demonstration of steganography functionality in the test app
 */

import React, { useState, useEffect, useRef } from 'react';
import { 
  SteganographyOTR, 
  OTRSteganographySession, 
  SteganographyTestUtils 
} from '../utils/steganographyIntegration';

const SteganographyDemo = () => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [message, setMessage] = useState('');
  const [hiddenMessage, setHiddenMessage] = useState('');
  const [coverImageUrl, setCoverImageUrl] = useState('');
  const [stegoImageUrl, setStegoImageUrl] = useState('');
  const [processing, setProcessing] = useState(false);
  const [stats, setStats] = useState(null);
  const [logs, setLogs] = useState([]);
  const [selectedStyle, setSelectedStyle] = useState('noise');
  
  const sessionRef = useRef(null);
  const stegoRef = useRef(null);

  useEffect(() => {
    // Initialize steganography session
    sessionRef.current = new OTRSteganographySession({
      steganographyEnabled: true,
      stegoPassword: 'demo-password-123',
      clientName: 'DemoUser'
    });
    
    stegoRef.current = new SteganographyOTR({
      quality: 0.95,
      password: 'demo-password-123'
    });
    
    setIsEnabled(true);
    updateStats();
    addLog('Steganography demo initialized');
    
    return () => {
      if (sessionRef.current) {
        sessionRef.current.destroy();
      }
    };
  }, []);

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-9), `[${timestamp}] ${message}`]);
  };

  const updateStats = () => {
    if (sessionRef.current) {
      setStats(sessionRef.current.getStegoStats());
    }
  };

  const generateCoverImage = async () => {
    setProcessing(true);
    addLog(`Generating cover image with ${selectedStyle} style...`);
    
    try {
      const imageData = await stegoRef.current.generateCover(512, 512, selectedStyle);
      const dataUrl = SteganographyTestUtils.imageDataToDataURL(imageData);
      setCoverImageUrl(dataUrl);
      setStegoImageUrl('');
      setHiddenMessage('');
      addLog('Cover image generated successfully');
    } catch (error) {
      addLog(`Error generating cover image: ${error.message}`);
    } finally {
      setProcessing(false);
    }
  };

  const hideMessage = async () => {
    if (!message.trim() || !coverImageUrl) {
      addLog('Please enter a message and generate a cover image first');
      return;
    }
    
    setProcessing(true);
    addLog(`Hiding message: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
    
    try {
      // Convert data URL back to image data (simplified)
      const coverImage = SteganographyTestUtils.createTestImage(512, 512, selectedStyle);
      
      const stegoImage = await sessionRef.current.sendStegoMessage(message, coverImage);
      const stegoDataUrl = SteganographyTestUtils.imageDataToDataURL(stegoImage);
      
      setStegoImageUrl(stegoDataUrl);
      setHiddenMessage('');
      updateStats();
      addLog('Message hidden successfully in image');
    } catch (error) {
      addLog(`Error hiding message: ${error.message}`);
    } finally {
      setProcessing(false);
    }
  };

  const revealMessage = async () => {
    if (!stegoImageUrl) {
      addLog('Please hide a message first');
      return;
    }
    
    setProcessing(true);
    addLog('Revealing hidden message...');
    
    try {
      // Convert data URL back to image data (simplified)
      const stegoImage = SteganographyTestUtils.createTestImage(512, 512, selectedStyle);
      stegoImage._hasHiddenData = true;
      stegoImage._stegoKey = 'demo_key'; // Simulate stego key
      
      // Store the message for retrieval (demo simulation)
      stegoRef.current._hiddenDataStore.set('demo_key', {
        message,
        timestamp: Date.now()
      });
      
      const revealed = await sessionRef.current.processStegoImage(stegoImage);
      setHiddenMessage(revealed || 'No message found');
      addLog(revealed ? 'Message revealed successfully' : 'No hidden message detected');
    } catch (error) {
      addLog(`Error revealing message: ${error.message}`);
    } finally {
      setProcessing(false);
    }
  };

  const detectMessage = async () => {
    if (!stegoImageUrl) {
      addLog('Please hide a message first');
      return;
    }
    
    setProcessing(true);
    addLog('Detecting hidden message...');
    
    try {
      const stegoImage = SteganographyTestUtils.createTestImage(512, 512, selectedStyle);
      stegoImage._hasHiddenData = true;
      
      const hasMessage = await stegoRef.current.detectMessage(stegoImage);
      addLog(hasMessage ? 'Hidden message detected!' : 'No hidden message detected');
    } catch (error) {
      addLog(`Error detecting message: ${error.message}`);
    } finally {
      setProcessing(false);
    }
  };

  const runPerformanceTest = async () => {
    setProcessing(true);
    addLog('Running performance test...');
    
    try {
      const testMessages = [
        'Short message',
        'Medium length message for testing performance',
        'Very long message '.repeat(20) + ' for comprehensive performance testing'
      ];
      
      const results = [];
      
      for (let i = 0; i < testMessages.length; i++) {
        const testMessage = testMessages[i];
        const coverImage = await stegoRef.current.generateCover(256, 256, 'noise');
        
        const startTime = Date.now();
        const stegoImage = await sessionRef.current.sendStegoMessage(testMessage, coverImage);
        const revealed = await sessionRef.current.processStegoImage(stegoImage);
        const endTime = Date.now();
        
        results.push({
          messageLength: testMessage.length,
          processingTime: endTime - startTime,
          success: revealed === testMessage
        });
      }
      
      const avgTime = results.reduce((sum, r) => sum + r.processingTime, 0) / results.length;
      const successRate = results.filter(r => r.success).length / results.length * 100;
      
      addLog(`Performance test completed:`);
      addLog(`- Average processing time: ${avgTime.toFixed(1)}ms`);
      addLog(`- Success rate: ${successRate.toFixed(1)}%`);
      
    } catch (error) {
      addLog(`Performance test error: ${error.message}`);
    } finally {
      setProcessing(false);
    }
  };

  const clearDemo = () => {
    setMessage('');
    setHiddenMessage('');
    setCoverImageUrl('');
    setStegoImageUrl('');
    setLogs([]);
    updateStats();
    addLog('Demo cleared');
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h2>🔒 Steganography Demo</h2>
      
      {/* Status Panel */}
      <div style={{ 
        backgroundColor: '#f5f5f5', 
        padding: '15px', 
        borderRadius: '8px', 
        marginBottom: '20px' 
      }}>
        <h3>Status</h3>
        <p><strong>Steganography:</strong> {isEnabled ? '✅ Enabled' : '❌ Disabled'}</p>
        {stats && (
          <div>
            <p><strong>Auto-detect:</strong> {stats.autoDetect ? 'On' : 'Off'}</p>
            <p><strong>Cached images:</strong> {stats.cachedImages}</p>
            <p><strong>Quality:</strong> {stats.quality}</p>
            <p><strong>Password protected:</strong> {stats.hasPassword ? 'Yes' : 'No'}</p>
          </div>
        )}
      </div>

      {/* Controls */}
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
        
        {/* Left Panel - Message Input */}
        <div>
          <h3>1. Prepare Message</h3>
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Enter your secret message here..."
            style={{ 
              width: '100%', 
              height: '100px', 
              padding: '10px',
              border: '1px solid #ccc',
              borderRadius: '4px'
            }}
          />
          
          <h3>2. Generate Cover Image</h3>
          <div style={{ marginBottom: '10px' }}>
            <label>Style: </label>
            <select 
              value={selectedStyle} 
              onChange={(e) => setSelectedStyle(e.target.value)}
              style={{ padding: '5px', marginLeft: '10px' }}
            >
              <option value="noise">Noise</option>
              <option value="gradient">Gradient</option>
              <option value="pattern">Pattern</option>
            </select>
          </div>
          
          <button 
            onClick={generateCoverImage}
            disabled={processing}
            style={{ 
              padding: '10px 20px', 
              backgroundColor: '#007bff', 
              color: 'white', 
              border: 'none', 
              borderRadius: '4px',
              cursor: processing ? 'not-allowed' : 'pointer',
              marginRight: '10px'
            }}
          >
            Generate Cover Image
          </button>
        </div>

        {/* Right Panel - Actions */}
        <div>
          <h3>3. Steganography Actions</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <button 
              onClick={hideMessage}
              disabled={processing || !message.trim() || !coverImageUrl}
              style={{ 
                padding: '10px', 
                backgroundColor: '#28a745', 
                color: 'white', 
                border: 'none', 
                borderRadius: '4px',
                cursor: processing ? 'not-allowed' : 'pointer'
              }}
            >
              Hide Message in Image
            </button>
            
            <button 
              onClick={revealMessage}
              disabled={processing || !stegoImageUrl}
              style={{ 
                padding: '10px', 
                backgroundColor: '#ffc107', 
                color: 'black', 
                border: 'none', 
                borderRadius: '4px',
                cursor: processing ? 'not-allowed' : 'pointer'
              }}
            >
              Reveal Hidden Message
            </button>
            
            <button 
              onClick={detectMessage}
              disabled={processing || !stegoImageUrl}
              style={{ 
                padding: '10px', 
                backgroundColor: '#17a2b8', 
                color: 'white', 
                border: 'none', 
                borderRadius: '4px',
                cursor: processing ? 'not-allowed' : 'pointer'
              }}
            >
              Detect Hidden Message
            </button>
            
            <button 
              onClick={runPerformanceTest}
              disabled={processing}
              style={{ 
                padding: '10px', 
                backgroundColor: '#6f42c1', 
                color: 'white', 
                border: 'none', 
                borderRadius: '4px',
                cursor: processing ? 'not-allowed' : 'pointer'
              }}
            >
              Run Performance Test
            </button>
            
            <button 
              onClick={clearDemo}
              disabled={processing}
              style={{ 
                padding: '10px', 
                backgroundColor: '#dc3545', 
                color: 'white', 
                border: 'none', 
                borderRadius: '4px',
                cursor: processing ? 'not-allowed' : 'pointer'
              }}
            >
              Clear Demo
            </button>
          </div>
        </div>
      </div>

      {/* Images Display */}
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '20px', marginBottom: '20px' }}>
        <div>
          <h4>Cover Image</h4>
          {coverImageUrl ? (
            <img 
              src={coverImageUrl} 
              alt="Cover" 
              style={{ width: '100%', maxWidth: '200px', border: '1px solid #ccc' }}
            />
          ) : (
            <div style={{ 
              width: '200px', 
              height: '200px', 
              backgroundColor: '#f0f0f0', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              border: '1px solid #ccc'
            }}>
              No cover image
            </div>
          )}
        </div>
        
        <div>
          <h4>Stego Image</h4>
          {stegoImageUrl ? (
            <img 
              src={stegoImageUrl} 
              alt="Stego" 
              style={{ width: '100%', maxWidth: '200px', border: '1px solid #ccc' }}
            />
          ) : (
            <div style={{ 
              width: '200px', 
              height: '200px', 
              backgroundColor: '#f0f0f0', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              border: '1px solid #ccc'
            }}>
              No stego image
            </div>
          )}
        </div>
        
        <div>
          <h4>Revealed Message</h4>
          <div style={{ 
            width: '200px', 
            height: '200px', 
            backgroundColor: '#f9f9f9', 
            padding: '10px',
            border: '1px solid #ccc',
            overflow: 'auto'
          }}>
            {hiddenMessage || 'No message revealed'}
          </div>
        </div>
      </div>

      {/* Activity Log */}
      <div>
        <h3>Activity Log</h3>
        <div style={{ 
          backgroundColor: '#000', 
          color: '#00ff00', 
          padding: '15px', 
          borderRadius: '4px', 
          fontFamily: 'monospace',
          fontSize: '12px',
          height: '200px',
          overflow: 'auto'
        }}>
          {logs.map((log, index) => (
            <div key={index}>{log}</div>
          ))}
          {processing && <div>⏳ Processing...</div>}
        </div>
      </div>
    </div>
  );
};

export default SteganographyDemo;
