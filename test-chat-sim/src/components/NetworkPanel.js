import React, { useRef, useEffect, useState } from 'react';
import './NetworkPanel.css';

/**
 * Component for displaying the network traffic between users
 * 
 * @param {Object} props
 * @param {Array} props.messages - Array of network message objects
 */
function NetworkPanel({ messages }) {
  const networkEndRef = useRef(null);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [popoutVisible, setPopoutVisible] = useState(false);
  const popoutRef = useRef(null);

  // Auto-scroll to bottom of network panel on new message
  useEffect(() => {
    if (networkEndRef.current) {
      networkEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle click outside popout to close it
  useEffect(() => {
    function handleClickOutside(event) {
      if (popoutRef.current && !popoutRef.current.contains(event.target)) {
        setPopoutVisible(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [popoutRef]);

  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  };

  // Handle message click to show popout
  const handleMessageClick = (message) => {
    setSelectedMessage(message);
    setPopoutVisible(true);
  };

  // Render message content based on type
  const renderMessageContent = (message) => {
    if (message.type === 'image') {
      return (
        <div className="network-message-content">
          <span className="message-emoji" role="img" aria-label="Image">🖼️</span> Message: [Image] ({Math.round(message.content.length / 1024)} KB)
        </div>
      );
    }
    if (message.type === 'gif') {
      return (
        <div className="network-message-content">
          <span className="message-emoji" role="img" aria-label="GIF">🎬</span> Message: [GIF] from Giphy
        </div>
      );
    }
    if (message.type === 'edit') {
      return (
        <div className="network-message-content">
          <span className="message-emoji" role="img" aria-label="Edit">✏️</span> {message.content}
        </div>
      );
    }
    return (
      <div className="network-message-content">
        <span className="message-emoji" role="img" aria-label="Text">💬</span> Message: "{message.content}"
      </div>
    );
  };

  // Render detailed message content for popout
  const renderDetailedContent = (message) => {
    if (message.type === 'image') {
      return (
        <div className="popout-message-content">
          <div className="popout-image-container">
            <img 
              src={message.content} 
              alt="Image transmission" 
              className="popout-image"
            />
          </div>
          <div className="popout-message-metadata">
            <div>Size: ~{Math.round(message.content.length / 1024)} KB</div>
            <div>Format: {message.content.substring(0, message.content.indexOf(';')).split('/')[1]}</div>
          </div>
        </div>
      );
    }
    if (message.type === 'gif') {
      return (
        <div className="popout-message-content">
          <div className="popout-image-container">
            <img 
              src={message.content} 
              alt="GIF transmission" 
              className="popout-image"
            />
          </div>
          <div className="popout-message-metadata">
            <div>Type: Animated GIF</div>
            <div>Source: Giphy</div>
          </div>
        </div>
      );
    }
    if (message.type === 'edit') {
      return (
        <div className="popout-message-content">
          <div className="popout-text-content">
            {message.content}
          </div>
          <div className="popout-message-metadata">
            <div>Edit operation on message: {message.originalMessageId}</div>
          </div>
        </div>
      );
    }
    return (
      <div className="popout-message-content">
        <div className="popout-text-content">
          {message.content}
        </div>
        <div className="popout-message-metadata">
          <div>Length: {message.content.length} characters</div>
        </div>
      </div>
    );
  };

  return (
    <div className="network-panel">
      <div className="network-header">
        <h3>Network Traffic</h3>
      </div>
      <div className="network-content">
        {messages.length === 0 ? (
          <div className="no-traffic">No network traffic yet</div>
        ) : (
          <div className="network-messages">
            {messages.map(message => (
              <div 
                key={message.id} 
                className="network-message" 
                data-testid="network-message"
                onClick={() => handleMessageClick(message)}
              >
                <div className="network-message-header">
                  <span className="network-timestamp">{formatTimestamp(message.networkTimestamp)}</span>
                  <span className={`network-status network-status-${message.status}`}>
                    {message.status.toUpperCase()}
                  </span>
                </div>
                <div className="network-message-details">
                  <div className="network-message-direction">
                    {message.sender.toUpperCase()} → {message.recipient.toUpperCase()}
                  </div>
                  <div className="network-message-type">
                    Type: {message.type}
                  </div>
                  {renderMessageContent(message)}
                  {message.status === 'delivered' && (
                    <div className="network-message-delivery">
                      Delivered at: {formatTimestamp(message.deliveredTimestamp)}
                    </div>
                  )}
                </div>
              </div>
            ))}
            <div ref={networkEndRef} />
          </div>
        )}
      </div>

      {/* Message details popout */}
      {popoutVisible && selectedMessage && (
        <div 
          className={`network-message-popout ${popoutVisible ? 'active' : ''}`}
          ref={popoutRef}
        >
          <div className="popout-header">
            <h4>Message Transmission Details</h4>
            <button 
              className="popout-close" 
              onClick={() => setPopoutVisible(false)}
              aria-label="Close details"
            >
              ×
            </button>
          </div>
          <div className="popout-content">
            <div className="popout-section">
              <h5>Transmission Info</h5>
              <div className="popout-grid">
                <div className="popout-label">From:</div>
                <div className="popout-value">{selectedMessage.sender.toUpperCase()}</div>
                
                <div className="popout-label">To:</div>
                <div className="popout-value">{selectedMessage.recipient.toUpperCase()}</div>
                
                <div className="popout-label">Sent at:</div>
                <div className="popout-value">{formatTimestamp(selectedMessage.networkTimestamp)}</div>
                
                <div className="popout-label">Status:</div>
                <div className="popout-value">{selectedMessage.status.toUpperCase()}</div>
                
                {selectedMessage.status === 'delivered' && (
                  <>
                    <div className="popout-label">Delivered at:</div>
                    <div className="popout-value">{formatTimestamp(selectedMessage.deliveredTimestamp)}</div>
                    
                    <div className="popout-label">Delivery time:</div>
                    <div className="popout-value">
                      {Math.round((new Date(selectedMessage.deliveredTimestamp) - new Date(selectedMessage.networkTimestamp)) / 10) / 100}s
                    </div>
                  </>
                )}
                
                <div className="popout-label">Message type:</div>
                <div className="popout-value">{selectedMessage.type.toUpperCase()}</div>
              </div>
            </div>
            
            <div className="popout-section">
              <h5>Message Content</h5>
              {renderDetailedContent(selectedMessage)}
            </div>
            
            <div className="popout-section">
              <h5>Technical Details</h5>
              <div className="popout-technical">
                <div className="popout-label">Message ID:</div>
                <div className="popout-value">{selectedMessage.id}</div>
                
                <div className="popout-label">Timestamp (ISO):</div>
                <div className="popout-value">{selectedMessage.timestamp}</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default NetworkPanel; 