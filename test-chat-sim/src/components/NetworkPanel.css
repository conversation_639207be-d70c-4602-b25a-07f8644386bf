.network-panel {
  background-color: var(--network-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow-color);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.network-header {
  border-bottom: 1px solid var(--border-color);
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--card-background);
}

.network-header h2 {
  font-size: 1.1rem;
  margin: 0;
  color: var(--text-color);
  font-weight: 600;
}

.network-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px 16px;
  background-color: var(--card-background);
  min-height: 100px;
}

.no-traffic {
  text-align: center;
  color: var(--text-muted);
  margin: 15px 0;
  font-style: italic;
}

.network-messages {
  overflow-y: auto;
  padding: 8px;
  flex: 1;
  min-height: 0;
}

.network-message {
  margin: 8px 0;
  padding: 10px 12px;
  border-radius: 6px;
  background-color: var(--network-bg);
  border-left: 3px solid var(--border-color);
  font-family: monospace;
  font-size: 0.9em;
  color: var(--text-color);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.network-message:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .network-message:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.network-message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 0.8em;
  color: var(--text-secondary);
}

.network-timestamp {
  font-weight: bold;
}

.network-status {
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 0.7em;
  font-weight: bold;
}

.network-status-transmitting {
  background-color: #fff3cd;
  color: #856404;
}

.network-status-delivered {
  background-color: #d4edda;
  color: #155724;
}

[data-theme="dark"] .network-status-transmitting {
  background-color: #413100;
  color: #ffd54f;
}

[data-theme="dark"] .network-status-delivered {
  background-color: #103420;
  color: #4caf50;
}

.network-message-details {
  font-size: 0.85em;
  line-height: 1.4;
}

.network-message-direction {
  font-weight: bold;
  margin-bottom: 4px;
}

.network-message-type {
  color: var(--text-secondary);
  font-size: 0.85rem;
  margin-bottom: 4px;
}

.network-message-content {
  margin-bottom: 4px;
  word-break: break-word;
}

.message-emoji {
  font-size: 1.2em;
  margin-right: 5px;
  vertical-align: middle;
}

.network-message-delivery {
  margin-top: 5px;
  font-size: 0.85em;
  color: var(--alice-color);
  font-style: italic;
}

/* Message Popout Styles */
.network-message-popout {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  z-index: 1200;
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.network-message-popout.active {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.popout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--card-background);
}

.popout-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-color);
}

.popout-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0 5px;
}

.popout-close:hover {
  color: var(--text-color);
}

.popout-content {
  padding: 16px;
  overflow-y: auto;
  max-height: calc(80vh - 50px);
}

.popout-section {
  margin-bottom: 20px;
}

.popout-section h5 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  color: var(--text-color);
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 6px;
}

.popout-grid {
  display: grid;
  grid-template-columns: 120px 1fr;
  row-gap: 10px;
  column-gap: 10px;
}

.popout-technical {
  display: grid;
  grid-template-columns: 120px 1fr;
  row-gap: 10px;
  column-gap: 10px;
}

.popout-label {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.popout-value {
  color: var(--text-color);
  font-size: 0.9rem;
  word-break: break-word;
}

.popout-message-content {
  background-color: var(--message-received-bg);
  border: 1px solid var(--message-received-border);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
}

.popout-text-content {
  margin-bottom: 10px;
  word-wrap: break-word;
  white-space: pre-wrap;
  font-size: 0.95rem;
}

.popout-image-container {
  margin-bottom: 10px;
  text-align: center;
}

.popout-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 6px;
}

.popout-message-metadata {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 10px;
} 