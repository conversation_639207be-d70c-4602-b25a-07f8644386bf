/* QR Code Verifier Styles */

/* Scanner UI */
.qr-scanner-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.qr-video-container {
  position: relative;
  width: 100%;
  max-width: 500px;
  height: 70vh;
  max-height: 500px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.qr-video-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

#qr-canvas {
  display: none;
}

.qr-scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  pointer-events: none;
}

.qr-scanner-header {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), transparent);
  padding: 20px;
  text-align: center;
  color: white;
  pointer-events: auto;
}

.qr-scanner-header h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
}

.qr-scanner-header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.qr-scanner-footer {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  pointer-events: auto;
}

.qr-scanner-footer button {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.qr-scanner-footer button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.qr-scan-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 20px;
  box-shadow: 0 0 0 4000px rgba(0, 0, 0, 0.3);
}

.qr-scan-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(0, 120, 255, 0.8);
  animation: scan 2s linear infinite;
}

@keyframes scan {
  0% {
    top: 0;
  }
  50% {
    top: 100%;
  }
  100% {
    top: 0;
  }
}

.scan-success .qr-scan-indicator {
  border-color: #2ecc71;
  box-shadow: 0 0 0 4000px rgba(46, 204, 113, 0.2);
}

.qr-error-message {
  background-color: rgba(231, 76, 60, 0.9);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  text-align: center;
  margin: 10px auto;
  max-width: 80%;
}

.qr-scanner-error {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  color: white;
  max-width: 80%;
  margin: 0 auto;
}

.qr-scanner-error h2 {
  margin-top: 0;
  color: #e74c3c;
}

.qr-scanner-error button {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin: 10px 5px 0;
  font-size: 14px;
}

.qr-scanner-error button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Manual Entry Form */
.qr-manual-entry-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.qr-manual-entry-dialog {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  display: flex;
  flex-direction: column;
}

.qr-manual-entry-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.qr-manual-entry-header h2 {
  margin: 0;
  font-size: 18px;
}

.qr-manual-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
}

.qr-manual-entry-body {
  padding: 20px;
}

.qr-manual-fingerprint-input {
  width: 100%;
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-family: monospace;
  margin-bottom: 16px;
  box-sizing: border-box;
}

.qr-manual-entry-note {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}

.qr-manual-error-message {
  color: #e74c3c;
  font-size: 14px;
  margin-top: 8px;
  min-height: 20px;
}

.qr-manual-entry-footer {
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.qr-manual-cancel {
  padding: 8px 16px;
  background-color: #f0f0f0;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  cursor: pointer;
}

.qr-manual-verify {
  padding: 8px 16px;
  background-color: #0078d4;
  color: white;
  border: 1px solid #0078d4;
  border-radius: 4px;
  cursor: pointer;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .qr-manual-entry-dialog {
    background-color: #2d2d2d;
    color: #f0f0f0;
  }
  
  .qr-manual-entry-header {
    border-bottom-color: #444;
  }
  
  .qr-manual-close {
    color: #f0f0f0;
  }
  
  .qr-manual-fingerprint-input {
    background-color: #333;
    border-color: #444;
    color: #f0f0f0;
  }
  
  .qr-manual-entry-note {
    color: #aaa;
  }
  
  .qr-manual-entry-footer {
    border-top-color: #444;
  }
  
  .qr-manual-cancel {
    background-color: #444;
    border-color: #555;
    color: #f0f0f0;
  }
}
