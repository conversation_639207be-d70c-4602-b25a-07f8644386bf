// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';
import { setupLocalStorageMock, resetLocalStorageMock } from './__mocks__/localStorageMock';

// Set up the localStorage mock
setupLocalStorageMock();

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollIntoView
Element.prototype.scrollIntoView = jest.fn();

// Set up global beforeEach for all tests
beforeEach(() => {
  // Reset all localStorage mocks before each test
  resetLocalStorageMock();
  
  // Clear scrollIntoView mocks
  if (Element.prototype.scrollIntoView) {
    Element.prototype.scrollIntoView.mockClear();
  }
});

// Suppress act() warnings for async updates in tests
const originalError = console.error;
console.error = (...args) => {
  if (/Warning.*not wrapped in act/.test(args[0])) {
    return;
  }
  originalError.call(console, ...args);
}; 