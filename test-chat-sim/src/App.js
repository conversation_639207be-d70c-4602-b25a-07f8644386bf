import React, { useState } from 'react';
import ChatPanel from './components/ChatPanel';

function App() {
  const [messages, setMessages] = useState([]);
  const [darkMode, setDarkMode] = useState(false);

  const handleSendMessage = (sender, content) => {
    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const newMessage = {
      id: messageId,
      sender,
      recipient: sender === 'alice' ? 'bob' : 'alice',
      content,
      timestamp: new Date().toISOString(),
      status: 'delivered',
      type: 'text'
    };
    
    setMessages(prevMessages => [...prevMessages, newMessage]);
  };

  const handleClearMessages = () => {
    setMessages([]);
  };

  const toggleDarkMode = () => {
    setDarkMode(prev => !prev);
  };

  // Filter messages for each user
  const aliceMessages = messages.filter(msg => 
    msg.sender === 'alice' || msg.recipient === 'alice'
  );
  
  const bobMessages = messages.filter(msg => 
    msg.sender === 'bob' || msg.recipient === 'bob'
  );

  return (
    <div className={`App ${darkMode ? 'dark-mode' : ''}`}>
      <div className="app-container">
        <button className="theme-toggle" onClick={toggleDarkMode}>
          {darkMode ? '☀️ Light Mode' : '🌙 Dark Mode'}
        </button>
        <div className="chat-layout">
          <div className="chats-container">
            <ChatPanel
              user="alice"
              displayName="Alice"
              messages={aliceMessages}
              onSendMessage={handleSendMessage}
              onClearMessages={handleClearMessages}
              darkMode={darkMode}
            />
            <ChatPanel
              user="bob"
              displayName="Bob"
              messages={bobMessages}
              onSendMessage={handleSendMessage}
              onClearMessages={handleClearMessages}
              darkMode={darkMode}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;