import React, { useState, useEffect } from 'react';
import ChatPanel from './components/ChatPanel';
import NetworkPanel from './components/NetworkPanel';
import { saveMessages, loadMessages } from './utils/storage';
// Import the mock OtrSession instead of the real one
import { OtrSession } from './utils/otrMock';

const NETWORK_DELAY_MS = 1500; // Simulated network delay

function App() {
  const [messages, setMessages] = useState([]);
  const [networkMessages, setNetworkMessages] = useState([]);
  const [darkMode, setDarkMode] = useState(() => {
    // Get saved theme preference or use system preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      return savedTheme === 'dark';
    }
    
    // Safely check for matchMedia support (for tests)
    try {
      return window.matchMedia && 
             window.matchMedia('(prefers-color-scheme: dark)') && 
             window.matchMedia('(prefers-color-scheme: dark)').matches;
    } catch (error) {
      console.log('matchMedia not available, defaulting to light mode');
      return false;
    }
  });
  
  // OTR sessions for <PERSON> and <PERSON>
  const [aliceSession, setAliceSession] = useState(null);
  const [bobSession, setBobSession] = useState(null);
  const [otrEnabled, setOtrEnabled] = useState(false);
  
  // SMP authentication state
  const [aliceSmpState, setAliceSmpState] = useState('none'); // 'none', 'pending', 'verified', 'failed'
  const [bobSmpState, setBobSmpState] = useState('none');
  const [aliceSmpQuestion, setAliceSmpQuestion] = useState('');
  const [bobSmpQuestion, setBobSmpQuestion] = useState('');

  // Initialize OTR sessions
  useEffect(() => {
    // Create a function to send messages to the other peer
    const createSendMessageFunction = (sender) => {
      return (message) => {
        console.log(`[OTR] ${sender} sending: ${message}`);
        // Add the OTR message to network messages for visualization
        const networkMessage = {
          id: `otr_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
          sender: sender,
          recipient: sender === 'alice' ? 'bob' : 'alice',
          content: message,
          timestamp: new Date().toISOString(),
          type: 'otr',
          status: 'transmitting',
          networkTimestamp: new Date().toISOString()
        };
        
        setNetworkMessages(prevNetworkMessages => [...prevNetworkMessages, networkMessage]);
        
        // Simulate delivery to recipient after network delay
        setTimeout(() => {
          // Update network message status
          setNetworkMessages(prevNetworkMessages => 
            prevNetworkMessages.map(msg => 
              msg.id === networkMessage.id 
                ? { ...msg, status: 'delivered', deliveredTimestamp: new Date().toISOString() } 
                : msg
            )
          );
          
          // Process the message by the recipient's OTR session
          if (sender === 'alice' && bobSession) {
            bobSession.processIncoming(message)
              .then(result => {
                if (result && !result.internal && result.message) {
                  // Add decrypted message to chat
                  handleReceivedMessage('alice', 'bob', result.message, result.encrypted);
                }
              })
              .catch(error => {
                console.error('[OTR] Error processing incoming message in Bob session:', error);
              });
          } else if (sender === 'bob' && aliceSession) {
            aliceSession.processIncoming(message)
              .then(result => {
                if (result && !result.internal && result.message) {
                  // Add decrypted message to chat
                  handleReceivedMessage('bob', 'alice', result.message, result.encrypted);
                }
              })
              .catch(error => {
                console.error('[OTR] Error processing incoming message in Alice session:', error);
              });
          }
        }, NETWORK_DELAY_MS);
      };
    };

    // Only initialize if sessions don't already exist
    if (!aliceSession && !bobSession) {
      try {
        // Initialize Alice's OTR session
        const alice = new OtrSession('bob', {
          sendMessage: createSendMessageFunction('alice'),
          version: 3
        });
        
        // Initialize Bob's OTR session
        const bob = new OtrSession('alice', {
          sendMessage: createSendMessageFunction('bob'),
          version: 3
        });
        
        // Initialize both sessions
        Promise.all([
          alice.init(),
          bob.init()
        ]).then(() => {
          // Register SMP callbacks
          alice.registerSMPCallback(result => {
            console.log('[SMP] Alice callback result:', result);
            if (result.result === 1) { // SUCCESS
              setAliceSmpState('verified');
            } else if (result.result === 2) { // FAILURE
              setAliceSmpState('failed');
            } else if (result.result === 3) { // ABORTED
              setAliceSmpState('none');
            }
          });
          
          bob.registerSMPCallback(result => {
            console.log('[SMP] Bob callback result:', result);
            if (result.result === 1) { // SUCCESS
              setBobSmpState('verified');
            } else if (result.result === 2) { // FAILURE
              setBobSmpState('failed');
            } else if (result.result === 3) { // ABORTED
              setBobSmpState('none');
            }
            
            // If Bob received a question, store it
            if (result.question) {
              setBobSmpQuestion(result.question);
            }
          });
          
          setAliceSession(alice);
          setBobSession(bob);
          console.log('OTR sessions initialized');
        }).catch(error => {
          console.error('Error initializing OTR sessions:', error);
        });
      } catch (error) {
        console.error('[OTR] Failed to create OTR sessions:', error);
      }
    }
    
    // Clean up function
    return () => {
      // This only runs on component unmount, so we can safely access the current state values
      if (aliceSession) {
        try {
          aliceSession.endOtr().catch(e => console.error('[OTR] Error ending Alice session:', e));
        } catch (error) {
          console.error('[OTR] Error ending Alice session:', error);
        }
      }
      if (bobSession) {
        try {
          bobSession.endOtr().catch(e => console.error('[OTR] Error ending Bob session:', e));
        } catch (error) {
          console.error('[OTR] Error ending Bob session:', error);
        }
      }
    };
  }, [aliceSession, bobSession]);

  // Function to handle received decrypted messages
  const handleReceivedMessage = (sender, recipient, content, encrypted) => {
    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // Create a new message object
    const newMessage = {
      id: messageId,
      sender: sender,
      recipient: recipient,
      content: content,
      timestamp: new Date().toISOString(),
      status: 'delivered',
      type: 'text',
      encrypted: encrypted
    };
    
    // Add message to state
    setMessages(prevMessages => {
      const updatedMessages = [...prevMessages, newMessage];
      // Save to localStorage
      saveMessages(updatedMessages);
      return updatedMessages;
    });
  };

  // Apply theme to document
  useEffect(() => {
    if (darkMode) {
      document.documentElement.setAttribute('data-theme', 'dark');
    } else {
      document.documentElement.removeAttribute('data-theme');
    }
    // Save theme preference
    localStorage.setItem('theme', darkMode ? 'dark' : 'light');
  }, [darkMode]);

  // Load saved messages on component mount
  useEffect(() => {
    try {
      const savedMessages = loadMessages();
      if (savedMessages && savedMessages.length > 0) {
        setMessages(savedMessages);
        console.log(`Loaded ${savedMessages.length} messages from localStorage`);
        
        // Recreate network messages from saved messages
        const newNetworkMessages = savedMessages.map(msg => ({
          ...msg,
          networkTimestamp: msg.timestamp,
          deliveredTimestamp: msg.status === 'delivered' ? 
            new Date(new Date(msg.timestamp).getTime() + NETWORK_DELAY_MS).toISOString() : 
            undefined
        }));
        
        setNetworkMessages(newNetworkMessages);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  }, []);

  // Save messages whenever they change
  useEffect(() => {
    if (messages.length > 0) {
      saveMessages(messages);
    }
  }, [messages]);

  const toggleDarkMode = () => {
    setDarkMode(prev => !prev);
  };

  const toggleOtr = () => {
    // If enabling OTR, initiate the OTR protocol
    if (!otrEnabled) {
      if (aliceSession && bobSession) {
        try {
          // Start OTR for Alice (Bob will respond automatically)
          aliceSession.startOtr()
            .then(() => {
              console.log("[OTR] Alice initiated OTR");
              // Toggle OTR state when successful
              setOtrEnabled(true);
            })
            .catch(error => {
              console.error('[OTR] Error starting Alice OTR session:', error);
              // Show an error message to the user (could be improved with a toast notification)
              alert('Failed to start OTR session. Check console for details.');
            });
        } catch (error) {
          console.error('[OTR] Unexpected error starting OTR:', error);
        }
      } else {
        console.error('[OTR] Cannot enable OTR: Sessions not initialized');
        // Show an error message
        alert('OTR sessions not initialized. Try refreshing the page.');
      }
    } else {
      // If disabling OTR, end the OTR sessions
      try {
        const promises = [];
        
        if (aliceSession) {
          promises.push(
            aliceSession.endOtr()
              .then(() => {
                console.log("[OTR] Alice ended OTR");
              })
              .catch(error => {
                console.error('[OTR] Error ending Alice OTR session:', error);
              })
          );
        }
        
        if (bobSession) {
          promises.push(
            bobSession.endOtr()
              .then(() => {
                console.log("[OTR] Bob ended OTR");
              })
              .catch(error => {
                console.error('[OTR] Error ending Bob OTR session:', error);
              })
          );
        }
        
        // Toggle state once all sessions have been ended (or attempted to end)
        Promise.all(promises)
          .then(() => {
            setOtrEnabled(false);
          })
          .catch(() => {
            // Even if ending sessions fails, still disable OTR in the UI
            setOtrEnabled(false);
          });
      } catch (error) {
        console.error('[OTR] Unexpected error ending OTR:', error);
        // Still toggle state off in case of error
        setOtrEnabled(false);
      }
    }
  };

  const handleSendMessage = (sender, content, type = 'text') => {
    const timestamp = new Date().toISOString();
    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // Create a new message object
    const newMessage = {
      id: messageId,
      sender,
      recipient: sender === 'alice' ? 'bob' : 'alice',
      content,
      timestamp,
      status: 'sent',
      type: type // Add type field for text or image
    };
    
    // Add message to state (immediately visible to sender)
    setMessages(prevMessages => {
      const updatedMessages = [...prevMessages, newMessage];
      // Save to localStorage after updating state
      saveMessages(updatedMessages);
      return updatedMessages;
    });
    
    // If OTR is enabled, process the message through OTR session
    if (otrEnabled && type === 'text') {
      const session = sender === 'alice' ? aliceSession : bobSession;
      
      if (session) {
        // Process outgoing message through OTR
        session.processOutgoing(content)
          .then(processedMessage => {
            if (processedMessage) {
              // The actual sending is handled by the sendMessage function in the session
              console.log(`[OTR] Processed outgoing message from ${sender}`);
            }
          })
          .catch(error => {
            console.error('[OTR] Error processing outgoing message:', error);
          });
        
        // Return early as the OTR session will handle message transmission
        return;
      }
    }
    
    // For non-OTR messages or when OTR session is not available, use the original logic
    // Simulate network transmission
    const networkMessage = {
      ...newMessage,
      status: 'transmitting',
      networkTimestamp: new Date().toISOString()
    };
    
    setNetworkMessages(prevNetworkMessages => [...prevNetworkMessages, networkMessage]);
    
    // Simulate network delay for message delivery
    setTimeout(() => {
      // Update message status to 'delivered'
      setMessages(prevMessages => {
        const updatedMessages = prevMessages.map(msg => 
          msg.id === messageId 
            ? { ...msg, status: 'delivered' } 
            : msg
        );
        // Save to localStorage after updating status
        saveMessages(updatedMessages);
        return updatedMessages;
      });
      
      // Update network message status
      setNetworkMessages(prevNetworkMessages => 
        prevNetworkMessages.map(msg => 
          msg.id === messageId 
            ? { ...msg, status: 'delivered', deliveredTimestamp: new Date().toISOString() } 
            : msg
        )
      );
    }, NETWORK_DELAY_MS);
  };

  const handleEditMessage = (messageId, newContent) => {
    // Update message in messages array
    setMessages(prevMessages => {
      const updatedMessages = prevMessages.map(msg => 
        msg.id === messageId 
          ? { ...msg, content: newContent, edited: true } 
          : msg
      );
      // Save to localStorage
      saveMessages(updatedMessages);
      return updatedMessages;
    });

    // Add a network message for the edit
    const editedMessage = messages.find(msg => msg.id === messageId);
    if (editedMessage) {
      const editNetworkMessage = {
        id: `edit_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        originalMessageId: messageId,
        sender: editedMessage.sender,
        recipient: editedMessage.recipient,
        content: `Message edited: "${newContent}"`,
        timestamp: new Date().toISOString(),
        type: 'edit',
        status: 'delivered',
        networkTimestamp: new Date().toISOString(),
        deliveredTimestamp: new Date().toISOString()
      };

      setNetworkMessages(prevNetworkMessages => [...prevNetworkMessages, editNetworkMessage]);
    }
  };

  const handleClearMessages = () => {
    setMessages([]);
    setNetworkMessages([]);
    saveMessages([]);
  };

  // Filter messages for each user
  const aliceMessages = messages.filter(msg => 
    msg.sender === 'alice' || (msg.recipient === 'alice' && msg.status === 'delivered')
  );
  
  const bobMessages = messages.filter(msg => 
    msg.sender === 'bob' || (msg.recipient === 'bob' && msg.status === 'delivered')
  );

  // Handle initiating SMP authentication
  const handleInitiateSMP = (initiator, secret) => {
    console.log(`[SMP] ${initiator} initiating SMP authentication with secret: ${secret}`);
    
    if (initiator === 'alice' && aliceSession) {
      // Update SMP state
      setAliceSmpState('pending');
      
      // Initiate SMP on Alice's session
      aliceSession.initiateSMP(secret)
        .then(() => {
          console.log('[SMP] Alice initiated SMP authentication');
        })
        .catch(error => {
          console.error('[SMP] Error initiating SMP authentication on Alice session:', error);
          setAliceSmpState('failed');
          // Show user error
          alert('Failed to initiate identity verification. Check console for details.');
        });
    } else if (initiator === 'bob' && bobSession) {
      // Update SMP state
      setBobSmpState('pending');
      
      // Initiate SMP on Bob's session
      bobSession.initiateSMP(secret)
        .then(() => {
          console.log('[SMP] Bob initiated SMP authentication');
        })
        .catch(error => {
          console.error('[SMP] Error initiating SMP authentication on Bob session:', error);
          setBobSmpState('failed');
          // Show user error
          alert('Failed to initiate identity verification. Check console for details.');
        });
    }
  };
  
  // Handle verification completion (for QR code or other methods)
  const handleVerificationComplete = (result) => {
    console.log('[Verification] Complete:', result);
    
    if (result.user === 'alice') {
      if (result.success) {
        setAliceSmpState('verified');
      } else {
        setAliceSmpState('failed');
      }
    } else if (result.user === 'bob') {
      if (result.success) {
        setBobSmpState('verified');
      } else {
        setBobSmpState('failed');
      }
    }
  };
  
  // Handle responding to SMP authentication
  const handleRespondSMP = (responder, secret) => {
    console.log(`[SMP] ${responder} responding to SMP authentication with secret: ${secret}`);
    
    if (responder === 'alice' && aliceSession) {
      // Update SMP state
      setAliceSmpState('pending');
      
      // Respond to SMP on Alice's session
      aliceSession.respondToSMP(secret)
        .then(() => {
          console.log('[SMP] Alice responded to SMP authentication');
        })
        .catch(error => {
          console.error('[SMP] Error responding to SMP authentication on Alice session:', error);
          setAliceSmpState('failed');
          // Show user error
          alert('Failed to respond to identity verification. Check console for details.');
        });
    } else if (responder === 'bob' && bobSession) {
      // Update SMP state
      setBobSmpState('pending');
      
      // Respond to SMP on Bob's session
      bobSession.respondToSMP(secret)
        .then(() => {
          console.log('[SMP] Bob responded to SMP authentication');
        })
        .catch(error => {
          console.error('[SMP] Error responding to SMP authentication on Bob session:', error);
          setBobSmpState('failed');
          // Show user error
          alert('Failed to respond to identity verification. Check console for details.');
        });
    }
  };

  return (
    <div className={`App ${darkMode ? 'dark-mode' : ''}`}>
      <header className="app-header">
        <h1>WebOTR Test Chat Simulator</h1>
        <div className="header-controls">
          <div className="otr-toggle">
            <label className="toggle-switch">
              <span>OTR:</span>
              <input
                type="checkbox"
                checked={otrEnabled}
                onChange={toggleOtr}
                disabled={!aliceSession || !bobSession}
              />
              <span className="slider"></span>
            </label>
          </div>
          <div className="dark-mode-toggle">
            <label className="toggle-switch">
              <span>Dark Mode:</span>
              <input
                type="checkbox"
                checked={darkMode}
                onChange={toggleDarkMode}
              />
              <span className="slider"></span>
            </label>
          </div>
        </div>
      </header>
      
      <div className="chat-layout">
        <div className="chats-container">
          <ChatPanel
            user="alice"
            displayName="Alice"
            messages={aliceMessages}
            onSendMessage={handleSendMessage}
            onClearMessages={handleClearMessages}
            darkMode={darkMode}
            onEditMessage={handleEditMessage}
            otrEnabled={otrEnabled}
            onInitiateSMP={handleInitiateSMP}
            onRespondSMP={handleRespondSMP}
            onVerificationComplete={handleVerificationComplete}
            smpState={aliceSmpState}
            smpQuestion={aliceSmpQuestion}
          />
          <ChatPanel
            user="bob"
            displayName="Bob"
            messages={bobMessages}
            onSendMessage={handleSendMessage}
            onClearMessages={handleClearMessages}
            darkMode={darkMode}
            onEditMessage={handleEditMessage}
            otrEnabled={otrEnabled}
            onInitiateSMP={handleInitiateSMP}
            onRespondSMP={handleRespondSMP}
            onVerificationComplete={handleVerificationComplete}
            smpState={bobSmpState}
            smpQuestion={bobSmpQuestion}
          />
        </div>
        <div className="network-container">
          <NetworkPanel
            messages={networkMessages}
            darkMode={darkMode}
          />
        </div>
      </div>
    </div>
  );
}

export default App;
