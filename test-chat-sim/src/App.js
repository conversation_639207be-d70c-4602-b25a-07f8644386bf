import React, { useState } from 'react';
import ChatPanel from './components/ChatPanel';
<<<<<<< HEAD
import NetworkPanel from './components/NetworkPanel';
import SteganographyDemo from './components/SteganographyDemo';
import HandshakeDemo from './components/HandshakeDemo';
import { saveMessages, loadMessages } from './utils/storage';
// Import the mock OtrSession instead of the real one
import { OtrSession } from './utils/otrMock';

const NETWORK_DELAY_MS = 1500; // Simulated network delay

function App() {
  const [messages, setMessages] = useState([]);
  const [networkMessages, setNetworkMessages] = useState([]);
  const [activeTab, setActiveTab] = useState('chat'); // 'chat', 'steganography', or 'handshake'
  const [darkMode, setDarkMode] = useState(() => {
    // Get saved theme preference or use system preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      return savedTheme === 'dark';
    }
    
    // Safely check for matchMedia support (for tests)
    try {
      return window.matchMedia && 
             window.matchMedia('(prefers-color-scheme: dark)') && 
             window.matchMedia('(prefers-color-scheme: dark)').matches;
    } catch (error) {
      console.log('matchMedia not available, defaulting to light mode');
      return false;
    }
  });
  
  // OTR sessions for Alice and Bob
  const [aliceSession, setAliceSession] = useState(null);
  const [bobSession, setBobSession] = useState(null);
  const [otrEnabled, setOtrEnabled] = useState(false);
  
  // SMP authentication state
  const [aliceSmpState, setAliceSmpState] = useState('none'); // 'none', 'pending', 'verified', 'failed'
  const [bobSmpState, setBobSmpState] = useState('none');
  const [aliceSmpQuestion, setAliceSmpQuestion] = useState('');
  const [bobSmpQuestion, setBobSmpQuestion] = useState('');
=======

function App() {
  const [messages, setMessages] = useState([]);
  const [darkMode, setDarkMode] = useState(false);
>>>>>>> origin/feature/browser-extension

  const handleSendMessage = (sender, content) => {
    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const newMessage = {
      id: messageId,
      sender,
      recipient: sender === 'alice' ? 'bob' : 'alice',
      content,
      timestamp: new Date().toISOString(),
      status: 'delivered',
      type: 'text'
    };
    
    setMessages(prevMessages => [...prevMessages, newMessage]);
  };

  const handleClearMessages = () => {
    setMessages([]);
  };

  const toggleDarkMode = () => {
    setDarkMode(prev => !prev);
  };

  // Filter messages for each user
  const aliceMessages = messages.filter(msg => 
    msg.sender === 'alice' || msg.recipient === 'alice'
  );
  
  const bobMessages = messages.filter(msg => 
    msg.sender === 'bob' || msg.recipient === 'bob'
  );

<<<<<<< HEAD
  // Handle initiating SMP authentication
  const handleInitiateSMP = (initiator, secret) => {
    console.log(`[SMP] ${initiator} initiating SMP authentication with secret: ${secret}`);
    
    if (initiator === 'alice' && aliceSession) {
      // Update SMP state
      setAliceSmpState('pending');
      
      // Initiate SMP on Alice's session
      aliceSession.initiateSMP(secret)
        .then(() => {
          console.log('[SMP] Alice initiated SMP authentication');
        })
        .catch(error => {
          console.error('[SMP] Error initiating SMP authentication on Alice session:', error);
          setAliceSmpState('failed');
          // Show user error
          alert('Failed to initiate identity verification. Check console for details.');
        });
    } else if (initiator === 'bob' && bobSession) {
      // Update SMP state
      setBobSmpState('pending');
      
      // Initiate SMP on Bob's session
      bobSession.initiateSMP(secret)
        .then(() => {
          console.log('[SMP] Bob initiated SMP authentication');
        })
        .catch(error => {
          console.error('[SMP] Error initiating SMP authentication on Bob session:', error);
          setBobSmpState('failed');
          // Show user error
          alert('Failed to initiate identity verification. Check console for details.');
        });
    }
  };
  
  // Handle verification completion (for QR code or other methods)
  const handleVerificationComplete = (result) => {
    console.log('[Verification] Complete:', result);
    
    if (result.user === 'alice') {
      if (result.success) {
        setAliceSmpState('verified');
      } else {
        setAliceSmpState('failed');
      }
    } else if (result.user === 'bob') {
      if (result.success) {
        setBobSmpState('verified');
      } else {
        setBobSmpState('failed');
      }
    }
  };
  
  // Handle responding to SMP authentication
  const handleRespondSMP = (responder, secret) => {
    console.log(`[SMP] ${responder} responding to SMP authentication with secret: ${secret}`);
    
    if (responder === 'alice' && aliceSession) {
      // Update SMP state
      setAliceSmpState('pending');
      
      // Respond to SMP on Alice's session
      aliceSession.respondToSMP(secret)
        .then(() => {
          console.log('[SMP] Alice responded to SMP authentication');
        })
        .catch(error => {
          console.error('[SMP] Error responding to SMP authentication on Alice session:', error);
          setAliceSmpState('failed');
          // Show user error
          alert('Failed to respond to identity verification. Check console for details.');
        });
    } else if (responder === 'bob' && bobSession) {
      // Update SMP state
      setBobSmpState('pending');
      
      // Respond to SMP on Bob's session
      bobSession.respondToSMP(secret)
        .then(() => {
          console.log('[SMP] Bob responded to SMP authentication');
        })
        .catch(error => {
          console.error('[SMP] Error responding to SMP authentication on Bob session:', error);
          setBobSmpState('failed');
          // Show user error
          alert('Failed to respond to identity verification. Check console for details.');
        });
    }
  };

  return (
    <div className={`App ${darkMode ? 'dark-mode' : ''}`}>
      <header className="app-header">
        <h1>WebOTR Test Chat Simulator</h1>
        <div className="header-controls">
          <div className="tab-controls">
            <button
              className={`tab-button ${activeTab === 'chat' ? 'active' : ''}`}
              onClick={() => setActiveTab('chat')}
            >
              💬 Chat
            </button>
            <button
              className={`tab-button ${activeTab === 'steganography' ? 'active' : ''}`}
              onClick={() => setActiveTab('steganography')}
            >
              🔒 Steganography
            </button>
            <button
              className={`tab-button ${activeTab === 'handshake' ? 'active' : ''}`}
              onClick={() => setActiveTab('handshake')}
            >
              🤝 Handshake
            </button>
          </div>
          <div className="otr-toggle">
            <label className="toggle-switch">
              <span>OTR:</span>
              <input
                type="checkbox"
                checked={otrEnabled}
                onChange={toggleOtr}
                disabled={!aliceSession || !bobSession}
              />
              <span className="slider"></span>
            </label>
          </div>
          <div className="dark-mode-toggle">
            <label className="toggle-switch">
              <span>Dark Mode:</span>
              <input
                type="checkbox"
                checked={darkMode}
                onChange={toggleDarkMode}
              />
              <span className="slider"></span>
            </label>
          </div>
        </div>
      </header>

      {activeTab === 'chat' && (
        <div className="chat-layout">
          <div className="chats-container">
            <ChatPanel
              user="alice"
              displayName="Alice"
              messages={aliceMessages}
              onSendMessage={handleSendMessage}
              onClearMessages={handleClearMessages}
              darkMode={darkMode}
              onEditMessage={handleEditMessage}
              otrEnabled={otrEnabled}
              onInitiateSMP={handleInitiateSMP}
              onRespondSMP={handleRespondSMP}
              onVerificationComplete={handleVerificationComplete}
              smpState={aliceSmpState}
              smpQuestion={aliceSmpQuestion}
            />
            <ChatPanel
              user="bob"
              displayName="Bob"
              messages={bobMessages}
              onSendMessage={handleSendMessage}
              onClearMessages={handleClearMessages}
              darkMode={darkMode}
              onEditMessage={handleEditMessage}
              otrEnabled={otrEnabled}
              onInitiateSMP={handleInitiateSMP}
              onRespondSMP={handleRespondSMP}
              onVerificationComplete={handleVerificationComplete}
              smpState={bobSmpState}
              smpQuestion={bobSmpQuestion}
            />
          </div>
          <div className="network-container">
            <NetworkPanel
              messages={networkMessages}
              darkMode={darkMode}
            />
          </div>
        </div>
      )}

      {activeTab === 'steganography' && (
        <div className="steganography-layout">
          <SteganographyDemo />
=======
  return (
    <div className={`App ${darkMode ? 'dark-mode' : ''}`}>
      <div className="app-container">
        <button className="theme-toggle" onClick={toggleDarkMode}>
          {darkMode ? '☀️ Light Mode' : '🌙 Dark Mode'}
        </button>
        <div className="chat-layout">
          <div className="chats-container">
            <ChatPanel
              user="alice"
              displayName="Alice"
              messages={aliceMessages}
              onSendMessage={handleSendMessage}
              onClearMessages={handleClearMessages}
              darkMode={darkMode}
            />
            <ChatPanel
              user="bob"
              displayName="Bob"
              messages={bobMessages}
              onSendMessage={handleSendMessage}
              onClearMessages={handleClearMessages}
              darkMode={darkMode}
            />
          </div>
>>>>>>> origin/feature/browser-extension
        </div>
      )}

      {activeTab === 'handshake' && (
        <div className="handshake-layout">
          <HandshakeDemo />
        </div>
      )}
    </div>
  );
}

<<<<<<< HEAD
export default App;
=======
export default App;
>>>>>>> origin/feature/browser-extension
