const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const port = process.env.PORT || 3003;

// Log current working directory and build path
console.log('Current working directory:', process.cwd());
console.log('Build directory:', path.resolve(__dirname, 'build'));
console.log('Static directory:', path.resolve(__dirname, 'build/static'));

// Enable CORS with specific options
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Parse JSON bodies
app.use(express.json());

// Log all requests
app.use((req, res, next) => {
  console.log('Request:', req.method, req.url);
  next();
});

// Serve static files from the build directory with proper caching
app.use('/static', express.static(path.join(__dirname, 'build/static'), {
  maxAge: '1y',
  etag: true
}));

// Serve other static files from the build directory
app.use(express.static(path.join(__dirname, 'build')));

// Logging endpoint
app.post('/api/log', (req, res) => {
  console.log('Received log:', req.body);
  res.json({ success: true });
});

// Serve index.html for all other routes (for client-side routing)
app.get('*', (req, res) => {
  const indexPath = path.join(__dirname, 'build', 'index.html');
  console.log('Serving index.html from:', indexPath);
  res.sendFile(indexPath);
});

app.listen(port, '0.0.0.0', () => {
  console.log(`Server running on port ${port}`);
  console.log(`Static files being served from: ${path.resolve('build')}`);
  console.log('Logging endpoint available at http://localhost:3003/api/log');
}); 