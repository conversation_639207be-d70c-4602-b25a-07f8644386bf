# WebOTR Test App Docker Makefile
# Convenient commands for Docker development environment

.PHONY: help setup start stop restart rebuild logs status clean test test-unit test-e2e shell install

# Default target
.DEFAULT_GOAL := help

# Docker Compose file and container
COMPOSE_FILE := docker-compose.dev.yml
CONTAINER_NAME := webotter-testapp-dev

# Detect Docker Compose command
DOCKER_COMPOSE := $(shell if command -v docker-compose >/dev/null 2>&1; then echo "docker-compose"; elif docker compose version >/dev/null 2>&1; then echo "docker compose"; else echo "docker-compose"; fi)

# Colors for output
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m # No Color

help: ## Show this help message
	@echo "$(BLUE)WebOTR Test App Docker Commands$(NC)"
	@echo "=================================="
	@echo ""
	@echo "$(GREEN)Setup Commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(setup|start|stop)"
	@echo ""
	@echo "$(GREEN)Development Commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(restart|rebuild|logs|status)"
	@echo ""
	@echo "$(GREEN)Testing Commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(test|shell)"
	@echo ""
	@echo "$(GREEN)Utility Commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST) | grep -E "(clean|install)"
	@echo ""
	@echo "$(GREEN)Access URLs:$(NC)"
	@echo "  $(YELLOW)WebOTR App:$(NC)     http://webotter.docker.localhost"
	@echo "  $(YELLOW)Traefik:$(NC)        http://traefik.docker.localhost"
	@echo "  $(YELLOW)Direct:$(NC)         http://localhost:3000"

setup: ## Initial Docker environment setup
	@echo "$(BLUE)Setting up WebOTR Test App Docker environment...$(NC)"
	@./docker-setup.sh setup

start: ## Start existing Docker services
	@echo "$(BLUE)Starting Docker services...$(NC)"
	@./docker-setup.sh start

stop: ## Stop Docker services
	@echo "$(BLUE)Stopping Docker services...$(NC)"
	@./docker-setup.sh stop

restart: ## Restart Docker services
	@echo "$(BLUE)Restarting Docker services...$(NC)"
	@./docker-setup.sh restart

rebuild: ## Rebuild and restart Docker services
	@echo "$(BLUE)Rebuilding Docker services...$(NC)"
	@./docker-setup.sh rebuild

logs: ## Show Docker service logs
	@echo "$(BLUE)Showing Docker logs (Ctrl+C to exit)...$(NC)"
	@./docker-setup.sh logs

status: ## Show Docker service status
	@echo "$(BLUE)Docker service status:$(NC)"
	@./docker-setup.sh status

clean: ## Clean up Docker resources
	@echo "$(BLUE)Cleaning up Docker resources...$(NC)"
	@./docker-setup.sh clean

# Testing commands
test: test-unit ## Run all tests (unit + e2e)

test-unit: ## Run unit tests in Docker container
	@echo "$(BLUE)Running unit tests...$(NC)"
	@docker exec -it $(CONTAINER_NAME) npm test -- --watchAll=false --verbose

test-steganography: ## Run steganography tests
	@echo "$(BLUE)Running steganography tests...$(NC)"
	@docker exec -it $(CONTAINER_NAME) npm test -- --testPathPattern=steganography --watchAll=false --verbose

test-handshake: ## Run handshake tests
	@echo "$(BLUE)Running handshake tests...$(NC)"
	@docker exec -it $(CONTAINER_NAME) npm test -- --testPathPattern=handshake --watchAll=false --verbose

test-e2e: ## Run Playwright E2E tests
	@echo "$(BLUE)Running E2E tests...$(NC)"
	@docker exec -it $(CONTAINER_NAME) npx playwright test

test-e2e-ui: ## Run Playwright tests with UI
	@echo "$(BLUE)Running E2E tests with UI...$(NC)"
	@docker exec -it $(CONTAINER_NAME) npx playwright test --ui

test-coverage: ## Run tests with coverage report
	@echo "$(BLUE)Running tests with coverage...$(NC)"
	@docker exec -it $(CONTAINER_NAME) npm test -- --coverage --watchAll=false

# Development commands
shell: ## Access container shell
	@echo "$(BLUE)Accessing container shell...$(NC)"
	@docker exec -it $(CONTAINER_NAME) sh

install: ## Install new npm dependencies
	@echo "$(BLUE)Installing npm dependencies...$(NC)"
	@docker exec -it $(CONTAINER_NAME) npm install

install-package: ## Install specific npm package (usage: make install-package PACKAGE=package-name)
	@echo "$(BLUE)Installing package: $(PACKAGE)...$(NC)"
	@docker exec -it $(CONTAINER_NAME) npm install $(PACKAGE)

build: ## Build the React application
	@echo "$(BLUE)Building React application...$(NC)"
	@docker exec -it $(CONTAINER_NAME) npm run build

lint: ## Run ESLint
	@echo "$(BLUE)Running ESLint...$(NC)"
	@docker exec -it $(CONTAINER_NAME) npm run lint

lint-fix: ## Run ESLint with auto-fix
	@echo "$(BLUE)Running ESLint with auto-fix...$(NC)"
	@docker exec -it $(CONTAINER_NAME) npm run lint -- --fix

# Utility commands
network-create: ## Create Traefik network manually
	@echo "$(BLUE)Creating Traefik network...$(NC)"
	@docker network create traefik_network || echo "$(YELLOW)Network already exists$(NC)"

network-remove: ## Remove Traefik network
	@echo "$(BLUE)Removing Traefik network...$(NC)"
	@docker network rm traefik_network || echo "$(YELLOW)Network doesn't exist$(NC)"

ps: ## Show running containers
	@echo "$(BLUE)Running containers:$(NC)"
	@$(DOCKER_COMPOSE) -f $(COMPOSE_FILE) ps

top: ## Show container resource usage
	@echo "$(BLUE)Container resource usage:$(NC)"
	@docker stats $(CONTAINER_NAME) --no-stream

inspect: ## Inspect container configuration
	@echo "$(BLUE)Container configuration:$(NC)"
	@docker inspect $(CONTAINER_NAME)

# Health checks
health: ## Check service health
	@echo "$(BLUE)Checking service health...$(NC)"
	@echo "$(YELLOW)WebOTR App:$(NC)"
	@curl -s -o /dev/null -w "  Status: %{http_code}\n" http://webotter.docker.localhost || echo "  Status: Failed"
	@echo "$(YELLOW)Traefik:$(NC)"
	@curl -s -o /dev/null -w "  Status: %{http_code}\n" http://traefik.docker.localhost || echo "  Status: Failed"
	@echo "$(YELLOW)Direct Access:$(NC)"
	@curl -s -o /dev/null -w "  Status: %{http_code}\n" http://localhost:3000 || echo "  Status: Failed"

# Quick development workflow
dev: setup ## Quick development setup (alias for setup)

dev-reset: clean setup ## Reset and restart development environment

dev-test: test-unit test-e2e ## Run full test suite

# Production-like testing
prod-build: ## Build production Docker image
	@echo "$(BLUE)Building production Docker image...$(NC)"
	@docker build -t webotter-testapp:prod --target production .

prod-run: ## Run production Docker image
	@echo "$(BLUE)Running production Docker image...$(NC)"
	@docker run -d -p 8080:80 --name webotter-testapp-prod webotter-testapp:prod

prod-stop: ## Stop production container
	@echo "$(BLUE)Stopping production container...$(NC)"
	@docker stop webotter-testapp-prod && docker rm webotter-testapp-prod

# Documentation
docs: ## Open documentation
	@echo "$(BLUE)Opening Docker documentation...$(NC)"
	@cat DOCKER.md

# Environment info
info: ## Show environment information
	@echo "$(BLUE)Environment Information:$(NC)"
	@echo "$(YELLOW)Docker Version:$(NC)"
	@docker --version
	@echo "$(YELLOW)Docker Compose Version:$(NC)"
	@docker-compose --version
	@echo "$(YELLOW)Node.js Version (in container):$(NC)"
	@docker exec $(CONTAINER_NAME) node --version 2>/dev/null || echo "  Container not running"
	@echo "$(YELLOW)NPM Version (in container):$(NC)"
	@docker exec $(CONTAINER_NAME) npm --version 2>/dev/null || echo "  Container not running"
