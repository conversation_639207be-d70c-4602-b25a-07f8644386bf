#!/bin/bash

# Script to set up symlinks for WebOTR framework

echo "===== Setting up WebOTR framework symlinks ====="

# Get the current directory and the parent (main project) directory
CURRENT_DIR="$(cd "$(dirname "$0")" && pwd)"
MAIN_PROJECT_DIR="$(cd "$CURRENT_DIR/.." && pwd)"

echo "Current directory: $CURRENT_DIR"
echo "Main project directory: $MAIN_PROJECT_DIR"

# Check if the main project src directory exists
if [ ! -d "$MAIN_PROJECT_DIR/src" ]; then
    echo "Error: Main project src directory not found. Make sure you're running this script from the test-chat-sim directory."
    exit 1
fi

# Create node_modules directory if it doesn't exist
mkdir -p "$CURRENT_DIR/node_modules/webotr"

# Create src directory inside the symlink target
mkdir -p "$CURRENT_DIR/node_modules/webotr/src"

# Create symlinks to WebOTR framework components
echo "Creating symlinks to WebOTR framework..."
ln -sf "$MAIN_PROJECT_DIR/src/core" "$CURRENT_DIR/node_modules/webotr/src/core"
ln -sf "$MAIN_PROJECT_DIR/src/ui" "$CURRENT_DIR/node_modules/webotr/src/ui"
ln -sf "$MAIN_PROJECT_DIR/src/platforms" "$CURRENT_DIR/node_modules/webotr/src/platforms"
ln -sf "$MAIN_PROJECT_DIR/src/extension" "$CURRENT_DIR/node_modules/webotr/src/extension"

echo "Symlinks created. You can now import WebOTR components from the 'webotr' package:"
echo "import { OtrSession } from 'webotr/src/core/session';"

echo ""
echo "===== Setup complete =====" 