version: '3.8'

services:
  app:
    container_name: webOTteR-app
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile
    ports:
      - "3004:3003"
    volumes:
      - ..:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=3003
    command: serve -s build -l 3003
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003"]
      interval: 5s
      timeout: 5s
      retries: 3
      start_period: 5s

  test:
    container_name: webOTteR-test
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile
    depends_on:
      app:
        condition: service_healthy
    volumes:
      - ..:/app
      - /app/node_modules
      - ../test-results:/app/test-results
      - ../playwright-report:/app/playwright-report
    environment:
      - NODE_ENV=test
      - PORT=3003
    command: npx playwright test --grep "should capture app screenshot" 