FROM node:20-slim

# Install dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash -

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install && \
    npm install -g serve && \
    npm install express

# Copy the rest of the application
COPY . .

# Build the application
RUN npm run build

# Install Playwright browsers
RUN npx playwright install

# Expose the port
EXPOSE 3003

# Start the application
CMD ["serve", "-s", "build", "-l", "3003"] 