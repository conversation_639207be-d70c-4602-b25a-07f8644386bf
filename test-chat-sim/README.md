# 🔒 WebOTR Test Chat Simulator

A comprehensive test application for demonstrating and testing WebOTR (Off-the-Record) messaging functionality, including steganography, handshake protocols, and secure communication features.

## 🚀 Quick Start Options

### 🐳 Docker Development (Recommended)
```bash
# One-command setup with Traefik integration
./docker-setup.sh

# Access at: http://webotter.docker.localhost
# Traefik Dashboard: http://traefik.docker.localhost
```

### 💻 Local Development
```bash
npm install
npm start
# Access at: http://localhost:3000
```

## ✨ Features

### 💬 Chat Functionality
- **Dual Chat Interface**: <PERSON> and <PERSON> chat panels with real-time messaging
- **Network Visualization**: Simulated network traffic and message transmission
- **Message Persistence**: localStorage-based message history
- **Visual Status Indicators**: Message status (sent, in transit, delivered)
- **Dark Mode Support**: Theme persistence and seamless switching
- **Conversation Management**: Clear history and message timestamps

### 🔒 Steganography Demo (20/20 Tests Passing)
- **Interactive Cover Image Generation**: Multiple styles (noise, gradient, pattern)
- **Message Hiding/Revealing**: Complete hide/reveal workflow with visual feedback
- **Message Detection**: Hidden message detection and validation
- **Performance Testing**: Benchmarking and optimization analysis
- **Activity Logging**: Comprehensive operation tracking
- **Error Handling**: Graceful handling of edge cases

### 🤝 Handshake Demo (18/18 Tests Passing)
- **OTR Handshake Simulation**: Complete AKE (Authenticated Key Exchange) process
- **Dual Session Status**: Real-time Alice and Bob session monitoring
- **Progress Visualization**: Step-by-step handshake progress tracking
- **Performance Metrics**: Handshake timing and throughput analysis
- **Instance Tag Management**: Unique session identification
- **State Transition Monitoring**: Visual state change indicators

### 🐳 Docker Integration
- **Development Container**: Volume mounting with hot reload
- **Traefik Integration**: Reverse proxy with custom domain (webotter.docker.localhost)
- **One-Command Setup**: Automated environment configuration
- **Health Monitoring**: Container health checks and status monitoring

## Technology Stack

- React.js
- localStorage for message persistence
- React Testing Library for component tests
- Playwright for end-to-end testing

## Project Structure

```
test-chat-sim/
├── public/
│   └── index.html
├── src/
│   ├── components/
│   │   ├── ChatPanel.js
│   │   ├── ChatPanel.css
│   │   ├── NetworkPanel.js
│   │   └── NetworkPanel.css
│   ├── utils/
│   │   └── storage.js
│   ├── __tests__/
│   │   ├── App.test.js
│   │   ├── ChatPanel.test.js
│   │   ├── NetworkPanel.test.js
│   │   ├── utils.test.js
│   │   ├── storage.test.js
│   │   └── edge-cases.test.js
│   ├── App.js
│   ├── index.js
│   └── index.css
├── e2e/
│   ├── basic-chat.spec.js
│   ├── persistence.spec.js
│   ├── ui-interactions.spec.js
│   ├── error-handling.spec.js
│   ├── visual-tests.spec.js
│   ├── integration-tests.spec.js
│   ├── performance-tests.spec.js
│   ├── accessibility.spec.js
│   ├── security-tests.spec.js
│   └── concurrent-actions.spec.js
├── Dockerfile
├── docker-compose.yml
├── playwright.config.js
├── package.json
└── README.md
```

## Installation and Setup

### Local Development

1. Install dependencies:

```bash
cd test-chat-sim
npm install
```

2. Start the development server:

```bash
npm start
```

The application will be available at http://localhost:3001.

### 🐳 Docker Development Environment

#### Quick Setup
```bash
# One-command setup
./docker-setup.sh

# Or use Make commands
make setup
```

#### Manual Setup
```bash
# Install Docker Compose if needed
./install-docker-compose.sh

# Create Traefik network
docker network create traefik_network

# Start services
docker-compose -f docker-compose.dev.yml up --build -d
```

#### Access Points
- **WebOTR Test App**: http://webotter.docker.localhost
- **Traefik Dashboard**: http://traefik.docker.localhost
- **Direct Access**: http://localhost:3000

#### Docker Features
- ✅ **Volume Mounting**: Live code changes with hot reload
- ✅ **Traefik Integration**: Custom domain routing
- ✅ **Health Monitoring**: Container health checks
- ✅ **Development Tools**: Shell access and debugging
- ✅ **Test Execution**: Run tests inside containers

#### Docker Commands
```bash
# View logs
./docker-setup.sh logs

# Restart services
./docker-setup.sh restart

# Access container shell
make shell

# Run tests in container
make test-unit
make test-e2e

# Stop services
./docker-setup.sh stop
```

#### Legacy Docker (Playwright Testing)
```bash
# Original Docker setup for testing
docker-compose up --build
```

## Running Tests

### Component Tests with React Testing Library

To run the React component tests:

```bash
npm test
```

### End-to-End Tests with Playwright

First, install Playwright browsers:

```bash
npx playwright install --with-deps
```

Then, run the Playwright tests:

```bash
# Run all tests headlessly
npm run test:e2e

# Run tests with UI mode
npm run test:e2e:ui

# Run tests in debug mode
npm run test:e2e:debug

# Generate tests using Codegen
npm run test:e2e:codegen

# View test reports
npm run test:e2e:report
```

The Playwright tests cover:

1. **Basic Chat Functionality** - Sending and receiving messages between Alice and Bob
2. **Message Persistence** - Verifying that messages persist in localStorage
3. **UI Interactions** - Testing dark mode toggle, input validation, and message status
4. **Error Handling** - Testing how the app handles localStorage errors and edge cases
5. **Visual Testing** - Capturing screenshots at various stages of conversation
6. **Integration Testing** - Simulating a complete, realistic conversation

## Testing Approach

This project uses a comprehensive testing approach that covers multiple aspects of the application:

### Unit Tests

Located in `src/__tests__/`, these tests focus on individual components and utilities:

- `App.test.js` - Tests the main App component functionality
- `ChatPanel.test.js` - Tests the chat interface component
- `NetworkPanel.test.js` - Tests the network visualization component
- `utils.test.js` - Tests utility functions
- `storage.test.js` - Tests localStorage persistence functionality
- `edge-cases.test.js` - Tests handling of edge cases (empty messages, very long messages, special characters, etc.)

### End-to-End Tests

Located in `e2e/`, these tests cover the application's behavior from a user perspective:

- `basic-chat.spec.js` - Tests fundamental message sending and receiving
- `persistence.spec.js` - Tests message persistence across page reloads
- `ui-interactions.spec.js` - Tests UI elements like buttons, forms, and theme toggle
- `error-handling.spec.js` - Tests how the app handles various error conditions
- `visual-tests.spec.js` - Tests visual appearance and layout
- `integration-tests.spec.js` - Tests interactions between different components
- `performance-tests.spec.js` - Tests application performance with metrics for load time, rendering, animations, etc.
- `accessibility.spec.js` - Tests WCAG compliance and screen reader accessibility
- `security-tests.spec.js` - Tests for common security vulnerabilities like XSS
- `concurrent-actions.spec.js` - Tests behavior when multiple actions occur simultaneously

### Running Tests

To run all tests:

```bash
./run-tests.sh
```

To run a specific test category:

```bash
# Unit tests
npm test

# E2E tests with Playwright
npx playwright test

# Accessibility tests
npm run test:a11y

# Performance tests
npx playwright test e2e/performance-tests.spec.js

# Security tests
npx playwright test e2e/security-tests.spec.js
```

### Test Coverage

The project maintains test coverage targets of 80% for:
- Statements
- Branches
- Functions
- Lines

View the coverage report:

```bash
npm test -- --coverage
```

Then open `coverage/lcov-report/index.html` in your browser.

## How It Works

1. Messages sent from either Alice or Bob are immediately displayed in the sender's panel
2. Messages appear in the network panel as "TRANSMITTING"
3. After a simulated delay, messages are delivered to the recipient and appear in their panel
4. The network panel updates to show "DELIVERED" status
5. All messages are saved to localStorage for persistence between sessions
6. Dark mode preference is also stored in localStorage

## Implementation Details

- The App component manages the main state and orchestrates message flow
- The ChatPanel component is reused for both Alice and Bob
- The NetworkPanel visualizes message transmission
- LocalStorage is used for data persistence
- CSS variables enable seamless theme switching 