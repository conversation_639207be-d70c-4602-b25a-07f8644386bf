# Chat Simulator

A simple React-based chat simulator that demonstrates a conversation between <PERSON> and <PERSON>, with visual representation of network transmission.

## Features

- Left panel shows <PERSON>'s chat interface
- Right panel shows <PERSON>'s chat interface
- Bottom panel shows the simulated network traffic between <PERSON> and <PERSON>
- Messages are stored in localStorage for persistence
- Visual indication of message status (sent, in transit, delivered)
- Timestamps for messages
- Dark mode support with theme persistence
- Clear conversation history

## Technology Stack

- React.js
- localStorage for message persistence
- React Testing Library for component tests
- Playwright for end-to-end testing

## Project Structure

```
test-chat-sim/
├── public/
│   └── index.html
├── src/
│   ├── components/
│   │   ├── ChatPanel.js
│   │   ├── ChatPanel.css
│   │   ├── NetworkPanel.js
│   │   └── NetworkPanel.css
│   ├── utils/
│   │   └── storage.js
│   ├── __tests__/
│   │   ├── App.test.js
│   │   ├── ChatPanel.test.js
│   │   ├── NetworkPanel.test.js
│   │   ├── utils.test.js
│   │   ├── storage.test.js
│   │   └── edge-cases.test.js
│   ├── App.js
│   ├── index.js
│   └── index.css
├── e2e/
│   ├── basic-chat.spec.js
│   ├── persistence.spec.js
│   ├── ui-interactions.spec.js
│   ├── error-handling.spec.js
│   ├── visual-tests.spec.js
│   ├── integration-tests.spec.js
│   ├── performance-tests.spec.js
│   ├── accessibility.spec.js
│   ├── security-tests.spec.js
│   └── concurrent-actions.spec.js
├── Dockerfile
├── docker-compose.yml
├── playwright.config.js
├── package.json
└── README.md
```

## Installation and Setup

### Local Development

1. Install dependencies:

```bash
cd test-chat-sim
npm install
```

2. Start the development server:

```bash
npm start
```

The application will be available at http://localhost:3001.

### Docker Setup

You can run the application and tests in a Docker container:

```bash
cd test-chat-sim
docker-compose up --build
```

This will:
- Build the Docker image with all dependencies
- Start the React application on port 3000
- Run the Playwright tests against the running application
- Save test results and screenshots to the playwright-report directory

## Running Tests

### Component Tests with React Testing Library

To run the React component tests:

```bash
npm test
```

### End-to-End Tests with Playwright

First, install Playwright browsers:

```bash
npx playwright install --with-deps
```

Then, run the Playwright tests:

```bash
# Run all tests headlessly
npm run test:e2e

# Run tests with UI mode
npm run test:e2e:ui

# Run tests in debug mode
npm run test:e2e:debug

# Generate tests using Codegen
npm run test:e2e:codegen

# View test reports
npm run test:e2e:report
```

The Playwright tests cover:

1. **Basic Chat Functionality** - Sending and receiving messages between Alice and Bob
2. **Message Persistence** - Verifying that messages persist in localStorage
3. **UI Interactions** - Testing dark mode toggle, input validation, and message status
4. **Error Handling** - Testing how the app handles localStorage errors and edge cases
5. **Visual Testing** - Capturing screenshots at various stages of conversation
6. **Integration Testing** - Simulating a complete, realistic conversation

## Testing Approach

This project uses a comprehensive testing approach that covers multiple aspects of the application:

### Unit Tests

Located in `src/__tests__/`, these tests focus on individual components and utilities:

- `App.test.js` - Tests the main App component functionality
- `ChatPanel.test.js` - Tests the chat interface component
- `NetworkPanel.test.js` - Tests the network visualization component
- `utils.test.js` - Tests utility functions
- `storage.test.js` - Tests localStorage persistence functionality
- `edge-cases.test.js` - Tests handling of edge cases (empty messages, very long messages, special characters, etc.)

### End-to-End Tests

Located in `e2e/`, these tests cover the application's behavior from a user perspective:

- `basic-chat.spec.js` - Tests fundamental message sending and receiving
- `persistence.spec.js` - Tests message persistence across page reloads
- `ui-interactions.spec.js` - Tests UI elements like buttons, forms, and theme toggle
- `error-handling.spec.js` - Tests how the app handles various error conditions
- `visual-tests.spec.js` - Tests visual appearance and layout
- `integration-tests.spec.js` - Tests interactions between different components
- `performance-tests.spec.js` - Tests application performance with metrics for load time, rendering, animations, etc.
- `accessibility.spec.js` - Tests WCAG compliance and screen reader accessibility
- `security-tests.spec.js` - Tests for common security vulnerabilities like XSS
- `concurrent-actions.spec.js` - Tests behavior when multiple actions occur simultaneously

### Running Tests

To run all tests:

```bash
./run-tests.sh
```

To run a specific test category:

```bash
# Unit tests
npm test

# E2E tests with Playwright
npx playwright test

# Accessibility tests
npm run test:a11y

# Performance tests
npx playwright test e2e/performance-tests.spec.js

# Security tests
npx playwright test e2e/security-tests.spec.js
```

### Test Coverage

The project maintains test coverage targets of 80% for:
- Statements
- Branches
- Functions
- Lines

View the coverage report:

```bash
npm test -- --coverage
```

Then open `coverage/lcov-report/index.html` in your browser.

## How It Works

1. Messages sent from either Alice or Bob are immediately displayed in the sender's panel
2. Messages appear in the network panel as "TRANSMITTING"
3. After a simulated delay, messages are delivered to the recipient and appear in their panel
4. The network panel updates to show "DELIVERED" status
5. All messages are saved to localStorage for persistence between sessions
6. Dark mode preference is also stored in localStorage

## Implementation Details

- The App component manages the main state and orchestrates message flow
- The ChatPanel component is reused for both Alice and Bob
- The NetworkPanel visualizes message transmission
- LocalStorage is used for data persistence
- CSS variables enable seamless theme switching 