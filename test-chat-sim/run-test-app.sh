#!/bin/bash

# <PERSON><PERSON>t to run the test chat simulator app

echo "===== WebOTR Test Chat Simulator ====="
echo "Starting setup..."

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed. Please install Node.js and npm first."
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then
    echo "Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies."
        exit 1
    fi
fi

# Start the app
echo "Starting the test chat simulator..."
echo "Once started, open your browser to the URL shown (typically http://localhost:3000)"
echo "====================================="

npm start 