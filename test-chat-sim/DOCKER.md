# 🐳 WebOTR Test App Docker Development Environment

Complete Docker setup for the WebOTR Test App with Traefik integration, volume mounting, and hot reload support.

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- Port 80, 443, 3000, and 8080 available

### One-Command Setup
```bash
./docker-setup.sh
```

This will:
- ✅ Create the Traefik network
- ✅ Build the development container
- ✅ Start all services
- ✅ Set up volume mounting for live development
- ✅ Configure Traefik reverse proxy

### Access Your Application
- **WebOTR Test App**: http://webotter.docker.localhost
- **Traefik Dashboard**: http://traefik.docker.localhost
- **Direct Access**: http://localhost:3000

## 📋 Available Commands

```bash
# Initial setup (run once)
./docker-setup.sh setup

# Start existing services
./docker-setup.sh start

# Stop services
./docker-setup.sh stop

# Restart services
./docker-setup.sh restart

# Rebuild and restart
./docker-setup.sh rebuild

# View logs
./docker-setup.sh logs

# Check service status
./docker-setup.sh status

# Clean up everything
./docker-setup.sh clean

# Show help
./docker-setup.sh help
```

## 🏗️ Architecture

### Services

#### WebOTR Test App (`webotter-testapp-dev`)
- **Image**: Custom Node.js 18 Alpine
- **Port**: 3000
- **Domain**: webotter.docker.localhost
- **Features**:
  - Volume mounting for live development
  - Hot reload enabled
  - Source maps enabled
  - File watching with polling

#### Traefik (`webotter-traefik`)
- **Image**: traefik:v2.10
- **Ports**: 80, 443, 8080
- **Domain**: traefik.docker.localhost
- **Features**:
  - Automatic service discovery
  - Load balancing
  - SSL termination (configurable)

### Networks
- **traefik_network**: External network for Traefik routing
- **webotter_internal**: Internal network for service communication

### Volumes
- **Source Code**: Live mounted from host (`./` → `/app`)
- **Node Modules**: Anonymous volume (preserved across restarts)
- **Cache**: Persistent cache volume
- **Traefik Data**: Persistent Traefik configuration

## 🔧 Development Features

### Hot Reload
- ✅ File changes automatically trigger rebuilds
- ✅ Browser auto-refresh on changes
- ✅ Fast Refresh for React components
- ✅ Source maps for debugging

### Volume Mounting
```yaml
volumes:
  - .:/app:cached                    # Source code
  - /app/node_modules               # Preserve node_modules
  - webotter-dev-cache:/app/.cache  # Persistent cache
```

### Environment Variables
```bash
NODE_ENV=development
CHOKIDAR_USEPOLLING=true
WATCHPACK_POLLING=true
FAST_REFRESH=true
GENERATE_SOURCEMAP=true
```

## 🌐 Traefik Configuration

### Routing Rules
```yaml
labels:
  - "traefik.http.routers.webotter-testapp.rule=Host(`webotter.docker.localhost`)"
  - "traefik.http.services.webotter-testapp.loadbalancer.server.port=3000"
```

### HTTPS Support (Optional)
```yaml
labels:
  - "traefik.http.routers.webotter-testapp-secure.tls=true"
  - "traefik.http.routers.webotter-testapp-secure.entrypoints=websecure"
```

## 🧪 Testing in Docker

### Unit Tests
```bash
# Run Jest tests
docker exec -it webotter-testapp-dev npm test

# Run specific test file
docker exec -it webotter-testapp-dev npm test -- --testPathPattern=steganography

# Run with coverage
docker exec -it webotter-testapp-dev npm test -- --coverage
```

### E2E Tests
```bash
# Run Playwright tests
docker exec -it webotter-testapp-dev npx playwright test

# Run specific test suite
docker exec -it webotter-testapp-dev npx playwright test handshake-ux.spec.js

# Run with UI mode
docker exec -it webotter-testapp-dev npx playwright test --ui
```

### Interactive Shell
```bash
# Access container shell
docker exec -it webotter-testapp-dev sh

# Install new dependencies
docker exec -it webotter-testapp-dev npm install package-name

# Run custom commands
docker exec -it webotter-testapp-dev npm run build
```

## 📊 Monitoring and Debugging

### View Logs
```bash
# All services
./docker-setup.sh logs

# Specific service
docker-compose -f docker-compose.dev.yml logs -f webotter-testapp

# Traefik logs
docker-compose -f docker-compose.dev.yml logs -f traefik
```

### Health Checks
```bash
# Check service health
docker-compose -f docker-compose.dev.yml ps

# Manual health check
curl -f http://webotter.docker.localhost
curl -f http://traefik.docker.localhost/ping
```

### Performance Monitoring
- **Traefik Dashboard**: http://traefik.docker.localhost
- **Container Stats**: `docker stats webotter-testapp-dev`
- **Resource Usage**: `docker exec webotter-testapp-dev top`

## 🔒 Security Considerations

### Development Security
- Container runs as non-root user (`appuser`)
- Limited container capabilities
- Internal network isolation
- Read-only Docker socket mount

### Production Considerations
- Use multi-stage builds for production
- Implement proper SSL certificates
- Configure security headers
- Use secrets management

## 🛠️ Customization

### Environment Variables
Edit `.env.docker` to customize:
```bash
# Custom domain
WEBOTTER_DOMAIN=myapp.docker.localhost

# Custom port
PORT=3001

# API configuration
REACT_APP_API_URL=http://api.docker.localhost
```

### Traefik Configuration
Modify `docker-compose.dev.yml` labels:
```yaml
labels:
  - "traefik.http.routers.webotter-testapp.rule=Host(`custom.domain.localhost`)"
  - "traefik.http.routers.webotter-testapp.middlewares=custom-middleware"
```

### Additional Services
Add to `docker-compose.dev.yml`:
```yaml
services:
  database:
    image: postgres:15
    environment:
      POSTGRES_DB: webotter
    networks:
      - webotter_internal
```

## 🐛 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Check what's using the port
lsof -i :80
lsof -i :3000

# Stop conflicting services
sudo systemctl stop apache2
sudo systemctl stop nginx
```

#### Traefik Network Not Found
```bash
# Create the network manually
docker network create traefik_network
```

#### Permission Issues
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x docker-setup.sh
```

#### Container Won't Start
```bash
# Check logs
docker-compose -f docker-compose.dev.yml logs webotter-testapp

# Rebuild container
./docker-setup.sh rebuild
```

#### Hot Reload Not Working
```bash
# Verify polling is enabled
docker exec webotter-testapp-dev env | grep CHOKIDAR_USEPOLLING

# Restart with rebuild
./docker-setup.sh rebuild
```

### Reset Everything
```bash
# Complete cleanup
./docker-setup.sh clean
docker system prune -a
docker volume prune

# Fresh setup
./docker-setup.sh setup
```

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Traefik Documentation](https://doc.traefik.io/traefik/)
- [React Development with Docker](https://create-react-app.dev/docs/deployment/#docker)
- [WebOTR Project Documentation](../README.md)

## 🤝 Contributing

When working with the Docker environment:

1. **Make changes** to source code (auto-reloads)
2. **Test changes** using the containerized environment
3. **Run tests** inside the container
4. **Commit changes** (Docker files are included)

The Docker setup preserves your development workflow while providing a consistent, isolated environment for the WebOTR Test App.
