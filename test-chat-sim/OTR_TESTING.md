# OTR Testing in Chat Simulator

This document explains how to use the Off-The-Record (OTR) messaging functionality in the Chat Simulator application.

## Overview

The Chat Simulator now supports OTR encryption between <PERSON> and <PERSON>, allowing you to test the encryption functionality in a realistic chat environment. When enabled, messages will be encrypted using the OTR protocol before being transmitted, and decrypted by the recipient.

## How to Use

1. Start the chat simulator application by running:
   ```
   cd test-chat-sim
   ./run-test-app.sh
   ```
   Or manually:
   ```
   cd test-chat-sim
   npm install
   npm start
   ```

2. Open your browser to the displayed URL (usually http://localhost:3000)

3. Enable OTR encryption by clicking the "🔓 OTR Disabled" button at the top of the screen. When enabled, it will change to "🔒 OTR Enabled".

4. Send messages between <PERSON> and <PERSON>. When OTR is enabled:
   - Messages are encrypted before transmission
   - Encrypted messages show a 🔒 icon in the chat
   - You can see the encrypted traffic in the Network View at the bottom

5. You can disable O<PERSON> at any time by clicking the "🔒 OTR Enabled" button again.

## Features

- **Automatic Session Setup**: When <PERSON>TR is enabled, an encrypted session is automatically established between <PERSON> and <PERSON>
- **Message Encryption**: Text messages are encrypted using the OTR protocol
- **Visual Indicators**: Encrypted messages display a lock icon
- **Network Visualization**: The Network View shows the encrypted messages being transmitted

## Limitations

- **Image and GIF messages**: Currently, only text messages are encrypted. Image and GIF messages are still sent unencrypted.
- **Group Conversations**: The current implementation only supports one-to-one conversations between Alice and Bob.

## Troubleshooting

If you encounter issues with the OTR functionality:

1. Check the browser console for error messages
2. Try clearing all messages and restarting the OTR session
3. Refresh the page to reset the application state

### Common Issues

- **Constructor Error**: If you see an error about OtrSession not being a constructor, the mock implementation might not be loaded correctly. Try refreshing the page.
- **Session Initialization Failed**: If OTR cannot be enabled, the sessions might not have initialized properly. Check the console for error details and refresh the page.
- **Message Not Encrypted**: Only text messages are encrypted. Images, GIFs, and other media are sent unencrypted.

## Implementation Details

The OTR functionality is implemented using a mock of the WebOTR library. The test application creates separate OTR sessions for Alice and Bob, and hooks into the send button functionality to encrypt messages before sending them.

In a production environment, this mock would be replaced with the actual WebOTR library implementation. 