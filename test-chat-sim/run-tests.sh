#!/bin/bash

# Exit on error
set -e

echo "Starting chat simulator test environment..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to run tests with nice formatting
run_test_category() {
  local test_type=$1
  local test_pattern=$2
  
  echo -e "${YELLOW}Running $test_type tests...${NC}"
  
  # Run the specific test category
  if [ -z "$test_pattern" ]; then
    npx playwright test
    TEST_EXIT_CODE=$?
  else
    npx playwright test $test_pattern
    TEST_EXIT_CODE=$?
  fi
  
  if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✓ $test_type tests passed${NC}"
  else
    echo -e "${RED}✗ $test_type tests failed${NC}"
    OVERALL_EXIT_CODE=1
  fi
  
  return $TEST_EXIT_CODE
}

# Check if running in Docker
if [ -f "/.dockerenv" ]; then
  echo "Running in Docker container"
  # Start app and run all tests
  npm start & 
  
  # Give the app some time to start
  echo "Waiting for app to start..."
  sleep 5
  
  # Run all tests
  run_test_category "All" ""
  EXIT_CODE=$?
  
  # Generate report
  npx playwright show-report
  
  exit $EXIT_CODE
else
  echo "Running in local environment"
  
  # Make sure we have dependencies installed
  if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
  fi
  
  # Build the app
  echo "Building React app..."
  npm run build
  
  # Start the app in background
  echo "Starting React app..."
  npm start &
  APP_PID=$!
  
  # Give the app some time to start
  echo "Waiting for app to start..."
  sleep 5
  
  # Initialize overall exit code
  OVERALL_EXIT_CODE=0
  
  # Run each test category separately
  run_test_category "Unit" "src/__tests__"
  run_test_category "Basic E2E" "e2e/basic-chat.spec.js e2e/persistence.spec.js"
  run_test_category "UI Interactions" "e2e/ui-interactions.spec.js"
  run_test_category "Error Handling" "e2e/error-handling.spec.js"
  run_test_category "Performance" "e2e/performance-tests.spec.js"
  run_test_category "Accessibility" "e2e/accessibility.spec.js"
  run_test_category "Security" "e2e/security-tests.spec.js"
  run_test_category "Visual" "e2e/visual-tests.spec.js"
  run_test_category "Concurrent Actions" "e2e/concurrent-actions.spec.js"
  run_test_category "Integration" "e2e/integration-tests.spec.js"
  
  # Generate comprehensive test report
  echo "Generating test report..."
  npx playwright show-report
  
  # Calculate test coverage
  echo "Calculating test coverage..."
  npm test -- --coverage --watchAll=false
  
  # Stop the app
  echo "Stopping React app..."
  kill $APP_PID
  
  # Generate HTML coverage report and open it
  echo -e "${YELLOW}Test coverage report generated${NC}"
  echo "Open coverage/lcov-report/index.html to view full coverage report"
  
  echo -e "${YELLOW}All tests completed with overall exit code: ${OVERALL_EXIT_CODE}${NC}"
  exit $OVERALL_EXIT_CODE
fi

# Script to run tests for the WebOTR framework

echo "===== WebOTR Framework Tests ====="

# Ensure we're in the test-chat-sim directory
cd "$(dirname "$0")"

# Check for required dependencies
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not installed. Please install Node.js and npm first."
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then
    echo "Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies."
        exit 1
    fi
fi

# Run the tests
echo "Running OTR framework tests..."

# First run the unit tests
echo "===== OTR Protocol Tests ====="
npx jest src/__tests__/OtrProtocol.test.js --silent

echo ""
echo "===== OTR Cryptography Tests ====="
npx jest src/__tests__/OtrCrypto.test.js --silent

echo ""
echo "===== OTR Negotiation Tests ====="
npx jest src/__tests__/OtrNegotiation.test.js --silent

echo ""
echo "===== All Tests ====="
npm test

echo ""
echo "Testing complete." 