#!/bin/bash

# Docker Compose Installation Script for WebOTR Test App
# Installs Docker Compose if not available

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker Compose is available
check_docker_compose() {
    if command -v docker-compose >/dev/null 2>&1; then
        print_success "docker-compose is already installed"
        docker-compose --version
        return 0
    elif docker compose version >/dev/null 2>&1; then
        print_success "docker compose (V2) is already available"
        docker compose version
        return 0
    else
        return 1
    fi
}

# Install Docker Compose V2 (recommended)
install_docker_compose_v2() {
    print_status "Installing Docker Compose V2..."
    
    # Check if Docker Desktop is installed (includes Compose V2)
    if docker info 2>/dev/null | grep -q "Docker Desktop"; then
        print_warning "Docker Desktop detected. Compose V2 should be included."
        print_status "Try running: docker compose version"
        return 0
    fi
    
    # For Linux systems, install Compose V2 plugin
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_status "Installing Docker Compose V2 plugin for Linux..."
        
        # Create plugin directory
        mkdir -p ~/.docker/cli-plugins/
        
        # Download latest Compose V2
        COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
        print_status "Downloading Docker Compose $COMPOSE_VERSION..."
        
        curl -SL "https://github.com/docker/compose/releases/download/$COMPOSE_VERSION/docker-compose-linux-x86_64" \
            -o ~/.docker/cli-plugins/docker-compose
        
        # Make executable
        chmod +x ~/.docker/cli-plugins/docker-compose
        
        print_success "Docker Compose V2 installed successfully"
        docker compose version
        
    else
        print_error "Automatic installation not supported for this OS: $OSTYPE"
        print_status "Please install Docker Compose manually:"
        print_status "https://docs.docker.com/compose/install/"
        return 1
    fi
}

# Install Docker Compose V1 (fallback)
install_docker_compose_v1() {
    print_status "Installing Docker Compose V1 as fallback..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Download and install Compose V1
        COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
        print_status "Downloading Docker Compose V1 $COMPOSE_VERSION..."
        
        sudo curl -L "https://github.com/docker/compose/releases/download/$COMPOSE_VERSION/docker-compose-$(uname -s)-$(uname -m)" \
            -o /usr/local/bin/docker-compose
        
        sudo chmod +x /usr/local/bin/docker-compose
        
        print_success "Docker Compose V1 installed successfully"
        docker-compose --version
        
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS - recommend using Homebrew
        if command -v brew >/dev/null 2>&1; then
            print_status "Installing Docker Compose via Homebrew..."
            brew install docker-compose
            print_success "Docker Compose installed via Homebrew"
        else
            print_error "Homebrew not found. Please install Docker Compose manually:"
            print_status "https://docs.docker.com/compose/install/"
            return 1
        fi
    else
        print_error "Automatic installation not supported for this OS: $OSTYPE"
        print_status "Please install Docker Compose manually:"
        print_status "https://docs.docker.com/compose/install/"
        return 1
    fi
}

# Main installation function
main() {
    echo "🐳 Docker Compose Installation for WebOTR Test App"
    echo "================================================="
    echo
    
    # Check if Docker is installed
    if ! command -v docker >/dev/null 2>&1; then
        print_error "Docker is not installed. Please install Docker first:"
        print_status "https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    print_success "Docker is installed: $(docker --version)"
    echo
    
    # Check if Docker Compose is already available
    if check_docker_compose; then
        echo
        print_success "Docker Compose is ready to use!"
        print_status "You can now run: ./docker-setup.sh"
        exit 0
    fi
    
    echo
    print_status "Docker Compose not found. Installing..."
    
    # Try to install Compose V2 first
    if install_docker_compose_v2; then
        echo
        print_success "Installation complete!"
        print_status "You can now run: ./docker-setup.sh"
    else
        print_warning "Compose V2 installation failed. Trying V1..."
        if install_docker_compose_v1; then
            echo
            print_success "Installation complete!"
            print_status "You can now run: ./docker-setup.sh"
        else
            echo
            print_error "Automatic installation failed."
            print_status "Please install Docker Compose manually:"
            print_status "https://docs.docker.com/compose/install/"
            exit 1
        fi
    fi
}

# Run main function
main "$@"
