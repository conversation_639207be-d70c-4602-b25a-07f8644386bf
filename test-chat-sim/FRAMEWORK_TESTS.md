# WebOTR Framework Tests

This document explains the tests implemented to validate the WebOTR framework's functionality.

## Overview

We've implemented a comprehensive test suite to validate the core functionality of the WebOTR framework. These tests are designed to ensure that the framework meets the requirements of the OTR protocol and can be reliably integrated into chat applications.

## Important Note on Test Environment

The tests in this directory are designed to test the WebOTR framework directly. However, Create React App (CRA) has a limitation that doesn't allow importing files from outside the `src/` directory. This means these tests cannot be run directly using the normal CRA test environment.

**To run these tests properly:**

1. They should be moved to the main WebOTR project directory (not inside the test-chat-sim app)
2. Run them using a standard Node.js test environment like <PERSON><PERSON> configured at the project root
3. Alternatively, they can be run using a testing setup that allows imports outside of the src directory

## Test Categories

### 1. Protocol Message Tests

Tests in `OtrProtocol.test.js` validate:
- Proper recognition and handling of OTR protocol messages
- Full OTR handshake between two participants
- Encrypted message exchange
- Session termination

### 2. Cryptographic Operation Tests

Tests in `OtrCrypto.test.js` validate:
- Key generation (DSA and DH keys)
- Instance tag generation
- Key derivation from shared secrets
- Message encryption and decryption

### 3. Protocol Negotiation Tests

Tests in `OtrNegotiation.test.js` validate:
- Complete negotiation sequence between participants
- State transitions during negotiation
- Version negotiation between different protocol versions
- End-to-end message exchange

## Running the Tests in the Main Project

To run these tests properly, they should be moved to the main WebOTR project directory:

```
# Copy the tests to the main project's test directory
cp test-chat-sim/src/__tests__/Otr*.test.js tests/core/

# Run them from the main project directory
cd ../
npm test tests/core/Otr*.test.js
```

## Demo App Integration

For the test chat app, we use a mock implementation of the OtrSession class (`utils/otrMock.js`) instead of the actual WebOTR implementation. This approach allows us to demonstrate the UI integration without running into CRA's import restrictions.

## Implementation Details

These tests are designed to validate the WebOTR framework independently of any specific chat application. They use a test harness that simulates message exchange between two OTR participants (typically named Alice and Bob).

### Test Structure

Each test follows a similar pattern:
1. Initialize OTR sessions for both participants
2. Perform the operation being tested
3. Verify the expected outcomes

### Test Coverage

These tests aim to provide coverage for:
- Happy path scenarios (normal operation)
- Edge cases (version differences, etc.)
- Proper state transitions
- Message format validation

## Future Enhancements

Future test enhancements may include:
- Performance testing
- Security validation tests
- Fuzzing tests for protocol robustness
- Cross-platform compatibility tests
- A custom webpack configuration that allows imports outside src/

## Relationship to the Test Chat App

While the test chat application demonstrates integration of the WebOTR framework into a chat UI, these framework tests validate the core functionality at a lower level, ensuring that the underlying protocol implementation is correct regardless of the UI integration approach. 