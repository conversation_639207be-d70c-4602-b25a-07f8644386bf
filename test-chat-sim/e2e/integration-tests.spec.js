const { test, expect } = require('@playwright/test');

test.describe('Comprehensive Conversation Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
    await page.reload();
  });

  test('simulates a full conversation between <PERSON> and <PERSON>', async ({ page }) => {
    await page.goto('/');
    
    // Define a conversation script
    const conversation = [
      { sender: 'alice', message: 'Hey <PERSON>, how are you today?' },
      { sender: 'bob', message: 'Hi <PERSON>! I\'m doing well, thanks for asking. How about you?' },
      { sender: 'alice', message: 'I\'m good! I was wondering if you\'d like to discuss that new security project.' },
      { sender: 'bob', message: 'Absolutely! What did you have in mind?' },
      { sender: 'alice', message: 'I think we should start by implementing end-to-end encryption for our messaging system.' },
      { sender: 'bob', message: 'That makes sense. Have you thought about which protocols we should use?' },
      { sender: 'alice', message: 'I was thinking of using OTR (Off-the-Record) as our base protocol.' },
      { sender: 'bob', message: 'Great choice! It provides perfect forward secrecy and deniability.' },
      { sender: 'alice', message: 'Exactly! Plus it\'s well-tested and has solid implementations we can reference.' },
      { sender: 'bob', message: 'Should we also look into incorporating Signal Protocol features for newer capabilities?' },
      { sender: 'alice', message: 'That\'s a good idea. We could create a hybrid approach that leverages the best of both worlds.' },
      { sender: 'bob', message: 'I\'ll start researching the technical requirements and create a proposal document.' },
      { sender: 'alice', message: 'Perfect! I\'ll focus on how we integrate this with our existing systems without disrupting users.' },
      { sender: 'bob', message: 'Sounds like a plan. When should we check in again to review our findings?' },
      { sender: 'alice', message: 'How about next Monday at 10am?' },
      { sender: 'bob', message: 'Works for me. I\'ll send a calendar invite to confirm.' },
      { sender: 'alice', message: 'Great! Looking forward to it.' },
      { sender: 'bob', message: 'Me too! This project is going to significantly improve our security posture.' },
      { sender: 'alice', message: 'Absolutely. Talk to you Monday!' },
      { sender: 'bob', message: 'See you then, have a great weekend!' },
    ];
    
    // Helper function to send a message and wait for delivery
    const sendMessageAndWaitForDelivery = async (sender, messageText) => {
      // Select sender's input and button
      const inputSelector = sender === 'alice' 
        ? '.chat-panel-alice .message-input-form input' 
        : '.chat-panel-bob .message-input-form input';
      
      const buttonSelector = sender === 'alice'
        ? '.chat-panel-alice .message-input-form button'
        : '.chat-panel-bob .message-input-form button';
      
      // Send message
      await page.locator(inputSelector).fill(messageText);
      await page.locator(buttonSelector).click();
      
      // Determine recipient for checking delivery
      const recipient = sender === 'alice' ? 'bob' : 'alice';
      const recipientPanelSelector = recipient === 'alice' 
        ? '.chat-panel-alice'
        : '.chat-panel-bob';
      
      // Wait for message to be delivered to recipient
      await expect(page.locator(`${recipientPanelSelector} .message-content`).filter({ hasText: messageText })).toBeVisible({ timeout: 3000 });
    };
    
    // Simulate the conversation
    for (const [index, { sender, message }] of conversation.entries()) {
      // Add delay between messages for a more realistic conversation flow
      if (index > 0) {
        await page.waitForTimeout(500);
      }
      
      await sendMessageAndWaitForDelivery(sender, message);
    }
    
    // Verify all messages are present in both panels
    for (const { message } of conversation) {
      // Each message should be visible somewhere
      await expect(page.locator('.message-content').filter({ hasText: message })).toBeVisible();
    }
    
    // Verify message count in network panel
    await expect(page.locator('.network-message')).toHaveCount(conversation.length);
    
    // Verify messages persist after page reload
    await page.reload();
    
    // After reload, verify a sample of messages from the beginning, middle, and end
    await expect(page.locator('.message-content').filter({ hasText: conversation[0].message })).toBeVisible();
    await expect(page.locator('.message-content').filter({ hasText: conversation[Math.floor(conversation.length / 2)].message })).toBeVisible();
    await expect(page.locator('.message-content').filter({ hasText: conversation[conversation.length - 1].message })).toBeVisible();
  });
}); 