const { test, expect } = require('@playwright/test');

test.describe('Security Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // Clear localStorage to start with a clean slate
    await page.evaluate(() => localStorage.clear());
    await page.reload();
  });

  test('sanitizes user input to prevent XSS', async ({ page }) => {
    // Test with various XSS payloads
    const xssPayloads = [
      '<script>alert("XSS")</script>',
      '<img src="x" onerror="alert(1)">',
      '<svg/onload=alert("XSS")>',
      'javascript:alert("XSS")',
      '<a href="javascript:alert(\'XSS\')">Click me</a>',
      '"><script>alert("XSS")</script>'
    ];

    for (const payload of xssPayloads) {
      // Input the XSS payload in Alice's chat input
      await page.locator('.chat-panel-alice .message-input-form input').fill(payload);
      await page.locator('.chat-panel-alice .message-input-form button').click();
      
      // Wait for the message to appear in the chat
      await page.waitForSelector(`.message-content:has-text("${payload.replace(/"/g, '\\"')}")`);
      
      // Verify that the message is treated as plain text, not executed as HTML/JavaScript
      const isExecuted = await page.evaluate((payload) => {
        // Check if the script was executed by looking for script tags in the DOM
        const scriptAdded = document.querySelector('script[src="x"]') !== null;
        
        // Check if the content is rendered as text
        const messageElement = Array.from(document.querySelectorAll('.message-content'))
          .find(el => el.textContent.includes(payload));
        
        // The message should contain the payload as text, not parsed HTML
        const contentIsText = messageElement && 
          messageElement.innerHTML !== messageElement.textContent;
        
        return scriptAdded || !contentIsText;
      }, payload);
      
      expect(isExecuted).toBeFalsy();
    }
  });

  test('prevents unauthorized storage access', async ({ page }) => {
    // Set a test value in localStorage
    await page.evaluate(() => {
      localStorage.setItem('sensitiveData', 'private-information');
    });
    
    // Send a message to verify normal functionality
    await page.locator('.chat-panel-alice .message-input-form input').fill('Test message');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Check that localStorage only contains expected items
    // (This verifies that the app doesn't expose sensitive data to the global scope)
    const storageKeys = await page.evaluate(() => {
      return Object.keys(localStorage);
    });
    
    // The app should store messages, but not expose the test sensitiveData key
    expect(storageKeys).toContain('chat_messages');
    
    // Verify application correctly handles localStorage (no prototype pollution)
    const prototypeAttack = await page.evaluate(() => {
      try {
        // Attempt prototype pollution
        const attackObject = JSON.stringify({
          "__proto__": {
            "polluted": true
          }
        });
        
        localStorage.setItem('attackObject', attackObject);
        const loadedObject = JSON.parse(localStorage.getItem('attackObject'));
        
        // Check if pollution worked (it shouldn't)
        return {}.polluted === true;
      } catch (error) {
        return false;
      }
    });
    
    expect(prototypeAttack).toBeFalsy();
  });

  test('validates message data structure', async ({ page }) => {
    // Inject malformed messages into localStorage
    await page.evaluate(() => {
      const malformedMessages = [
        // Missing required fields
        {
          id: 'bad1',
          // Missing sender
          recipient: 'bob',
          content: 'Malformed message 1',
          timestamp: new Date().toISOString()
        },
        // Incorrect data types
        {
          id: 'bad2',
          sender: 123, // Should be a string
          recipient: 'alice',
          content: 'Malformed message 2',
          timestamp: new Date().toISOString()
        },
        // Extra unexpected properties
        {
          id: 'bad3',
          sender: 'alice',
          recipient: 'bob',
          content: 'Malformed message 3',
          timestamp: new Date().toISOString(),
          maliciousProperty: 'attempt-to-break'
        }
      ];
      
      localStorage.setItem('chat_messages', JSON.stringify(malformedMessages));
    });
    
    // Reload the page to load the malformed messages
    await page.reload();
    
    // The application should handle the malformed data gracefully (not crash)
    // Check that the app still functions after loading potentially bad data
    await page.locator('.chat-panel-alice .message-input-form input').fill('Test after malformed data');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Verify the new message appears
    await expect(page.locator('.message-content').filter({ hasText: 'Test after malformed data' })).toBeVisible();
  });

  test('handles large message payloads without crashing', async ({ page }) => {
    // Create very large message to test buffer overflow protection
    const largeMessage = 'A'.repeat(50000); // 50KB message
    
    // Try to send the large message
    await page.locator('.chat-panel-alice .message-input-form input').fill(largeMessage);
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Check that the application either:
    // 1. Truncates the message appropriately, or
    // 2. Handles it correctly without crashing
    
    // Wait a moment to see if anything happens (crash, error, etc.)
    await page.waitForTimeout(1000);
    
    // Verify the app is still functional by testing basic interactions
    await page.locator('.chat-panel-bob .message-input-form input').fill('Test after large message');
    await page.locator('.chat-panel-bob .message-input-form button').click();
    
    // Verify new message appears
    await expect(page.locator('.message-content').filter({ hasText: 'Test after large message' })).toBeVisible();
  });

  test('prevents clickjacking attacks', async ({ page }) => {
    // Get the current page's Content-Security-Policy headers
    const response = await page.goto('/');
    const cspHeader = response.headers()['content-security-policy'] || '';
    
    // Check if the CSP headers include frame-ancestors directive (to prevent clickjacking)
    const hasFrameAncestorsDirective = cspHeader.includes('frame-ancestors');
    
    // Check if X-Frame-Options header is set (alternative to frame-ancestors)
    const xFrameOptionsHeader = response.headers()['x-frame-options'] || '';
    const hasXFrameOptions = xFrameOptionsHeader.includes('DENY') || xFrameOptionsHeader.includes('SAMEORIGIN');
    
    // Application should implement at least one protection against clickjacking
    expect(hasFrameAncestorsDirective || hasXFrameOptions).toBeTruthy();
  });

  test('validates input length restrictions', async ({ page }) => {
    // Test extremely long input to ensure app has proper length validation
    const veryLongInput = 'A'.repeat(10000);
    
    // Type the very long message
    await page.locator('.chat-panel-alice .message-input-form input').fill(veryLongInput);
    
    // Get the actual value in the input field after typing
    const actualInputValue = await page.locator('.chat-panel-alice .message-input-form input').inputValue();
    
    // Check if input is either:
    // 1. Truncated to a reasonable length
    // 2. Handled properly with validation
    
    // Input should either be truncated or validation message shown
    if (actualInputValue.length < veryLongInput.length) {
      // Input was truncated, that's fine
      expect(actualInputValue.length).toBeLessThan(veryLongInput.length);
    } else {
      // Input wasn't truncated, so check if validation prevents sending
      // Try to send the message
      const sendButton = page.locator('.chat-panel-alice .message-input-form button');
      
      // Check if send button is disabled or if validation message appears
      const isDisabled = await sendButton.isDisabled();
      
      if (!isDisabled) {
        // If not disabled, click and check for validation message
        await sendButton.click();
        
        // Look for validation message or error indicator
        const hasValidationMessage = await page.locator('.error-message, [aria-invalid="true"]').count() > 0;
        expect(hasValidationMessage).toBeTruthy();
      } else {
        // Button is disabled, which is a valid validation approach
        expect(isDisabled).toBeTruthy();
      }
    }
  });
}); 