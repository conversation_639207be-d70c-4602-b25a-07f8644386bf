const { test, expect } = require('@playwright/test');

test.describe('Network Message Popout', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('clicking on network message shows popout with details', async ({ page }) => {
    // Send a message to generate network traffic
    await page.locator('.chat-panel-alice .message-input-form input').fill('Test message for network details');
    await page.locator('.chat-panel-alice .message-input-form button[type="submit"]').click();
    
    // Wait for the message to be delivered
    await page.waitForSelector('.message-status:has-text("Delivered")');
    
    // Wait for network message to appear
    await page.waitForSelector('.network-message');
    
    // Click on the network message
    await page.locator('.network-message').first().click();
    
    // Check if popout appears
    const popout = page.locator('.network-message-popout');
    await expect(popout).toBeVisible();
    
    // Verify popout title
    await expect(popout.locator('.popout-header h4')).toContainText('Message Transmission Details');
    
    // Verify message content is displayed
    await expect(popout.locator('.popout-text-content')).toContainText('Test message for network details');
    
    // Verify transmission info sections
    await expect(popout.locator('.popout-section h5').first()).toContainText('Transmission Info');
    
    // Verify sender information
    await expect(popout.locator('.popout-grid').first()).toContainText('ALICE → BOB');
    
    // Close popout by clicking the close button
    await popout.locator('.popout-close').click();
    
    // Verify popout is closed
    await expect(popout).not.toBeVisible();
  });

  test('popout shows image when clicking on image message in network panel', async ({ page }) => {
    // Create a simple base64 image and send it
    await page.evaluate(() => {
      // Simple 1x1 pixel transparent GIF in base64
      const imageData = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
      
      // Create message object
      const messageId = `img_${Date.now()}`;
      const message = {
        id: messageId,
        sender: 'alice',
        recipient: 'bob',
        content: imageData,
        timestamp: new Date().toISOString(),
        status: 'delivered',
        type: 'image',
        networkTimestamp: new Date().toISOString(),
        deliveredTimestamp: new Date().toISOString()
      };
      
      // Add to messages in localStorage
      const messages = [];
      messages.push(message);
      localStorage.setItem('chat_simulator_messages', JSON.stringify(messages));
      
      // Add to network messages (for display purposes)
      const networkMessages = [];
      networkMessages.push({...message});
      
      // Refresh to show the message
      window.location.reload();
    });
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Wait for network message to appear
    await page.waitForSelector('.network-message');
    
    // Click on the network message
    await page.locator('.network-message').first().click();
    
    // Check if popout appears
    const popout = page.locator('.network-message-popout');
    await expect(popout).toBeVisible();
    
    // Verify image is displayed
    await expect(popout.locator('.popout-image')).toBeVisible();
    
    // Verify image details are shown
    await expect(popout.locator('.popout-message-metadata')).toContainText('Format: gif');
  });

  test('clicking outside popout closes it', async ({ page }) => {
    // Send a message to generate network traffic
    await page.locator('.chat-panel-alice .message-input-form input').fill('Test for clicking outside');
    await page.locator('.chat-panel-alice .message-input-form button[type="submit"]').click();
    
    // Wait for the message to be delivered
    await page.waitForSelector('.message-status:has-text("Delivered")');
    
    // Click on the network message to open popout
    await page.locator('.network-message').first().click();
    
    // Check if popout appears
    const popout = page.locator('.network-message-popout');
    await expect(popout).toBeVisible();
    
    // Click outside the popout (on the chat header)
    await page.locator('.chat-header').first().click();
    
    // Verify popout is closed
    await expect(popout).not.toBeVisible();
  });
}); 