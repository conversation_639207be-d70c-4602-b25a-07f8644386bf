const { test, expect } = require('@playwright/test');
const AxeBuilder = require('@axe-core/playwright').default;

test.describe('Accessibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // Clear localStorage to start with a clean slate
    await page.evaluate(() => localStorage.clear());
    await page.reload();
  });

  test('should pass axe accessibility tests', async ({ page }) => {
    // Run axe accessibility analysis with all rules
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21a', 'wcag21aa'])
      .analyze();
    
    // Assert no violations are found or log them for debugging
    if (accessibilityScanResults.violations.length > 0) {
      console.log('Accessibility violations found:');
      console.log(JSON.stringify(accessibilityScanResults.violations, null, 2));
    }
    
    // We expect no violations, but if there are any, they should be reviewed
    // Adjust this expectation based on your specific requirements
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('chat panels are keyboard navigable', async ({ page }) => {
    // Start by focusing on the page
    await page.keyboard.press('Tab');
    
    // Check if theme toggle is focused first (should be the first interactive element)
    const focusedElementText = await page.evaluate(() => document.activeElement.textContent.trim());
    expect(focusedElementText).toMatch(/Dark Mode|Light Mode/);
    
    // Tab to Alice's input field
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    
    // Check if Alice's input is focused
    const inputFocused = await page.evaluate(() => 
      document.activeElement.getAttribute('aria-label')?.includes('Alice')
    );
    expect(inputFocused).toBeTruthy();
    
    // Type a message using keyboard
    await page.keyboard.type('Hello via keyboard');
    
    // Tab to the send button
    await page.keyboard.press('Tab');
    
    // Check if send button is focused
    const sendButtonFocused = await page.evaluate(() => 
      document.activeElement.textContent.includes('Send')
    );
    expect(sendButtonFocused).toBeTruthy();
    
    // Press Enter to send the message
    await page.keyboard.press('Enter');
    
    // Verify message was sent
    await expect(page.locator('.message-content').filter({ hasText: 'Hello via keyboard' })).toBeVisible();
  });

  test('dark mode toggle has appropriate contrast', async ({ page }) => {
    // Test light mode contrast
    const lightModeResults = await new AxeBuilder({ page })
      .withRules(['color-contrast'])
      .analyze();
    
    expect(lightModeResults.violations).toEqual([]);
    
    // Toggle to dark mode
    await page.locator('button').filter({ hasText: /Dark Mode/i }).click();
    
    // Verify dark mode is active
    await expect(page.locator('button').filter({ hasText: /Light Mode/i })).toBeVisible();
    
    // Test contrast in dark mode
    const darkModeResults = await new AxeBuilder({ page })
      .withRules(['color-contrast'])
      .analyze();
    
    expect(darkModeResults.violations).toEqual([]);
  });

  test('network panel has proper ARIA attributes', async ({ page }) => {
    // Send a message first to have something in the network panel
    await page.locator('.chat-panel-alice .message-input-form input').fill('Accessibility test message');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Wait for the message to appear in network panel
    await page.waitForSelector('.network-message');
    
    // Check for appropriate heading levels
    const h3Count = await page.locator('h3').count();
    expect(h3Count).toBeGreaterThan(0);
    
    // Check for appropriate ARIA roles
    const hasRoleHeading = await page.locator('[role="heading"]').count();
    expect(hasRoleHeading).toBeGreaterThan(0);
    
    // Check for appropriate section structure
    const mainSectionCount = await page.locator('main, [role="main"], section, [role="region"]').count();
    expect(mainSectionCount).toBeGreaterThan(0);
    
    // Check that status information is properly communicated
    const statusElements = await page.locator('.network-status').count();
    expect(statusElements).toBeGreaterThan(0);
  });

  test('form controls have accessible labels', async ({ page }) => {
    // Run specific axe test for form labels
    const formResults = await new AxeBuilder({ page })
      .withRules(['label', 'label-content-name-mismatch', 'label-title-only'])
      .analyze();
      
    // Forms should have proper labels
    expect(formResults.violations).toEqual([]);
    
    // Check for aria-label attributes on inputs
    const inputsWithLabels = await page.locator('input[aria-label], button[aria-label]').count();
    expect(inputsWithLabels).toBeGreaterThan(0);
  });

  test('page is navigable with screen reader', async ({ page }) => {
    // This test focuses on landmarks and regions that a screen reader would use
    
    // Check that main landmarks are present
    const mainLandmark = await page.locator('main, [role="main"]').count();
    expect(mainLandmark).toBeGreaterThan(0);
    
    // Check for navigation landmarks if applicable
    const navigationLandmark = await page.locator('nav, [role="navigation"]').count();
    expect(navigationLandmark).toBeGreaterThanOrEqual(0); // May or may not have nav
    
    // Check for complementary landmarks for secondary content
    const complementaryLandmark = await page.locator('aside, [role="complementary"]').count();
    expect(complementaryLandmark).toBeGreaterThanOrEqual(0); // May or may not have aside
    
    // Verify chat sections have appropriate ARIA roles for live regions
    await page.locator('.chat-panel-alice .message-input-form input').fill('Screen reader test');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Chat messages should have appropriate live region attributes
    await page.waitForSelector('.message-content');
    const liveRegions = await page.locator('[aria-live], [role="log"]').count();
    expect(liveRegions).toBeGreaterThan(0);
  });
  
  test('focus management when navigating between panels', async ({ page }) => {
    // Setup: Add some messages
    await page.locator('.chat-panel-alice .message-input-form input').fill('Focus test');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Verify focus is returned properly after sending a message
    const activeElement = await page.evaluate(() => document.activeElement.tagName.toLowerCase());
    expect(['input', 'body']).toContain(activeElement);
    
    // Check for focus trap in modals if there are any
    // For example, if there's a settings modal or help dialog
    const modalTrigger = await page.locator('button').filter({ hasText: /settings|help|info/i }).count();
    
    if (modalTrigger > 0) {
      // Click the first modal trigger
      await page.locator('button').filter({ hasText: /settings|help|info/i }).first().click();
      
      // Verify modal appears
      const modal = await page.locator('[role="dialog"], [aria-modal="true"]').count();
      expect(modal).toBeGreaterThan(0);
      
      // Check that focus is trapped inside the modal
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      
      // Focus should still be inside the modal
      const focusStillInModal = await page.evaluate(() => {
        const modal = document.querySelector('[role="dialog"], [aria-modal="true"]');
        return modal?.contains(document.activeElement);
      });
      
      expect(focusStillInModal).toBeTruthy();
      
      // Close modal and check focus returns
      await page.keyboard.press('Escape');
      
      // Modal should be closed
      const modalClosed = await page.locator('[role="dialog"], [aria-modal="true"]').count();
      expect(modalClosed).toBe(0);
    }
  });
  
  test('page structure follows semantic HTML', async ({ page }) => {
    // Check for semantic structure of the page
    
    // Should have a single h1
    const h1Count = await page.locator('h1').count();
    expect(h1Count).toBe(1);
    
    // Heading levels should be properly nested
    const h2BeforeH3 = await page.evaluate(() => {
      const h2Elements = Array.from(document.querySelectorAll('h2'));
      const h3Elements = Array.from(document.querySelectorAll('h3'));
      
      if (h2Elements.length === 0 || h3Elements.length === 0) return true;
      
      // Get the index of the first h2 and h3
      const firstH2Index = Array.from(document.querySelectorAll('*')).indexOf(h2Elements[0]);
      const firstH3Index = Array.from(document.querySelectorAll('*')).indexOf(h3Elements[0]);
      
      return firstH2Index < firstH3Index;
    });
    
    expect(h2BeforeH3).toBeTruthy();
    
    // Check that all images have alt text
    const imagesWithoutAlt = await page.locator('img:not([alt])').count();
    expect(imagesWithoutAlt).toBe(0);
    
    // Check that lists are properly structured
    const listsWithoutListItems = await page.evaluate(() => {
      const lists = Array.from(document.querySelectorAll('ul, ol'));
      return lists.filter(list => list.children.length === 0 || 
        !Array.from(list.children).every(child => child.tagName === 'LI')).length;
    });
    
    expect(listsWithoutListItems).toBe(0);
  });
  
  test('dynamic content updates are accessible', async ({ page }) => {
    // Test that dynamic updates are properly announced
    
    // Send a message from Alice
    await page.locator('.chat-panel-alice .message-input-form input').fill('Dynamic update test');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Wait for delivery status to appear
    await page.waitForSelector('.message-status');
    
    // Check that delivery status has appropriate ARIA attributes
    const statusHasAriaLive = await page.evaluate(() => {
      const statusElement = document.querySelector('.message-status');
      return statusElement && 
        (statusElement.getAttribute('aria-live') === 'polite' || 
         statusElement.closest('[aria-live]') !== null);
    });
    
    expect(statusHasAriaLive).toBeTruthy();
    
    // Check that network visualization has appropriate ARIA attributes for updates
    await page.waitForSelector('.network-message');
    
    const networkUpdatesAccessible = await page.evaluate(() => {
      const networkPanel = document.querySelector('.network-panel');
      return networkPanel && 
        (networkPanel.getAttribute('aria-live') === 'polite' || 
         networkPanel.querySelector('[aria-live]') !== null);
    });
    
    expect(networkUpdatesAccessible).toBeTruthy();
  });
}); 