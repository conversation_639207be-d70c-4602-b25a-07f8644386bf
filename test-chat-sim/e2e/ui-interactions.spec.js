const { test, expect } = require('@playwright/test');

test.describe('UI Interactions and Edge Cases', () => {
  // Reset localStorage before each test
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => {
      localStorage.clear();
      localStorage.removeItem('theme');
    });
    await page.reload();
  });

  test('dark mode toggle changes theme', async ({ page }) => {
    await page.goto('/');
    
    // Check initial state (light mode)
    const initialTheme = await page.evaluate(() => document.documentElement.getAttribute('data-theme'));
    expect(initialTheme).toBeNull();
    
    // Click dark mode toggle
    await page.locator('.theme-toggle').click();
    
    // Check that theme changed to dark
    const darkTheme = await page.evaluate(() => document.documentElement.getAttribute('data-theme'));
    expect(darkTheme).toBe('dark');
    
    // Verify button text changed
    await expect(page.locator('.theme-toggle')).toContainText('Light Mode');
    
    // Check if theme persists after reload
    await page.reload();
    const persistedTheme = await page.evaluate(() => document.documentElement.getAttribute('data-theme'));
    expect(persistedTheme).toBe('dark');
    
    // Toggle back to light mode
    await page.locator('.theme-toggle').click();
    const lightTheme = await page.evaluate(() => document.documentElement.getAttribute('data-theme'));
    expect(lightTheme).toBeNull();
  });

  test('message input is cleared after sending', async ({ page }) => {
    await page.goto('/');
    
    // Type a message in Alice's input
    const aliceInput = page.locator('.chat-panel-alice .message-input-form input');
    await aliceInput.fill('Test message');
    
    // Send the message
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Input should be empty after sending
    await expect(aliceInput).toHaveValue('');
  });

  test('send button is disabled when input is empty', async ({ page }) => {
    await page.goto('/');
    
    // Check that send buttons are initially disabled
    await expect(page.locator('.chat-panel-alice .message-input-form button')).toBeDisabled();
    await expect(page.locator('.chat-panel-bob .message-input-form button')).toBeDisabled();
    
    // Type in Alice's input and check button state
    const aliceInput = page.locator('.chat-panel-alice .message-input-form input');
    await aliceInput.fill('Test message');
    await expect(page.locator('.chat-panel-alice .message-input-form button')).toBeEnabled();
    
    // Clear input and check that button is disabled again
    await aliceInput.clear();
    await expect(page.locator('.chat-panel-alice .message-input-form button')).toBeDisabled();
  });

  test('white space only messages cannot be sent', async ({ page }) => {
    await page.goto('/');
    
    // Try to send a message with only spaces
    const aliceInput = page.locator('.chat-panel-alice .message-input-form input');
    await aliceInput.fill('   ');
    
    // Button should be disabled
    await expect(page.locator('.chat-panel-alice .message-input-form button')).toBeDisabled();
    
    // Try to send a message with only a tab
    await aliceInput.fill('\t');
    await expect(page.locator('.chat-panel-alice .message-input-form button')).toBeDisabled();
    
    // Try to send a message with newlines
    await aliceInput.fill('\n\n');
    await expect(page.locator('.chat-panel-alice .message-input-form button')).toBeDisabled();
  });

  test('handles long messages correctly', async ({ page }) => {
    await page.goto('/');
    
    // Create a very long message
    const longMessage = 'This is a very long message that should still be displayed correctly. '.repeat(10);
    
    // Send the long message
    await page.locator('.chat-panel-alice .message-input-form input').fill(longMessage);
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Check that message appears in Alice's panel
    await expect(page.locator('.chat-panel-alice .message-content').filter({ hasText: longMessage })).toBeVisible();
    
    // Wait for it to be delivered to Bob
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: longMessage })).toBeVisible({ timeout: 3000 });
    
    // Check that message is fully displayed in the network panel
    await expect(page.locator('.network-message-content')).toContainText(longMessage.substring(0, 20));
  });

  test('messages are displayed with proper timestamps', async ({ page }) => {
    await page.goto('/');
    
    // Send a message
    await page.locator('.chat-panel-alice .message-input-form input').fill('Message with timestamp');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Check that timestamp is displayed
    await expect(page.locator('.chat-panel-alice .message-timestamp')).toBeVisible();
    
    // Wait for delivery
    await expect(page.locator('.network-status-delivered')).toBeVisible({ timeout: 3000 });
    
    // Check that delivery timestamp is shown in network panel
    await expect(page.locator('.network-message-delivery')).toContainText('Delivered at:');
  });

  test('message status indicators are displayed correctly', async ({ page }) => {
    await page.goto('/');
    
    // Send a message from Alice
    await page.locator('.chat-panel-alice .message-input-form input').fill('Message to check status');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Check that "Sending..." is displayed initially
    await expect(page.locator('.chat-panel-alice .message-status')).toContainText('Sending...');
    
    // After delivery, status should change to "Delivered"
    await expect(page.locator('.chat-panel-alice .message-status')).toContainText('Delivered', { timeout: 3000 });
  });

  test('can send multiple messages in quick succession', async ({ page }) => {
    await page.goto('/');
    
    // Send 5 messages quickly
    for (let i = 1; i <= 5; i++) {
      await page.locator('.chat-panel-alice .message-input-form input').fill(`Quick message ${i}`);
      await page.locator('.chat-panel-alice .message-input-form button').click();
    }
    
    // Check that all messages are in the network panel
    await expect(page.locator('.network-message')).toHaveCount(5);
    
    // Wait for all messages to be delivered
    await expect(page.locator('.network-status-delivered')).toHaveCount(5, { timeout: 10000 });
    
    // Check that all messages are in Bob's panel
    for (let i = 1; i <= 5; i++) {
      await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: `Quick message ${i}` })).toBeVisible();
    }
  });
}); 