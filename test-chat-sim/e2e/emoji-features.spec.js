const { test, expect } = require('@playwright/test');

test.describe('Emoji Features', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('emoji picker button opens emoji selector', async ({ page }) => {
    // Check that the emoji button exists
    const emojiButton = page.locator('.emoji-toggle').first();
    await expect(emojiButton).toBeVisible();
    
    // Click the emoji button
    await emojiButton.click();
    
    // Check that the emoji picker opens
    const emojiPicker = page.locator('.emoji-picker-container');
    await expect(emojiPicker).toBeVisible();
    
    // The emoji picker should have a search input
    const searchInput = page.locator('.emoji-picker-container input[type="text"]');
    await expect(searchInput).toBeVisible();
    
    // Clicking outside should close the picker
    await page.click('.chat-header');
    await expect(emojiPicker).not.toBeVisible();
  });

  test('can select emoji and send in message', async ({ page }) => {
    // Open emoji picker
    await page.locator('.emoji-toggle').first().click();
    
    // Wait for the emoji picker to be visible
    await page.waitForSelector('.emoji-picker-container');
    
    // This part is tricky since emoji-picker-react structure can change
    // We'll simulate selecting an emoji by clicking on one
    // Using a common emoji category first
    
    // Try to find and click on a common emoji
    const emojiElement = page.locator('.emoji-picker-container [role="button"]').first();
    
    // Click the first emoji
    await emojiElement.click();
    
    // Check that the emoji picker is closed after selection
    await expect(page.locator('.emoji-picker-container')).not.toBeVisible();
    
    // The input should now contain the emoji
    const inputField = page.locator('.chat-panel-alice input');
    
    // Verify input contains some content (should be the emoji)
    const inputValue = await inputField.inputValue();
    expect(inputValue.length).toBeGreaterThan(0);
    
    // Type additional text
    await inputField.type(' Hello with emoji');
    
    // Send the message
    await page.locator('.chat-panel-alice .message-input-form button[type="submit"]').click();
    
    // Check that message appears in the chat
    const messageContent = page.locator('.chat-panel-alice .message-content');
    await expect(messageContent).toContainText('Hello with emoji');
    
    // The network panel should show the message was sent
    const networkPanel = page.locator('.network-panel');
    await expect(networkPanel).toContainText('ALICE → BOB');
  });
}); 