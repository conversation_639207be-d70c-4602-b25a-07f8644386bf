const { test, expect } = require('@playwright/test');

test.describe('Message Editing', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('can edit own messages via context menu', async ({ page }) => {
    // Send a test message from Alice
    await page.locator('.chat-panel-alice .message-input-form input').fill('Original message text');
    await page.locator('.chat-panel-alice .message-input-form button[type="submit"]').click();
    
    // Wait for message to be delivered
    await page.waitForSelector('.message-status:has-text("Delivered")');
    
    // Right-click on the message to open context menu
    const message = page.locator('.chat-panel-alice .message').filter({ hasText: 'Original message text' });
    await message.click({ button: 'right' });
    
    // Context menu should appear
    await expect(page.locator('.message-context-menu')).toBeVisible();
    
    // Click on "Edit message" option
    await page.locator('.message-context-menu button:has-text("Edit message")').click();
    
    // Verify edit form appears
    await expect(page.locator('.edit-message-form')).toBeVisible();
    
    // Clear the input and type the edited message
    await page.locator('.edit-message-input').fill('Edited message text');
    
    // Click Save button
    await page.locator('.edit-save-button').click();
    
    // Verify message content is updated
    await expect(message).toContainText('Edited message text');
    
    // Verify edit indicator is shown
    await expect(message.locator('.edited-indicator')).toBeVisible();
    await expect(message.locator('.edited-indicator')).toContainText('(edited)');
    
    // Check if edit is reflected in the network panel
    const networkMessage = page.locator('.network-message-content').filter({ hasText: 'Message edited' });
    await expect(networkMessage).toBeVisible();
    
    // Verify edit emoji is shown in network panel
    const editEmoji = page.locator('[aria-label="Edit"]');
    await expect(editEmoji).toBeVisible();
  });

  test('can cancel editing a message', async ({ page }) => {
    // Send a test message from Bob
    await page.locator('.chat-panel-bob .message-input-form input').fill('Message to edit and cancel');
    await page.locator('.chat-panel-bob .message-input-form button[type="submit"]').click();
    
    // Wait for message to be delivered
    await page.waitForSelector('.message-status:has-text("Delivered")');
    
    // Right-click on the message to open context menu
    const message = page.locator('.chat-panel-bob .message').filter({ hasText: 'Message to edit and cancel' });
    await message.click({ button: 'right' });
    
    // Click on "Edit message" option
    await page.locator('.message-context-menu button:has-text("Edit message")').click();
    
    // Verify edit form appears
    await expect(page.locator('.edit-message-form')).toBeVisible();
    
    // Clear the input and type something new
    await page.locator('.edit-message-input').fill('This edit will be canceled');
    
    // Click Cancel button
    await page.locator('.edit-cancel-button').click();
    
    // Verify edit form disappears
    await expect(page.locator('.edit-message-form')).not.toBeVisible();
    
    // Verify original message content is preserved
    await expect(message).toContainText('Message to edit and cancel');
    
    // Verify edit indicator is NOT shown
    await expect(message.locator('.edited-indicator')).not.toBeVisible();
  });
  
  test('cannot edit messages from other users', async ({ page }) => {
    // Send a test message from Alice to Bob
    await page.locator('.chat-panel-alice .message-input-form input').fill('Message from Alice');
    await page.locator('.chat-panel-alice .message-input-form button[type="submit"]').click();
    
    // Wait for message to be delivered to Bob
    await page.waitForTimeout(2000);
    
    // Right-click on the received message in Bob's panel
    const receivedMessage = page.locator('.chat-panel-bob .message.received').filter({ hasText: 'Message from Alice' });
    await receivedMessage.click({ button: 'right' });
    
    // Context menu should NOT appear for received messages
    await expect(page.locator('.message-context-menu')).not.toBeVisible();
  });
}); 