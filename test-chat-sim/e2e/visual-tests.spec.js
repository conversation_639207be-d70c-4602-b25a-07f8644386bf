const { test, expect } = require('@playwright/test');

test.describe('Visual Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
    await page.reload();
  });

  test('capture screenshots of conversation flow', async ({ page }) => {
    await page.goto('/');
    
    // Capture initial state
    await page.screenshot({ path: 'test-results/01-initial-state.png', fullPage: true });
    
    // <PERSON> sends a message
    await page.locator('.chat-panel-alice .message-input-form input').fill('Hello <PERSON>! How are you today?');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Capture right after <PERSON> sends the message (should be in Alice's panel and network panel with "transmitting" status)
    await page.screenshot({ path: 'test-results/02-alice-sends-message.png', fullPage: true });
    
    // Wait for delivery to <PERSON>
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'Hello <PERSON>! How are you today?' })).toBeVisible({ timeout: 3000 });
    
    // Capture after message is delivered to <PERSON>
    await page.screenshot({ path: 'test-results/03-message-delivered-to-bob.png', fullPage: true });
    
    // Bob responds
    await page.locator('.chat-panel-bob .message-input-form input').fill("I'm doing well, thanks for asking! How about you?");
    await page.locator('.chat-panel-bob .message-input-form button').click();
    
    // Capture Bob's response in transit
    await page.screenshot({ path: 'test-results/04-bob-responds.png', fullPage: true });
    
    // Wait for delivery to Alice
    await expect(page.locator('.chat-panel-alice .message-content').filter({ hasText: "I'm doing well, thanks for asking! How about you?" })).toBeVisible({ timeout: 3000 });
    
    // Capture after Bob's message is delivered to Alice
    await page.screenshot({ path: 'test-results/05-conversation-complete.png', fullPage: true });
  });

  test('capture dark mode screenshots', async ({ page }) => {
    await page.goto('/');
    
    // Switch to dark mode
    await page.locator('.theme-toggle').click();
    
    // Capture initial dark mode state
    await page.screenshot({ path: 'test-results/06-dark-mode-initial.png', fullPage: true });
    
    // Send messages in dark mode
    await page.locator('.chat-panel-alice .message-input-form input').fill('This is a dark mode message');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Wait for delivery
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'This is a dark mode message' })).toBeVisible({ timeout: 3000 });
    
    // Capture dark mode conversation
    await page.screenshot({ path: 'test-results/07-dark-mode-conversation.png', fullPage: true });
  });

  test('capture different screen sizes', async ({ page }) => {
    await page.goto('/');
    
    // Send a few messages to create content
    await page.locator('.chat-panel-alice .message-input-form input').fill('Message 1 from Alice');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'Message 1 from Alice' })).toBeVisible({ timeout: 3000 });
    
    await page.locator('.chat-panel-bob .message-input-form input').fill('Response from Bob');
    await page.locator('.chat-panel-bob .message-input-form button').click();
    await expect(page.locator('.chat-panel-alice .message-content').filter({ hasText: 'Response from Bob' })).toBeVisible({ timeout: 3000 });
    
    // Test desktop view (already captured)
    await page.setViewportSize({ width: 1280, height: 800 });
    await page.screenshot({ path: 'test-results/08-desktop-view.png', fullPage: true });
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.screenshot({ path: 'test-results/09-tablet-view.png', fullPage: true });
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await page.screenshot({ path: 'test-results/10-mobile-view.png', fullPage: true });
  });

  test('capture network panel state transitions', async ({ page }) => {
    await page.goto('/');
    
    // Capture empty network panel
    await page.screenshot({ path: 'test-results/11-empty-network-panel.png', clip: { x: 0, y: page.viewportSize().height - 200, width: page.viewportSize().width, height: 200 } });
    
    // Send a message
    await page.locator('.chat-panel-alice .message-input-form input').fill('Message to track in network panel');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Capture network panel with transmitting message
    await page.screenshot({ path: 'test-results/12-network-panel-transmitting.png', clip: { x: 0, y: page.viewportSize().height - 200, width: page.viewportSize().width, height: 200 } });
    
    // Wait for delivery
    await expect(page.locator('.network-status-delivered')).toBeVisible({ timeout: 3000 });
    
    // Capture network panel with delivered message
    await page.screenshot({ path: 'test-results/13-network-panel-delivered.png', clip: { x: 0, y: page.viewportSize().height - 200, width: page.viewportSize().width, height: 200 } });
  });
}); 