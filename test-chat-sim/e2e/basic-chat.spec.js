const { test, expect } = require('@playwright/test');

test.describe('Basic Chat Functionality', () => {
  // Reset localStorage before each test
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
    await page.reload();
  });

  test('page should have Alice and <PERSON> chat panels', async ({ page }) => {
    await page.goto('/');
    
    // Check that both chat panels exist
    await expect(page.locator('h2').filter({ hasText: 'Alice' })).toBeVisible();
    await expect(page.locator('h2').filter({ hasText: 'Bob' })).toBeVisible();
    
    // Check for network panel
    await expect(page.locator('h3').filter({ hasText: 'Network Traffic' })).toBeVisible();
    
    // Check for empty state messages
    await expect(page.locator('.no-messages').filter({ hasText: 'No messages yet' })).toHaveCount(2);
    await expect(page.locator('.no-traffic').filter({ hasText: 'No network traffic yet' })).toBeVisible();
  });

  test('Alice can send a message to Bob', async ({ page }) => {
    await page.goto('/');
    
    // Locate Alice's input and send button
    const aliceInput = page.locator('.chat-panel-alice .message-input-form input');
    const aliceSendButton = page.locator('.chat-panel-alice .message-input-form button');
    
    // Type and send a message
    await aliceInput.fill('Hello Bob!');
    await aliceSendButton.click();
    
    // Message should appear in Alice's panel
    await expect(page.locator('.chat-panel-alice .message-content').filter({ hasText: 'Hello Bob!' })).toBeVisible();
    
    // Message should appear in the network panel
    await expect(page.locator('.network-message-direction').filter({ hasText: 'ALICE → BOB' })).toBeVisible();
    await expect(page.locator('.network-message-content').filter({ hasText: 'Message: "Hello Bob!"' })).toBeVisible();
    
    // Status should initially be "TRANSMITTING"
    await expect(page.locator('.network-status-transmitting')).toBeVisible();
    
    // After delay, message should appear in Bob's panel
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'Hello Bob!' })).toBeVisible({ timeout: 3000 });
    
    // Status should change to "DELIVERED"
    await expect(page.locator('.network-status-delivered')).toBeVisible();
  });
  
  test('Bob can send a message to Alice', async ({ page }) => {
    await page.goto('/');
    
    // Locate Bob's input and send button
    const bobInput = page.locator('.chat-panel-bob .message-input-form input');
    const bobSendButton = page.locator('.chat-panel-bob .message-input-form button');
    
    // Type and send a message
    await bobInput.fill('Hey Alice!');
    await bobSendButton.click();
    
    // Message should appear in Bob's panel
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'Hey Alice!' })).toBeVisible();
    
    // Message should appear in the network panel
    await expect(page.locator('.network-message-direction').filter({ hasText: 'BOB → ALICE' })).toBeVisible();
    await expect(page.locator('.network-message-content').filter({ hasText: 'Message: "Hey Alice!"' })).toBeVisible();
    
    // Status should initially be "TRANSMITTING"
    await expect(page.locator('.network-status-transmitting')).toBeVisible();
    
    // After delay, message should appear in Alice's panel
    await expect(page.locator('.chat-panel-alice .message-content').filter({ hasText: 'Hey Alice!' })).toBeVisible({ timeout: 3000 });
    
    // Status should change to "DELIVERED"
    await expect(page.locator('.network-status-delivered')).toBeVisible();
  });
  
  test('can have a conversation with multiple messages', async ({ page }) => {
    await page.goto('/');
    
    // 1. Alice sends a message
    await page.locator('.chat-panel-alice .message-input-form input').fill('Hello Bob, how are you?');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Wait for message to be delivered
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'Hello Bob, how are you?' })).toBeVisible({ timeout: 3000 });
    
    // 2. Bob responds
    await page.locator('.chat-panel-bob .message-input-form input').fill("I'm good, thanks for asking!");
    await page.locator('.chat-panel-bob .message-input-form button').click();
    
    // Wait for message to be delivered
    await expect(page.locator('.chat-panel-alice .message-content').filter({ hasText: "I'm good, thanks for asking!" })).toBeVisible({ timeout: 3000 });
    
    // 3. Alice sends another message
    await page.locator('.chat-panel-alice .message-input-form input').fill('Great! What are you working on?');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Wait for message to be delivered
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'Great! What are you working on?' })).toBeVisible({ timeout: 3000 });
    
    // Check that all messages are in the network panel
    const networkMessages = await page.locator('.network-message').count();
    expect(networkMessages).toBe(3);
  });
}); 