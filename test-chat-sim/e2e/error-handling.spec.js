const { test, expect } = require('@playwright/test');

test.describe('Error Handling Scenarios', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
    await page.reload();
  });

  test('handles localStorage failures gracefully', async ({ page }) => {
    // Mock localStorage to throw an error when accessed
    await page.addInitScript(() => {
      const originalSetItem = Storage.prototype.setItem;
      Storage.prototype.setItem = function(key, value) {
        if (key === 'chat_simulator_messages') {
          throw new Error('Mock localStorage error');
        }
        return originalSetItem.call(this, key, value);
      };

      // Capture console errors
      window.consoleErrors = [];
      const originalConsoleError = console.error;
      console.error = function(...args) {
        window.consoleErrors.push(args);
        originalConsoleError.apply(console, args);
      };
    });

    // Reload page with the mock in place
    await page.reload();

    // Send a message (which will try to save to localStorage)
    await page.locator('.chat-panel-alice .message-input-form input').fill('This should trigger a localStorage error');
    await page.locator('.chat-panel-alice .message-input-form button').click();

    // The app should continue to function despite localStorage error
    await expect(page.locator('.chat-panel-alice .message-content').filter({ hasText: 'This should trigger a localStorage error' })).toBeVisible();
    
    // Wait for message delivery
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'This should trigger a localStorage error' })).toBeVisible({ timeout: 3000 });

    // Check that error was handled (logged to console)
    const consoleErrors = await page.evaluate(() => window.consoleErrors);
    expect(consoleErrors.length).toBeGreaterThan(0);
  });

  test('handles JSON parse errors from corrupted localStorage', async ({ page }) => {
    // Set invalid JSON in localStorage
    await page.evaluate(() => {
      localStorage.setItem('chat_simulator_messages', '{invalid-json');
    });

    // Reload page - app should handle the corrupted data gracefully
    await page.reload();

    // The app should still load and function
    await expect(page.locator('h2').filter({ hasText: 'Alice' })).toBeVisible();
    await expect(page.locator('h2').filter({ hasText: 'Bob' })).toBeVisible();

    // Send a new message to verify app still works
    await page.locator('.chat-panel-alice .message-input-form input').fill('Testing after corrupt localStorage');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Message should appear and be delivered
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'Testing after corrupt localStorage' })).toBeVisible({ timeout: 3000 });
  });

  test('validate message object structure', async ({ page }) => {
    // Send a message
    await page.locator('.chat-panel-alice .message-input-form input').fill('Message to inspect');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Wait for delivery
    await expect(page.locator('.network-status-delivered')).toBeVisible({ timeout: 3000 });
    
    // Get stored messages and verify structure
    const storedMessages = await page.evaluate(() => {
      return JSON.parse(localStorage.getItem('chat_simulator_messages'));
    });
    
    expect(storedMessages.length).toBeGreaterThan(0);
    
    const message = storedMessages[0];
    expect(message).toHaveProperty('id');
    expect(message).toHaveProperty('sender');
    expect(message).toHaveProperty('recipient');
    expect(message).toHaveProperty('content');
    expect(message).toHaveProperty('timestamp');
    expect(message).toHaveProperty('status');
    
    // Values should be of the correct type
    expect(typeof message.id).toBe('string');
    expect(typeof message.sender).toBe('string');
    expect(typeof message.recipient).toBe('string');
    expect(typeof message.content).toBe('string');
    expect(typeof message.timestamp).toBe('string');
    expect(typeof message.status).toBe('string');
  });
  
  test('handles rapid toggling of dark mode', async ({ page }) => {
    await page.goto('/');
    
    // Toggle dark mode multiple times rapidly
    for (let i = 0; i < 10; i++) {
      await page.locator('.theme-toggle').click();
    }
    
    // App should still be functional
    await page.locator('.chat-panel-alice .message-input-form input').fill('Testing after rapid toggles');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Message should appear and be delivered
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'Testing after rapid toggles' })).toBeVisible({ timeout: 3000 });
  });
}); 