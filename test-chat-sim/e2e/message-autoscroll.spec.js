const { test, expect } = require('@playwright/test');

test.describe('Message Auto-scrolling', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
  });

  test('chat auto-scrolls when new messages arrive', async ({ page }) => {
    // First, populate the chat with enough messages to enable scrolling
    const generateMessages = async (count) => {
      for (let i = 1; i <= count; i++) {
        await page.locator('.chat-panel-alice .message-input-form input').fill(`Test message ${i}`);
        await page.locator('.chat-panel-alice .message-input-form button[type="submit"]').click();
        // Wait a bit between messages to simulate natural sending
        if (i % 3 === 0) {
          await page.waitForTimeout(300);
        }
      }
    };

    // Generate 20 messages to create a scrollable chat
    await generateMessages(20);
    
    // Wait for last message to be delivered
    await page.waitForSelector('.message-status:has-text("Delivered")');
    
    // Get the scroll position of the messages container
    const scrollPosition = await page.evaluate(() => {
      const container = document.querySelector('.chat-panel-alice .messages-container');
      return {
        scrollTop: container.scrollTop,
        scrollHeight: container.scrollHeight,
        clientHeight: container.clientHeight
      };
    });
    
    // Check that the scroll position is at the bottom (or very close)
    // We consider it scrolled to bottom if it's within 20px of the bottom
    const isAtBottom = scrollPosition.scrollTop + scrollPosition.clientHeight >= scrollPosition.scrollHeight - 20;
    expect(isAtBottom).toBeTruthy();
    
    // Now send another message and verify scroll position updates
    await page.locator('.chat-panel-bob .message-input-form input').fill('New message that should trigger auto-scroll');
    await page.locator('.chat-panel-bob .message-input-form button[type="submit"]').click();
    
    // Wait for the new message to be delivered
    await page.waitForTimeout(2000);
    
    // Check scroll position after new message
    const newScrollPosition = await page.evaluate(() => {
      const container = document.querySelector('.chat-panel-alice .messages-container');
      return {
        scrollTop: container.scrollTop,
        scrollHeight: container.scrollHeight,
        clientHeight: container.clientHeight
      };
    });
    
    // Verify it's still at the bottom
    const isStillAtBottom = newScrollPosition.scrollTop + newScrollPosition.clientHeight >= newScrollPosition.scrollHeight - 20;
    expect(isStillAtBottom).toBeTruthy();
  });

  test('preserves scroll position when user has scrolled up', async ({ page }) => {
    // Generate enough messages
    for (let i = 1; i <= 25; i++) {
      await page.locator('.chat-panel-alice .message-input-form input').fill(`Scroll test message ${i}`);
      await page.locator('.chat-panel-alice .message-input-form button[type="submit"]').click();
      if (i % 5 === 0) await page.waitForTimeout(200);
    }
    
    // Wait for messages to be delivered
    await page.waitForTimeout(2000);
    
    // Scroll up to middle of chat
    await page.evaluate(() => {
      const container = document.querySelector('.chat-panel-alice .messages-container');
      container.scrollTop = container.scrollHeight / 2;
    });
    
    // Get that scroll position
    const midScrollPosition = await page.evaluate(() => {
      const container = document.querySelector('.chat-panel-alice .messages-container');
      return container.scrollTop;
    });
    
    // Send a new message
    await page.locator('.chat-panel-bob .message-input-form input').fill('This should not change scroll position');
    await page.locator('.chat-panel-bob .message-input-form button[type="submit"]').click();
    
    // Wait for the message to be delivered
    await page.waitForTimeout(2000);
    
    // Check if scroll position was maintained
    const newMidScrollPosition = await page.evaluate(() => {
      const container = document.querySelector('.chat-panel-alice .messages-container');
      return container.scrollTop;
    });
    
    // The scroll position should stay similar (allowing some small variation)
    // Typically we'd want to check exactly, but browser rendering can cause slight differences
    expect(Math.abs(newMidScrollPosition - midScrollPosition)).toBeLessThan(50);
  });
}); 