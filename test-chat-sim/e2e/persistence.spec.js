const { test, expect } = require('@playwright/test');

test.describe('Message Persistence', () => {
  // Reset localStorage before each test
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
    await page.reload();
  });

  test('messages persist after page reload', async ({ page }) => {
    // Send a message from Alice
    await page.goto('/');
    await page.locator('.chat-panel-alice .message-input-form input').fill('This message should persist');
    await page.locator('.chat-panel-alice .message-input-form button').click();

    // Wait for it to be delivered to <PERSON>
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'This message should persist' })).toBeVisible({ timeout: 3000 });

    // Bob sends a response
    await page.locator('.chat-panel-bob .message-input-form input').fill('This response should also persist');
    await page.locator('.chat-panel-bob .message-input-form button').click();

    // Wait for it to be delivered to Alice
    await expect(page.locator('.chat-panel-alice .message-content').filter({ hasText: 'This response should also persist' })).toBeVisible({ timeout: 3000 });

    // Reload the page
    await page.reload();

    // Wait for page to load and check if messages are still there
    await expect(page.locator('.chat-panel-alice .message-content').filter({ hasText: 'This message should persist' })).toBeVisible();
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'This response should also persist' })).toBeVisible();
    
    // Check network panel has the messages too
    await expect(page.locator('.network-message-content').filter({ hasText: 'Message: "This message should persist"' })).toBeVisible();
    await expect(page.locator('.network-message-content').filter({ hasText: 'Message: "This response should also persist"' })).toBeVisible();
  });

  test('clear button removes all messages from localStorage', async ({ page }) => {
    // Send a message from Alice
    await page.goto('/');
    await page.locator('.chat-panel-alice .message-input-form input').fill('Message to be cleared');
    await page.locator('.chat-panel-alice .message-input-form button').click();

    // Wait for it to be delivered to Bob
    await expect(page.locator('.chat-panel-bob .message-content').filter({ hasText: 'Message to be cleared' })).toBeVisible({ timeout: 3000 });

    // Click clear button
    await page.locator('.chat-panel-alice .clear-button').click();

    // Check that messages are cleared
    await expect(page.locator('.no-messages').filter({ hasText: 'No messages yet' })).toHaveCount(2);
    await expect(page.locator('.no-traffic').filter({ hasText: 'No network traffic yet' })).toBeVisible();

    // Reload the page to verify localStorage was cleared too
    await page.reload();

    // Messages should still be gone
    await expect(page.locator('.no-messages').filter({ hasText: 'No messages yet' })).toHaveCount(2);
    await expect(page.locator('.no-traffic').filter({ hasText: 'No network traffic yet' })).toBeVisible();
  });

  test('messages persist across multiple sessions', async ({ browser }) => {
    // First session
    const context1 = await browser.newContext();
    const page1 = await context1.newPage();
    await page1.goto('/');
    await page1.evaluate(() => localStorage.clear());
    await page1.reload();

    // Send a message from Alice
    await page1.locator('.chat-panel-alice .message-input-form input').fill('Message from first session');
    await page1.locator('.chat-panel-alice .message-input-form button').click();
    
    // Wait for it to be delivered
    await expect(page1.locator('.network-status-delivered')).toBeVisible({ timeout: 3000 });
    
    // Close first session
    await context1.close();

    // Second session
    const context2 = await browser.newContext();
    const page2 = await context2.newPage();
    await page2.goto('/');

    // Check if message from first session is visible
    await expect(page2.locator('.chat-panel-alice .message-content').filter({ hasText: 'Message from first session' })).toBeVisible();
    await expect(page2.locator('.chat-panel-bob .message-content').filter({ hasText: 'Message from first session' })).toBeVisible();

    // Send a second message
    await page2.locator('.chat-panel-bob .message-input-form input').fill('Message from second session');
    await page2.locator('.chat-panel-bob .message-input-form button').click();
    
    // Wait for it to be delivered
    await expect(page2.locator('.network-status-delivered')).toBeVisible({ timeout: 3000 });
    
    // Check that we have both messages
    await expect(page2.locator('.network-message')).toHaveCount(2);
    
    await context2.close();
  });
}); 