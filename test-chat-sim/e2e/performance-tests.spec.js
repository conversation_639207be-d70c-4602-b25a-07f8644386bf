const { test, expect } = require('@playwright/test');

// Performance thresholds for assertions
const PERFORMANCE_THRESHOLDS = {
  INITIAL_LOAD_TIME_MS: 1000,
  RENDER_TIME_MS: 200,
  MESSAGE_DELIVERY_TIME_MS: 1000,
  CPU_IDLE_PERCENT: 70,
  MEMORY_LIMIT_MB: 100,
  ANIMATION_FRAMES_PER_SECOND: 50
};

test.describe('Chat Application Performance Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
    await page.reload();
  });

  test('can handle multiple messages without performance degradation', async ({ page }) => {
    // Function to send a message from Alice
    async function sendAliceMessage(message) {
      await page.locator('.chat-panel-alice .message-input-form input').fill(message);
      await page.locator('.chat-panel-alice .message-input-form button').click();
      await page.waitForSelector(`.chat-panel-bob .message-content:has-text("${message}")`, { timeout: 3000 });
    }
    
    // Send 20 messages in sequence and measure time
    const startTime = Date.now();
    
    for (let i = 1; i <= 20; i++) {
      await sendAliceMessage(`Test message ${i}`);
    }
    
    const endTime = Date.now();
    const timePerMessage = (endTime - startTime) / 20;
    
    console.log(`Average time per message: ${timePerMessage}ms`);
    
    // Check that all messages are displayed
    const messageCount = await page.locator('.chat-panel-alice .message').count();
    expect(messageCount).toBe(20);
    
    // Verify that time per message is reasonable
    expect(timePerMessage).toBeLessThan(PERFORMANCE_THRESHOLDS.MESSAGE_DELIVERY_TIME_MS);
  });

  test('app loads and renders quickly', async ({ page }) => {
    // Enable performance metrics
    const client = await page.context().newCDPSession(page);
    await client.send('Performance.enable');
    
    // Navigate to the page and measure load time
    const navigationStart = Date.now();
    const response = await page.goto('/', { waitUntil: 'networkidle' });
    const navigationEnd = Date.now();
    
    // Get performance metrics
    const performanceMetrics = await client.send('Performance.getMetrics');
    const metrics = {};
    performanceMetrics.metrics.forEach(metric => {
      metrics[metric.name] = metric.value;
    });
    
    // Calculate key metrics
    const navigationTime = navigationEnd - navigationStart;
    const domContentLoaded = metrics.DomContentLoaded - metrics.NavigationStart;
    const firstPaint = metrics.FirstPaint - metrics.NavigationStart;
    const firstContentfulPaint = metrics.FirstContentfulPaint - metrics.NavigationStart;
    
    console.log(`Navigation time: ${navigationTime}ms`);
    console.log(`DOM Content Loaded: ${domContentLoaded}ms`);
    console.log(`First Paint: ${firstPaint}ms`);
    console.log(`First Contentful Paint: ${firstContentfulPaint}ms`);
    
    // Assert performance requirements
    expect(navigationTime).toBeLessThan(PERFORMANCE_THRESHOLDS.INITIAL_LOAD_TIME_MS);
    expect(firstContentfulPaint).toBeLessThan(PERFORMANCE_THRESHOLDS.INITIAL_LOAD_TIME_MS);
    
    // Check response status and content type
    expect(response.status()).toBe(200);
    
    // Check that main UI elements are visible quickly
    await expect(page.locator('.chat-panel-alice')).toBeVisible({ timeout: 500 });
    await expect(page.locator('.chat-panel-bob')).toBeVisible({ timeout: 500 });
    await expect(page.locator('.network-panel')).toBeVisible({ timeout: 500 });
  });

  test('measures frame rate during message animation', async ({ page }) => {
    // Start performance monitoring
    await page.evaluate(() => {
      window.frameMetrics = {
        frames: 0,
        startTime: performance.now(),
        frameTimeStamps: []
      };
      
      window.frameCounter = () => {
        window.frameMetrics.frames++;
        window.frameMetrics.frameTimeStamps.push(performance.now());
        window.requestAnimationFrame(window.frameCounter);
      };
      
      window.requestAnimationFrame(window.frameCounter);
    });
    
    // Let it collect baseline frame data
    await page.waitForTimeout(500);
    
    // Send a message to trigger animations
    await page.locator('.chat-panel-alice .message-input-form input').fill('Animation test message');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Collect data during animation
    await page.waitForTimeout(2000);
    
    // Stop frame counter and collect metrics
    const frameMetrics = await page.evaluate(() => {
      const metrics = { ...window.frameMetrics };
      metrics.endTime = performance.now();
      metrics.duration = metrics.endTime - metrics.startTime;
      metrics.fps = metrics.frames / (metrics.duration / 1000);
      
      // Calculate frame times
      metrics.frameTimes = [];
      for (let i = 1; i < metrics.frameTimeStamps.length; i++) {
        metrics.frameTimes.push(metrics.frameTimeStamps[i] - metrics.frameTimeStamps[i-1]);
      }
      
      // Calculate jank (dropped frames)
      metrics.longFrames = metrics.frameTimes.filter(time => time > 16.7).length;
      metrics.jankPercentage = (metrics.longFrames / metrics.frameTimes.length) * 100;
      
      // Cleanup
      window.cancelAnimationFrame(window.frameCounter);
      delete window.frameCounter;
      delete window.frameMetrics;
      
      return metrics;
    });
    
    console.log(`FPS during animation: ${frameMetrics.fps.toFixed(2)}`);
    console.log(`Jank percentage: ${frameMetrics.jankPercentage.toFixed(2)}%`);
    
    // Assert acceptable frame rate (assuming 60fps target, we expect at least 50fps)
    expect(frameMetrics.fps).toBeGreaterThanOrEqual(PERFORMANCE_THRESHOLDS.ANIMATION_FRAMES_PER_SECOND);
    expect(frameMetrics.jankPercentage).toBeLessThan(20); // Less than 20% dropped frames
  });

  test('maintains performance with large number of messages', async ({ page }) => {
    // Function to generate a large number of messages
    async function sendMessage(count) {
      await page.evaluate((count) => {
        // Generate messages directly in the browser for performance
        const messages = [];
        const now = new Date();
        
        for (let i = 0; i < count; i++) {
          const timestamp = new Date(now.getTime() - (count - i) * 60000);
          messages.push({
            id: `msg${i}`,
            sender: i % 2 === 0 ? 'alice' : 'bob',
            recipient: i % 2 === 0 ? 'bob' : 'alice',
            content: `Test message ${i}`,
            timestamp: timestamp.toISOString(),
            status: 'delivered'
          });
        }
        
        // Store messages in localStorage
        localStorage.setItem('chat_messages', JSON.stringify(messages));
      }, count);
      
      // Reload to load the messages
      await page.reload();
    }
    
    // Start performance monitoring
    const client = await page.context().newCDPSession(page);
    await client.send('Performance.enable');
    
    // Send a large number of messages (200)
    const loadStart = Date.now();
    await sendMessage(200);
    const loadEnd = Date.now();
    
    // Wait for rendering to complete
    await page.waitForSelector('.message', { timeout: 5000 });
    
    // Check DOM node count to ensure all messages loaded
    const nodeCount = await page.evaluate(() => document.querySelectorAll('.message').length);
    expect(nodeCount).toBe(200);
    
    // Measure rendering time
    const renderTime = loadEnd - loadStart;
    console.log(`Time to render 200 messages: ${renderTime}ms`);
    
    // Measure memory usage
    const memoryInfo = await page.evaluate(() => performance.memory ? performance.memory : null);
    if (memoryInfo) {
      const usedHeapMB = Math.round(memoryInfo.usedJSHeapSize / (1024 * 1024));
      console.log(`Used heap size: ${usedHeapMB}MB`);
      
      // Assert reasonable memory usage
      expect(usedHeapMB).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_LIMIT_MB);
    }
    
    // Measure CPU usage during scroll
    await client.send('Emulation.setCPUThrottlingRate', { rate: 1 }); // No throttling
    
    // Start tracking idle times
    await page.evaluate(() => {
      window.idleTime = 0;
      window.totalTime = 0;
      window.lastMark = performance.now();
      
      window.idleTracker = () => {
        const now = performance.now();
        const elapsed = now - window.lastMark;
        window.totalTime += elapsed;
        
        // Consider the thread idle if we can run this tracker
        window.idleTime += elapsed;
        window.lastMark = now;
        
        requestIdleCallback(window.idleTracker);
      };
      
      requestIdleCallback(window.idleTracker);
    });
    
    // Scroll through all messages
    await page.evaluate(() => {
      const scrollStep = () => {
        window.scrollBy(0, 50);
        if (window.scrollY < document.body.scrollHeight - window.innerHeight) {
          setTimeout(scrollStep, 50);
        }
      };
      scrollStep();
    });
    
    // Wait for scrolling to complete
    await page.waitForFunction(() => {
      return window.scrollY >= document.body.scrollHeight - window.innerHeight;
    }, { timeout: 10000 });
    
    // Get idle metrics
    const idleMetrics = await page.evaluate(() => {
      const metrics = {
        idleTime: window.idleTime,
        totalTime: window.totalTime,
        idlePercent: (window.idleTime / window.totalTime) * 100
      };
      
      // Cleanup
      cancelIdleCallback(window.idleTracker);
      delete window.idleTracker;
      delete window.idleTime;
      delete window.totalTime;
      delete window.lastMark;
      
      return metrics;
    });
    
    console.log(`CPU idle time during scroll: ${idleMetrics.idlePercent.toFixed(2)}%`);
    
    // Assert good CPU utilization (higher idle percent is better)
    expect(idleMetrics.idlePercent).toBeGreaterThan(PERFORMANCE_THRESHOLDS.CPU_IDLE_PERCENT);
  });

  test('handles large message history load efficiently', async ({ page }) => {
    // Pre-populate localStorage with a large number of messages
    await page.goto('/');
    await page.evaluate(() => {
      const messages = [];
      const now = new Date();
      
      // Create 100 messages
      for (let i = 1; i <= 100; i++) {
        const timestamp = new Date(now.getTime() - (100 - i) * 60000).toISOString();
        messages.push({
          id: `msg_${i}`,
          sender: i % 2 === 0 ? 'alice' : 'bob',
          recipient: i % 2 === 0 ? 'bob' : 'alice',
          content: `Historical message ${i}`,
          timestamp: timestamp,
          status: 'delivered'
        });
      }
      
      localStorage.setItem('chatMessages', JSON.stringify(messages));
    });
    
    // Reload the page and measure loading time
    const startTime = performance.now();
    await page.reload();
    
    // Wait for all messages to load
    await page.waitForSelector('.message-content:has-text("Historical message 100")');
    const endTime = performance.now();
    
    const loadTime = endTime - startTime;
    console.log(`Time to load 100 messages: ${loadTime}ms`);
    
    // Check that all messages are loaded
    const messageCount = await page.locator('.message').count();
    expect(messageCount).toBe(100);
    
    // Check that the most recent message (100) is visible
    await expect(page.locator('.message-content:has-text("Historical message 100")').last()).toBeVisible();
    
    // Send a new message and check it appears after the historical ones
    await page.locator('.chat-panel-alice .message-input-form input').fill('New message after history');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Wait for the new message to be delivered to Bob
    await page.waitForSelector('.chat-panel-bob .message-content:has-text("New message after history")');
    
    // Verify the message count increased
    const newMessageCount = await page.locator('.message').count();
    expect(newMessageCount).toBe(101);
  });

  test('scroll performance with many messages', async ({ page }) => {
    // Pre-populate localStorage with a large number of messages
    await page.goto('/');
    await page.evaluate(() => {
      const messages = [];
      const now = new Date();
      
      // Create 200 messages
      for (let i = 1; i <= 200; i++) {
        const timestamp = new Date(now.getTime() - (200 - i) * 60000).toISOString();
        messages.push({
          id: `msg_${i}`,
          sender: i % 2 === 0 ? 'alice' : 'bob',
          recipient: i % 2 === 0 ? 'bob' : 'alice',
          content: `Message for scroll test ${i}`,
          timestamp: timestamp,
          status: 'delivered'
        });
      }
      
      localStorage.setItem('chatMessages', JSON.stringify(messages));
    });
    
    await page.reload();
    await page.waitForSelector('.message-content:has-text("Message for scroll test 200")');
    
    // Record scroll start time
    await page.evaluate(() => {
      window.scrollStartTime = performance.now();
      
      // Get the Alice chat panel message container
      const messageContainer = document.querySelector('.chat-panel-alice .messages-container');
      
      // Scroll to the top (earliest messages)
      messageContainer.scrollTop = 0;
    });
    
    // Wait a moment for the scroll to complete
    await page.waitForTimeout(100);
    
    // Check earliest message is visible after scroll
    await expect(page.locator('.message-content:has-text("Message for scroll test 1")').first()).toBeVisible();
    
    // Record scroll time
    const scrollTime = await page.evaluate(() => {
      return performance.now() - window.scrollStartTime;
    });
    
    console.log(`Scroll time: ${scrollTime}ms`);
    
    // Test assertion for scroll performance (adjust threshold as needed)
    expect(scrollTime).toBeLessThan(1000); // Should scroll in less than 1 second
  });

  test('network panel updates efficiently with many messages', async ({ page }) => {
    await page.goto('/');
    
    // Function to send a message from alternating users
    async function sendMessage(count) {
      const sender = count % 2 === 0 ? 'alice' : 'bob';
      const receiver = count % 2 === 0 ? 'bob' : 'alice';
      
      await page.locator(`.chat-panel-${sender} .message-input-form input`).fill(`Performance message ${count}`);
      await page.locator(`.chat-panel-${sender} .message-input-form button`).click();
      
      // Wait for message to be delivered
      await page.waitForSelector(`.chat-panel-${receiver} .message-content:has-text("Performance message ${count}")`, 
        { timeout: 3000 });
    }
    
    // Send 15 messages in sequence
    for (let i = 1; i <= 15; i++) {
      await sendMessage(i);
    }
    
    // Check network panel shows all messages
    const networkMessageCount = await page.locator('.network-message').count();
    expect(networkMessageCount).toBe(15);
    
    // Check the rendering time of the network panel
    const renderTime = await page.evaluate(() => {
      const startTime = performance.now();
      
      // Force a repaint of the network panel
      const networkPanel = document.querySelector('.network-panel');
      networkPanel.style.display = 'none';
      void networkPanel.offsetHeight; // Force reflow
      networkPanel.style.display = 'block';
      
      return performance.now() - startTime;
    });
    
    console.log(`Network panel render time: ${renderTime}ms`);
    
    // Network panel should render efficiently
    expect(renderTime).toBeLessThan(500); // Should render in less than 500ms
  });
}); 