const { test, expect } = require('@playwright/test');

test.describe('Concurrent User Actions', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
    await page.reload();
  });

  test('handles rapid alternating messages between users', async ({ page }) => {
    // Send messages back and forth rapidly
    for (let i = 1; i <= 10; i++) {
      // <PERSON> sends message
      await page.locator('.chat-panel-alice .message-input-form input').fill(`Alice message ${i}`);
      await page.locator('.chat-panel-alice .message-input-form button').click();
      
      // Don't wait for delivery, immediately have <PERSON> send a message
      await page.locator('.chat-panel-bob .message-input-form input').fill(`Bob message ${i}`);
      await page.locator('.chat-panel-bob .message-input-form button').click();
    }
    
    // Wait for all messages to be delivered (20 total)
    await page.waitForFunction(() => {
      const deliveredCount = document.querySelectorAll('.network-status-delivered').length;
      return deliveredCount >= 20;
    }, { timeout: 10000 });
    
    // Verify all messages were delivered in correct order
    for (let i = 1; i <= 10; i++) {
      // Check Alice's messages
      await expect(page.locator(`.chat-panel-alice .message-content:has-text("Alice message ${i}")`))
        .toBeVisible();
      await expect(page.locator(`.chat-panel-bob .message-content:has-text("Alice message ${i}")`))
        .toBeVisible();
      
      // Check Bob's messages
      await expect(page.locator(`.chat-panel-bob .message-content:has-text("Bob message ${i}")`))
        .toBeVisible();
      await expect(page.locator(`.chat-panel-alice .message-content:has-text("Bob message ${i}")`))
        .toBeVisible();
    }
    
    // Check network panel has all 20 messages
    const networkMessages = await page.locator('.network-message').count();
    expect(networkMessages).toBe(20);
  });

  test('handles concurrent inputs in both chat panels', async ({ page }) => {
    // Type in both chat panels simultaneously
    await Promise.all([
      page.locator('.chat-panel-alice .message-input-form input').type('Typing from Alice...', { delay: 50 }),
      page.locator('.chat-panel-bob .message-input-form input').type('Typing from Bob...', { delay: 50 })
    ]);
    
    // Check that both inputs have the correct text
    await expect(page.locator('.chat-panel-alice .message-input-form input')).toHaveValue('Typing from Alice...');
    await expect(page.locator('.chat-panel-bob .message-input-form input')).toHaveValue('Typing from Bob...');
    
    // Send both messages concurrently
    await Promise.all([
      page.locator('.chat-panel-alice .message-input-form button').click(),
      page.locator('.chat-panel-bob .message-input-form button').click()
    ]);
    
    // Verify both messages appear in respective panels
    await expect(page.locator('.chat-panel-alice .message-content:has-text("Typing from Alice...")')).toBeVisible();
    await expect(page.locator('.chat-panel-bob .message-content:has-text("Typing from Bob...")')).toBeVisible();
    
    // Wait for messages to be delivered
    await page.waitForSelector('.chat-panel-bob .message-content:has-text("Typing from Alice...")');
    await page.waitForSelector('.chat-panel-alice .message-content:has-text("Typing from Bob...")');
    
    // Verify both messages are in the network panel
    await expect(page.locator('.network-message-content:has-text("Typing from Alice...")')).toBeVisible();
    await expect(page.locator('.network-message-content:has-text("Typing from Bob...")')).toBeVisible();
  });

  test('handles rapid clear actions from both users', async ({ page }) => {
    // Setup: send messages from both users
    await page.locator('.chat-panel-alice .message-input-form input').fill('Message from Alice');
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    await page.locator('.chat-panel-bob .message-input-form input').fill('Message from Bob');
    await page.locator('.chat-panel-bob .message-input-form button').click();
    
    // Wait for messages to be delivered
    await page.waitForSelector('.chat-panel-bob .message-content:has-text("Message from Alice")');
    await page.waitForSelector('.chat-panel-alice .message-content:has-text("Message from Bob")');
    
    // Both users clear their messages concurrently
    await Promise.all([
      page.locator('.chat-panel-alice button:has-text("Clear")').click(),
      page.locator('.chat-panel-bob button:has-text("Clear")').click()
    ]);
    
    // Verify all messages are cleared
    await expect(page.locator('.no-messages')).toHaveCount(2);
    await expect(page.locator('.no-traffic')).toBeVisible();
  });

  test('handles theme toggle while sending messages', async ({ page }) => {
    // Start typing a message with Alice
    await page.locator('.chat-panel-alice .message-input-form input').fill('Message during theme change');
    
    // Toggle theme while typing
    await page.locator('button:has-text("Dark Mode")').click();
    
    // Verify dark mode is active
    expect(await page.evaluate(() => document.documentElement.getAttribute('data-theme'))).toBe('dark');
    
    // Complete sending the message
    await page.locator('.chat-panel-alice .message-input-form button').click();
    
    // Verify message was sent and delivered correctly despite theme change
    await expect(page.locator('.chat-panel-alice .message-content:has-text("Message during theme change")')).toBeVisible();
    await expect(page.locator('.chat-panel-bob .message-content:has-text("Message during theme change")')).toBeVisible();
    
    // Toggle theme back during message transmission
    await page.locator('button:has-text("Light Mode")').click();
    
    // Verify light mode is active
    expect(await page.evaluate(() => document.documentElement.getAttribute('data-theme'))).toBe(null);
    
    // Send another message and verify it works
    await page.locator('.chat-panel-bob .message-input-form input').fill('Reply after theme changes');
    await page.locator('.chat-panel-bob .message-input-form button').click();
    
    await expect(page.locator('.chat-panel-alice .message-content:has-text("Reply after theme changes")')).toBeVisible();
  });

  test('handles multiple sends without waiting for delivery', async ({ page }) => {
    // Send 5 messages from Alice without waiting for delivery
    for (let i = 1; i <= 5; i++) {
      await page.locator('.chat-panel-alice .message-input-form input').fill(`Quick message ${i}`);
      await page.locator('.chat-panel-alice .message-input-form button').click();
    }
    
    // Check that all messages appear in the Alice panel immediately
    for (let i = 1; i <= 5; i++) {
      await expect(page.locator(`.chat-panel-alice .message-content:has-text("Quick message ${i}")`)).toBeVisible();
    }
    
    // Check the network panel shows all messages in transmission
    const transmittingCount = await page.locator('.network-status-transmitting').count();
    expect(transmittingCount).toBeGreaterThan(0);
    
    // Wait for all messages to be delivered
    await page.waitForFunction(() => {
      return document.querySelectorAll('.network-status-delivered').length >= 5;
    }, { timeout: 10000 });
    
    // Verify all messages were delivered to Bob in the correct order
    for (let i = 1; i <= 5; i++) {
      await expect(page.locator(`.chat-panel-bob .message-content:has-text("Quick message ${i}")`)).toBeVisible();
    }
  });
}); 