/**
 * Handshake UX End-to-End Tests
 * Comprehensive testing of OTR handshake user experience in the test app
 */

const { test, expect } = require('@playwright/test');

test.describe('Handshake UX Experience', () => {
  // Reset localStorage before each test
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
    await page.reload();
  });

  test.describe('Tab Navigation and Interface', () => {
    test('should display handshake tab and allow navigation', async ({ page }) => {
      await page.goto('/');
      
      // Check that all tabs are visible
      await expect(page.locator('.tab-button').filter({ hasText: '💬 Chat' })).toBeVisible();
      await expect(page.locator('.tab-button').filter({ hasText: '🔒 Steganography' })).toBeVisible();
      await expect(page.locator('.tab-button').filter({ hasText: '🤝 Handshake' })).toBeVisible();
      
      // Chat tab should be active by default
      await expect(page.locator('.tab-button.active').filter({ hasText: '💬 Chat' })).toBeVisible();
      
      // Click handshake tab
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Handshake tab should now be active
      await expect(page.locator('.tab-button.active').filter({ hasText: '🤝 Handshake' })).toBeVisible();
      
      // Chat layout should be hidden, handshake layout should be visible
      await expect(page.locator('.chat-layout')).not.toBeVisible();
      await expect(page.locator('.handshake-layout')).toBeVisible();
    });

    test('should display handshake demo interface', async ({ page }) => {
      await page.goto('/');
      
      // Navigate to handshake tab
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Check main handshake demo elements
      await expect(page.locator('h2').filter({ hasText: '🤝 OTR Handshake Demo' })).toBeVisible();
      
      // Check status panels
      await expect(page.locator('h3').filter({ hasText: '👩‍💻 Alice Status' })).toBeVisible();
      await expect(page.locator('h3').filter({ hasText: '👨‍💻 Bob Status' })).toBeVisible();
      
      // Check control buttons
      await expect(page.locator('button').filter({ hasText: '🤝 Start Handshake' })).toBeVisible();
      await expect(page.locator('button').filter({ hasText: '📊 Performance Test' })).toBeVisible();
      await expect(page.locator('button').filter({ hasText: '🔄 Reset Demo' })).toBeVisible();
      
      // Check activity log
      await expect(page.locator('h3').filter({ hasText: '📋 Activity Log' })).toBeVisible();
      await expect(page.locator('text=Handshake demo initialized')).toBeVisible();
    });

    test('should display initial session status', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Check Alice status
      await expect(page.locator('text=State: PLAINTEXT')).toBeVisible();
      await expect(page.locator('text=Messages: 0')).toBeVisible();
      await expect(page.locator('text=Encrypted: ❌')).toBeVisible();
      
      // Check instance tags are displayed
      await expect(page.locator('text=Instance Tag:')).toHaveCount(2); // Alice and Bob
    });
  });

  test.describe('Basic Handshake Functionality', () => {
    test('should perform complete handshake successfully', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Start handshake
      await page.locator('button').filter({ hasText: '🤝 Start Handshake' }).click();
      
      // Button should change to processing state
      await expect(page.locator('button').filter({ hasText: '⏳ Handshaking...' })).toBeVisible();
      
      // Wait for handshake to complete
      await expect(page.locator('text=Handshake completed successfully')).toBeVisible({ timeout: 10000 });
      
      // Check that both sessions are now encrypted
      await expect(page.locator('text=State: ENCRYPTED')).toHaveCount(2);
      await expect(page.locator('text=Encrypted: ✅')).toHaveCount(2);
      
      // Button should return to normal state
      await expect(page.locator('button').filter({ hasText: '🤝 Start Handshake' })).toBeVisible();
    });

    test('should display handshake progress steps', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Start handshake
      await page.locator('button').filter({ hasText: '🤝 Start Handshake' }).click();
      
      // Wait for progress section to appear
      await expect(page.locator('h3').filter({ hasText: '🔄 Handshake Progress' })).toBeVisible({ timeout: 5000 });
      
      // Check for handshake steps
      await expect(page.locator('text=DH COMMIT SENT')).toBeVisible();
      await expect(page.locator('text=DH KEY RECEIVED')).toBeVisible();
      await expect(page.locator('text=REVEAL SIGNATURE SENT')).toBeVisible();
      await expect(page.locator('text=SIGNATURE RECEIVED')).toBeVisible();
      await expect(page.locator('text=HANDSHAKE COMPLETED')).toBeVisible();
      
      // Check that steps have icons
      await expect(page.locator('text=🔑')).toBeVisible(); // DH Commit icon
      await expect(page.locator('text=🔒')).toBeVisible(); // Handshake completed icon
    });

    test('should show performance metrics after handshake', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Start handshake
      await page.locator('button').filter({ hasText: '🤝 Start Handshake' }).click();
      
      // Wait for completion
      await expect(page.locator('text=Handshake completed successfully')).toBeVisible({ timeout: 10000 });
      
      // Check performance metrics section
      await expect(page.locator('h3').filter({ hasText: '📈 Performance Metrics' })).toBeVisible();
      await expect(page.locator('text=Total Duration:')).toBeVisible();
      await expect(page.locator('text=Messages/Second:')).toBeVisible();
      await expect(page.locator('text=Avg Step Time:')).toBeVisible();
      
      // Check that metrics have reasonable values
      await expect(page.locator('text=/\\d+ms/')).toBeVisible(); // Duration in milliseconds
    });

    test('should update status panels during handshake', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Record initial state
      const initialAliceState = await page.locator('h3').filter({ hasText: '👩‍💻 Alice Status' }).locator('..').locator('text=State:').textContent();
      
      // Start handshake
      await page.locator('button').filter({ hasText: '🤝 Start Handshake' }).click();
      
      // Wait for state changes
      await expect(page.locator('text=State: ENCRYPTED')).toBeVisible({ timeout: 10000 });
      
      // Check that message counts increased
      await expect(page.locator('text=/Messages: [1-9]/')).toBeVisible(); // At least 1 message
      
      // Check that status panel borders changed color (indicating state change)
      const alicePanel = page.locator('h3').filter({ hasText: '👩‍💻 Alice Status' }).locator('..');
      await expect(alicePanel).toHaveCSS('border-color', /.+/); // Should have some border color
    });
  });

  test.describe('Performance Testing', () => {
    test('should run performance test and display results', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Run performance test
      await page.locator('button').filter({ hasText: '📊 Performance Test' }).click();
      
      // Button should show processing state
      await expect(page.locator('text=⏳ Handshaking...')).toBeVisible();
      
      // Wait for test to complete
      await expect(page.locator('text=Performance test completed:')).toBeVisible({ timeout: 15000 });
      
      // Check that results are displayed
      await expect(page.locator('text=Average duration:')).toBeVisible();
      await expect(page.locator('text=Success rate:')).toBeVisible();
      await expect(page.locator('text=Tests completed:')).toBeVisible();
      
      // Success rate should be 100%
      await expect(page.locator('text=Success rate: 100.0%')).toBeVisible();
    });

    test('should handle performance test button states', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Initially, performance test button should be enabled
      await expect(page.locator('button').filter({ hasText: '📊 Performance Test' })).toBeEnabled();
      
      // Start performance test
      await page.locator('button').filter({ hasText: '📊 Performance Test' }).click();
      
      // During test, all buttons should be disabled
      await expect(page.locator('button').filter({ hasText: '🤝 Start Handshake' })).toBeDisabled();
      await expect(page.locator('button').filter({ hasText: '🔄 Reset Demo' })).toBeDisabled();
      
      // Wait for completion
      await expect(page.locator('text=Performance test completed:')).toBeVisible({ timeout: 15000 });
      
      // Buttons should be enabled again
      await expect(page.locator('button').filter({ hasText: '🤝 Start Handshake' })).toBeEnabled();
      await expect(page.locator('button').filter({ hasText: '📊 Performance Test' })).toBeEnabled();
    });
  });

  test.describe('Demo Reset Functionality', () => {
    test('should reset demo state properly', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Perform a handshake first
      await page.locator('button').filter({ hasText: '🤝 Start Handshake' }).click();
      await expect(page.locator('text=Handshake completed successfully')).toBeVisible({ timeout: 10000 });
      
      // Verify encrypted state
      await expect(page.locator('text=State: ENCRYPTED')).toHaveCount(2);
      
      // Reset demo
      await page.locator('button').filter({ hasText: '🔄 Reset Demo' }).click();
      
      // Check that state is reset
      await expect(page.locator('text=Demo reset')).toBeVisible();
      await expect(page.locator('text=State: PLAINTEXT')).toHaveCount(2);
      await expect(page.locator('text=Messages: 0')).toHaveCount(2);
      await expect(page.locator('text=Encrypted: ❌')).toHaveCount(2);
      
      // Progress section should be hidden
      await expect(page.locator('h3').filter({ hasText: '🔄 Handshake Progress' })).not.toBeVisible();
      
      // Performance metrics should be hidden
      await expect(page.locator('h3').filter({ hasText: '📈 Performance Metrics' })).not.toBeVisible();
    });

    test('should generate new instance tags on reset', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Get initial instance tags
      const initialAliceTag = await page.locator('h3').filter({ hasText: '👩‍💻 Alice Status' }).locator('..').locator('text=Instance Tag:').textContent();
      const initialBobTag = await page.locator('h3').filter({ hasText: '👨‍💻 Bob Status' }).locator('..').locator('text=Instance Tag:').textContent();
      
      // Reset demo
      await page.locator('button').filter({ hasText: '🔄 Reset Demo' }).click();
      await expect(page.locator('text=Demo reset')).toBeVisible();
      
      // Get new instance tags
      const newAliceTag = await page.locator('h3').filter({ hasText: '👩‍💻 Alice Status' }).locator('..').locator('text=Instance Tag:').textContent();
      const newBobTag = await page.locator('h3').filter({ hasText: '👨‍💻 Bob Status' }).locator('..').locator('text=Instance Tag:').textContent();
      
      // Instance tags should be different
      expect(newAliceTag).not.toBe(initialAliceTag);
      expect(newBobTag).not.toBe(initialBobTag);
    });
  });

  test.describe('Activity Logging', () => {
    test('should log all handshake activities with timestamps', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Check initial log entries
      await expect(page.locator('text=Handshake demo initialized')).toBeVisible();
      await expect(page.locator('text=Alice instance tag:')).toBeVisible();
      await expect(page.locator('text=Bob instance tag:')).toBeVisible();
      
      // Start handshake and check logging
      await page.locator('button').filter({ hasText: '🤝 Start Handshake' }).click();
      
      await expect(page.locator('text=Starting OTR handshake...')).toBeVisible();
      await expect(page.locator('text=Alice: DH COMMIT SENT')).toBeVisible();
      await expect(page.locator('text=Bob: DH KEY RECEIVED')).toBeVisible();
      
      // Wait for completion
      await expect(page.locator('text=Handshake completed successfully')).toBeVisible({ timeout: 10000 });
      
      // Check that all log entries have timestamps
      const logEntries = await page.locator('.activity-log div').allTextContents();
      for (const entry of logEntries) {
        if (entry.trim() && !entry.includes('Processing')) {
          expect(entry).toMatch(/^\[\d{1,2}:\d{2}:\d{2}\]/); // Timestamp format [HH:MM:SS]
        }
      }
    });

    test('should show processing indicator during handshake', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Start handshake
      await page.locator('button').filter({ hasText: '🤝 Start Handshake' }).click();
      
      // Should show processing indicator
      await expect(page.locator('text=⏳ Processing handshake...')).toBeVisible();
      
      // Wait for completion
      await expect(page.locator('text=Handshake completed successfully')).toBeVisible({ timeout: 10000 });
      
      // Processing indicator should disappear
      await expect(page.locator('text=⏳ Processing handshake...')).not.toBeVisible();
    });
  });

  test.describe('Error Handling and Edge Cases', () => {
    test('should handle rapid button clicks gracefully', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Click start handshake multiple times rapidly
      const startButton = page.locator('button').filter({ hasText: '🤝 Start Handshake' });
      await startButton.click();
      await startButton.click(); // Second click should be ignored
      await startButton.click(); // Third click should be ignored
      
      // Should only see one handshake process
      await expect(page.locator('text=Starting OTR handshake...')).toHaveCount(1);
      
      // Wait for completion
      await expect(page.locator('text=Handshake completed successfully')).toBeVisible({ timeout: 10000 });
    });

    test('should handle reset during handshake', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Start handshake
      await page.locator('button').filter({ hasText: '🤝 Start Handshake' }).click();
      
      // Wait a moment then reset (reset should be disabled during handshake)
      await page.waitForTimeout(100);
      
      // Reset button should be disabled during handshake
      await expect(page.locator('button').filter({ hasText: '🔄 Reset Demo' })).toBeDisabled();
      
      // Wait for handshake to complete
      await expect(page.locator('text=Handshake completed successfully')).toBeVisible({ timeout: 10000 });
      
      // Now reset should work
      await expect(page.locator('button').filter({ hasText: '🔄 Reset Demo' })).toBeEnabled();
      await page.locator('button').filter({ hasText: '🔄 Reset Demo' }).click();
      await expect(page.locator('text=Demo reset')).toBeVisible();
    });
  });

  test.describe('Responsive Design and Accessibility', () => {
    test('should work on different screen sizes', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Test on mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Main elements should still be visible
      await expect(page.locator('h2').filter({ hasText: '🤝 OTR Handshake Demo' })).toBeVisible();
      await expect(page.locator('button').filter({ hasText: '🤝 Start Handshake' })).toBeVisible();
      
      // Test on tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // Status panels should be visible
      await expect(page.locator('h3').filter({ hasText: '👩‍💻 Alice Status' })).toBeVisible();
      await expect(page.locator('h3').filter({ hasText: '👨‍💻 Bob Status' })).toBeVisible();
      
      // Test on desktop viewport
      await page.setViewportSize({ width: 1280, height: 720 });
      
      // All elements should be visible in full layout
      await expect(page.locator('.handshake-layout')).toBeVisible();
    });

    test('should have proper keyboard navigation', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Tab through interactive elements
      await page.keyboard.press('Tab'); // Should focus on start handshake button
      await expect(page.locator('button').filter({ hasText: '🤝 Start Handshake' })).toBeFocused();
      
      await page.keyboard.press('Tab'); // Should focus on performance test button
      await expect(page.locator('button').filter({ hasText: '📊 Performance Test' })).toBeFocused();
      
      await page.keyboard.press('Tab'); // Should focus on reset button
      await expect(page.locator('button').filter({ hasText: '🔄 Reset Demo' })).toBeFocused();
      
      // Enter key should activate focused button
      await page.keyboard.press('Enter');
      await expect(page.locator('text=Demo reset')).toBeVisible();
    });

    test('should display status with proper color coding', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🤝 Handshake' }).click();
      
      // Initial state should have appropriate styling
      const alicePanel = page.locator('h3').filter({ hasText: '👩‍💻 Alice Status' }).locator('..');
      
      // Start handshake to change state
      await page.locator('button').filter({ hasText: '🤝 Start Handshake' }).click();
      
      // Wait for encrypted state
      await expect(page.locator('text=State: ENCRYPTED')).toBeVisible({ timeout: 10000 });
      
      // Panel should have different border color for encrypted state
      await expect(alicePanel).toHaveCSS('border-color', /.+/);
    });
  });
});
