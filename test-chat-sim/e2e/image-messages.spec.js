const { test, expect } = require('@playwright/test');

test.describe('Image Messages', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // Clear localStorage to start fresh
    await page.evaluate(() => localStorage.clear());
  });

  test('network panel shows emoji for image messages', async ({ page }) => {
    // Since we can't easily simulate drag and drop in <PERSON><PERSON>,
    // we'll inject a test image message directly
    await page.evaluate(() => {
      // Create a mock image message
      const imageMessage = {
        id: `img_${Date.now()}`,
        sender: 'alice',
        recipient: 'bob',
        content: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==',
        timestamp: new Date().toISOString(),
        status: 'delivered',
        type: 'image'
      };
      
      // Get the App's handleSendMessage function by calling it manually
      if (window.app && window.app.handleSendMessage) {
        window.app.handleSendMessage(
          imageMessage.sender, 
          imageMessage.content, 
          imageMessage.type
        );
      } else {
        // Fallback: manually add the message to localStorage
        const messages = JSON.parse(localStorage.getItem('chat_messages') || '[]');
        messages.push(imageMessage);
        localStorage.setItem('chat_messages', JSON.stringify(messages));
        
        // Force reload to show the messages
        window.location.reload();
      }
    });
    
    // Wait for the network panel to update
    await page.waitForTimeout(500);
    
    // Check if the image emoji is visible in the network panel
    const networkPanel = page.locator('.network-panel');
    await expect(networkPanel).toContainText('Type: image');
    
    // Look for the emoji in the network panel
    const emojiLocator = networkPanel.locator('text=🖼️');
    await expect(emojiLocator).toBeVisible();
    
    // Check that the [Image] text is also visible
    await expect(networkPanel).toContainText('[Image]');
  });
}); 