/**
 * Steganography UX End-to-End Tests
 * Comprehensive testing of steganography user experience in the test app
 */

const { test, expect } = require('@playwright/test');

test.describe('Steganography UX Experience', () => {
  // Reset localStorage before each test
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.evaluate(() => localStorage.clear());
    await page.reload();
  });

  test.describe('Tab Navigation and Interface', () => {
    test('should display steganography tab and allow navigation', async ({ page }) => {
      await page.goto('/');
      
      // Check that both tabs are visible
      await expect(page.locator('.tab-button').filter({ hasText: '💬 Chat' })).toBeVisible();
      await expect(page.locator('.tab-button').filter({ hasText: '🔒 Steganography' })).toBeVisible();
      
      // Chat tab should be active by default
      await expect(page.locator('.tab-button.active').filter({ hasText: '💬 Chat' })).toBeVisible();
      
      // Click steganography tab
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Steganography tab should now be active
      await expect(page.locator('.tab-button.active').filter({ hasText: '🔒 Steganography' })).toBeVisible();
      
      // Chat layout should be hidden, steganography layout should be visible
      await expect(page.locator('.chat-layout')).not.toBeVisible();
      await expect(page.locator('.steganography-layout')).toBeVisible();
    });

    test('should display steganography demo interface', async ({ page }) => {
      await page.goto('/');
      
      // Navigate to steganography tab
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Check main steganography demo elements
      await expect(page.locator('h2').filter({ hasText: '🔒 Steganography Demo' })).toBeVisible();
      
      // Check status panel
      await expect(page.locator('h3').filter({ hasText: 'Status' })).toBeVisible();
      await expect(page.locator('text=Steganography: ✅ Enabled')).toBeVisible();
      
      // Check message input section
      await expect(page.locator('h3').filter({ hasText: '1. Prepare Message' })).toBeVisible();
      await expect(page.locator('textarea[placeholder*="Enter your secret message"]')).toBeVisible();
      
      // Check cover image generation section
      await expect(page.locator('h3').filter({ hasText: '2. Generate Cover Image' })).toBeVisible();
      await expect(page.locator('select')).toBeVisible();
      await expect(page.locator('button').filter({ hasText: 'Generate Cover Image' })).toBeVisible();
      
      // Check steganography actions section
      await expect(page.locator('h3').filter({ hasText: '3. Steganography Actions' })).toBeVisible();
      await expect(page.locator('button').filter({ hasText: 'Hide Message in Image' })).toBeVisible();
      await expect(page.locator('button').filter({ hasText: 'Reveal Hidden Message' })).toBeVisible();
      await expect(page.locator('button').filter({ hasText: 'Detect Hidden Message' })).toBeVisible();
      
      // Check activity log
      await expect(page.locator('h3').filter({ hasText: 'Activity Log' })).toBeVisible();
      await expect(page.locator('text=Steganography demo initialized')).toBeVisible();
    });
  });

  test.describe('Cover Image Generation', () => {
    test('should generate cover images with different styles', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      const styles = ['noise', 'gradient', 'pattern'];
      
      for (const style of styles) {
        // Select style
        await page.locator('select').selectOption(style);
        
        // Generate cover image
        await page.locator('button').filter({ hasText: 'Generate Cover Image' }).click();
        
        // Wait for processing
        await expect(page.locator('text=⏳ Processing...')).toBeVisible();
        await expect(page.locator('text=⏳ Processing...')).not.toBeVisible({ timeout: 5000 });
        
        // Check that cover image is displayed
        await expect(page.locator('h4').filter({ hasText: 'Cover Image' })).toBeVisible();
        await expect(page.locator('img[alt="Cover"]')).toBeVisible();
        
        // Check activity log
        await expect(page.locator(`text=Generating cover image with ${style} style...`)).toBeVisible();
        await expect(page.locator('text=Cover image generated successfully')).toBeVisible();
      }
    });

    test('should update button states correctly during generation', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Initially, hide message button should be disabled
      await expect(page.locator('button').filter({ hasText: 'Hide Message in Image' })).toBeDisabled();
      
      // Generate cover image
      await page.locator('button').filter({ hasText: 'Generate Cover Image' }).click();
      
      // During processing, button should be disabled
      await expect(page.locator('button').filter({ hasText: 'Generate Cover Image' })).toBeDisabled();
      
      // Wait for completion
      await expect(page.locator('text=Cover image generated successfully')).toBeVisible({ timeout: 5000 });
      
      // Button should be enabled again
      await expect(page.locator('button').filter({ hasText: 'Generate Cover Image' })).toBeEnabled();
    });
  });

  test.describe('Message Hiding and Revealing', () => {
    test('should hide and reveal messages successfully', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      const secretMessage = 'This is a secret message for testing!';
      
      // Enter secret message
      await page.locator('textarea[placeholder*="Enter your secret message"]').fill(secretMessage);
      
      // Generate cover image
      await page.locator('button').filter({ hasText: 'Generate Cover Image' }).click();
      await expect(page.locator('text=Cover image generated successfully')).toBeVisible({ timeout: 5000 });
      
      // Hide message button should now be enabled
      await expect(page.locator('button').filter({ hasText: 'Hide Message in Image' })).toBeEnabled();
      
      // Hide message in image
      await page.locator('button').filter({ hasText: 'Hide Message in Image' }).click();
      
      // Wait for processing
      await expect(page.locator('text=⏳ Processing...')).toBeVisible();
      await expect(page.locator('text=Message hidden successfully in image')).toBeVisible({ timeout: 5000 });
      
      // Check that stego image is displayed
      await expect(page.locator('h4').filter({ hasText: 'Stego Image' })).toBeVisible();
      await expect(page.locator('img[alt="Stego"]')).toBeVisible();
      
      // Reveal message button should now be enabled
      await expect(page.locator('button').filter({ hasText: 'Reveal Hidden Message' })).toBeEnabled();
      
      // Reveal hidden message
      await page.locator('button').filter({ hasText: 'Reveal Hidden Message' }).click();
      
      // Wait for processing and check result
      await expect(page.locator('text=Revealing hidden message...')).toBeVisible();
      await expect(page.locator('text=Message revealed successfully')).toBeVisible({ timeout: 5000 });
      
      // Check that revealed message matches original
      await expect(page.locator('h4').filter({ hasText: 'Revealed Message' })).toBeVisible();
      await expect(page.locator('text=' + secretMessage)).toBeVisible();
    });

    test('should handle different message lengths', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      const messages = [
        'Short',
        'Medium length message for testing steganography functionality',
        'Very long message that tests the capacity and performance of the steganography system. '.repeat(5)
      ];
      
      for (const message of messages) {
        // Clear previous state
        await page.locator('button').filter({ hasText: 'Clear Demo' }).click();
        await expect(page.locator('text=Demo cleared')).toBeVisible();
        
        // Enter message
        await page.locator('textarea[placeholder*="Enter your secret message"]').fill(message);
        
        // Generate cover image
        await page.locator('button').filter({ hasText: 'Generate Cover Image' }).click();
        await expect(page.locator('text=Cover image generated successfully')).toBeVisible({ timeout: 5000 });
        
        // Hide message
        await page.locator('button').filter({ hasText: 'Hide Message in Image' }).click();
        await expect(page.locator('text=Message hidden successfully in image')).toBeVisible({ timeout: 5000 });
        
        // Reveal message
        await page.locator('button').filter({ hasText: 'Reveal Hidden Message' }).click();
        await expect(page.locator('text=Message revealed successfully')).toBeVisible({ timeout: 5000 });
        
        // Verify message content (check first 20 characters for long messages)
        const expectedText = message.length > 50 ? message.substring(0, 20) : message;
        await expect(page.locator('text=' + expectedText)).toBeVisible();
      }
    });
  });

  test.describe('Message Detection', () => {
    test('should detect hidden messages correctly', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Enter message and generate cover image
      await page.locator('textarea[placeholder*="Enter your secret message"]').fill('Detection test message');
      await page.locator('button').filter({ hasText: 'Generate Cover Image' }).click();
      await expect(page.locator('text=Cover image generated successfully')).toBeVisible({ timeout: 5000 });
      
      // Hide message
      await page.locator('button').filter({ hasText: 'Hide Message in Image' }).click();
      await expect(page.locator('text=Message hidden successfully in image')).toBeVisible({ timeout: 5000 });
      
      // Detect hidden message
      await page.locator('button').filter({ hasText: 'Detect Hidden Message' }).click();
      
      // Should detect the hidden message
      await expect(page.locator('text=Detecting hidden message...')).toBeVisible();
      await expect(page.locator('text=Hidden message detected!')).toBeVisible({ timeout: 5000 });
    });
  });

  test.describe('Performance Testing', () => {
    test('should run performance tests and display results', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Run performance test
      await page.locator('button').filter({ hasText: 'Run Performance Test' }).click();
      
      // Wait for test to start
      await expect(page.locator('text=Running performance test...')).toBeVisible();
      
      // Wait for completion (may take several seconds)
      await expect(page.locator('text=Performance test completed:')).toBeVisible({ timeout: 15000 });
      
      // Check that results are displayed
      await expect(page.locator('text=Average processing time:')).toBeVisible();
      await expect(page.locator('text=Success rate:')).toBeVisible();
      
      // Results should show good performance
      await expect(page.locator('text=Success rate: 100.0%')).toBeVisible();
    });
  });

  test.describe('Activity Logging', () => {
    test('should log all activities with timestamps', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Check initial log entry
      await expect(page.locator('text=Steganography demo initialized')).toBeVisible();
      
      // Generate cover image and check log
      await page.locator('button').filter({ hasText: 'Generate Cover Image' }).click();
      await expect(page.locator('text=Generating cover image with noise style...')).toBeVisible();
      await expect(page.locator('text=Cover image generated successfully')).toBeVisible({ timeout: 5000 });
      
      // Enter message and hide it
      await page.locator('textarea[placeholder*="Enter your secret message"]').fill('Log test message');
      await page.locator('button').filter({ hasText: 'Hide Message in Image' }).click();
      await expect(page.locator('text=Hiding message: "Log test message"')).toBeVisible();
      await expect(page.locator('text=Message hidden successfully in image')).toBeVisible({ timeout: 5000 });
      
      // Check that all log entries have timestamps
      const logEntries = await page.locator('.activity-log div').allTextContents();
      for (const entry of logEntries) {
        if (entry.trim()) {
          expect(entry).toMatch(/^\[\d{1,2}:\d{2}:\d{2}\]/); // Timestamp format [HH:MM:SS]
        }
      }
    });

    test('should limit log entries and show processing indicator', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Generate multiple actions to fill the log
      for (let i = 0; i < 5; i++) {
        await page.locator('button').filter({ hasText: 'Generate Cover Image' }).click();
        await expect(page.locator('text=Cover image generated successfully')).toBeVisible({ timeout: 5000 });
      }
      
      // Start an action and check processing indicator
      await page.locator('textarea[placeholder*="Enter your secret message"]').fill('Processing test');
      await page.locator('button').filter({ hasText: 'Hide Message in Image' }).click();
      
      // Should show processing indicator
      await expect(page.locator('text=⏳ Processing...')).toBeVisible();
      
      // Wait for completion
      await expect(page.locator('text=Message hidden successfully in image')).toBeVisible({ timeout: 5000 });
      await expect(page.locator('text=⏳ Processing...')).not.toBeVisible();
    });
  });

  test.describe('Error Handling and Edge Cases', () => {
    test('should handle empty message gracefully', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Generate cover image
      await page.locator('button').filter({ hasText: 'Generate Cover Image' }).click();
      await expect(page.locator('text=Cover image generated successfully')).toBeVisible({ timeout: 5000 });
      
      // Try to hide empty message
      await page.locator('button').filter({ hasText: 'Hide Message in Image' }).click();
      
      // Should show error message
      await expect(page.locator('text=Please enter a message and generate a cover image first')).toBeVisible();
    });

    test('should handle missing cover image gracefully', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Enter message but don't generate cover image
      await page.locator('textarea[placeholder*="Enter your secret message"]').fill('Test message');
      
      // Try to hide message without cover image
      await page.locator('button').filter({ hasText: 'Hide Message in Image' }).click();
      
      // Should show error message
      await expect(page.locator('text=Please enter a message and generate a cover image first')).toBeVisible();
    });

    test('should handle reveal without stego image gracefully', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Try to reveal message without stego image
      await page.locator('button').filter({ hasText: 'Reveal Hidden Message' }).click();
      
      // Should show error message
      await expect(page.locator('text=Please hide a message first')).toBeVisible();
    });

    test('should clear demo state properly', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Set up some state
      await page.locator('textarea[placeholder*="Enter your secret message"]').fill('Clear test message');
      await page.locator('button').filter({ hasText: 'Generate Cover Image' }).click();
      await expect(page.locator('text=Cover image generated successfully')).toBeVisible({ timeout: 5000 });
      
      // Clear demo
      await page.locator('button').filter({ hasText: 'Clear Demo' }).click();
      
      // Check that state is cleared
      await expect(page.locator('text=Demo cleared')).toBeVisible();
      await expect(page.locator('textarea[placeholder*="Enter your secret message"]')).toHaveValue('');
      await expect(page.locator('text=No cover image')).toBeVisible();
      await expect(page.locator('text=No stego image')).toBeVisible();
      await expect(page.locator('text=No message revealed')).toBeVisible();
    });
  });

  test.describe('Responsive Design and Accessibility', () => {
    test('should work on different screen sizes', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Test on mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Main elements should still be visible
      await expect(page.locator('h2').filter({ hasText: '🔒 Steganography Demo' })).toBeVisible();
      await expect(page.locator('textarea[placeholder*="Enter your secret message"]')).toBeVisible();
      await expect(page.locator('button').filter({ hasText: 'Generate Cover Image' })).toBeVisible();
      
      // Test on tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // Elements should still be accessible
      await expect(page.locator('h3').filter({ hasText: 'Status' })).toBeVisible();
      await expect(page.locator('h3').filter({ hasText: 'Activity Log' })).toBeVisible();
      
      // Test on desktop viewport
      await page.setViewportSize({ width: 1280, height: 720 });
      
      // All elements should be visible in full layout
      await expect(page.locator('.steganography-layout')).toBeVisible();
    });

    test('should have proper keyboard navigation', async ({ page }) => {
      await page.goto('/');
      await page.locator('.tab-button').filter({ hasText: '🔒 Steganography' }).click();
      
      // Tab through interactive elements
      await page.keyboard.press('Tab'); // Should focus on textarea
      await expect(page.locator('textarea[placeholder*="Enter your secret message"]')).toBeFocused();
      
      await page.keyboard.press('Tab'); // Should focus on select
      await expect(page.locator('select')).toBeFocused();
      
      await page.keyboard.press('Tab'); // Should focus on generate button
      await expect(page.locator('button').filter({ hasText: 'Generate Cover Image' })).toBeFocused();
      
      // Enter key should activate focused button
      await page.keyboard.press('Enter');
      await expect(page.locator('text=Generating cover image')).toBeVisible();
    });
  });
});
