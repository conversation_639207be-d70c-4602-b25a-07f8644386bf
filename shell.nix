{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    nodejs_20
    # System dependencies for Playwright
    icu
    libxml2
    sqlite
    libxslt
    lcms2
    libevent
    opus
    libwebp
    enchant
    libtasn1
    hyphen
    pcre2
    libpsl
    libnghttp2
    libgudev
    libffi
    libevdev
    json-glib
    gnutls
    x264
  ];

  shellHook = ''
    export PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
    export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
  '';
} 