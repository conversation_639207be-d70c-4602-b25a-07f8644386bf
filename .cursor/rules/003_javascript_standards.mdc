# JavaScript/TypeScript Coding Standards

## General Style

1. Use 2 spaces for indentation
2. Use camelCase for variable and function names
3. Use PascalCase for class names and React components
4. Use UPPER_SNAKE_CASE for constants
5. Keep line length under 100 characters
6. Use semicolons at the end of statements
7. Use single quotes for strings
8. Add trailing commas for multi-line arrays and objects
9. Use template literals for string interpolation
10. Use destructuring assignment when appropriate

## TypeScript

1. Use explicit types for function parameters and return values
2. Use interfaces for object shapes
3. Use enums for fixed sets of values
4. Use union types for variables that can have multiple types
5. Use optional parameters and properties when appropriate
6. Use readonly for immutable properties
7. Use type guards to narrow types

Example:

```typescript
interface User {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'user';
  metadata?: Record<string, unknown>;
}

function getUserDisplayName(user: User): string {
  return `${user.name} (${user.role})`;
}
```

## React Components

1. Use functional components with hooks
2. Define prop types using TypeScript interfaces
3. Use destructuring for props
4. Keep components small and focused
5. Use meaningful component and prop names
6. Extract reusable logic into custom hooks
7. Use React.memo for performance optimization when appropriate

Example:

```tsx
interface UserProfileProps {
  user: User;
  onUpdate: (user: User) => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ user, onUpdate }) => {
  const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate({ ...user, name: event.target.value });
  };

  return (
    <div className="user-profile">
      <h2>{user.name}</h2>
      <input
        type="text"
        value={user.name}
        onChange={handleNameChange}
      />
    </div>
  );
};
```

## Testing

1. Write unit tests using Jest
2. Use React Testing Library for component tests
3. Test component behavior, not implementation details
4. Mock external dependencies
5. Use snapshot tests sparingly 