#!/bin/bash

# DevContainer Test Script
# Tests that the development environment is working correctly

set -e

echo "🧪 Testing DevContainer Setup"
echo "============================="

# Function to print colored output
print_status() {
    echo -e "\033[1;34m[TEST]\033[0m $1"
}

print_success() {
    echo -e "\033[1;32m[PASS]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[FAIL]\033[0m $1"
}

# Test 1: Node.js version
print_status "Checking Node.js version..."
NODE_VERSION=$(node --version)
if [[ $NODE_VERSION == v20* ]]; then
    print_success "Node.js version: $NODE_VERSION"
else
    print_error "Expected Node.js v20.x, got: $NODE_VERSION"
    exit 1
fi

# Test 2: npm is available
print_status "Checking npm..."
NPM_VERSION=$(npm --version)
print_success "npm version: $NPM_VERSION"

# Test 3: Dependencies are installed
print_status "Checking main project dependencies..."
if [ -d "node_modules" ]; then
    print_success "Main project dependencies installed"
else
    print_error "Main project dependencies not found"
    exit 1
fi

# Test 4: Test chat sim dependencies
print_status "Checking test-chat-sim dependencies..."
if [ -d "test-chat-sim/node_modules" ]; then
    print_success "Test-chat-sim dependencies installed"
else
    print_error "Test-chat-sim dependencies not found"
    exit 1
fi

# Test 5: Playwright is available
print_status "Checking Playwright..."
if npx playwright --version > /dev/null 2>&1; then
    PLAYWRIGHT_VERSION=$(npx playwright --version)
    print_success "Playwright available: $PLAYWRIGHT_VERSION"
else
    print_error "Playwright not available"
    exit 1
fi

# Test 6: Jest is available
print_status "Checking Jest..."
if npx jest --version > /dev/null 2>&1; then
    JEST_VERSION=$(npx jest --version)
    print_success "Jest available: $JEST_VERSION"
else
    print_error "Jest not available"
    exit 1
fi

# Test 7: Build system works
print_status "Testing build system..."
if npm run build > /dev/null 2>&1; then
    print_success "Build system working"
else
    print_error "Build system failed"
    exit 1
fi

# Test 8: Unit tests can run
print_status "Testing unit test runner..."
if npm test -- --passWithNoTests --silent > /dev/null 2>&1; then
    print_success "Unit test runner working"
else
    print_error "Unit test runner failed"
    exit 1
fi

# Test 9: ESLint is available
print_status "Checking ESLint..."
if npx eslint --version > /dev/null 2>&1; then
    ESLINT_VERSION=$(npx eslint --version)
    print_success "ESLint available: $ESLINT_VERSION"
else
    print_error "ESLint not available"
    exit 1
fi

# Test 10: Git is available
print_status "Checking Git..."
if git --version > /dev/null 2>&1; then
    GIT_VERSION=$(git --version)
    print_success "Git available: $GIT_VERSION"
else
    print_error "Git not available"
    exit 1
fi

# Test 11: Check if we can start the test chat simulator
print_status "Testing test-chat-sim build..."
cd test-chat-sim
if npm run build > /dev/null 2>&1; then
    print_success "Test-chat-sim build working"
else
    print_error "Test-chat-sim build failed"
    cd ..
    exit 1
fi
cd ..

# Test 12: Check browser dependencies for Playwright
print_status "Checking Playwright browser dependencies..."
if npx playwright install --dry-run > /dev/null 2>&1; then
    print_success "Playwright browser dependencies available"
else
    print_error "Playwright browser dependencies missing"
    exit 1
fi

# Test 13: Check system dependencies
print_status "Checking system dependencies..."
MISSING_DEPS=()

# Check for essential system packages
if ! command -v xvfb-run &> /dev/null; then
    MISSING_DEPS+=("xvfb")
fi

if ! ldconfig -p | grep -q libnss3; then
    MISSING_DEPS+=("libnss3")
fi

if ! ldconfig -p | grep -q libgtk-3; then
    MISSING_DEPS+=("libgtk-3")
fi

if [ ${#MISSING_DEPS[@]} -eq 0 ]; then
    print_success "System dependencies available"
else
    print_error "Missing system dependencies: ${MISSING_DEPS[*]}"
    exit 1
fi

# Test 14: Check workspace permissions
print_status "Checking workspace permissions..."
if [ -w "." ]; then
    print_success "Workspace is writable"
else
    print_error "Workspace is not writable"
    exit 1
fi

# Test 15: Check environment variables
print_status "Checking environment variables..."
if [ "$NODE_ENV" = "development" ]; then
    print_success "NODE_ENV set correctly: $NODE_ENV"
else
    print_error "NODE_ENV not set correctly. Expected: development, Got: $NODE_ENV"
    exit 1
fi

echo ""
echo "🎉 All DevContainer tests passed!"
echo ""
echo "Your development environment is ready for:"
echo "  ✅ Node.js development"
echo "  ✅ React development"
echo "  ✅ Unit testing with Jest"
echo "  ✅ End-to-end testing with Playwright"
echo "  ✅ Code linting with ESLint"
echo "  ✅ Git version control"
echo "  ✅ Browser automation"
echo ""
echo "Quick start commands:"
echo "  npm start          - Start development server"
echo "  npm test           - Run unit tests"
echo "  npm run test:e2e   - Run end-to-end tests"
echo "  cd test-chat-sim && npm start - Start test chat simulator"
echo ""
echo "Happy coding! 🚀"
