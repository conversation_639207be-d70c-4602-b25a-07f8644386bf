#!/usr/bin/env node

/**
 * Store Submission Preparation Script
 * 
 * Prepares WebOTR extension packages for submission to various browser stores:
 * - Chrome Web Store
 * - Firefox Add-ons (AMO)
 * - Safari Extensions
 * - Edge Add-ons
 */

const fs = require('fs').promises;
const path = require('path');
const archiver = require('archiver');

const STORES = {
  chrome: {
    name: 'Chrome Web Store',
    manifestVersion: 3,
    outputDir: 'store-packages/chrome',
    zipName: 'webOTR-chrome.zip'
  },
  firefox: {
    name: 'Firefox Add-ons',
    manifestVersion: 2, // Firefox still supports V2
    outputDir: 'store-packages/firefox',
    zipName: 'webOTR-firefox.zip'
  },
  safari: {
    name: 'Safari Extensions',
    manifestVersion: 2,
    outputDir: 'store-packages/safari',
    zipName: 'webOTR-safari.zip'
  },
  edge: {
    name: 'Edge Add-ons',
    manifestVersion: 3,
    outputDir: 'store-packages/edge',
    zipName: 'webOTR-edge.zip'
  }
};

class StorePreparation {
  constructor() {
    this.baseDir = process.cwd();
    this.distDir = path.join(this.baseDir, 'dist');
    this.storeDir = path.join(this.baseDir, 'store-packages');
  }

  async prepare() {
    console.log('🏪 Preparing WebOTR for browser store submissions...\n');

    // Ensure store packages directory exists
    await this.ensureDirectory(this.storeDir);

    // Prepare each store package
    for (const [storeId, config] of Object.entries(STORES)) {
      await this.prepareStore(storeId, config);
    }

    console.log('\n✅ All store packages prepared successfully!');
    console.log('\n📦 Package locations:');
    for (const [storeId, config] of Object.entries(STORES)) {
      console.log(`   ${config.name}: ${config.outputDir}/${config.zipName}`);
    }
  }

  async prepareStore(storeId, config) {
    console.log(`📦 Preparing ${config.name} package...`);

    const outputDir = path.join(this.baseDir, config.outputDir);
    await this.ensureDirectory(outputDir);

    // Copy base extension files
    await this.copyExtensionFiles(outputDir);

    // Customize manifest for store
    await this.customizeManifest(outputDir, config);

    // Add store-specific files
    await this.addStoreSpecificFiles(storeId, outputDir);

    // Create submission package
    await this.createZipPackage(outputDir, config.zipName);

    console.log(`   ✅ ${config.name} package ready`);
  }

  async copyExtensionFiles(outputDir) {
    // Copy all files from extension directory
    await this.copyDirectory(path.join(this.baseDir, 'extension'), outputDir);
  }

  async copyDirectory(src, dest) {
    await fs.mkdir(dest, { recursive: true });
    const entries = await fs.readdir(src, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);

      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  async customizeManifest(outputDir, config) {
    const manifestPath = path.join(outputDir, 'manifest.json');

    // Use store-specific manifest if available
    const storeManifestPath = path.join(this.baseDir, 'extension/store-submissions', config.name, 'manifest.json');

    try {
      await fs.access(storeManifestPath);
      await fs.copyFile(storeManifestPath, manifestPath);
      console.log(`   Using ${config.name}-specific manifest`);
    } catch (error) {
      // Fallback to base manifest with modifications
      const baseManifest = JSON.parse(await fs.readFile(path.join(this.baseDir, 'extension/manifest.json'), 'utf8'));

      // Apply store-specific modifications
      if (config.manifestVersion === 2) {
        // Convert Manifest V3 to V2 for Firefox
        baseManifest.manifest_version = 2;
        baseManifest.browser_action = baseManifest.action;
        delete baseManifest.action;

        if (baseManifest.background?.service_worker) {
          baseManifest.background = {
            scripts: [baseManifest.background.service_worker],
            persistent: false
          };
        }

        // Move host_permissions to permissions for V2
        if (baseManifest.host_permissions) {
          baseManifest.permissions = [...(baseManifest.permissions || []), ...baseManifest.host_permissions];
          delete baseManifest.host_permissions;
        }
      }

      await fs.writeFile(manifestPath, JSON.stringify(baseManifest, null, 2));
    }
  }

  async addStoreSpecificFiles(storeId, outputDir) {
    const storeDir = path.join(this.baseDir, 'extension/store-submissions', storeId);

    try {
      await fs.access(storeDir);

      // Copy store-specific files
      const entries = await fs.readdir(storeDir, { withFileTypes: true });

      for (const entry of entries) {
        if (entry.name !== 'manifest.json') { // Skip manifest, already handled
          const srcPath = path.join(storeDir, entry.name);
          const destPath = path.join(outputDir, entry.name);

          if (entry.isDirectory()) {
            await this.copyDirectory(srcPath, destPath);
          } else {
            await fs.copyFile(srcPath, destPath);
          }
        }
      }

    } catch (error) {
      // No store-specific files, that's okay
    }
  }

  async createZipPackage(outputDir, zipName) {
    const zipPath = path.join(outputDir, zipName);
    const output = require('fs').createWriteStream(zipPath);
    const archive = archiver('zip', { zlib: { level: 9 } });

    return new Promise((resolve, reject) => {
      output.on('close', () => {
        console.log(`   Package created: ${zipName} (${archive.pointer()} bytes)`);
        resolve();
      });

      archive.on('error', reject);
      archive.pipe(output);

      // Add all files except the zip itself
      archive.glob('**/*', {
        cwd: outputDir,
        ignore: [zipName]
      });

      archive.finalize();
    });
  }

  async ensureDirectory(dir) {
    await fs.mkdir(dir, { recursive: true });
  }

  async customizeManifest(outputDir, config) {
    const manifestPath = path.join(outputDir, 'manifest.json');
    const manifest = JSON.parse(await fs.readFile(manifestPath, 'utf8'));

    // Set manifest version
    manifest.manifest_version = config.manifestVersion;

    // Store-specific customizations
    if (config.manifestVersion === 2) {
      // Convert Manifest V3 to V2 for Firefox/Safari
      await this.convertToManifestV2(manifest);
    }

    await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2));
  }

  async convertToManifestV2(manifest) {
    // Convert service worker to background scripts
    if (manifest.background && manifest.background.service_worker) {
      manifest.background = {
        scripts: [manifest.background.service_worker],
        persistent: false
      };
    }

    // Convert action to browser_action
    if (manifest.action) {
      manifest.browser_action = manifest.action;
      delete manifest.action;
    }

    // Adjust host permissions
    if (manifest.host_permissions) {
      manifest.permissions = manifest.permissions || [];
      manifest.permissions.push(...manifest.host_permissions);
      delete manifest.host_permissions;
    }
  }

  async addStoreSpecificFiles(storeId, outputDir) {
    const storeAssetsDir = path.join(this.baseDir, 'store-assets', storeId);
    
    try {
      await this.copyDirectory(storeAssetsDir, outputDir);
    } catch (error) {
      // Store assets directory might not exist, that's okay
      console.log(`   ℹ️  No store-specific assets found for ${storeId}`);
    }
  }

  async createZipPackage(sourceDir, zipName) {
    const zipPath = path.join(sourceDir, zipName);
    const output = require('fs').createWriteStream(zipPath);
    const archive = archiver('zip', { zlib: { level: 9 } });

    return new Promise((resolve, reject) => {
      output.on('close', resolve);
      archive.on('error', reject);

      archive.pipe(output);
      archive.directory(sourceDir, false, (entry) => {
        // Exclude the zip file itself
        return entry.name !== zipName;
      });
      archive.finalize();
    });
  }

  async copyDirectory(src, dest) {
    await this.ensureDirectory(dest);
    const entries = await fs.readdir(src, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);

      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  async ensureDirectory(dir) {
    try {
      await fs.mkdir(dir, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  }
}

// Run if called directly
if (require.main === module) {
  const preparation = new StorePreparation();
  preparation.prepare().catch(error => {
    console.error('❌ Store preparation failed:', error);
    process.exit(1);
  });
}

module.exports = StorePreparation;
