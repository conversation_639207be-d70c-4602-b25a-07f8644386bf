/**
 * WebOTR Development Environment Setup Script
 * 
 * This script prepares the development environment by:
 * 
 * 1. Creating any missing required directories in the src/ui structure
 * 2. Creating stub implementation files for UI components if they don't exist
 * 3. Creating SVG icon files in the icons directory to fix glob pattern errors
 * 
 * The goal is to make it easy for developers to get started without having to
 * manually create all the necessary files and directory structure.
 * 
 * This is especially useful for new contributors or when pulling updates that
 * may include new component dependencies.
 * 
 * Usage: 
 *   node scripts/setup.js
 * 
 * Or simply run:
 *   npm start
 */

const fs = require('fs');
const path = require('path');

// Define paths that need to exist
const requiredPaths = [
  'src/ui',
  'src/ui/components',
  'src/ui/components/toggle',
  'src/ui/components/status',
  'src/ui/dialogs',
  'src/ui/dialogs/verification',
  'src/ui/notifications',
  'src/ui/icons'
];

// Define stub files to create if missing
const stubFiles = {
  'src/ui/index.js': `/**
 * UI Components for WebOTR
 * 
 * This file exports UI components used by the browser extensions.
 */

import { OtrToggle } from './components/toggle';
import { OtrStatus } from './components/status';
import { VerificationDialog } from './dialogs/verification';
import { Notifications } from './notifications';

export {
  OtrToggle,
  OtrStatus,
  VerificationDialog,
  Notifications
};
`,
  'src/ui/components/toggle/index.js': `/**
 * OTR Toggle Component
 * 
 * This component renders a toggle for enabling/disabling OTR encryption.
 */

export class OtrToggle {
  constructor(container, options = {}) {
    this.container = container;
    this.options = options;
    this.enabled = false;
  }

  render() {
    // Placeholder for actual implementation
    console.log('OtrToggle rendered');
  }

  toggle() {
    this.enabled = !this.enabled;
    return this.enabled;
  }
}
`,
  'src/ui/components/status/index.js': `/**
 * OTR Status Component
 * 
 * This component displays the current OTR encryption status.
 */

export class OtrStatus {
  constructor(container, options = {}) {
    this.container = container;
    this.options = options;
    this.status = 'disabled';
  }

  render() {
    // Placeholder for actual implementation
    console.log('OtrStatus rendered');
  }

  update(status) {
    this.status = status;
  }
}
`,
  'src/ui/dialogs/verification/index.js': `/**
 * Verification Dialog Component
 * 
 * This component displays a dialog for OTR verification.
 */

export class VerificationDialog {
  constructor(container, options = {}) {
    this.container = container;
    this.options = options;
  }

  show() {
    // Placeholder for actual implementation
    console.log('VerificationDialog shown');
  }

  hide() {
    // Placeholder for actual implementation
    console.log('VerificationDialog hidden');
  }
}
`,
  'src/ui/notifications/index.js': `/**
 * Notifications Component
 * 
 * This component handles displaying notifications to the user.
 */

export class Notifications {
  constructor(container, options = {}) {
    this.container = container;
    this.options = options;
  }

  notify(message, type = 'info') {
    // Placeholder for actual implementation
    console.log('Notification:', message, type);
  }
}
`,
  'src/ui/icons/index.js': `/**
 * UI Icons for WebOTR
 * 
 * This file exports SVG icons used by the UI components.
 */

export const ICONS = {
  LOCK: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/></svg>',
  UNLOCK: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 17c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm6-9h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6h1.9c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm0 12H6V10h12v10z"/></svg>',
  WARNING: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/></svg>'
};
`
};

// Create example SVG icons to fix glob pattern error
const iconFiles = {
  'src/ui/icons/lock.svg': `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
  <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/>
</svg>`,
  'src/ui/icons/unlock.svg': `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
  <path d="M12 17c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm6-9h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6h1.9c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm0 12H6V10h12v10z"/>
</svg>`,
  'src/ui/icons/warning.svg': `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
  <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
</svg>`
};

console.log('Setting up WebOTR development environment...');

// Create required directories
for (const dirPath of requiredPaths) {
  const fullPath = path.join(process.cwd(), dirPath);
  if (!fs.existsSync(fullPath)) {
    console.log(`Creating directory: ${dirPath}`);
    fs.mkdirSync(fullPath, { recursive: true });
  }
}

// Create stub files if they don't exist
for (const [filePath, content] of Object.entries(stubFiles)) {
  const fullPath = path.join(process.cwd(), filePath);
  if (!fs.existsSync(fullPath)) {
    console.log(`Creating stub file: ${filePath}`);
    fs.writeFileSync(fullPath, content);
  }
}

// Create icon files if they don't exist
for (const [filePath, content] of Object.entries(iconFiles)) {
  const fullPath = path.join(process.cwd(), filePath);
  if (!fs.existsSync(fullPath)) {
    console.log(`Creating icon file: ${filePath}`);
    fs.writeFileSync(fullPath, content);
  }
}

console.log('Setup complete. Ready to start development server.'); 