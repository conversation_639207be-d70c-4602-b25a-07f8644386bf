/**
 * WebOTR Start Verification Script
 * 
 * This script verifies that the development environment is properly set up
 * by checking for the existence of all required files and directories.
 * 
 * Usage: 
 *   node scripts/verify-start.js
 */

const fs = require('fs');
const path = require('path');

// Required directories to check
const requiredDirs = [
  'src/ui',
  'src/ui/components',
  'src/ui/components/toggle',
  'src/ui/components/status',
  'src/ui/dialogs',
  'src/ui/dialogs/verification',
  'src/ui/notifications',
  'src/ui/icons'
];

// Required files to check
const requiredFiles = [
  'src/ui/index.js',
  'src/ui/components/toggle/index.js',
  'src/ui/components/status/index.js',
  'src/ui/dialogs/verification/index.js',
  'src/ui/notifications/index.js',
  'src/ui/icons/index.js',
  'src/ui/icons/lock.svg',
  'src/ui/icons/unlock.svg',
  'src/ui/icons/warning.svg'
];

console.log('Verifying WebOTR development environment setup...');

// Check directories
let dirsOk = true;
for (const dir of requiredDirs) {
  const fullPath = path.join(process.cwd(), dir);
  if (!fs.existsSync(fullPath)) {
    console.error(`❌ Missing directory: ${dir}`);
    dirsOk = false;
  }
}

if (dirsOk) {
  console.log('✅ All required directories exist');
} else {
  console.log('❌ Some directories are missing. Run `npm start` to set up the environment.');
}

// Check files
let filesOk = true;
for (const file of requiredFiles) {
  const fullPath = path.join(process.cwd(), file);
  if (!fs.existsSync(fullPath)) {
    console.error(`❌ Missing file: ${file}`);
    filesOk = false;
  }
}

if (filesOk) {
  console.log('✅ All required files exist');
} else {
  console.log('❌ Some files are missing. Run `npm start` to set up the environment.');
}

// Overall status
if (dirsOk && filesOk) {
  console.log('✅ Development environment is properly set up. You can run `npm start` to start the development server.');
  process.exit(0);
} else {
  console.log('❌ Development environment is not properly set up. Run `npm start` to fix the issues.');
  process.exit(1);
} 