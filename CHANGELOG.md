# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html)
with specific guidelines defined in [.claude/versioning_guidelines.md](.claude/versioning_guidelines.md).

## [Unreleased]

### Added
- **Comprehensive Test Suite Expansion** - Major testing infrastructure overhaul
  - Added 6 new test suites with 90+ comprehensive test cases
  - Core Session Tests (22 tests) - 100% passing with complete API coverage
  - Core Message Utils Tests (17 tests) - 100% passing with full message handling
  - Core Protocol AKE Tests (15 tests) - Function availability verification
  - Platform Teams Adapter Tests (25+ tests) - Complete platform integration patterns
  - Integration Protocol Tests (10+ tests) - End-to-end OTR protocol flows
  - DevContainer validation script for environment testing
- **DevContainer Development Environment** - Production-ready containerized development
  - Complete Docker setup with Node.js v20.18.3 and npm 10.8.2
  - Enhanced port forwarding (3000, 3003, 3004, 8080, 9323)
  - Browser testing capabilities with security options
  - Automated environment validation and testing
- **Test Infrastructure Improvements** - Professional-grade testing foundation
  - Fixed ES6 import/export issues with Jest and Babel configuration
  - Enhanced module resolution for modern JavaScript
  - Crypto fallback testing (Web Crypto API → Pure JS implementations)
  - Zero regression policy maintained throughout expansion
- **Branch Merge PRD Implementation** - Systematic integration of major features
  - Complete AKE (Authenticated Key Exchange) protocol implementation
  - Enhanced SMP (Socialist Millionaire Protocol) with comprehensive testing
  - Security updates with dependency vulnerability patches
  - Import compatibility fixes and protocol integration
- Added libotr as a submodule for reference implementation
- Enhanced protocol compatibility with other OTR clients
- Implemented Authenticated Key Exchange (AKE) protocol
  - DH commit, DH key, Reveal Signature, and Signature message handling
  - DSA signing and verification with Web Crypto API and JS fallback
  - Secure session establishment with authentication
  - Integration with OTR session management
- Initial implementation of Socialist Millionaire Protocol (SMP)
  - SMP message structure and state management
  - Integration with OTR session management
  - Support for authentication questions
  - User notification of SMP results

### Changed
- **Test Infrastructure Transformation** - From 60 to 155+ tests with professional coverage
- **Development Environment** - Standardized with DevContainer for team collaboration
- **Quality Assurance** - Established systematic testing across all project layers
- **API Compatibility** - Aligned all test implementations with actual codebase APIs

## [0.2.0] - 2023-06-15

### Added
- Core OTR protocol implementation
  - Diffie-Hellman key exchange with MODP Group from RFC 3526
  - AES-CTR encryption/decryption with Web Crypto API and CryptoJS fallback
  - SHA-256 HMAC with constant-time verification
  - Socialist Millionaire Protocol for secure authentication
  - OTR message formatting and parsing
  - OTR protocol state machine
  - Key derivation functions
  - Session management with instance tags
  - Protocol version negotiation (v2 and v3)
- Platform adapters
  - Microsoft Teams adapter
  - Discord adapter
  - Slack adapter
  - Generic adapter for other platforms
- Browser extension
  - Background script
  - Content scripts for each platform
  - UI components for OTR controls
- Key management system
  - Secure key generation
  - Session management
  - Key storage
- Documentation
  - Protocol specification
  - Architecture overview
  - Developer guides

### Changed
- Refactor project structure to support WebOTR
- Update documentation to reflect new focus
- Update build system for browser extension

## [0.1.0] - 2023-05-01

### Added
- Initial project setup
- Core protocol implementation
- Key management system

### Technical Details
- Uses Web Crypto API for cryptographic operations
- Implements OTRv3 protocol specification
- Provides browser extension for easy installation 