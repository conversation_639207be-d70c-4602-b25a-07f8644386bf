#!/bin/bash

# Get all branches
branches=$(git branch -a | grep -v HEAD | sed 's/^[ *]*//')

# Start the mermaid diagram
echo "```mermaid"
echo "gitGraph:"
echo "  commit"

# Process each branch
for branch in $branches; do
  # Clean up branch name
  clean_branch=$(echo $branch | sed 's/remotes\/origin\///')
  
  # Skip if it's the same as a local branch we've already processed
  if [[ $branch == remotes/origin/* && " $branches " == *" ${clean_branch} "* ]]; then
    continue
  fi
  
  echo "  branch ${clean_branch}"
  echo "  checkout ${clean_branch}"
  
  # Get commits for this branch
  commits=$(git log --oneline --decorate --graph $branch | head -5)
  for i in {1..3}; do
    echo "  commit"
  done
  
  # Return to main branch
  echo "  checkout master"
done

# End the mermaid diagram
echo "```"
