/**
 * Web Crypto API Mocks for Testing
 * 
 * Comprehensive mocking of Web Crypto API for testing Forward Secrecy
 * and security components with Jest
 */

// Mock Web Crypto API
const mockCrypto = {
  subtle: {
    digest: jest.fn(),
    importKey: jest.fn(),
    exportKey: jest.fn(),
    generateKey: jest.fn(),
    deriveBits: jest.fn(),
    deriveKey: jest.fn(),
    encrypt: jest.fn(),
    decrypt: jest.fn(),
    sign: jest.fn(),
    verify: jest.fn(),
    wrapKey: jest.fn(),
    unwrapKey: jest.fn()
  },
  getRandomValues: jest.fn()
};

// Mock implementations
mockCrypto.getRandomValues.mockImplementation((array) => {
  for (let i = 0; i < array.length; i++) {
    array[i] = Math.floor(Math.random() * 256);
  }
  return array;
});

mockCrypto.subtle.digest.mockImplementation(async (algorithm, data) => {
  const hash = new Uint8Array(algorithm === 'SHA-256' ? 32 : 64);
  mockCrypto.getRandomValues(hash);
  return hash.buffer;
});

mockCrypto.subtle.generateKey.mockImplementation(async (algorithm, extractable, keyUsages) => {
  if (algorithm.name === 'ECDH' || algorithm.name === 'ECDSA') {
    return {
      privateKey: {
        type: 'private',
        algorithm: algorithm,
        extractable: extractable,
        usages: keyUsages.filter(usage => ['sign', 'deriveKey', 'deriveBits'].includes(usage))
      },
      publicKey: {
        type: 'public',
        algorithm: algorithm,
        extractable: true,
        usages: keyUsages.filter(usage => ['verify', 'deriveKey', 'deriveBits'].includes(usage))
      }
    };
  } else {
    return {
      type: 'secret',
      algorithm: algorithm,
      extractable: extractable,
      usages: keyUsages
    };
  }
});

mockCrypto.subtle.importKey.mockImplementation(async (format, keyData, algorithm, extractable, keyUsages) => {
  return {
    type: algorithm.name.includes('EC') ? 'public' : 'secret',
    algorithm: algorithm,
    extractable: extractable,
    usages: keyUsages
  };
});

mockCrypto.subtle.exportKey.mockImplementation(async (format, key) => {
  if (format === 'raw') {
    const keyData = new Uint8Array(32);
    mockCrypto.getRandomValues(keyData);
    return keyData.buffer;
  } else {
    const keyData = new Uint8Array(64);
    mockCrypto.getRandomValues(keyData);
    return keyData.buffer;
  }
});

mockCrypto.subtle.encrypt.mockImplementation(async (algorithm, key, data) => {
  const encrypted = new Uint8Array(data.byteLength + 16);
  mockCrypto.getRandomValues(encrypted);
  return encrypted.buffer;
});

mockCrypto.subtle.decrypt.mockImplementation(async (algorithm, key, data) => {
  const decrypted = new Uint8Array(Math.max(0, data.byteLength - 16));
  mockCrypto.getRandomValues(decrypted);
  return decrypted.buffer;
});

mockCrypto.subtle.deriveBits.mockImplementation(async (algorithm, baseKey, length) => {
  const bits = new Uint8Array(length / 8);
  mockCrypto.getRandomValues(bits);
  return bits.buffer;
});

mockCrypto.subtle.deriveKey.mockImplementation(async (algorithm, baseKey, derivedKeyType, extractable, keyUsages) => {
  return {
    type: 'secret',
    algorithm: derivedKeyType,
    extractable: extractable,
    usages: keyUsages
  };
});

mockCrypto.subtle.sign.mockImplementation(async (algorithm, key, data) => {
  const signature = new Uint8Array(64);
  mockCrypto.getRandomValues(signature);
  return signature.buffer;
});

mockCrypto.subtle.verify.mockImplementation(async (algorithm, key, signature, data) => {
  return true;
});

// Mock performance API
const mockPerformance = {
  now: jest.fn(() => Date.now()),
  mark: jest.fn(),
  measure: jest.fn(),
  memory: {
    usedJSHeapSize: 1024 * 1024,
    totalJSHeapSize: 2 * 1024 * 1024,
    jsHeapSizeLimit: 4 * 1024 * 1024
  }
};

// Apply mocks to global scope
global.crypto = mockCrypto;
global.performance = mockPerformance;

module.exports = {
  mockCrypto,
  mockPerformance
};
