/**
 * DOM Mocks for Testing
 * 
 * Comprehensive DOM mocking for User Experience component testing
 */

// Mock DOM element
const createMockElement = (tagName = 'div') => ({
  tagName: tagName.toUpperCase(),
  style: {},
  classList: {
    add: jest.fn(),
    remove: jest.fn(),
    contains: jest.fn(() => false),
    toggle: jest.fn()
  },
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  appendChild: jest.fn(),
  removeChild: jest.fn(),
  insertBefore: jest.fn(),
  setAttribute: jest.fn(),
  getAttribute: jest.fn(() => null),
  removeAttribute: jest.fn(),
  hasAttribute: jest.fn(() => false),
  innerHTML: '',
  textContent: '',
  innerText: '',
  children: [],
  childNodes: [],
  parentNode: null,
  parentElement: null,
  querySelector: jest.fn(() => null),
  querySelectorAll: jest.fn(() => []),
  getElementById: jest.fn(() => null),
  getElementsByClassName: jest.fn(() => []),
  getElementsByTagName: jest.fn(() => []),
  click: jest.fn(),
  focus: jest.fn(),
  blur: jest.fn(),
  scrollIntoView: jest.fn(),
  getBoundingClientRect: jest.fn(() => ({
    top: 0,
    left: 0,
    right: 100,
    bottom: 100,
    width: 100,
    height: 100
  })),
  offsetWidth: 100,
  offsetHeight: 100,
  clientWidth: 100,
  clientHeight: 100,
  scrollWidth: 100,
  scrollHeight: 100,
  scrollTop: 0,
  scrollLeft: 0
});

// Mock document
const mockDocument = {
  createElement: jest.fn((tagName) => createMockElement(tagName)),
  createTextNode: jest.fn((text) => ({ textContent: text, nodeType: 3 })),
  getElementById: jest.fn(() => createMockElement()),
  querySelector: jest.fn(() => createMockElement()),
  querySelectorAll: jest.fn(() => []),
  getElementsByClassName: jest.fn(() => []),
  getElementsByTagName: jest.fn(() => []),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  body: createMockElement('body'),
  head: createMockElement('head'),
  documentElement: createMockElement('html'),
  title: 'Test Document',
  URL: 'https://test.example.com',
  domain: 'test.example.com',
  readyState: 'complete',
  cookie: '',
  createEvent: jest.fn(() => ({
    initEvent: jest.fn(),
    preventDefault: jest.fn(),
    stopPropagation: jest.fn()
  })),
  createDocumentFragment: jest.fn(() => createMockElement('fragment'))
};

// Mock window
const mockWindow = {
  document: mockDocument,
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
  getComputedStyle: jest.fn(() => ({})),
  requestAnimationFrame: jest.fn((cb) => setTimeout(cb, 16)),
  cancelAnimationFrame: jest.fn(),
  setTimeout: jest.fn((cb, delay) => setTimeout(cb, delay)),
  clearTimeout: jest.fn(),
  setInterval: jest.fn((cb, delay) => setInterval(cb, delay)),
  clearInterval: jest.fn(),
  location: {
    href: 'https://test.example.com',
    origin: 'https://test.example.com',
    protocol: 'https:',
    host: 'test.example.com',
    hostname: 'test.example.com',
    port: '',
    pathname: '/',
    search: '',
    hash: ''
  },
  navigator: {
    userAgent: 'Mozilla/5.0 (Test Browser)',
    platform: 'Test Platform',
    language: 'en-US',
    languages: ['en-US', 'en'],
    onLine: true,
    vibrate: jest.fn()
  },
  screen: {
    width: 1920,
    height: 1080,
    availWidth: 1920,
    availHeight: 1040
  },
  innerWidth: 1920,
  innerHeight: 1080,
  outerWidth: 1920,
  outerHeight: 1080,
  devicePixelRatio: 1,
  scrollX: 0,
  scrollY: 0,
  pageXOffset: 0,
  pageYOffset: 0,
  alert: jest.fn(),
  confirm: jest.fn(() => true),
  prompt: jest.fn(() => 'test'),
  open: jest.fn(),
  close: jest.fn(),
  focus: jest.fn(),
  blur: jest.fn(),
  matchMedia: jest.fn(() => ({
    matches: false,
    media: '',
    onchange: null,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
};

// Mock event
const createMockEvent = (type, options = {}) => ({
  type,
  target: options.target || createMockElement(),
  currentTarget: options.currentTarget || null,
  bubbles: options.bubbles || false,
  cancelable: options.cancelable || false,
  defaultPrevented: false,
  preventDefault: jest.fn(function() { this.defaultPrevented = true; }),
  stopPropagation: jest.fn(),
  stopImmediatePropagation: jest.fn(),
  timeStamp: Date.now(),
  ...options
});

// Mock keyboard event
const createMockKeyboardEvent = (type, options = {}) => ({
  ...createMockEvent(type, options),
  key: options.key || '',
  code: options.code || '',
  keyCode: options.keyCode || 0,
  which: options.which || 0,
  altKey: options.altKey || false,
  ctrlKey: options.ctrlKey || false,
  metaKey: options.metaKey || false,
  shiftKey: options.shiftKey || false,
  repeat: options.repeat || false
});

// Mock mouse event
const createMockMouseEvent = (type, options = {}) => ({
  ...createMockEvent(type, options),
  button: options.button || 0,
  buttons: options.buttons || 0,
  clientX: options.clientX || 0,
  clientY: options.clientY || 0,
  screenX: options.screenX || 0,
  screenY: options.screenY || 0,
  pageX: options.pageX || 0,
  pageY: options.pageY || 0,
  offsetX: options.offsetX || 0,
  offsetY: options.offsetY || 0,
  altKey: options.altKey || false,
  ctrlKey: options.ctrlKey || false,
  metaKey: options.metaKey || false,
  shiftKey: options.shiftKey || false
});

// Mock touch event
const createMockTouchEvent = (type, options = {}) => ({
  ...createMockEvent(type, options),
  touches: options.touches || [],
  targetTouches: options.targetTouches || [],
  changedTouches: options.changedTouches || []
});

// Apply mocks to global scope
global.document = mockDocument;
global.window = mockWindow;
global.Element = jest.fn(() => createMockElement());
global.HTMLElement = jest.fn(() => createMockElement());
global.Node = jest.fn();

// Mock CSS and style-related APIs
global.CSSStyleDeclaration = jest.fn();
global.getComputedStyle = mockWindow.getComputedStyle;

// Mock intersection observer
global.IntersectionObserver = jest.fn(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));

// Mock mutation observer
global.MutationObserver = jest.fn(() => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
  takeRecords: jest.fn(() => [])
}));

// Mock resize observer
global.ResizeObserver = jest.fn(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));

// Export mocks for direct use in tests
module.exports = {
  mockDocument,
  mockWindow,
  createMockElement,
  createMockEvent,
  createMockKeyboardEvent,
  createMockMouseEvent,
  createMockTouchEvent
};
