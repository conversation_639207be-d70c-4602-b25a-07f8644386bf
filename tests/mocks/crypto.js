/**
 * Mock implementation of crypto functions for testing
 */

// Check if we need real randomness (for DH tests)
const needsRealRandomness = process.env.JEST_REAL_CRYPTO === 'true' ||
  (typeof expect !== 'undefined' && expect.getState && expect.getState().testPath &&
   expect.getState().testPath.includes('dh.test.js'));

// Mock implementation of getRandomValues
export function getRandomValues(array) {
  if (needsRealRandomness) {
    // Use real Node.js crypto for DH tests
    try {
      const nodeCrypto = require('crypto');
      const randomBytes = nodeCrypto.randomBytes(array.length);
      array.set(randomBytes);
      return array;
    } catch (error) {
      console.warn('Failed to use real crypto, falling back to mock');
    }
  }

  // Use deterministic "random" generator for other tests
  for (let i = 0; i < array.length; i++) {
    array[i] = (i * 17 + 41) % 256;
  }
  return array;
}

// Mock implementation of random
export function random(length) {
  const array = new Uint8Array(length);
  return getRandomValues(array);
}

// Mock implementation of hmacSha256 that's deterministic for testing
export async function hmacSha256(data, key) {
  // For testing, create a deterministic hash-like result
  // This isn't cryptographically sound, but works for testing
  const result = new Uint8Array(32); // SHA-256 produces 32 bytes
  
  // Simple deterministic algorithm that depends on both data and key
  let sum = 0;
  for (let i = 0; i < data.length; i++) {
    sum = (sum + data[i]) % 256;
  }
  
  let keySum = 0;
  for (let i = 0; i < key.length; i++) {
    keySum = (keySum + key[i]) % 256;
  }
  
  for (let i = 0; i < result.length; i++) {
    result[i] = (sum + keySum + i * 37) % 256;
  }
  
  return result;
} 