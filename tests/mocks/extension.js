/**
 * Browser Extension API Mocks
 * 
 * Comprehensive mocking of Chrome/Firefox extension APIs for testing
 */

// Mock Chrome Extension APIs
const mockChrome = {
  runtime: {
    sendMessage: jest.fn(),
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
      hasListener: jest.fn(() => false)
    },
    onConnect: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onInstalled: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onStartup: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    getManifest: jest.fn(() => ({
      version: '1.0.0',
      manifest_version: 3,
      name: 'WebOTR Test Extension'
    })),
    getURL: jest.fn((path) => `chrome-extension://test-id/${path}`),
    id: 'test-extension-id',
    connect: jest.fn(() => ({
      postMessage: jest.fn(),
      onMessage: {
        addListener: jest.fn(),
        removeListener: jest.fn()
      },
      onDisconnect: {
        addListener: jest.fn(),
        removeListener: jest.fn()
      },
      disconnect: jest.fn()
    })),
    lastError: null
  },
  
  tabs: {
    query: jest.fn(),
    get: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    sendMessage: jest.fn(),
    executeScript: jest.fn(),
    insertCSS: jest.fn(),
    removeCSS: jest.fn(),
    onUpdated: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onActivated: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onCreated: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onRemoved: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
      getBytesInUse: jest.fn(() => 0),
      onChanged: {
        addListener: jest.fn(),
        removeListener: jest.fn()
      }
    },
    sync: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
      getBytesInUse: jest.fn(() => 0),
      onChanged: {
        addListener: jest.fn(),
        removeListener: jest.fn()
      }
    },
    session: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn()
    },
    onChanged: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  
  action: {
    setBadgeText: jest.fn(),
    getBadgeText: jest.fn(),
    setBadgeBackgroundColor: jest.fn(),
    getBadgeBackgroundColor: jest.fn(),
    setTitle: jest.fn(),
    getTitle: jest.fn(),
    setIcon: jest.fn(),
    setPopup: jest.fn(),
    getPopup: jest.fn(),
    enable: jest.fn(),
    disable: jest.fn(),
    onClicked: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  
  scripting: {
    executeScript: jest.fn(),
    insertCSS: jest.fn(),
    removeCSS: jest.fn(),
    registerContentScripts: jest.fn(),
    unregisterContentScripts: jest.fn(),
    getRegisteredContentScripts: jest.fn(() => [])
  },
  
  permissions: {
    request: jest.fn(),
    remove: jest.fn(),
    contains: jest.fn(),
    getAll: jest.fn(),
    onAdded: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onRemoved: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  
  webNavigation: {
    onBeforeNavigate: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onCommitted: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onCompleted: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onErrorOccurred: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  
  webRequest: {
    onBeforeRequest: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onBeforeSendHeaders: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onHeadersReceived: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onCompleted: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  
  contextMenus: {
    create: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    removeAll: jest.fn(),
    onClicked: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  
  notifications: {
    create: jest.fn(),
    update: jest.fn(),
    clear: jest.fn(),
    getAll: jest.fn(),
    onClicked: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    onClosed: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  
  alarms: {
    create: jest.fn(),
    get: jest.fn(),
    getAll: jest.fn(),
    clear: jest.fn(),
    clearAll: jest.fn(),
    onAlarm: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  
  cookies: {
    get: jest.fn(),
    getAll: jest.fn(),
    set: jest.fn(),
    remove: jest.fn(),
    onChanged: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  }
};

// Mock default implementations
mockChrome.runtime.sendMessage.mockImplementation(async (message) => {
  return { success: true, response: 'mock-response' };
});

mockChrome.tabs.query.mockImplementation(async (queryInfo) => {
  return [{
    id: 123,
    url: 'https://discord.com/channels/123/456',
    title: 'Discord',
    active: true,
    windowId: 1
  }];
});

mockChrome.tabs.sendMessage.mockImplementation(async (tabId, message) => {
  return { success: true, response: 'mock-tab-response' };
});

mockChrome.storage.local.get.mockImplementation(async (keys) => {
  const mockData = {
    'webottr-config': { theme: 'modern', enabled: true },
    'webottr-session': { sessionId: 'test-session-123' }
  };
  
  if (typeof keys === 'string') {
    return { [keys]: mockData[keys] };
  } else if (Array.isArray(keys)) {
    const result = {};
    keys.forEach(key => {
      if (mockData[key]) {
        result[key] = mockData[key];
      }
    });
    return result;
  } else {
    return mockData;
  }
});

mockChrome.storage.local.set.mockImplementation(async (items) => {
  return Promise.resolve();
});

mockChrome.storage.sync.get.mockImplementation(mockChrome.storage.local.get);
mockChrome.storage.sync.set.mockImplementation(mockChrome.storage.local.set);

mockChrome.action.setBadgeText.mockImplementation(async (details) => {
  return Promise.resolve();
});

mockChrome.action.setIcon.mockImplementation(async (details) => {
  return Promise.resolve();
});

mockChrome.permissions.contains.mockImplementation(async (permissions) => {
  return true; // Mock that all permissions are granted
});

mockChrome.permissions.request.mockImplementation(async (permissions) => {
  return true; // Mock successful permission request
});

// Mock Firefox browser API (same as Chrome for compatibility)
const mockBrowser = { ...mockChrome };

// Apply mocks to global scope
global.chrome = mockChrome;
global.browser = mockBrowser;

// Mock extension environment detection
global.isExtensionEnvironment = true;
global.isChrome = true;
global.isFirefox = false;
global.isSafari = false;
global.isEdge = false;

// Mock extension-specific globals
global.extensionId = 'test-extension-id';
global.manifestVersion = 3;

// Export mocks for direct use in tests
module.exports = {
  mockChrome,
  mockBrowser
};
