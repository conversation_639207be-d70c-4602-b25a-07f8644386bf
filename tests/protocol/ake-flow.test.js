/**
 * @fileoverview Complete AKE Flow Testing
 * Tests all aspects of the Authenticated Key Exchange protocol
 * Part of Enhanced Testing PRD implementation
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { OtrSession } from '../../src/core/session/index.js';
import { AKEHandler } from '../../src/core/protocol/ake.js';
import { STATE, MESSAGE_TYPE } from '../../src/core/protocol/state.js';
import { generateKeyPair } from '../../src/core/crypto/dsa.js';

describe('Complete AKE Flow Testing', () => {
  let aliceSession, bobSession;
  let aliceKeys, bobKeys;

  beforeEach(async () => {
    // Generate test key pairs
    aliceKeys = await generateKeyPair();
    bobKeys = await generateKeyPair();

    // Create test sessions
    aliceSession = new OtrSession({
      privateKey: aliceKeys.privateKey,
      publicKey: aliceKeys.publicKey,
      instanceTag: 0x12345678
    });

    bobSession = new OtrSession({
      privateKey: bobKeys.privateKey,
      publicKey: bobKeys.publicKey,
      instanceTag: 0x87654321
    });
  });

  afterEach(() => {
    aliceSession?.cleanup();
    bobSession?.cleanup();
  });

  describe('Happy Path AKE Flow', () => {
    test('should complete full AKE from initiation to encrypted session', async () => {
      // Alice initiates AKE
      const dhCommitMsg = await aliceSession.initiateAKE();
      expect(dhCommitMsg).toBeDefined();
      expect(dhCommitMsg.type).toBe(MESSAGE_TYPE.DH_COMMIT);
      expect(aliceSession.getState()).toBe(STATE.AWAITING_DH_KEY);

      // Bob responds with DH Key
      const dhKeyMsg = await bobSession.processMessage(dhCommitMsg);
      expect(dhKeyMsg).toBeDefined();
      expect(dhKeyMsg.type).toBe(MESSAGE_TYPE.DH_KEY);
      expect(bobSession.getState()).toBe(STATE.AWAITING_REVEAL_SIG);

      // Alice sends Reveal Signature
      const revealSigMsg = await aliceSession.processMessage(dhKeyMsg);
      expect(revealSigMsg).toBeDefined();
      expect(revealSigMsg.type).toBe(MESSAGE_TYPE.REVEAL_SIG);
      expect(aliceSession.getState()).toBe(STATE.AWAITING_SIG);

      // Bob sends Signature
      const sigMsg = await bobSession.processMessage(revealSigMsg);
      expect(sigMsg).toBeDefined();
      expect(sigMsg.type).toBe(MESSAGE_TYPE.SIGNATURE);
      expect(bobSession.getState()).toBe(STATE.ENCRYPTED);

      // Alice processes final signature
      const finalResult = await aliceSession.processMessage(sigMsg);
      expect(finalResult).toBeNull(); // No response needed
      expect(aliceSession.getState()).toBe(STATE.ENCRYPTED);

      // Verify both sessions have established encryption
      expect(aliceSession.isEncrypted()).toBe(true);
      expect(bobSession.isEncrypted()).toBe(true);

      // Verify session keys match
      const aliceSessionKey = aliceSession.getSessionKey();
      const bobSessionKey = bobSession.getSessionKey();
      expect(aliceSessionKey).toEqual(bobSessionKey);
    });

    test('should handle AKE with instance tags correctly', async () => {
      const dhCommitMsg = await aliceSession.initiateAKE();
      
      // Verify instance tags are included
      expect(dhCommitMsg.senderInstanceTag).toBe(0x12345678);
      expect(dhCommitMsg.receiverInstanceTag).toBe(0x00000000); // Unknown initially

      const dhKeyMsg = await bobSession.processMessage(dhCommitMsg);
      expect(dhKeyMsg.senderInstanceTag).toBe(0x87654321);
      expect(dhKeyMsg.receiverInstanceTag).toBe(0x12345678);

      // Complete the handshake
      const revealSigMsg = await aliceSession.processMessage(dhKeyMsg);
      const sigMsg = await bobSession.processMessage(revealSigMsg);
      await aliceSession.processMessage(sigMsg);

      // Verify instance tags are properly set
      expect(aliceSession.getRemoteInstanceTag()).toBe(0x87654321);
      expect(bobSession.getRemoteInstanceTag()).toBe(0x12345678);
    });
  });

  describe('Concurrent AKE Scenarios', () => {
    test('should handle simultaneous AKE initiation', async () => {
      // Both parties initiate AKE simultaneously
      const aliceDHCommit = await aliceSession.initiateAKE();
      const bobDHCommit = await bobSession.initiateAKE();

      expect(aliceSession.getState()).toBe(STATE.AWAITING_DH_KEY);
      expect(bobSession.getState()).toBe(STATE.AWAITING_DH_KEY);

      // Process each other's DH Commit messages
      const aliceResponse = await aliceSession.processMessage(bobDHCommit);
      const bobResponse = await bobSession.processMessage(aliceDHCommit);

      // One should win (higher instance tag or other tie-breaker)
      // The protocol should resolve to a single AKE flow
      expect(aliceResponse || bobResponse).toBeDefined();
      
      // Continue with the winning flow
      let winner, loser, winnerMsg;
      if (aliceResponse) {
        winner = aliceSession;
        loser = bobSession;
        winnerMsg = aliceResponse;
      } else {
        winner = bobSession;
        loser = aliceSession;
        winnerMsg = bobResponse;
      }

      // Complete the AKE flow
      const nextMsg = await loser.processMessage(winnerMsg);
      if (nextMsg) {
        const finalMsg = await winner.processMessage(nextMsg);
        if (finalMsg) {
          await loser.processMessage(finalMsg);
        }
      }

      // Both should end up encrypted
      expect(aliceSession.isEncrypted()).toBe(true);
      expect(bobSession.isEncrypted()).toBe(true);
    });

    test('should handle AKE restart scenarios', async () => {
      // Start initial AKE
      const dhCommitMsg = await aliceSession.initiateAKE();
      await bobSession.processMessage(dhCommitMsg);

      // Alice restarts AKE before completion
      aliceSession.reset();
      const newDHCommitMsg = await aliceSession.initiateAKE();
      
      expect(newDHCommitMsg.type).toBe(MESSAGE_TYPE.DH_COMMIT);
      expect(aliceSession.getState()).toBe(STATE.AWAITING_DH_KEY);

      // Bob should handle the restart gracefully
      const dhKeyMsg = await bobSession.processMessage(newDHCommitMsg);
      expect(dhKeyMsg).toBeDefined();

      // Complete the restarted AKE
      const revealSigMsg = await aliceSession.processMessage(dhKeyMsg);
      const sigMsg = await bobSession.processMessage(revealSigMsg);
      await aliceSession.processMessage(sigMsg);

      expect(aliceSession.isEncrypted()).toBe(true);
      expect(bobSession.isEncrypted()).toBe(true);
    });
  });

  describe('Parameter Validation', () => {
    test('should reject invalid DH parameters', async () => {
      const dhCommitMsg = await aliceSession.initiateAKE();
      
      // Corrupt the DH parameters
      const corruptedMsg = { ...dhCommitMsg };
      corruptedMsg.dhCommit = new Uint8Array(32).fill(0); // Invalid all-zeros

      await expect(bobSession.processMessage(corruptedMsg))
        .rejects.toThrow(/invalid.*dh.*parameter/i);
    });

    test('should reject bad signatures', async () => {
      const dhCommitMsg = await aliceSession.initiateAKE();
      const dhKeyMsg = await bobSession.processMessage(dhCommitMsg);
      const revealSigMsg = await aliceSession.processMessage(dhKeyMsg);

      // Corrupt the signature
      const corruptedMsg = { ...revealSigMsg };
      corruptedMsg.signature = new Uint8Array(40).fill(0xFF); // Invalid signature

      await expect(bobSession.processMessage(corruptedMsg))
        .rejects.toThrow(/invalid.*signature/i);
    });

    test('should validate message sequence', async () => {
      // Try to send DH_KEY without DH_COMMIT
      const invalidDHKey = {
        type: MESSAGE_TYPE.DH_KEY,
        dhPublicKey: new Uint8Array(192),
        senderInstanceTag: 0x12345678,
        receiverInstanceTag: 0x87654321
      };

      await expect(bobSession.processMessage(invalidDHKey))
        .rejects.toThrow(/unexpected.*message.*type/i);
    });
  });

  describe('Timeout Handling', () => {
    test('should handle AKE timeout and retry logic', async () => {
      const dhCommitMsg = await aliceSession.initiateAKE();
      
      // Simulate timeout by not responding
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Alice should be able to retry
      const retryMsg = await aliceSession.retryAKE();
      expect(retryMsg).toBeDefined();
      expect(retryMsg.type).toBe(MESSAGE_TYPE.DH_COMMIT);
      
      // Bob should handle the retry
      const dhKeyMsg = await bobSession.processMessage(retryMsg);
      expect(dhKeyMsg).toBeDefined();
    });

    test('should cleanup after AKE timeout', async () => {
      await aliceSession.initiateAKE();
      
      // Simulate timeout
      aliceSession.handleAKETimeout();
      
      expect(aliceSession.getState()).toBe(STATE.PLAINTEXT);
      expect(aliceSession.getAKEData()).toBeNull();
    });
  });

  describe('Memory and Resource Management', () => {
    test('should properly cleanup AKE resources', async () => {
      const dhCommitMsg = await aliceSession.initiateAKE();
      const dhKeyMsg = await bobSession.processMessage(dhCommitMsg);
      
      // Cleanup before completion
      aliceSession.cleanup();
      bobSession.cleanup();
      
      // Verify resources are released
      expect(aliceSession.getAKEData()).toBeNull();
      expect(bobSession.getAKEData()).toBeNull();
    });

    test('should handle multiple AKE attempts without memory leaks', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform multiple AKE attempts
      for (let i = 0; i < 10; i++) {
        const dhCommitMsg = await aliceSession.initiateAKE();
        await bobSession.processMessage(dhCommitMsg);
        
        // Reset for next iteration
        aliceSession.reset();
        bobSession.reset();
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 1MB)
      expect(memoryIncrease).toBeLessThan(1024 * 1024);
    });
  });
});
