/**
 * @fileoverview AKE State Machine Testing
 * Tests state transitions and validation in the AKE protocol
 * Part of Enhanced Testing PRD implementation
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { OtrSession } from '../../src/core/session/index.js';
import { STATE, MESSAGE_TYPE } from '../../src/core/protocol/state.js';
import { generateKeyPair } from '../../src/core/crypto/dsa.js';

describe('AKE State Machine', () => {
  let session;
  let keyPair;

  beforeEach(async () => {
    keyPair = await generateKeyPair();
    session = new OtrSession({
      privateKey: keyPair.privateKey,
      publicKey: keyPair.publicKey,
      instanceTag: 0x12345678
    });
  });

  afterEach(() => {
    session?.cleanup();
  });

  describe('State Transitions', () => {
    test('should transition states correctly during AKE initiation', async () => {
      // Initial state
      expect(session.getState()).toBe(STATE.PLAINTEXT);

      // Initiate AKE
      await session.initiateAKE();
      expect(session.getState()).toBe(STATE.AWAITING_DH_KEY);

      // Verify state data is set
      const akeData = session.getAKEData();
      expect(akeData).toBeDefined();
      expect(akeData.dhPrivateKey).toBeDefined();
      expect(akeData.dhPublicKey).toBeDefined();
    });

    test('should transition correctly when receiving DH_COMMIT', async () => {
      const dhCommitMsg = {
        type: MESSAGE_TYPE.DH_COMMIT,
        dhCommit: new Uint8Array(32),
        encryptedGx: new Uint8Array(192),
        senderInstanceTag: 0x87654321,
        receiverInstanceTag: 0x00000000
      };

      expect(session.getState()).toBe(STATE.PLAINTEXT);

      await session.processMessage(dhCommitMsg);
      expect(session.getState()).toBe(STATE.AWAITING_REVEAL_SIG);

      const akeData = session.getAKEData();
      expect(akeData.dhCommit).toEqual(dhCommitMsg.dhCommit);
      expect(akeData.encryptedGx).toEqual(dhCommitMsg.encryptedGx);
    });

    test('should transition correctly through complete AKE flow', async () => {
      const stateTransitions = [];
      
      // Mock state change listener
      const originalSetState = session.setState;
      session.setState = function(newState) {
        stateTransitions.push({ from: this.getState(), to: newState });
        return originalSetState.call(this, newState);
      };

      // Create second session for complete flow
      const bobKeys = await generateKeyPair();
      const bobSession = new OtrSession({
        privateKey: bobKeys.privateKey,
        publicKey: bobKeys.publicKey,
        instanceTag: 0x87654321
      });

      try {
        // Complete AKE flow
        const dhCommitMsg = await session.initiateAKE();
        const dhKeyMsg = await bobSession.processMessage(dhCommitMsg);
        const revealSigMsg = await session.processMessage(dhKeyMsg);
        const sigMsg = await bobSession.processMessage(revealSigMsg);
        await session.processMessage(sigMsg);

        // Verify expected state transitions
        expect(stateTransitions).toContainEqual({
          from: STATE.PLAINTEXT,
          to: STATE.AWAITING_DH_KEY
        });
        expect(stateTransitions).toContainEqual({
          from: STATE.AWAITING_DH_KEY,
          to: STATE.AWAITING_SIG
        });
        expect(stateTransitions).toContainEqual({
          from: STATE.AWAITING_SIG,
          to: STATE.ENCRYPTED
        });

        expect(session.getState()).toBe(STATE.ENCRYPTED);
      } finally {
        bobSession.cleanup();
      }
    });

    test('should handle state transitions with instance tag validation', async () => {
      const dhCommitMsg = {
        type: MESSAGE_TYPE.DH_COMMIT,
        dhCommit: new Uint8Array(32),
        encryptedGx: new Uint8Array(192),
        senderInstanceTag: 0x87654321,
        receiverInstanceTag: 0x12345678 // Correct receiver tag
      };

      await session.processMessage(dhCommitMsg);
      expect(session.getState()).toBe(STATE.AWAITING_REVEAL_SIG);
      expect(session.getRemoteInstanceTag()).toBe(0x87654321);
    });
  });

  describe('Invalid State Transitions', () => {
    test('should reject invalid state transitions', async () => {
      // Try to process REVEAL_SIG without being in correct state
      const revealSigMsg = {
        type: MESSAGE_TYPE.REVEAL_SIG,
        revealedKey: new Uint8Array(16),
        signature: new Uint8Array(40),
        senderInstanceTag: 0x87654321,
        receiverInstanceTag: 0x12345678
      };

      expect(session.getState()).toBe(STATE.PLAINTEXT);

      await expect(session.processMessage(revealSigMsg))
        .rejects.toThrow(/unexpected.*message.*type/i);

      // State should remain unchanged
      expect(session.getState()).toBe(STATE.PLAINTEXT);
    });

    test('should reject messages with wrong instance tags', async () => {
      const dhCommitMsg = {
        type: MESSAGE_TYPE.DH_COMMIT,
        dhCommit: new Uint8Array(32),
        encryptedGx: new Uint8Array(192),
        senderInstanceTag: 0x87654321,
        receiverInstanceTag: 0x99999999 // Wrong receiver tag
      };

      await expect(session.processMessage(dhCommitMsg))
        .rejects.toThrow(/instance.*tag.*mismatch/i);

      expect(session.getState()).toBe(STATE.PLAINTEXT);
    });

    test('should reject duplicate message types in same state', async () => {
      // Initiate AKE
      await session.initiateAKE();
      expect(session.getState()).toBe(STATE.AWAITING_DH_KEY);

      // Try to initiate again
      await expect(session.initiateAKE())
        .rejects.toThrow(/ake.*already.*in.*progress/i);

      expect(session.getState()).toBe(STATE.AWAITING_DH_KEY);
    });

    test('should handle protocol violations gracefully', async () => {
      const invalidMsg = {
        type: 'INVALID_MESSAGE_TYPE',
        data: new Uint8Array(100),
        senderInstanceTag: 0x87654321,
        receiverInstanceTag: 0x12345678
      };

      await expect(session.processMessage(invalidMsg))
        .rejects.toThrow(/unknown.*message.*type/i);

      expect(session.getState()).toBe(STATE.PLAINTEXT);
    });
  });

  describe('State Persistence', () => {
    test('should save and restore state correctly', async () => {
      // Initiate AKE to create state
      await session.initiateAKE();
      const originalState = session.getState();
      const originalAKEData = session.getAKEData();

      // Save state
      const savedState = session.saveState();
      expect(savedState).toBeDefined();
      expect(savedState.state).toBe(originalState);
      expect(savedState.akeData).toBeDefined();

      // Create new session and restore state
      const newKeyPair = await generateKeyPair();
      const newSession = new OtrSession({
        privateKey: newKeyPair.privateKey,
        publicKey: newKeyPair.publicKey,
        instanceTag: 0x12345678
      });

      try {
        newSession.restoreState(savedState);

        // Verify state is restored
        expect(newSession.getState()).toBe(originalState);
        
        const restoredAKEData = newSession.getAKEData();
        expect(restoredAKEData).toBeDefined();
        expect(restoredAKEData.dhPrivateKey).toEqual(originalAKEData.dhPrivateKey);
        expect(restoredAKEData.dhPublicKey).toEqual(originalAKEData.dhPublicKey);
      } finally {
        newSession.cleanup();
      }
    });

    test('should handle state corruption gracefully', async () => {
      const corruptedState = {
        state: 'INVALID_STATE',
        akeData: { invalid: 'data' },
        instanceTag: 'not_a_number'
      };

      await expect(session.restoreState(corruptedState))
        .rejects.toThrow(/invalid.*state.*data/i);

      // Session should remain in safe state
      expect(session.getState()).toBe(STATE.PLAINTEXT);
    });

    test('should validate state consistency after restoration', async () => {
      await session.initiateAKE();
      const savedState = session.saveState();

      // Modify saved state to be inconsistent
      savedState.state = STATE.ENCRYPTED;
      // But keep AKE data that doesn't match encrypted state

      await expect(session.restoreState(savedState))
        .rejects.toThrow(/inconsistent.*state/i);
    });
  });

  describe('State Machine Edge Cases', () => {
    test('should handle rapid state changes', async () => {
      // Rapidly change states
      await session.initiateAKE();
      session.reset();
      await session.initiateAKE();
      session.reset();

      expect(session.getState()).toBe(STATE.PLAINTEXT);
      expect(session.getAKEData()).toBeNull();
    });

    test('should handle concurrent state modifications', async () => {
      await session.initiateAKE();

      // Simulate concurrent modifications
      const promises = [
        session.reset(),
        session.handleAKETimeout(),
        session.cleanup()
      ];

      await Promise.allSettled(promises);

      // Session should be in a consistent state
      expect([STATE.PLAINTEXT, STATE.FINISHED].includes(session.getState())).toBe(true);
    });

    test('should maintain state invariants', async () => {
      // Test various state invariants
      expect(session.getState()).toBe(STATE.PLAINTEXT);
      expect(session.getAKEData()).toBeNull();
      expect(session.isEncrypted()).toBe(false);

      await session.initiateAKE();
      expect(session.getState()).toBe(STATE.AWAITING_DH_KEY);
      expect(session.getAKEData()).toBeDefined();
      expect(session.isEncrypted()).toBe(false);

      session.reset();
      expect(session.getState()).toBe(STATE.PLAINTEXT);
      expect(session.getAKEData()).toBeNull();
      expect(session.isEncrypted()).toBe(false);
    });
  });

  describe('State Debugging and Monitoring', () => {
    test('should provide detailed state information', () => {
      const stateInfo = session.getStateInfo();
      
      expect(stateInfo).toHaveProperty('currentState');
      expect(stateInfo).toHaveProperty('instanceTag');
      expect(stateInfo).toHaveProperty('remoteInstanceTag');
      expect(stateInfo).toHaveProperty('isEncrypted');
      expect(stateInfo).toHaveProperty('akeInProgress');
    });

    test('should track state transition history', async () => {
      // Enable state history tracking
      session.enableStateHistory();

      await session.initiateAKE();
      session.reset();

      const history = session.getStateHistory();
      expect(history).toBeInstanceOf(Array);
      expect(history.length).toBeGreaterThan(0);
      
      // Should contain transition records
      expect(history[0]).toHaveProperty('from');
      expect(history[0]).toHaveProperty('to');
      expect(history[0]).toHaveProperty('timestamp');
    });
  });
});
