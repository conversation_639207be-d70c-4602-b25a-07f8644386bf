/**
 * Enhanced AKE Protocol Testing
 * Tests for improved Authenticated Key Exchange flows
 */

import { OtrSession } from '../../src/core/session';

describe('Enhanced AKE Protocol Testing', () => {
  let aliceSession, bobSession;
  let messageExchange;

  beforeEach(async () => {
    // Create sessions for <PERSON> and <PERSON>
    aliceSession = new OtrSession({ 
      versions: [3, 4],
      testing: true 
    });
    
    bobSession = new OtrSession({ 
      versions: [3, 4],
      testing: true 
    });

    // Message exchange simulator
    messageExchange = [];
  });

  afterEach(() => {
    if (aliceSession) aliceSession.destroy();
    if (bobSession) bobSession.destroy();
  });

  describe('Basic AKE Flow', () => {
    test('should initiate AKE successfully', async () => {
      // Alice starts OTR
      const result = await aliceSession.startOtr();
      expect(result).toBe(true);
      
      // Session should be in appropriate state
      expect(aliceSession.state).toBeDefined();
      expect(aliceSession.instanceTag).toBeGreaterThan(0);
    });

    test('should handle AKE state transitions', async () => {
      const stateHistory = [];
      
      // Monitor Alice's state changes
      aliceSession.onStateChange((state) => {
        stateHistory.push({
          timestamp: Date.now(),
          state: state.state,
          instanceTag: state.instanceTag
        });
      });

      // Start AKE
      await aliceSession.startOtr();
      
      // Bob also starts
      await bobSession.startOtr();

      // Verify state changes were recorded
      expect(stateHistory.length).toBeGreaterThan(0);
      
      // Verify instance tags are set
      const latestState = stateHistory[stateHistory.length - 1];
      expect(latestState.instanceTag).toBeGreaterThan(0);
    });

    test('should generate unique instance tags', async () => {
      const sessions = [];
      const instanceTags = new Set();
      
      // Create multiple sessions
      for (let i = 0; i < 10; i++) {
        const session = new OtrSession({ testing: true });
        sessions.push(session);
        instanceTags.add(session.instanceTag);
      }
      
      // All instance tags should be unique
      expect(instanceTags.size).toBe(10);
      
      // All should be valid 32-bit numbers
      instanceTags.forEach(tag => {
        expect(tag).toBeGreaterThan(0);
        expect(tag).toBeLessThan(0xFFFFFFFF);
      });
      
      // Cleanup
      sessions.forEach(session => session.destroy());
    });
  });

  describe('AKE Message Processing', () => {
    test('should process incoming messages correctly', async () => {
      await aliceSession.startOtr();
      
      // Test with various message types
      const testMessages = [
        'Hello, world!',
        'This is a test message',
        '🔒 Secure message with emoji',
        'A'.repeat(1000) // Long message
      ];
      
      for (const message of testMessages) {
        const encrypted = await aliceSession.encryptMessage(message);
        expect(encrypted).toBeDefined();
        expect(encrypted).toContain('[ENCRYPTED]');
        
        const decrypted = await bobSession.processIncoming(encrypted);
        // In testing mode, this might not work perfectly, but should not throw
        expect(typeof decrypted).toBe('string');
      }
    });

    test('should handle malformed messages gracefully', async () => {
      await aliceSession.startOtr();
      
      const malformedMessages = [
        null,
        undefined,
        '',
        'not-an-otr-message',
        '?OTR:INVALID',
        'random text'
      ];
      
      for (const message of malformedMessages) {
        // Should not throw errors
        const result = await aliceSession.processIncoming(message);
        expect(result === null || typeof result === 'string').toBe(true);
      }
    });

    test('should validate message format', async () => {
      await aliceSession.startOtr();
      
      // Test message encryption format
      const message = 'Test message';
      const encrypted = await aliceSession.encryptMessage(message);
      
      // Should have OTR format markers
      expect(encrypted).toMatch(/\[ENCRYPTED\]/);
      expect(typeof encrypted).toBe('string');
      expect(encrypted.length).toBeGreaterThan(message.length);
    });
  });

  describe('Session State Management', () => {
    test('should maintain session state correctly', async () => {
      // Initial state
      expect(aliceSession.state.state).toBe(0); // PLAINTEXT
      
      // Start OTR
      await aliceSession.startOtr();
      
      // State should change (in testing mode, might go directly to encrypted)
      expect(aliceSession.state.state).toBeGreaterThanOrEqual(0);
    });

    test('should handle session destruction properly', () => {
      const session = new OtrSession({ testing: true });
      
      // Add some mock state
      session.state.sendingAESKey = new Uint8Array([1, 2, 3, 4]);
      session.state.receivingAESKey = new Uint8Array([5, 6, 7, 8]);
      
      // Destroy should not throw
      expect(() => session.destroy()).not.toThrow();
      
      // Callbacks should be cleared
      expect(session.callbacks).toEqual({});
    });

    test('should support version negotiation', () => {
      const session1 = new OtrSession({ versions: [3] });
      const session2 = new OtrSession({ versions: [4] });
      const session3 = new OtrSession({ versions: [3, 4] });
      
      expect(session1.options.versions).toEqual([3]);
      expect(session2.options.versions).toEqual([4]);
      expect(session3.options.versions).toEqual([3, 4]);
      
      // Test version support checking
      expect(session1.getSupportedVersion([3, 4])).toBe(3);
      expect(session2.getSupportedVersion([3, 4])).toBe(4);
      expect(session3.getSupportedVersion([3, 4])).toBe(4); // Should prefer highest
      
      session1.destroy();
      session2.destroy();
      session3.destroy();
    });
  });

  describe('Concurrent Operations', () => {
    test('should handle multiple sessions concurrently', async () => {
      const sessions = [];
      const promises = [];
      
      // Create multiple session pairs
      for (let i = 0; i < 5; i++) {
        const alice = new OtrSession({ testing: true, clientName: `Alice-${i}` });
        const bob = new OtrSession({ testing: true, clientName: `Bob-${i}` });
        sessions.push(alice, bob);
        
        // Start OTR on each pair
        promises.push(
          Promise.all([
            alice.startOtr(),
            bob.startOtr()
          ])
        );
      }
      
      // All should complete successfully
      const results = await Promise.all(promises);
      expect(results).toHaveLength(5);
      
      results.forEach(([aliceResult, bobResult]) => {
        expect(aliceResult).toBe(true);
        expect(bobResult).toBe(true);
      });
      
      // Cleanup
      sessions.forEach(session => session.destroy());
    });

    test('should handle rapid message exchange', async () => {
      await aliceSession.startOtr();
      await bobSession.startOtr();
      
      const messages = Array.from({ length: 10 }, (_, i) => `Message ${i}`);
      const encryptPromises = messages.map(msg => 
        aliceSession.encryptMessage(msg)
      );
      
      const encryptedMessages = await Promise.all(encryptPromises);
      expect(encryptedMessages).toHaveLength(10);
      
      // All should be encrypted
      encryptedMessages.forEach(encrypted => {
        expect(encrypted).toContain('[ENCRYPTED]');
      });
    });
  });

  describe('Error Recovery', () => {
    test('should recover from session errors', async () => {
      await aliceSession.startOtr();
      
      // Simulate error by corrupting state
      const originalState = aliceSession.state.state;
      aliceSession.state.state = -1; // Invalid state
      
      // Should handle gracefully
      const result = await aliceSession.encryptMessage('test');
      expect(typeof result).toBe('string');
      
      // Restore state
      aliceSession.state.state = originalState;
    });

    test('should handle network simulation', async () => {
      await aliceSession.startOtr();
      
      // Simulate network delays
      const delayedEncrypt = async (message) => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return await aliceSession.encryptMessage(message);
      };
      
      const result = await delayedEncrypt('Delayed message');
      expect(result).toBeDefined();
    });

    test('should handle session timeout scenarios', async () => {
      const session = new OtrSession({ 
        testing: true,
        timeout: 100 // Short timeout for testing
      });
      
      await session.startOtr();
      
      // Wait for potential timeout
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Session should still be functional
      const result = await session.encryptMessage('Post-timeout message');
      expect(result).toBeDefined();
      
      session.destroy();
    });
  });

  describe('Performance Testing', () => {
    test('should complete AKE within reasonable time', async () => {
      const startTime = Date.now();
      
      await Promise.all([
        aliceSession.startOtr(),
        bobSession.startOtr()
      ]);
      
      const duration = Date.now() - startTime;
      
      // Should complete within 2 seconds
      expect(duration).toBeLessThan(2000);
    });

    test('should handle message throughput efficiently', async () => {
      await aliceSession.startOtr();
      
      const messageCount = 50;
      const messages = Array.from({ length: messageCount }, (_, i) => `Message ${i}`);
      
      const startTime = Date.now();
      
      const encryptedMessages = await Promise.all(
        messages.map(msg => aliceSession.encryptMessage(msg))
      );
      
      const duration = Date.now() - startTime;
      const throughput = messageCount / (duration / 1000); // messages per second
      
      expect(encryptedMessages).toHaveLength(messageCount);
      expect(throughput).toBeGreaterThan(10); // At least 10 messages/second
    });

    test('should manage memory efficiently', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Create and destroy many sessions
      for (let i = 0; i < 100; i++) {
        const session = new OtrSession({ testing: true });
        await session.startOtr();
        session.destroy();
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });
  });
});
