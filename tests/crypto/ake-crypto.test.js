/**
 * @fileoverview AKE Cryptographic Validation Testing
 * Tests cryptographic operations and validation in AKE protocol
 * Part of Enhanced Testing PRD implementation
 */

import { describe, test, expect, beforeEach } from '@jest/globals';
import { generateDHKeyPair, validateDHParameters, computeDHSharedSecret } from '../../src/core/crypto/dh.js';
import { generateKeyPair, sign, verify } from '../../src/core/crypto/dsa.js';
import { deriveSessionKeys, validateSessionKeys } from '../../src/core/crypto/keys.js';
import { AKEHandler } from '../../src/core/protocol/ake.js';

describe('AKE Cryptographic Validation', () => {
  let dsaKeyPair;
  let dhKeyPair;

  beforeEach(async () => {
    dsaKeyPair = await generateKeyPair();
    dhKeyPair = await generateDHKeyPair();
  });

  describe('DH Parameter Validation', () => {
    test('should validate correct DH parameters', async () => {
      const { publicKey, privateKey } = await generateDHKeyPair();
      
      // Validate the generated parameters
      const isValid = await validateDHParameters(publicKey);
      expect(isValid).toBe(true);
      
      // Verify key pair consistency
      expect(publicKey).toBeInstanceOf(Uint8Array);
      expect(privateKey).toBeInstanceOf(Uint8Array);
      expect(publicKey.length).toBe(192); // 1536-bit key
      expect(privateKey.length).toBe(20);  // 160-bit exponent
    });

    test('should reject invalid DH public keys', async () => {
      // Test various invalid public keys
      const invalidKeys = [
        new Uint8Array(192).fill(0),           // All zeros
        new Uint8Array(192).fill(0xFF),        // All ones
        new Uint8Array(191),                   // Wrong length
        new Uint8Array(193),                   // Wrong length
        new Uint8Array(192).fill(1),           // Too small (< 2)
      ];

      for (const invalidKey of invalidKeys) {
        const isValid = await validateDHParameters(invalidKey);
        expect(isValid).toBe(false);
      }
    });

    test('should validate DH public key is in valid range', async () => {
      // Generate valid key pair
      const { publicKey } = await generateDHKeyPair();
      
      // Verify key is in range [2, p-2] where p is the DH prime
      const isValid = await validateDHParameters(publicKey);
      expect(isValid).toBe(true);
      
      // Test edge cases
      const tooSmall = new Uint8Array(192);
      tooSmall[191] = 1; // Value = 1, should be invalid
      expect(await validateDHParameters(tooSmall)).toBe(false);
    });

    test('should validate DH shared secret computation', async () => {
      const aliceKeys = await generateDHKeyPair();
      const bobKeys = await generateDHKeyPair();
      
      // Compute shared secrets
      const aliceSharedSecret = await computeDHSharedSecret(
        aliceKeys.privateKey, 
        bobKeys.publicKey
      );
      const bobSharedSecret = await computeDHSharedSecret(
        bobKeys.privateKey, 
        aliceKeys.publicKey
      );
      
      // Shared secrets should match
      expect(aliceSharedSecret).toEqual(bobSharedSecret);
      expect(aliceSharedSecret.length).toBe(192); // 1536 bits
      
      // Shared secret should not be zero
      const isZero = aliceSharedSecret.every(byte => byte === 0);
      expect(isZero).toBe(false);
    });

    test('should handle DH parameter edge cases', async () => {
      // Test with maximum valid private key
      const maxPrivateKey = new Uint8Array(20).fill(0xFF);
      maxPrivateKey[0] = 0x7F; // Ensure it's less than q
      
      // Should not throw
      await expect(generateDHKeyPair(maxPrivateKey)).resolves.toBeDefined();
      
      // Test with minimum valid private key
      const minPrivateKey = new Uint8Array(20);
      minPrivateKey[19] = 2; // Minimum value > 1
      
      await expect(generateDHKeyPair(minPrivateKey)).resolves.toBeDefined();
    });
  });

  describe('DSA Signature Verification', () => {
    test('should verify signatures correctly', async () => {
      const message = new TextEncoder().encode('Test message for AKE');
      
      // Sign the message
      const signature = await sign(message, dsaKeyPair.privateKey);
      expect(signature).toBeInstanceOf(Uint8Array);
      expect(signature.length).toBe(40); // r (20 bytes) + s (20 bytes)
      
      // Verify the signature
      const isValid = await verify(message, signature, dsaKeyPair.publicKey);
      expect(isValid).toBe(true);
    });

    test('should reject invalid signatures', async () => {
      const message = new TextEncoder().encode('Test message for AKE');
      const signature = await sign(message, dsaKeyPair.privateKey);
      
      // Test various invalid signatures
      const invalidSignatures = [
        new Uint8Array(40).fill(0),           // All zeros
        new Uint8Array(40).fill(0xFF),        // All ones
        new Uint8Array(39),                   // Wrong length
        signature.slice(0, 20),               // Only r component
        new Uint8Array(40),                   // Random bytes
      ];
      
      for (const invalidSig of invalidSignatures) {
        const isValid = await verify(message, invalidSig, dsaKeyPair.publicKey);
        expect(isValid).toBe(false);
      }
    });

    test('should reject signatures with wrong message', async () => {
      const originalMessage = new TextEncoder().encode('Original message');
      const modifiedMessage = new TextEncoder().encode('Modified message');
      
      const signature = await sign(originalMessage, dsaKeyPair.privateKey);
      
      // Signature should be invalid for modified message
      const isValid = await verify(modifiedMessage, signature, dsaKeyPair.publicKey);
      expect(isValid).toBe(false);
    });

    test('should handle signature edge cases', async () => {
      const message = new TextEncoder().encode('Edge case test');
      
      // Test with empty message
      const emptyMessage = new Uint8Array(0);
      const emptySignature = await sign(emptyMessage, dsaKeyPair.privateKey);
      const emptyValid = await verify(emptyMessage, emptySignature, dsaKeyPair.publicKey);
      expect(emptyValid).toBe(true);
      
      // Test with large message
      const largeMessage = new Uint8Array(10000).fill(0xAA);
      const largeSignature = await sign(largeMessage, dsaKeyPair.privateKey);
      const largeValid = await verify(largeMessage, largeSignature, dsaKeyPair.publicKey);
      expect(largeValid).toBe(true);
    });

    test('should validate AKE-specific signature format', async () => {
      // Create AKE signature data
      const akeData = {
        dhPublicKey: dhKeyPair.publicKey,
        dhSharedSecret: new Uint8Array(192).fill(0xAB),
        instanceTag: 0x12345678
      };
      
      const akeHandler = new AKEHandler(dsaKeyPair);
      const akeSignature = await akeHandler.createAKESignature(akeData);
      
      expect(akeSignature).toBeInstanceOf(Uint8Array);
      expect(akeSignature.length).toBe(40);
      
      // Verify AKE signature
      const isValid = await akeHandler.verifyAKESignature(akeData, akeSignature);
      expect(isValid).toBe(true);
    });
  });

  describe('Session Key Derivation', () => {
    test('should derive session keys properly', async () => {
      const sharedSecret = new Uint8Array(192).fill(0xCD);
      const sessionId = new Uint8Array(8).fill(0x12);
      
      const sessionKeys = await deriveSessionKeys(sharedSecret, sessionId);
      
      expect(sessionKeys).toHaveProperty('sendingAESKey');
      expect(sessionKeys).toHaveProperty('receivingAESKey');
      expect(sessionKeys).toHaveProperty('sendingMACKey');
      expect(sessionKeys).toHaveProperty('receivingMACKey');
      
      // Verify key lengths
      expect(sessionKeys.sendingAESKey.length).toBe(16);    // 128-bit AES key
      expect(sessionKeys.receivingAESKey.length).toBe(16);
      expect(sessionKeys.sendingMACKey.length).toBe(20);    // 160-bit MAC key
      expect(sessionKeys.receivingMACKey.length).toBe(20);
      
      // Keys should be different
      expect(sessionKeys.sendingAESKey).not.toEqual(sessionKeys.receivingAESKey);
      expect(sessionKeys.sendingMACKey).not.toEqual(sessionKeys.receivingMACKey);
    });

    test('should derive deterministic keys from same input', async () => {
      const sharedSecret = new Uint8Array(192).fill(0xEF);
      const sessionId = new Uint8Array(8).fill(0x34);
      
      const keys1 = await deriveSessionKeys(sharedSecret, sessionId);
      const keys2 = await deriveSessionKeys(sharedSecret, sessionId);
      
      // Should be identical
      expect(keys1.sendingAESKey).toEqual(keys2.sendingAESKey);
      expect(keys1.receivingAESKey).toEqual(keys2.receivingAESKey);
      expect(keys1.sendingMACKey).toEqual(keys2.sendingMACKey);
      expect(keys1.receivingMACKey).toEqual(keys2.receivingMACKey);
    });

    test('should derive different keys for different inputs', async () => {
      const sharedSecret1 = new Uint8Array(192).fill(0x11);
      const sharedSecret2 = new Uint8Array(192).fill(0x22);
      const sessionId = new Uint8Array(8).fill(0x56);
      
      const keys1 = await deriveSessionKeys(sharedSecret1, sessionId);
      const keys2 = await deriveSessionKeys(sharedSecret2, sessionId);
      
      // Should be different
      expect(keys1.sendingAESKey).not.toEqual(keys2.sendingAESKey);
      expect(keys1.receivingAESKey).not.toEqual(keys2.receivingAESKey);
      expect(keys1.sendingMACKey).not.toEqual(keys2.sendingMACKey);
      expect(keys1.receivingMACKey).not.toEqual(keys2.receivingMACKey);
    });

    test('should validate session key properties', async () => {
      const sharedSecret = new Uint8Array(192).fill(0x78);
      const sessionId = new Uint8Array(8).fill(0x9A);
      
      const sessionKeys = await deriveSessionKeys(sharedSecret, sessionId);
      
      // Validate keys are not all zeros
      const isAESZero = sessionKeys.sendingAESKey.every(b => b === 0);
      const isMACZero = sessionKeys.sendingMACKey.every(b => b === 0);
      expect(isAESZero).toBe(false);
      expect(isMACZero).toBe(false);
      
      // Validate key entropy (should have reasonable distribution)
      const aesUnique = new Set(sessionKeys.sendingAESKey).size;
      const macUnique = new Set(sessionKeys.sendingMACKey).size;
      expect(aesUnique).toBeGreaterThan(8); // At least 8 unique bytes
      expect(macUnique).toBeGreaterThan(10); // At least 10 unique bytes
    });

    test('should handle key derivation edge cases', async () => {
      // Test with all-zero shared secret
      const zeroSecret = new Uint8Array(192);
      const sessionId = new Uint8Array(8).fill(0xBC);
      
      const zeroKeys = await deriveSessionKeys(zeroSecret, sessionId);
      expect(zeroKeys.sendingAESKey).toBeDefined();
      
      // Test with all-one shared secret
      const oneSecret = new Uint8Array(192).fill(0xFF);
      const oneKeys = await deriveSessionKeys(oneSecret, sessionId);
      expect(oneKeys.sendingAESKey).toBeDefined();
      
      // Should be different
      expect(zeroKeys.sendingAESKey).not.toEqual(oneKeys.sendingAESKey);
    });
  });

  describe('Cryptographic Integration', () => {
    test('should perform complete cryptographic AKE flow', async () => {
      // Generate two key pairs for Alice and Bob
      const aliceDSA = await generateKeyPair();
      const bobDSA = await generateKeyPair();
      
      const aliceDH = await generateDHKeyPair();
      const bobDH = await generateDHKeyPair();
      
      // Compute shared secret
      const sharedSecret = await computeDHSharedSecret(
        aliceDH.privateKey, 
        bobDH.publicKey
      );
      
      // Create session ID (simplified)
      const sessionId = new Uint8Array(8);
      crypto.getRandomValues(sessionId);
      
      // Derive session keys
      const aliceKeys = await deriveSessionKeys(sharedSecret, sessionId);
      const bobKeys = await deriveSessionKeys(sharedSecret, sessionId);
      
      // Keys should match (Alice's sending = Bob's receiving, etc.)
      expect(aliceKeys.sendingAESKey).toEqual(bobKeys.receivingAESKey);
      expect(aliceKeys.receivingAESKey).toEqual(bobKeys.sendingAESKey);
      expect(aliceKeys.sendingMACKey).toEqual(bobKeys.receivingMACKey);
      expect(aliceKeys.receivingMACKey).toEqual(bobKeys.sendingMACKey);
    });

    test('should validate complete AKE signature verification', async () => {
      const aliceHandler = new AKEHandler(dsaKeyPair);
      
      // Create complete AKE signature data
      const akeData = {
        dhPublicKey: dhKeyPair.publicKey,
        dhSharedSecret: new Uint8Array(192).fill(0xDE),
        instanceTag: 0x87654321,
        remotePublicKey: (await generateKeyPair()).publicKey
      };
      
      // Create and verify signature
      const signature = await aliceHandler.createAKESignature(akeData);
      const isValid = await aliceHandler.verifyAKESignature(akeData, signature);
      
      expect(isValid).toBe(true);
      
      // Modify data and verify signature becomes invalid
      akeData.instanceTag = 0x11111111;
      const isInvalid = await aliceHandler.verifyAKESignature(akeData, signature);
      expect(isInvalid).toBe(false);
    });
  });
});
