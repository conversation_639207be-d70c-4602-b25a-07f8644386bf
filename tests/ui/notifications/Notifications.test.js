/**
 * Tests for Notifications UI component
 */

import { Notifications, createNotification } from '../../../src/ui/notifications';

// Mock DOM elements
class MockElement {
  constructor() {
    this.classList = {
      add: jest.fn(),
      remove: jest.fn(),
      contains: jest.fn()
    };
    this.style = {};
    this.innerHTML = '';
    this.appendChild = jest.fn();
    this.querySelector = jest.fn();
    this.querySelectorAll = jest.fn().mockReturnValue([]);
    this.addEventListener = jest.fn();
    this.removeEventListener = jest.fn();
    this.setAttribute = jest.fn();
    this.getAttribute = jest.fn();
    this.dataset = {};
  }
}

// Mock document
global.document = {
  createElement: jest.fn().mockImplementation(() => new MockElement()),
  querySelector: jest.fn().mockImplementation(() => new MockElement()),
  body: new MockElement()
};

// Mock window
global.window = {
  setTimeout: jest.fn().mockImplementation((callback, delay) => {
    // Call the callback immediately for testing
    callback();
    return 123; // Mock timer ID
  }),
  clearTimeout: jest.fn()
};

describe('Notifications UI Component', () => {
  // Test initialization
  describe('Initialization', () => {
    test('should initialize with default options', () => {
      const notifications = new Notifications();
      
      expect(notifications).toBeDefined();
      expect(notifications.container).toBeDefined();
      expect(notifications.options).toBeDefined();
    });
    
    test('should initialize with custom options', () => {
      const container = new MockElement();
      const options = {
        position: 'top-right',
        duration: 5000
      };
      
      const notifications = new Notifications(container, options);
      
      expect(notifications).toBeDefined();
      expect(notifications.container).toBe(container);
      expect(notifications.options.position).toBe('top-right');
      expect(notifications.options.duration).toBe(5000);
    });
    
    test('should create container if not provided', () => {
      const notifications = new Notifications();
      
      // Verify createElement was called
      expect(document.createElement).toHaveBeenCalled();
      
      // Verify container was added to document.body
      expect(document.body.appendChild).toHaveBeenCalled();
    });
  });
  
  // Test notification creation
  describe('Notification Creation', () => {
    test('should create a notification with message', () => {
      const notifications = new Notifications();
      const message = 'Test notification';
      
      // Mock the internal _createNotificationElement method
      notifications._createNotificationElement = jest.fn().mockReturnValue(new MockElement());
      
      notifications.show(message);
      
      // Verify _createNotificationElement was called with message
      expect(notifications._createNotificationElement).toHaveBeenCalledWith(
        message, 
        expect.any(Object)
      );
      
      // Verify notification was added to container
      expect(notifications.container.appendChild).toHaveBeenCalled();
    });
    
    test('should create a notification with custom type', () => {
      const notifications = new Notifications();
      const message = 'Test notification';
      const options = { type: 'error' };
      
      // Mock the internal _createNotificationElement method
      notifications._createNotificationElement = jest.fn().mockReturnValue(new MockElement());
      
      notifications.show(message, options);
      
      // Verify _createNotificationElement was called with message and options
      expect(notifications._createNotificationElement).toHaveBeenCalledWith(
        message, 
        expect.objectContaining({ type: 'error' })
      );
    });
    
    test('should create a notification with custom duration', () => {
      const notifications = new Notifications();
      const message = 'Test notification';
      const options = { duration: 10000 };
      
      // Mock the internal _createNotificationElement method
      notifications._createNotificationElement = jest.fn().mockReturnValue(new MockElement());
      
      notifications.show(message, options);
      
      // Verify _createNotificationElement was called with message and options
      expect(notifications._createNotificationElement).toHaveBeenCalledWith(
        message, 
        expect.objectContaining({ duration: 10000 })
      );
      
      // Verify setTimeout was called with custom duration
      expect(window.setTimeout).toHaveBeenCalledWith(
        expect.any(Function),
        10000
      );
    });
  });
  
  // Test notification removal
  describe('Notification Removal', () => {
    test('should remove notification after duration', () => {
      const notifications = new Notifications();
      const message = 'Test notification';
      
      // Create a mock notification element
      const notificationElement = new MockElement();
      notifications._createNotificationElement = jest.fn().mockReturnValue(notificationElement);
      
      notifications.show(message);
      
      // Verify setTimeout was called
      expect(window.setTimeout).toHaveBeenCalled();
      
      // Verify notification was removed
      expect(notifications.container.removeChild).toHaveBeenCalledWith(notificationElement);
    });
    
    test('should remove notification when close button is clicked', () => {
      const notifications = new Notifications();
      const message = 'Test notification';
      
      // Create a mock notification element
      const notificationElement = new MockElement();
      const closeButton = new MockElement();
      notificationElement.querySelector.mockReturnValue(closeButton);
      
      notifications._createNotificationElement = jest.fn().mockReturnValue(notificationElement);
      
      notifications.show(message);
      
      // Get the click handler for the close button
      const clickHandlerCall = closeButton.addEventListener.mock.calls.find(
        call => call[0] === 'click'
      );
      
      if (clickHandlerCall && clickHandlerCall[1]) {
        // Simulate click on close button
        clickHandlerCall[1]();
        
        // Verify notification was removed
        expect(notifications.container.removeChild).toHaveBeenCalledWith(notificationElement);
      }
    });
    
    test('should clear timeout when notification is removed manually', () => {
      const notifications = new Notifications();
      const message = 'Test notification';
      
      // Create a mock notification element
      const notificationElement = new MockElement();
      const closeButton = new MockElement();
      notificationElement.querySelector.mockReturnValue(closeButton);
      
      notifications._createNotificationElement = jest.fn().mockReturnValue(notificationElement);
      
      // Show notification
      notifications.show(message);
      
      // Get the click handler for the close button
      const clickHandlerCall = closeButton.addEventListener.mock.calls.find(
        call => call[0] === 'click'
      );
      
      if (clickHandlerCall && clickHandlerCall[1]) {
        // Simulate click on close button
        clickHandlerCall[1]();
        
        // Verify clearTimeout was called
        expect(window.clearTimeout).toHaveBeenCalled();
      }
    });
  });
  
  // Test factory function
  describe('Factory Function', () => {
    test('should create notification using factory function', () => {
      const container = new MockElement();
      const message = 'Test notification';
      const options = { type: 'success' };
      
      // Mock Notifications.prototype.show
      const originalShow = Notifications.prototype.show;
      Notifications.prototype.show = jest.fn();
      
      createNotification(message, options, container);
      
      // Verify Notifications.prototype.show was called
      expect(Notifications.prototype.show).toHaveBeenCalledWith(message, options);
      
      // Restore original method
      Notifications.prototype.show = originalShow;
    });
  });
  
  // Test cleanup
  describe('Cleanup', () => {
    test('should remove container on destroy', () => {
      const container = new MockElement();
      document.body.contains = jest.fn().mockReturnValue(true);
      
      const notifications = new Notifications(container);
      
      // Add destroy method if it doesn't exist
      if (!notifications.destroy) {
        notifications.destroy = function() {
          if (document.body.contains(this.container)) {
            document.body.removeChild(this.container);
          }
        };
      }
      
      notifications.destroy();
      
      // Verify container was removed
      expect(document.body.removeChild).toHaveBeenCalledWith(container);
    });
  });
});
