/**
 * Tests for StatusIndicator UI component
 */

import { StatusIndicator } from '../../../../src/ui/components/status/StatusIndicator';
import { STATE } from '../../../../src/core/protocol/state';

// Mock DOM elements
class MockElement {
  constructor() {
    this.classList = {
      add: jest.fn(),
      remove: jest.fn(),
      contains: jest.fn()
    };
    this.style = {};
    this.innerHTML = '';
    this.appendChild = jest.fn();
    this.querySelector = jest.fn();
    this.querySelectorAll = jest.fn().mockReturnValue([]);
    this.addEventListener = jest.fn();
    this.removeEventListener = jest.fn();
  }
}

// Mock document
global.document = {
  createElement: jest.fn().mockImplementation(() => new MockElement()),
  querySelector: jest.fn().mockImplementation(() => new MockElement())
};

describe('StatusIndicator UI Component', () => {
  // Test initialization
  describe('Initialization', () => {
    test('should initialize with default options', () => {
      const container = new MockElement();
      const statusIndicator = new StatusIndicator(container);
      
      expect(statusIndicator).toBeDefined();
      expect(statusIndicator.container).toBe(container);
      expect(statusIndicator.options).toBeDefined();
    });
    
    test('should initialize with custom options', () => {
      const container = new MockElement();
      const options = {
        showText: true,
        clickable: true
      };
      
      const statusIndicator = new StatusIndicator(container, options);
      
      expect(statusIndicator).toBeDefined();
      expect(statusIndicator.options.showText).toBe(true);
      expect(statusIndicator.options.clickable).toBe(true);
    });
    
    test('should create DOM elements on initialization', () => {
      const container = new MockElement();
      const statusIndicator = new StatusIndicator(container);
      
      // Verify createElement was called
      expect(document.createElement).toHaveBeenCalled();
      
      // Verify elements were added to container
      expect(container.appendChild).toHaveBeenCalled();
    });
    
    test('should throw error with invalid container', () => {
      expect(() => new StatusIndicator(null)).toThrow();
      expect(() => new StatusIndicator(undefined)).toThrow();
    });
  });
  
  // Test state updates
  describe('State Updates', () => {
    test('should update UI for PLAINTEXT state', () => {
      const container = new MockElement();
      const statusIndicator = new StatusIndicator(container);
      
      statusIndicator.updateState(STATE.PLAINTEXT);
      
      // Verify classes were updated
      expect(statusIndicator.element.classList.remove).toHaveBeenCalledWith('encrypted');
      expect(statusIndicator.element.classList.remove).toHaveBeenCalledWith('in-progress');
    });
    
    test('should update UI for ENCRYPTED state', () => {
      const container = new MockElement();
      const statusIndicator = new StatusIndicator(container);
      
      statusIndicator.updateState(STATE.ENCRYPTED);
      
      // Verify classes were updated
      expect(statusIndicator.element.classList.add).toHaveBeenCalledWith('encrypted');
      expect(statusIndicator.element.classList.remove).toHaveBeenCalledWith('in-progress');
    });
    
    test('should update UI for AKE in progress states', () => {
      const container = new MockElement();
      const statusIndicator = new StatusIndicator(container);
      
      // Test AWAITING_DHKEY state
      statusIndicator.updateState(STATE.AWAITING_DHKEY);
      
      // Verify classes were updated
      expect(statusIndicator.element.classList.remove).toHaveBeenCalledWith('encrypted');
      expect(statusIndicator.element.classList.add).toHaveBeenCalledWith('in-progress');
      
      // Reset mocks
      statusIndicator.element.classList.add.mockClear();
      statusIndicator.element.classList.remove.mockClear();
      
      // Test AWAITING_REVEALSIG state
      statusIndicator.updateState(STATE.AWAITING_REVEALSIG);
      
      // Verify classes were updated
      expect(statusIndicator.element.classList.remove).toHaveBeenCalledWith('encrypted');
      expect(statusIndicator.element.classList.add).toHaveBeenCalledWith('in-progress');
    });
    
    test('should update text content when showText is true', () => {
      const container = new MockElement();
      const options = { showText: true };
      const statusIndicator = new StatusIndicator(container, options);
      
      // Create a mock text element
      const textElement = new MockElement();
      statusIndicator.textElement = textElement;
      
      // Test PLAINTEXT state
      statusIndicator.updateState(STATE.PLAINTEXT);
      expect(textElement.innerHTML).toContain('Not Encrypted');
      
      // Test ENCRYPTED state
      statusIndicator.updateState(STATE.ENCRYPTED);
      expect(textElement.innerHTML).toContain('Encrypted');
      
      // Test AKE in progress state
      statusIndicator.updateState(STATE.AWAITING_DHKEY);
      expect(textElement.innerHTML).toContain('Establishing');
    });
  });
  
  // Test click handling
  describe('Click Handling', () => {
    test('should register click handler when clickable is true', () => {
      const container = new MockElement();
      const options = { clickable: true };
      const statusIndicator = new StatusIndicator(container, options);
      
      // Verify addEventListener was called
      expect(statusIndicator.element.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
    });
    
    test('should not register click handler when clickable is false', () => {
      const container = new MockElement();
      const options = { clickable: false };
      const statusIndicator = new StatusIndicator(container, options);
      
      // Verify addEventListener was not called with 'click'
      const addEventListenerCalls = statusIndicator.element.addEventListener.mock.calls;
      const clickHandlerCalls = addEventListenerCalls.filter(call => call[0] === 'click');
      expect(clickHandlerCalls.length).toBe(0);
    });
    
    test('should call onClick callback when clicked', () => {
      const container = new MockElement();
      const mockOnClick = jest.fn();
      const options = { clickable: true, onClick: mockOnClick };
      const statusIndicator = new StatusIndicator(container, options);
      
      // Get the click handler
      const addEventListenerCalls = statusIndicator.element.addEventListener.mock.calls;
      const clickHandlerCall = addEventListenerCalls.find(call => call[0] === 'click');
      const clickHandler = clickHandlerCall[1];
      
      // Simulate click
      clickHandler();
      
      // Verify onClick was called
      expect(mockOnClick).toHaveBeenCalled();
    });
  });
  
  // Test cleanup
  describe('Cleanup', () => {
    test('should remove event listeners on destroy', () => {
      const container = new MockElement();
      const options = { clickable: true };
      const statusIndicator = new StatusIndicator(container, options);
      
      statusIndicator.destroy();
      
      // Verify removeEventListener was called
      expect(statusIndicator.element.removeEventListener).toHaveBeenCalled();
    });
    
    test('should remove elements from DOM on destroy', () => {
      const container = new MockElement();
      container.contains = jest.fn().mockReturnValue(true);
      container.removeChild = jest.fn();
      
      const statusIndicator = new StatusIndicator(container);
      
      statusIndicator.destroy();
      
      // Verify removeChild was called
      expect(container.removeChild).toHaveBeenCalled();
    });
  });
});
