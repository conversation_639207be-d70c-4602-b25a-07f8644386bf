/**
 * Tests for QRCodeVerifier UI component
 */

// Mock CSS import
jest.mock("/src/ui/components/verification/qr-code-verifier.css", () => ({}), {
  virtual: true,
});

import { QRCodeVerifier } from "../../../../src/ui/components/verification/QRCodeVerifier";

// Mock DOM elements
class MockElement {
  constructor() {
    this.classList = {
      add: jest.fn(),
      remove: jest.fn(),
      contains: jest.fn(),
    };
    this.style = {};
    this.innerHTML = "";
    this.appendChild = jest.fn();
    this.querySelector = jest.fn();
    this.querySelectorAll = jest.fn().mockReturnValue([]);
    this.addEventListener = jest.fn();
    this.removeEventListener = jest.fn();
    this.setAttribute = jest.fn();
    this.getAttribute = jest.fn();
    this.dataset = {};
  }
}

// Mock document
global.document = {
  createElement: jest.fn().mockImplementation(() => new MockElement()),
  querySelector: jest.fn().mockImplementation(() => new MockElement()),
};

describe("QRCodeVerifier UI Component", () => {
  // Test initialization
  describe("Initialization", () => {
    test("should initialize with default options", () => {
      const container = new MockElement();
      const qrVerifier = new QRCodeVerifier(container);

      expect(qrVerifier).toBeDefined();
      expect(qrVerifier.container).toBe(container);
      expect(qrVerifier.options).toBeDefined();
    });

    test("should initialize with custom options", () => {
      const container = new MockElement();
      const options = {
        size: 300,
        darkColor: "#000000",
        lightColor: "#FFFFFF",
      };

      const qrVerifier = new QRCodeVerifier(container, options);

      expect(qrVerifier).toBeDefined();
      expect(qrVerifier.options.size).toBe(300);
      expect(qrVerifier.options.darkColor).toBe("#000000");
      expect(qrVerifier.options.lightColor).toBe("#FFFFFF");
    });

    test("should create DOM elements on initialization", () => {
      const container = new MockElement();
      const qrVerifier = new QRCodeVerifier(container);

      // Verify createElement was called
      expect(document.createElement).toHaveBeenCalled();

      // Verify elements were added to container
      expect(container.appendChild).toHaveBeenCalled();
    });

    test("should throw error with invalid container", () => {
      expect(() => new QRCodeVerifier(null)).toThrow();
      expect(() => new QRCodeVerifier(undefined)).toThrow();
    });
  });

  // Test QR code generation
  describe("QR Code Generation", () => {
    test("should generate QR code with provided data", () => {
      const container = new MockElement();
      const qrVerifier = new QRCodeVerifier(container);

      // Mock the QR code generation method
      qrVerifier._generateQRCode = jest.fn();

      const data = "test-fingerprint-data";
      qrVerifier.setData(data);

      // Verify QR code generation was called
      expect(qrVerifier._generateQRCode).toHaveBeenCalledWith(data);
    });

    test("should update QR code when data changes", () => {
      const container = new MockElement();
      const qrVerifier = new QRCodeVerifier(container);

      // Mock the QR code generation method
      qrVerifier._generateQRCode = jest.fn();

      // Set initial data
      const initialData = "initial-data";
      qrVerifier.setData(initialData);

      // Verify QR code generation was called
      expect(qrVerifier._generateQRCode).toHaveBeenCalledWith(initialData);

      // Reset mock
      qrVerifier._generateQRCode.mockClear();

      // Set new data
      const newData = "new-data";
      qrVerifier.setData(newData);

      // Verify QR code generation was called again
      expect(qrVerifier._generateQRCode).toHaveBeenCalledWith(newData);
    });

    test("should not regenerate QR code if data is the same", () => {
      const container = new MockElement();
      const qrVerifier = new QRCodeVerifier(container);

      // Mock the QR code generation method
      qrVerifier._generateQRCode = jest.fn();

      // Set initial data
      const data = "test-data";
      qrVerifier.setData(data);

      // Verify QR code generation was called
      expect(qrVerifier._generateQRCode).toHaveBeenCalledWith(data);

      // Reset mock
      qrVerifier._generateQRCode.mockClear();

      // Set same data again
      qrVerifier.setData(data);

      // Verify QR code generation was not called again
      expect(qrVerifier._generateQRCode).not.toHaveBeenCalled();
    });
  });

  // Test QR code scanning
  describe("QR Code Scanning", () => {
    test("should start scanning when requested", () => {
      const container = new MockElement();
      const qrVerifier = new QRCodeVerifier(container);

      // Mock the scanning methods
      qrVerifier._setupCamera = jest.fn().mockResolvedValue();
      qrVerifier._startScanning = jest.fn();

      // Start scanning
      qrVerifier.startScanning();

      // Verify setup was called
      expect(qrVerifier._setupCamera).toHaveBeenCalled();
    });

    test("should stop scanning when requested", () => {
      const container = new MockElement();
      const qrVerifier = new QRCodeVerifier(container);

      // Mock video element and stream
      const mockVideoElement = new MockElement();
      qrVerifier.videoElement = mockVideoElement;

      const mockMediaStream = {
        getTracks: jest.fn().mockReturnValue([{ stop: jest.fn() }]),
      };
      mockVideoElement.srcObject = mockMediaStream;

      // Stop scanning
      qrVerifier.stopScanning();

      // Verify tracks were stopped
      expect(mockMediaStream.getTracks).toHaveBeenCalled();
      expect(mockMediaStream.getTracks()[0].stop).toHaveBeenCalled();
    });

    test("should call onScan callback when QR code is detected", () => {
      const container = new MockElement();
      const mockOnScan = jest.fn();
      const options = { onScan: mockOnScan };
      const qrVerifier = new QRCodeVerifier(container, options);

      // Simulate QR code detection
      const detectedData = "detected-qr-data";
      qrVerifier._onQRCodeDetected(detectedData);

      // Verify onScan was called
      expect(mockOnScan).toHaveBeenCalledWith(detectedData);
    });
  });

  // Test verification
  describe("Verification", () => {
    test("should verify matching fingerprints", () => {
      const container = new MockElement();
      const qrVerifier = new QRCodeVerifier(container);

      const fingerprint1 = "test-fingerprint-data";
      const fingerprint2 = "test-fingerprint-data";

      const result = qrVerifier.verifyFingerprints(fingerprint1, fingerprint2);

      expect(result).toBe(true);
    });

    test("should reject non-matching fingerprints", () => {
      const container = new MockElement();
      const qrVerifier = new QRCodeVerifier(container);

      const fingerprint1 = "fingerprint-1";
      const fingerprint2 = "fingerprint-2";

      const result = qrVerifier.verifyFingerprints(fingerprint1, fingerprint2);

      expect(result).toBe(false);
    });

    test("should call onVerificationResult callback with result", () => {
      const container = new MockElement();
      const mockOnVerificationResult = jest.fn();
      const options = { onVerificationResult: mockOnVerificationResult };
      const qrVerifier = new QRCodeVerifier(container, options);

      // Test with matching fingerprints
      const fingerprint1 = "test-fingerprint";
      const fingerprint2 = "test-fingerprint";

      qrVerifier.verifyFingerprints(fingerprint1, fingerprint2);

      // Verify callback was called with true
      expect(mockOnVerificationResult).toHaveBeenCalledWith(true);

      // Reset mock
      mockOnVerificationResult.mockClear();

      // Test with non-matching fingerprints
      const fingerprint3 = "different-fingerprint";

      qrVerifier.verifyFingerprints(fingerprint1, fingerprint3);

      // Verify callback was called with false
      expect(mockOnVerificationResult).toHaveBeenCalledWith(false);
    });
  });

  // Test UI state updates
  describe("UI State Updates", () => {
    test("should show verification result in UI", () => {
      const container = new MockElement();
      const qrVerifier = new QRCodeVerifier(container);

      // Create mock result element
      const resultElement = new MockElement();
      qrVerifier.resultElement = resultElement;

      // Test success state
      qrVerifier.showVerificationResult(true);

      // Verify success class was added
      expect(resultElement.classList.add).toHaveBeenCalledWith("success");
      expect(resultElement.classList.remove).toHaveBeenCalledWith("error");

      // Reset mocks
      resultElement.classList.add.mockClear();
      resultElement.classList.remove.mockClear();

      // Test failure state
      qrVerifier.showVerificationResult(false);

      // Verify error class was added
      expect(resultElement.classList.add).toHaveBeenCalledWith("error");
      expect(resultElement.classList.remove).toHaveBeenCalledWith("success");
    });

    test("should update UI when switching between generate and scan modes", () => {
      const container = new MockElement();
      const qrVerifier = new QRCodeVerifier(container);

      // Create mock elements
      const generateElement = new MockElement();
      const scanElement = new MockElement();
      qrVerifier.generateElement = generateElement;
      qrVerifier.scanElement = scanElement;

      // Test switching to scan mode
      qrVerifier.showScanMode();

      // Verify classes were updated
      expect(generateElement.classList.add).toHaveBeenCalledWith("hidden");
      expect(scanElement.classList.remove).toHaveBeenCalledWith("hidden");

      // Reset mocks
      generateElement.classList.add.mockClear();
      generateElement.classList.remove.mockClear();
      scanElement.classList.add.mockClear();
      scanElement.classList.remove.mockClear();

      // Test switching to generate mode
      qrVerifier.showGenerateMode();

      // Verify classes were updated
      expect(generateElement.classList.remove).toHaveBeenCalledWith("hidden");
      expect(scanElement.classList.add).toHaveBeenCalledWith("hidden");
    });
  });

  // Test cleanup
  describe("Cleanup", () => {
    test("should stop scanning on destroy", () => {
      const container = new MockElement();
      const qrVerifier = new QRCodeVerifier(container);

      // Mock stopScanning method
      qrVerifier.stopScanning = jest.fn();

      qrVerifier.destroy();

      // Verify stopScanning was called
      expect(qrVerifier.stopScanning).toHaveBeenCalled();
    });

    test("should remove elements from DOM on destroy", () => {
      const container = new MockElement();
      container.contains = jest.fn().mockReturnValue(true);
      container.removeChild = jest.fn();

      const qrVerifier = new QRCodeVerifier(container);

      qrVerifier.destroy();

      // Verify removeChild was called
      expect(container.removeChild).toHaveBeenCalled();
    });
  });
});
