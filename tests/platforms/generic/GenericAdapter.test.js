/**
 * Tests for Generic platform adapter
 */

import GenericAdapter from '../../../src/platforms/generic';
import { OtrSession } from '../../../src/core/session';

// Mock OtrSession
jest.mock('../../../src/core/session', () => ({
  OtrSession: jest.fn().mockImplementation(() => ({
    init: jest.fn().mockResolvedValue({
      sendMessage: jest.fn(),
      processIncoming: jest.fn(),
      startOtr: jest.fn(),
      endOtr: jest.fn()
    }),
    recipient: 'test-user'
  }))
}));

describe('Generic Platform Adapter', () => {
  // Test initialization
  describe('Initialization', () => {
    test('should initialize adapter', async () => {
      const adapter = new GenericAdapter();
      
      await adapter.init();
      
      expect(adapter.initialized).toBe(true);
    });
    
    test('should add UI elements during initialization', async () => {
      const adapter = new GenericAdapter();
      
      // Mock the addUIElements method
      adapter.addUIElements = jest.fn();
      
      await adapter.init();
      
      // Verify addUIElements was called
      expect(adapter.addUIElements).toHaveBeenCalled();
    });
  });
  
  // Test session management
  describe('Session Management', () => {
    test('should create a new session for a peer', async () => {
      const adapter = new GenericAdapter();
      const peerId = 'test-user';
      
      const session = await adapter.getSession(peerId);
      
      // Verify session was created
      expect(session).toBeDefined();
      expect(OtrSession).toHaveBeenCalledWith(peerId);
      expect(adapter.sessions.has(peerId)).toBe(true);
    });
    
    test('should return existing session for a peer', async () => {
      const adapter = new GenericAdapter();
      const peerId = 'test-user';
      
      // Create first session
      const session1 = await adapter.getSession(peerId);
      
      // Reset the mock to verify it's not called again
      OtrSession.mockClear();
      
      // Get session again
      const session2 = await adapter.getSession(peerId);
      
      // Verify same session was returned
      expect(session2).toBe(session1);
      expect(OtrSession).not.toHaveBeenCalled();
    });
    
    test('should handle multiple peers', async () => {
      const adapter = new GenericAdapter();
      const peer1 = 'user1';
      const peer2 = 'user2';
      
      // Create sessions for both peers
      const session1 = await adapter.getSession(peer1);
      const session2 = await adapter.getSession(peer2);
      
      // Verify both sessions were created
      expect(session1).not.toBe(session2);
      expect(adapter.sessions.has(peer1)).toBe(true);
      expect(adapter.sessions.has(peer2)).toBe(true);
    });
  });
  
  // Test message processing
  describe('Message Processing', () => {
    test('should process outgoing message', async () => {
      const adapter = new GenericAdapter();
      const peerId = 'test-user';
      const message = 'Hello, world!';
      
      // Initialize adapter
      await adapter.init();
      
      // Process outgoing message
      const processedMessage = await adapter.processOutgoing(message, peerId);
      
      // For generic adapter, should return the original message
      expect(processedMessage).toBe(message);
    });
    
    test('should process incoming message', async () => {
      const adapter = new GenericAdapter();
      const peerId = 'test-user';
      const message = 'Hello, world!';
      
      // Initialize adapter
      await adapter.init();
      
      // Process incoming message
      const processedMessage = await adapter.processIncoming(message, peerId);
      
      // For generic adapter, should return the original message
      expect(processedMessage).toBe(message);
    });
  });
  
  // Test UI integration
  describe('UI Integration', () => {
    test('should add UI elements', () => {
      const adapter = new GenericAdapter();
      
      // Mock document.createElement
      const originalCreateElement = document.createElement;
      document.createElement = jest.fn().mockReturnValue({
        classList: { add: jest.fn() },
        style: {},
        appendChild: jest.fn()
      });
      
      // Mock document.body
      const originalBody = document.body;
      document.body = { appendChild: jest.fn() };
      
      // Call addUIElements
      adapter.addUIElements();
      
      // Verify document.createElement was called
      expect(document.createElement).toHaveBeenCalled();
      
      // Restore original methods
      document.createElement = originalCreateElement;
      document.body = originalBody;
    });
  });
});
