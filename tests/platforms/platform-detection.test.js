/**
 * Tests for platform detection and adapter selection
 */

import { detectPlatform, getPlatformAdapter } from '../../src/platforms';
import DiscordAdapter from '../../src/platforms/discord';
import TeamsAdapter from '../../src/platforms/teams';
import SlackAdapter from '../../src/platforms/slack';
import GenericAdapter from '../../src/platforms/generic';

// Mock window.location for testing
const originalLocation = global.window ? global.window.location : undefined;

describe('Platform Detection and Adapter Selection', () => {
  // Setup and teardown for window.location mocking
  beforeEach(() => {
    // Create a mock window.location
    delete global.window;
    global.window = {
      location: {
        href: '',
        hostname: ''
      }
    };
  });

  afterEach(() => {
    // Restore original window.location
    if (originalLocation) {
      global.window.location = originalLocation;
    } else {
      delete global.window;
    }
  });

  // Test platform detection
  describe('detectPlatform', () => {
    test('should detect Discord platform', () => {
      // Set window.location to Discord URL
      global.window.location.hostname = 'discord.com';
      global.window.location.href = 'https://discord.com/channels/123456789';
      
      const platform = detectPlatform();
      
      expect(platform).toBe('discord');
    });
    
    test('should detect Microsoft Teams platform', () => {
      // Set window.location to Teams URL
      global.window.location.hostname = 'teams.microsoft.com';
      global.window.location.href = 'https://teams.microsoft.com/_#/conversations/123456789';
      
      const platform = detectPlatform();
      
      expect(platform).toBe('teams');
    });
    
    test('should detect Slack platform', () => {
      // Set window.location to Slack URL
      global.window.location.hostname = 'app.slack.com';
      global.window.location.href = 'https://app.slack.com/client/123456789';
      
      const platform = detectPlatform();
      
      expect(platform).toBe('slack');
    });
    
    test('should return "generic" for unknown platforms', () => {
      // Set window.location to unknown URL
      global.window.location.hostname = 'example.com';
      global.window.location.href = 'https://example.com';
      
      const platform = detectPlatform();
      
      expect(platform).toBe('generic');
    });
    
    test('should handle null or undefined window.location', () => {
      // Remove window.location
      delete global.window.location;
      
      const platform = detectPlatform();
      
      // Should default to generic
      expect(platform).toBe('generic');
    });
  });
  
  // Test platform adapter selection
  describe('getPlatformAdapter', () => {
    test('should return DiscordAdapter for Discord platform', () => {
      // Set window.location to Discord URL
      global.window.location.hostname = 'discord.com';
      global.window.location.href = 'https://discord.com/channels/123456789';
      
      const adapter = getPlatformAdapter();
      
      expect(adapter).toBeInstanceOf(DiscordAdapter);
    });
    
    test('should return TeamsAdapter for Microsoft Teams platform', () => {
      // Set window.location to Teams URL
      global.window.location.hostname = 'teams.microsoft.com';
      global.window.location.href = 'https://teams.microsoft.com/_#/conversations/123456789';
      
      const adapter = getPlatformAdapter();
      
      expect(adapter).toBeInstanceOf(TeamsAdapter);
    });
    
    test('should return SlackAdapter for Slack platform', () => {
      // Set window.location to Slack URL
      global.window.location.hostname = 'app.slack.com';
      global.window.location.href = 'https://app.slack.com/client/123456789';
      
      const adapter = getPlatformAdapter();
      
      expect(adapter).toBeInstanceOf(SlackAdapter);
    });
    
    test('should return GenericAdapter for unknown platforms', () => {
      // Set window.location to unknown URL
      global.window.location.hostname = 'example.com';
      global.window.location.href = 'https://example.com';
      
      const adapter = getPlatformAdapter();
      
      expect(adapter).toBeInstanceOf(GenericAdapter);
    });
  });
});
