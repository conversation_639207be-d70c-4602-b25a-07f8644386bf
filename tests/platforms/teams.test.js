/**
 * Microsoft Teams Platform Adapter Tests
 * Tests the Teams-specific OTR integration including message interception,
 * UI injection, and Teams API interactions.
 */

import { TeamsAdapter } from '../../src/platforms/teams/index.js';
import { OtrSession } from '../../src/core/session/index.js';

// Mock DOM environment
const mockDOM = {
  document: {
    querySelector: jest.fn(),
    querySelectorAll: jest.fn(),
    createElement: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  },
  window: {
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    location: { href: 'https://teams.microsoft.com/chat' }
  }
};

// Mock Teams-specific elements
const mockTeamsElements = {
  messageInput: {
    value: '',
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    focus: jest.fn(),
    blur: jest.fn()
  },
  sendButton: {
    click: jest.fn(),
    addEventListener: jest.fn(),
    disabled: false
  },
  messageContainer: {
    appendChild: jest.fn(),
    removeChild: jest.fn(),
    children: []
  }
};

describe('Microsoft Teams Platform Adapter', () => {
  let adapter;
  let mockOtrSession;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock OTR session
    mockOtrSession = {
      processIncoming: jest.fn(),
      processOutgoing: jest.fn(),
      startOtr: jest.fn(),
      endOtr: jest.fn(),
      isEncrypted: jest.fn().mockReturnValue(false),
      onMessage: jest.fn(),
      onStateChange: jest.fn()
    };

    // Setup DOM mocks
    global.document = mockDOM.document;
    global.window = mockDOM.window;
    
    // Mock querySelector to return Teams elements
    mockDOM.document.querySelector.mockImplementation((selector) => {
      switch (selector) {
        case '[data-tid="ckeditor"]':
        case '.ck-editor__editable':
          return mockTeamsElements.messageInput;
        case '[data-tid="send-message-button"]':
          return mockTeamsElements.sendButton;
        case '[data-tid="chat-pane-message-list"]':
          return mockTeamsElements.messageContainer;
        default:
          return null;
      }
    });

    adapter = new TeamsAdapter();
  });

  afterEach(() => {
    if (adapter) {
      adapter.destroy();
    }
  });

  describe('Initialization', () => {
    test('should initialize with default configuration', () => {
      expect(adapter.platform).toBe('teams');
      expect(adapter.isActive).toBe(false);
      expect(adapter.sessions).toEqual(new Map());
    });

    test('should detect Teams environment', () => {
      const isTeams = adapter.detectPlatform();
      expect(isTeams).toBe(true);
    });

    test('should reject non-Teams environments', () => {
      mockDOM.window.location.href = 'https://discord.com/channels';
      const isTeams = adapter.detectPlatform();
      expect(isTeams).toBe(false);
    });

    test('should initialize UI components', async () => {
      await adapter.initialize();
      
      expect(adapter.isActive).toBe(true);
      expect(mockDOM.document.querySelector).toHaveBeenCalledWith('[data-tid="ckeditor"]');
    });
  });

  describe('Message Interception', () => {
    beforeEach(async () => {
      await adapter.initialize();
    });

    test('should intercept outgoing messages', async () => {
      const testMessage = 'Hello, world!';
      mockTeamsElements.messageInput.value = testMessage;
      
      const result = await adapter.interceptOutgoing(testMessage);
      
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });

    test('should intercept incoming messages', async () => {
      const testMessage = 'Incoming message';
      const senderId = 'user123';
      
      const result = await adapter.interceptIncoming(testMessage, senderId);
      
      expect(result).toBeDefined();
      expect(result.message).toBeDefined();
    });

    test('should handle OTR query messages', async () => {
      const otrQuery = '?OTRv3?';
      const senderId = 'user123';
      
      const result = await adapter.interceptIncoming(otrQuery, senderId);
      
      expect(result.isOtr).toBe(true);
      expect(result.type).toBe('query');
    });

    test('should handle encrypted OTR messages', async () => {
      const encryptedMessage = '?OTR:AAMG' + btoa('encrypted_data');
      const senderId = 'user123';
      
      const result = await adapter.interceptIncoming(encryptedMessage, senderId);
      
      expect(result.isOtr).toBe(true);
      expect(result.type).toBe('data');
    });
  });

  describe('UI Integration', () => {
    beforeEach(async () => {
      await adapter.initialize();
    });

    test('should inject OTR controls into Teams UI', () => {
      adapter.injectOtrControls();
      
      expect(mockDOM.document.createElement).toHaveBeenCalled();
      expect(mockTeamsElements.messageContainer.appendChild).toHaveBeenCalled();
    });

    test('should show OTR status indicator', () => {
      adapter.showOtrStatus('encrypted');
      
      const statusCalls = mockDOM.document.createElement.mock.calls.filter(
        call => call[0] === 'div'
      );
      expect(statusCalls.length).toBeGreaterThan(0);
    });

    test('should handle OTR button clicks', () => {
      const mockButton = {
        addEventListener: jest.fn(),
        click: jest.fn()
      };
      
      mockDOM.document.createElement.mockReturnValue(mockButton);
      
      adapter.injectOtrControls();
      
      expect(mockButton.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
    });

    test('should update message input placeholder', () => {
      adapter.updateInputPlaceholder('OTR session active');
      
      // In a real implementation, this would update the placeholder
      expect(adapter.isActive).toBe(true);
    });
  });

  describe('Session Management', () => {
    beforeEach(async () => {
      await adapter.initialize();
    });

    test('should create OTR session for new contact', async () => {
      const contactId = 'user123';
      
      const session = await adapter.getOrCreateSession(contactId);
      
      expect(session).toBeDefined();
      expect(adapter.sessions.has(contactId)).toBe(true);
    });

    test('should reuse existing OTR session', async () => {
      const contactId = 'user123';
      
      const session1 = await adapter.getOrCreateSession(contactId);
      const session2 = await adapter.getOrCreateSession(contactId);
      
      expect(session1).toBe(session2);
    });

    test('should start OTR negotiation', async () => {
      const contactId = 'user123';
      const session = await adapter.getOrCreateSession(contactId);
      
      session.startOtr = jest.fn().mockResolvedValue(true);
      
      const result = await adapter.startOtr(contactId);
      
      expect(result).toBe(true);
      expect(session.startOtr).toHaveBeenCalled();
    });

    test('should end OTR session', async () => {
      const contactId = 'user123';
      const session = await adapter.getOrCreateSession(contactId);
      
      session.endOtr = jest.fn().mockResolvedValue(true);
      
      const result = await adapter.endOtr(contactId);
      
      expect(result).toBe(true);
      expect(session.endOtr).toHaveBeenCalled();
    });
  });

  describe('Teams-Specific Features', () => {
    beforeEach(async () => {
      await adapter.initialize();
    });

    test('should handle Teams message formatting', () => {
      const message = 'Hello **world**!';
      const formatted = adapter.formatMessage(message);
      
      expect(formatted).toBeDefined();
      expect(typeof formatted).toBe('string');
    });

    test('should extract sender ID from Teams message', () => {
      const mockMessageElement = {
        getAttribute: jest.fn().mockReturnValue('user123'),
        querySelector: jest.fn().mockReturnValue({
          textContent: 'John Doe'
        })
      };
      
      const senderId = adapter.extractSenderId(mockMessageElement);
      
      expect(senderId).toBe('user123');
    });

    test('should handle Teams emoji reactions', () => {
      const message = 'Hello 😊';
      const processed = adapter.processEmojis(message);
      
      expect(processed).toBeDefined();
      expect(processed).toContain('😊');
    });

    test('should handle Teams file attachments', () => {
      const attachment = {
        name: 'document.pdf',
        url: 'https://teams.microsoft.com/file/123'
      };
      
      const processed = adapter.processAttachment(attachment);
      
      expect(processed).toBeDefined();
      expect(processed.type).toBe('attachment');
    });
  });

  describe('Error Handling', () => {
    test('should handle missing Teams elements gracefully', async () => {
      mockDOM.document.querySelector.mockReturnValue(null);
      
      await expect(adapter.initialize()).rejects.toThrow('Teams elements not found');
    });

    test('should handle OTR session errors', async () => {
      const contactId = 'user123';
      const session = await adapter.getOrCreateSession(contactId);
      
      session.startOtr = jest.fn().mockRejectedValue(new Error('OTR failed'));
      
      await expect(adapter.startOtr(contactId)).rejects.toThrow('OTR failed');
    });

    test('should handle message processing errors', async () => {
      const malformedMessage = '?OTR:INVALID_DATA';
      const senderId = 'user123';
      
      const result = await adapter.interceptIncoming(malformedMessage, senderId);
      
      expect(result.error).toBeDefined();
      expect(result.error).toContain('processing failed');
    });

    test('should handle network errors gracefully', async () => {
      // Mock network failure
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));
      
      const result = await adapter.sendMessage('test', 'user123');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Network error');
    });
  });

  describe('Performance', () => {
    test('should initialize quickly', async () => {
      const startTime = Date.now();
      
      await adapter.initialize();
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(1000); // Should initialize within 1 second
    });

    test('should handle multiple concurrent sessions', async () => {
      const contactIds = ['user1', 'user2', 'user3', 'user4', 'user5'];
      
      const promises = contactIds.map(id => adapter.getOrCreateSession(id));
      const sessions = await Promise.all(promises);
      
      expect(sessions).toHaveLength(5);
      expect(adapter.sessions.size).toBe(5);
    });

    test('should clean up resources on destroy', () => {
      adapter.destroy();
      
      expect(adapter.isActive).toBe(false);
      expect(adapter.sessions.size).toBe(0);
    });
  });

  describe('Security', () => {
    test('should validate message sources', async () => {
      const suspiciousMessage = '<script>alert("xss")</script>';
      const senderId = 'user123';
      
      const result = await adapter.interceptIncoming(suspiciousMessage, senderId);
      
      expect(result.message).not.toContain('<script>');
    });

    test('should sanitize user input', () => {
      const maliciousInput = '<img src="x" onerror="alert(1)">';
      const sanitized = adapter.sanitizeInput(maliciousInput);
      
      expect(sanitized).not.toContain('onerror');
      expect(sanitized).not.toContain('<img');
    });

    test('should prevent message injection', async () => {
      const injectionAttempt = '?OTR:FAKE_ENCRYPTED_MESSAGE';
      
      const result = await adapter.interceptOutgoing(injectionAttempt);
      
      expect(result).not.toBe(injectionAttempt);
    });
  });
});
