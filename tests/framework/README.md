# WebOTR Framework Tests

This directory contains tests that validate the core functionality of the WebOTR framework, with a focus on the Socialist Millionaire Protocol (SMP).

## Test Files

- **OtrSMP.test.js**: Tests for the Socialist Millionaire Protocol implementation

## Socialist Millionaire Protocol (SMP)

The SMP is a critical security feature of OTR that allows two participants to verify they share the same secret without actually revealing that secret to each other or to anyone who might be observing the conversation.

### Key features tested:

1. **Authentication through shared secrets**:
   - Successfully authenticating when both parties have the same secret
   - Correctly failing when secrets don't match

2. **Question-based authentication**:
   - Sending and receiving questions with SMP challenges
   - Processing question-based responses correctly

3. **Error handling and recovery**:
   - Properly handling malformed SMP messages
   - Recovering from aborted SMP exchanges

4. **Multiple authentication attempts**:
   - Handling overlapping SMP exchanges
   - State management across multiple authentication attempts

## Running the Tests

To run the SMP tests:

```
cd /path/to/webOTR
npx jest tests/framework/OtrSMP.test.js
```

## Implementation Notes

The SMP implementation follows the protocol as specified in the OTR version 3 documentation. The tests simulate a full OTR session establishment followed by SMP authentication exchanges.

### Test Structure

Each test follows a similar pattern:
1. Set up two OTR sessions (<PERSON> and <PERSON>)
2. Establish an encrypted OTR session between them
3. Initiate an SMP exchange from one participant
4. Process all messages back and forth until the SMP exchange completes
5. Verify the expected results based on the test scenario

### Requirements

For a compliant SMP implementation, all these tests should pass, demonstrating that the implementation:
- Correctly handles the cryptographic operations required by SMP
- Maintains proper state throughout the multi-step protocol
- Reports appropriate results to both parties
- Handles errors and edge cases gracefully 