/**
 * Tests for OTR cryptographic operations
 * 
 * NOTE: These tests import from outside the src/ directory and 
 * cannot be run directly with CRA's test environment.
 * They need to be run in a proper Node.js environment with direct 
 * access to the WebOTR library.
 */
import { 
  generateKeys, 
  deriveKeys, 
  generateInstanceTag 
} from '../../src/core/crypto';
import { 
  encryptMessage, 
  decryptMessage 
} from '../../src/core/protocol';

describe('OTR Cryptographic Operations', () => {
  test('Generate keys should create proper key structure', async () => {
    const keys = await generateKeys();
    
    // Keys should exist
    expect(keys).toBeDefined();
    
    // DSA keys should be generated
    expect(keys.dsa).toBeDefined();
    expect(keys.dsa.publicKey).toBeDefined();
    expect(keys.dsa.privateKey).toBeDefined();
    
    // DH keys should be generated
    expect(keys.dh).toBeDefined();
    expect(keys.dh.publicKey).toBeDefined();
    expect(keys.dh.privateKey).toBeDefined();
  });
  
  test('Instance tags should be valid', () => {
    const tag = generateInstanceTag();
    
    // Should be a number
    expect(typeof tag).toBe('number');
    
    // Should meet OTR protocol requirements (32 bits, highest bit not zero)
    expect(tag).toBeGreaterThan(0x00000100);
    expect(tag).toBeLessThan(0xffffffff);
  });
  
  test('Key derivation should produce matching keys', async () => {
    // Generate shared secret (typically derived from DH key exchange)
    const sharedSecret = new Uint8Array(32);
    for (let i = 0; i < sharedSecret.length; i++) {
      sharedSecret[i] = i;
    }
    
    // Derive keys from shared secret
    const keys = await deriveKeys(sharedSecret);
    
    // Should have AES and MAC keys
    expect(keys.aesKey).toBeDefined();
    expect(keys.macKey).toBeDefined();
    
    // AES key should be appropriate length for AES
    expect(keys.aesKey.length).toBe(16); // 16 bytes for AES-128
    
    // MAC key should be appropriate length for HMAC-SHA256
    expect(keys.macKey.length).toBe(32); // 32 bytes for SHA-256
  });
  
  test('Message encryption and decryption should work correctly', async () => {
    // Generate AES and MAC keys
    const aesKey = new Uint8Array(16);
    const macKey = new Uint8Array(32);
    
    // Fill with deterministic test values
    for (let i = 0; i < aesKey.length; i++) {
      aesKey[i] = i;
    }
    for (let i = 0; i < macKey.length; i++) {
      macKey[i] = i;
    }
    
    // Test message
    const originalMessage = "This is a secret message for testing";
    
    // Encrypt message
    const counter = 1; // Message counter
    const encrypted = await encryptMessage(originalMessage, aesKey, macKey, counter);
    
    // Encrypted message should exist
    expect(encrypted).toBeDefined();
    expect(encrypted.encryptedMessage).toBeDefined();
    expect(encrypted.mac).toBeDefined();
    
    // Encrypted message should not be the same as original
    const textEncoder = new TextEncoder();
    const originalBytes = textEncoder.encode(originalMessage);
    expect(encrypted.encryptedMessage).not.toEqual(originalBytes);
    
    // Decrypt message
    const decrypted = await decryptMessage(encrypted.encryptedMessage, aesKey, counter, encrypted.mac, macKey);
    
    // Decrypted message should match original
    expect(decrypted).toEqual(originalMessage);
  });
}); 