/**
 * Global Test Setup
 * 
 * Setup that runs once before all tests
 */

module.exports = async () => {
  console.log('🧪 Setting up WebOTR comprehensive test environment...');
  
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.WEBOTTR_TEST_MODE = 'true';
  
  // Setup global test timeout (handled by Jest config)
  
  console.log('✅ Global test setup complete');
};
