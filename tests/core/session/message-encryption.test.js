/**
 * Message encryption tests
 */
import { OtrSession } from '../../../src/core/session';
import { STATE } from '../../../src/core/protocol/state';

describe('Message Encryption', () => {
  let aliceSession;
  let bobSession;
  let mockSendMessage;

  beforeEach(async () => {
    mockSendMessage = jest.fn();
    
    // Create sessions for <PERSON> and <PERSON>
    aliceSession = new OtrSession('bob', { 
      testing: false, // Use real encryption for these tests
      sendMessage: mockSendMessage 
    });
    
    bobSession = new OtrSession('alice', { 
      testing: false, // Use real encryption for these tests
      sendMessage: mockSendMessage 
    });

    // Initialize sessions
    await aliceSession.init();
    await bobSession.init();

    // Set up encryption keys for testing
    const testAESKey = new Uint8Array(16).fill(42);
    const testMACKey = new Uint8Array(32).fill(24);

    // Set <PERSON>'s sending keys and <PERSON>'s receiving keys to the same values
    aliceSession.state.sendingAESKey = testAESKey;
    aliceSession.state.sendingMACKey = testMACKey;
    aliceSession.state.sendCounter = 0;

    bobSession.state.receivingAESKey = testAESKey;
    bobSession.state.receivingMACKey = testMACKey;

    // Set Bob's sending keys and Alice's receiving keys to the same values
    bobSession.state.sendingAESKey = testAESKey;
    bobSession.state.sendingMACKey = testMACKey;
    bobSession.state.sendCounter = 0;

    aliceSession.state.receivingAESKey = testAESKey;
    aliceSession.state.receivingMACKey = testMACKey;

    // Set both sessions to encrypted state
    if (typeof aliceSession.state.setState === 'function') {
      aliceSession.state.setState(STATE.ENCRYPTED);
      bobSession.state.setState(STATE.ENCRYPTED);
    } else if (typeof aliceSession.state.setstate === 'function') {
      aliceSession.state.setstate(STATE.ENCRYPTED);
      bobSession.state.setstate(STATE.ENCRYPTED);
    } else {
      aliceSession.state.state = STATE.ENCRYPTED;
      bobSession.state.state = STATE.ENCRYPTED;
    }
  });

  describe('Message Encryption', () => {
    test('should encrypt a simple message', async () => {
      const plaintext = 'Hello, Bob!';
      
      const encryptedMessage = await aliceSession.encryptMessage(plaintext);
      
      expect(encryptedMessage).toBeDefined();
      expect(typeof encryptedMessage).toBe('string');
      expect(encryptedMessage).toMatch(/^\?OTR:AAMG/);
      expect(encryptedMessage).not.toContain(plaintext);
    });

    test('should encrypt different messages differently', async () => {
      const message1 = 'First message';
      const message2 = 'Second message';
      
      const encrypted1 = await aliceSession.encryptMessage(message1);
      const encrypted2 = await aliceSession.encryptMessage(message2);
      
      expect(encrypted1).not.toBe(encrypted2);
      expect(encrypted1).toMatch(/^\?OTR:AAMG/);
      expect(encrypted2).toMatch(/^\?OTR:AAMG/);
    });

    test('should increment counter for each message', async () => {
      const initialCounter = aliceSession.state.sendCounter || 0;
      
      await aliceSession.encryptMessage('Message 1');
      expect(aliceSession.state.sendCounter).toBe(initialCounter + 1);
      
      await aliceSession.encryptMessage('Message 2');
      expect(aliceSession.state.sendCounter).toBe(initialCounter + 2);
    });

    test('should throw error when not in encrypted state', async () => {
      // Set state using the same method as in the constructor
      if (typeof aliceSession.state.setState === 'function') {
        aliceSession.state.setState(STATE.PLAINTEXT);
      } else if (typeof aliceSession.state.setstate === 'function') {
        aliceSession.state.setstate(STATE.PLAINTEXT);
      } else {
        aliceSession.state.state = STATE.PLAINTEXT;
      }

      await expect(aliceSession.encryptMessage('test'))
        .rejects.toThrow('Cannot encrypt message: session is not in encrypted state');
    });

    test('should throw error when encryption keys are missing', async () => {
      aliceSession.state.sendingAESKey = null;
      
      await expect(aliceSession.encryptMessage('test'))
        .rejects.toThrow('Cannot encrypt message: encryption keys not available');
    });
  });

  describe('Message Decryption', () => {
    test('should decrypt a message encrypted by the same session', async () => {
      const plaintext = 'Hello, Alice!';

      // Bob encrypts a message
      const encryptedMessage = await bobSession.encryptMessage(plaintext);

      // Alice decrypts it
      const decryptedMessage = await aliceSession.decryptMessage(encryptedMessage);

      expect(decryptedMessage).toBe(plaintext);
    });

    test('should verify base64 conversion integrity', () => {
      // Test that our base64 conversion preserves data integrity
      const originalData = new Uint8Array([1, 2, 3, 4, 5, 255, 128, 0]);

      // Convert to base64
      const base64 = btoa(String.fromCharCode(...originalData));

      // Convert back to Uint8Array
      const convertedData = Uint8Array.from(atob(base64), c => c.charCodeAt(0));

      expect(convertedData).toEqual(originalData);
    });

    test('should verify protocol encrypt/decrypt compatibility', async () => {
      // Import the protocol functions directly
      const { encryptMessage, decryptMessage } = await import('../../../src/core/protocol/message');

      const plaintext = 'Test message';
      const aesKey = new Uint8Array(16).fill(42);
      const macKey = new Uint8Array(32).fill(24);
      const counter = 0;

      // Encrypt using protocol function
      const encryptedData = await encryptMessage(plaintext, aesKey, macKey, counter);

      // Decrypt using protocol function
      const decryptedMessage = await decryptMessage(
        encryptedData.encryptedMessage,
        aesKey,
        macKey,
        encryptedData.mac,
        counter
      );

      expect(decryptedMessage).toBe(plaintext);
    });

    test('should verify MAC serialization integrity', async () => {
      // Import the protocol functions directly
      const { encryptMessage, decryptMessage } = await import('../../../src/core/protocol/message');

      const plaintext = 'Test message';
      const aesKey = new Uint8Array(16).fill(42);
      const macKey = new Uint8Array(32).fill(24);
      const counter = 0;

      // Encrypt using protocol function
      const encryptedData = await encryptMessage(plaintext, aesKey, macKey, counter);

      // Serialize to base64 (like our OTR data message does)
      const encryptedBase64 = btoa(String.fromCharCode(...encryptedData.encryptedMessage));
      const macBase64 = btoa(String.fromCharCode(...encryptedData.mac));

      // Deserialize from base64
      const encryptedDeserialized = Uint8Array.from(atob(encryptedBase64), c => c.charCodeAt(0));
      const macDeserialized = Uint8Array.from(atob(macBase64), c => c.charCodeAt(0));

      // Verify that serialization preserves data
      expect(encryptedDeserialized).toEqual(encryptedData.encryptedMessage);
      expect(macDeserialized).toEqual(encryptedData.mac);

      // Try to decrypt with deserialized data
      const decryptedMessage = await decryptMessage(
        encryptedDeserialized,
        aesKey,
        macKey,
        macDeserialized,
        counter
      );

      expect(decryptedMessage).toBe(plaintext);
    });

    test('should verify empty string protocol encryption', async () => {
      // Import the protocol functions directly
      const { encryptMessage, decryptMessage } = await import('../../../src/core/protocol/message');

      const plaintext = '';
      const aesKey = new Uint8Array(16).fill(42);
      const macKey = new Uint8Array(32).fill(24);
      const counter = 0;

      // Encrypt using protocol function
      const encryptedData = await encryptMessage(plaintext, aesKey, macKey, counter);

      // Check that we get valid encrypted data
      expect(encryptedData.encryptedMessage).toBeDefined();
      expect(encryptedData.mac).toBeDefined();
      // Empty string encryption results in empty encrypted message, but MAC should still exist
      expect(encryptedData.encryptedMessage.length).toBe(0); // Empty string = empty encrypted message
      expect(encryptedData.mac.length).toBeGreaterThan(0); // MAC should always exist

      // Try to decrypt
      const decryptedMessage = await decryptMessage(
        encryptedData.encryptedMessage,
        aesKey,
        macKey,
        encryptedData.mac,
        counter
      );

      expect(decryptedMessage).toBe(plaintext);
    });

    test('should handle empty messages', async () => {
      const plaintext = '';

      const encryptedMessage = await bobSession.encryptMessage(plaintext);
      const decryptedMessage = await aliceSession.decryptMessage(encryptedMessage);

      expect(decryptedMessage).toBe(plaintext);
    });

    test('should handle unicode messages', async () => {
      const plaintext = '🔒 Secure message with émojis and ñ characters! 🎉';
      
      const encryptedMessage = await bobSession.encryptMessage(plaintext);
      const decryptedMessage = await aliceSession.decryptMessage(encryptedMessage);
      
      expect(decryptedMessage).toBe(plaintext);
    });

    test('should throw error when not in encrypted state', async () => {
      // Set state using the same method as in the constructor
      if (typeof aliceSession.state.setState === 'function') {
        aliceSession.state.setState(STATE.PLAINTEXT);
      } else if (typeof aliceSession.state.setstate === 'function') {
        aliceSession.state.setstate(STATE.PLAINTEXT);
      } else {
        aliceSession.state.state = STATE.PLAINTEXT;
      }

      await expect(aliceSession.decryptMessage('?OTR:AAMGdGVzdA=='))
        .rejects.toThrow('Cannot decrypt message: session is not in encrypted state');
    });

    test('should throw error when decryption keys are missing', async () => {
      aliceSession.state.receivingAESKey = null;
      
      await expect(aliceSession.decryptMessage('?OTR:AAMGdGVzdA=='))
        .rejects.toThrow('Cannot decrypt message: decryption keys not available');
    });

    test('should throw error for invalid message format', async () => {
      await expect(aliceSession.decryptMessage('invalid message'))
        .rejects.toThrow('Invalid OTR data message format');
    });

    test('should throw error for corrupted message data', async () => {
      await expect(aliceSession.decryptMessage('?OTR:AAMGinvalid-base64'))
        .rejects.toThrow('Failed to parse OTR data message');
    });
  });

  describe('End-to-End Message Flow', () => {
    test('should complete full encrypt-decrypt cycle', async () => {
      const messages = [
        'Hello, Bob!',
        'How are you today?',
        'This is a secure conversation.',
        '🔒 With emojis! 🎉'
      ];
      
      for (const plaintext of messages) {
        // Alice encrypts and sends to Bob
        const encryptedMessage = await aliceSession.encryptMessage(plaintext);
        
        // Bob receives and decrypts
        const decryptedMessage = await bobSession.decryptMessage(encryptedMessage);
        
        expect(decryptedMessage).toBe(plaintext);
      }
    });

    test('should handle bidirectional communication', async () => {
      const aliceMessage = 'Hello from Alice!';
      const bobMessage = 'Hello from Bob!';
      
      // Alice to Bob
      const aliceEncrypted = await aliceSession.encryptMessage(aliceMessage);
      const bobDecrypted = await bobSession.decryptMessage(aliceEncrypted);
      expect(bobDecrypted).toBe(aliceMessage);
      
      // Bob to Alice
      const bobEncrypted = await bobSession.encryptMessage(bobMessage);
      const aliceDecrypted = await aliceSession.decryptMessage(bobEncrypted);
      expect(aliceDecrypted).toBe(bobMessage);
    });

    test('should maintain message order with counters', async () => {
      const messages = ['First', 'Second', 'Third'];
      const encryptedMessages = [];
      
      // Encrypt all messages
      for (const message of messages) {
        const encrypted = await aliceSession.encryptMessage(message);
        encryptedMessages.push(encrypted);
      }
      
      // Decrypt all messages
      for (let i = 0; i < encryptedMessages.length; i++) {
        const decrypted = await bobSession.decryptMessage(encryptedMessages[i]);
        expect(decrypted).toBe(messages[i]);
      }
    });
  });

  describe('Integration with Session Processing', () => {
    test('should process outgoing messages correctly', async () => {
      const plaintext = 'Test outgoing message';
      
      const result = await aliceSession.processOutgoing(plaintext);
      
      expect(result).toBeDefined();
      expect(result.encrypted).toBe(true);
      expect(result.internal).toBe(false);
      expect(result.message).toMatch(/^\?OTR:AAMG/);
      expect(mockSendMessage).toHaveBeenCalledWith(result.message);
    });

    test('should process incoming encrypted messages correctly', async () => {
      const plaintext = 'Test incoming message';
      
      // Bob encrypts a message
      const encryptedMessage = await bobSession.encryptMessage(plaintext);
      
      // Alice processes the incoming encrypted message
      const result = await aliceSession.processIncoming(encryptedMessage);
      
      expect(result).toBeDefined();
      expect(result.encrypted).toBe(true);
      expect(result.internal).toBe(false);
      expect(result.message).toBe(plaintext);
    });

    test('should handle decryption errors gracefully', async () => {
      const invalidMessage = '?OTR:AAMGinvalid-data';
      
      const result = await aliceSession.processIncoming(invalidMessage);
      
      expect(result).toBeDefined();
      expect(result.encrypted).toBe(false);
      expect(result.internal).toBe(true);
      expect(result.message).toBe('Failed to decrypt message');
      expect(result.error).toBeDefined();
    });
  });

  describe('Testing Mode Compatibility', () => {
    test('should pass through messages in testing mode', async () => {
      const testSession = new OtrSession('test', { testing: true, sendMessage: mockSendMessage });
      const plaintext = 'Test message';
      
      const encrypted = await testSession.encryptMessage(plaintext);
      expect(encrypted).toBe(plaintext);
      
      const decrypted = await testSession.decryptMessage(plaintext);
      expect(decrypted).toBe(plaintext);
    });
  });
});
