/**
 * Tests for OTR message formatting and parsing
 */

import {
  createQueryMessage,
  createDataMessage,
  createErrorMessage,
  parseMessage,
} from "../../../src/core/protocol/message";
import { PROTOCOL_VERSION } from "../../../src/core/protocol/constants";
import { MESSAGE_TYPE } from "../../../src/core/protocol/state";

describe("OTR Message Formatting and Parsing", () => {
  // Test query message creation
  describe("Query Message Creation", () => {
    test("should create a valid OTRv2 query message", () => {
      const versions = [PROTOCOL_VERSION.V2];

      const message = createQueryMessage(versions);

      expect(message).toBe("?OTR?v2?");
    });

    test("should create a valid OTRv3 query message", () => {
      const versions = [PROTOCOL_VERSION.V3];

      const message = createQueryMessage(versions);

      expect(message).toBe("?OTR?v3?");
    });

    test("should create a valid OTRv2+v3 query message", () => {
      const versions = [PROTOCOL_VERSION.V2, PROTOCOL_VERSION.V3];

      const message = createQueryMessage(versions);

      expect(message).toBe("?OTR?v23?");
    });

    test("should handle empty versions array", () => {
      const versions = [];

      const message = createQueryMessage(versions);

      expect(message).toBe("?OTR?v?");
    });
  });

  // Test data message creation
  describe("Data Message Creation", () => {
    test("should create a valid data message", () => {
      const content = "encrypted-data";

      const message = createDataMessage(content);

      expect(message).toBe("?OTR:encrypted-data");
    });

    test("should handle empty content", () => {
      const content = "";

      const message = createDataMessage(content);

      expect(message).toBe("?OTR:");
    });

    test("should handle content with special characters", () => {
      const content = "!@#$%^&*()_+";

      const message = createDataMessage(content);

      expect(message).toBe("?OTR:!@#$%^&*()_+");
    });
  });

  // Test error message creation
  describe("Error Message Creation", () => {
    test("should create a valid error message", () => {
      const errorText = "An error occurred";

      const message = createErrorMessage(errorText);

      expect(message).toBe("?OTR ERROR: An error occurred");
    });

    test("should handle empty error text", () => {
      const errorText = "";

      const message = createErrorMessage(errorText);

      expect(message).toBe("?OTR ERROR: ");
    });

    test("should handle error text with special characters", () => {
      const errorText = "Error: Invalid key (0x1234)";

      const message = createErrorMessage(errorText);

      expect(message).toBe("?OTR ERROR: Error: Invalid key (0x1234)");
    });
  });

  // Test message parsing
  describe("Message Parsing", () => {
    test("should parse a query message", () => {
      const messageStr = "?OTR?v3?";

      const message = parseMessage(messageStr);

      expect(message).toBeDefined();
      expect(message.type).toBe(MESSAGE_TYPE.QUERY);
      expect(message.versions).toContain(PROTOCOL_VERSION.V3);
    });

    test("should parse a data message", () => {
      const messageStr = "?OTR:encrypted-data";

      const message = parseMessage(messageStr);

      expect(message).toBeDefined();
      expect(message.type).toBe(MESSAGE_TYPE.DATA);
      expect(message.content).toBe("encrypted-data");
    });

    test("should parse an error message", () => {
      const messageStr = "?OTR ERROR: An error occurred";

      const message = parseMessage(messageStr);

      expect(message).toBeDefined();
      expect(message.type).toBe(MESSAGE_TYPE.ERROR);
      expect(message.error).toBe("An error occurred");
    });

    test("should parse a whitespace tag message", () => {
      const messageStr =
        "Normal message with whitespace tag at end \t  \t\t\t\t \t \t \t    \t\t  \t";

      const message = parseMessage(messageStr);

      expect(message).toBeDefined();
      expect(message.type).toBe(MESSAGE_TYPE.WHITESPACE_TAG);
    });

    test("should return null for non-OTR messages", () => {
      const messageStr = "This is a normal message";

      const message = parseMessage(messageStr);

      expect(message).toBeNull();
    });

    test("should handle null or undefined messages", () => {
      expect(parseMessage(null)).toBeNull();
      expect(parseMessage(undefined)).toBeNull();
    });
  });

  // Test message format validation
  describe("Message Format Validation", () => {
    test("should validate query message format", () => {
      const validFormats = ["?OTR?v2?", "?OTR?v3?", "?OTR?v23?"];

      for (const format of validFormats) {
        const message = parseMessage(format);
        expect(message).toBeDefined();
        expect(message.type).toBe(MESSAGE_TYPE.QUERY);
      }

      const invalidFormats = ["?OTR?", "?OTR?v", "?OTR?v?", "?OTRv3?"];

      for (const format of invalidFormats) {
        const message = parseMessage(format);
        expect(message).not.toBe(MESSAGE_TYPE.QUERY);
      }
    });

    test("should validate data message format", () => {
      const validFormats = ["?OTR:data", "?OTR:", "?OTR:123456"];

      for (const format of validFormats) {
        const message = parseMessage(format);
        expect(message).toBeDefined();
        expect(message.type).toBe(MESSAGE_TYPE.DATA);
      }

      const invalidFormats = ["?OTR", "?OTR data", "OTR:data"];

      for (const format of invalidFormats) {
        const message = parseMessage(format);
        expect(message).not.toBe(MESSAGE_TYPE.DATA);
      }
    });
  });
});
