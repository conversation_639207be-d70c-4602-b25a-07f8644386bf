/**
 * Authenticated Key Exchange (AKE) Protocol Tests
 * Tests the OTR AKE implementation including DH key exchange,
 * signature verification, and key derivation.
 */

import {
  createDHCommit,
  createDHKey,
  createRevealSignature,
  createSignature,
  processDHCommit,
  processDHKey,
  processRevealSignature,
  processSignature,
  startAKE
} from '../../../src/core/protocol/ake.js';
import { generateDSAKeyPair } from '../../../src/core/crypto/dsa.js';
import { STATE } from '../../../src/core/protocol/state.js';

describe('Authenticated Key Exchange (AKE)', () => {
  let dsaAlice, dsaBob;
  let mockStateAlice, mockStateBob;

  beforeEach(async () => {
    // Create DSA key pairs for testing
    dsaAlice = await generateDSAKeyPair();
    dsaBob = await generateDSAKeyPair();

    // Create mock state objects
    mockStateAlice = {
      dhKeyPair: null,
      dsaKeyPair: dsaAlice,
      state: STATE.PLAINTEXT,
      setState: jest.fn(),
      getState: jest.fn().mockReturnValue(STATE.PLAINTEXT)
    };

    mockStateBob = {
      dhKeyPair: null,
      dsaKeyPair: dsaBob,
      state: STATE.PLAINTEXT,
      setState: jest.fn(),
      getState: jest.fn().mockReturnValue(STATE.PLAINTEXT)
    };
  });

  describe('AKE Initialization', () => {
    test('should initialize with correct default state', () => {
      expect(mockStateAlice.state).toBe(STATE.PLAINTEXT);
      expect(mockStateAlice.dsaKeyPair).toBeDefined();
      expect(mockStateAlice.dsaKeyPair.privateKey).toBeDefined();
      expect(mockStateAlice.dsaKeyPair.publicKey).toBeDefined();
    });

    test('should start AKE process', async () => {
      const result = await startAKE(mockStateAlice);

      expect(result).toBeDefined();
      expect(result.state).toBeDefined();
      expect(result.message).toBeDefined();
    });

    test('should create valid DH commit message', async () => {
      const commitMessage = await createDHCommit();

      expect(commitMessage).toBeDefined();
      expect(typeof commitMessage).toBe('string');
      expect(commitMessage.length).toBeGreaterThan(0);
    });
  });

  describe('DH Key Exchange', () => {
    test('should process DH commit message', async () => {
      const dhCommit = await createDHCommit();
      const result = await processDHCommit(dhCommit, mockStateBob);

      expect(result).toBeDefined();
      expect(result.state).toBeDefined();
      expect(result.message).toBeDefined();
    });

    test('should create DH key message', async () => {
      const dhKey = await createDHKey();

      expect(dhKey).toBeDefined();
      expect(typeof dhKey).toBe('string');
    });

    test('should process DH key message', async () => {
      const dhKey = await createDHKey();
      const result = await processDHKey(dhKey, mockStateAlice);

      expect(result).toBeDefined();
      expect(result.state).toBeDefined();
    });

    test('should create reveal signature message', async () => {
      const revealSig = await createRevealSignature();

      expect(revealSig).toBeDefined();
      expect(typeof revealSig).toBe('string');
    });

    test('should create signature message', async () => {
      const signature = await createSignature();

      expect(signature).toBeDefined();
      expect(typeof signature).toBe('string');
    });
  });

  describe('Message Processing', () => {
    test('should process reveal signature message', async () => {
      const revealSig = await createRevealSignature();
      const result = await processRevealSignature(revealSig, mockStateBob);

      expect(result).toBeDefined();
      expect(result.state).toBeDefined();
    });

    test('should process signature message', async () => {
      const signature = await createSignature();
      const result = await processSignature(signature, mockStateAlice);

      expect(result).toBeDefined();
      expect(result.state).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle malformed messages gracefully', async () => {
      const malformedMessage = 'invalid_message';

      try {
        await processDHCommit(malformedMessage, mockStateBob);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('should handle null state gracefully', async () => {
      const dhCommit = await createDHCommit();

      try {
        await processDHCommit(dhCommit, null);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('should handle invalid state objects', async () => {
      const dhCommit = await createDHCommit();
      const invalidState = {};

      try {
        await processDHCommit(dhCommit, invalidState);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('Function Availability', () => {
    test('should have all required AKE functions available', () => {
      expect(typeof createDHCommit).toBe('function');
      expect(typeof createDHKey).toBe('function');
      expect(typeof createRevealSignature).toBe('function');
      expect(typeof createSignature).toBe('function');
      expect(typeof processDHCommit).toBe('function');
      expect(typeof processDHKey).toBe('function');
      expect(typeof processRevealSignature).toBe('function');
      expect(typeof processSignature).toBe('function');
      expect(typeof startAKE).toBe('function');
    });

    test('should have DSA key generation available', () => {
      expect(typeof generateDSAKeyPair).toBe('function');
      expect(dsaAlice).toBeDefined();
      expect(dsaBob).toBeDefined();
      expect(dsaAlice.privateKey).toBeDefined();
      expect(dsaAlice.publicKey).toBeDefined();
    });

    test('should handle async operations', async () => {
      const startTime = Date.now();

      await createDHCommit();
      await createDHKey();
      await createRevealSignature();
      await createSignature();

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(2000);
    });
  });
});
