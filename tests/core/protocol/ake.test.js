/**
 * Authenticated Key Exchange (AKE) Protocol Tests
<<<<<<< HEAD
 * Comprehensive tests for OTR AKE implementation including DH key exchange,
 * signature verification, key derivation, and protocol flow validation.
=======
 * Tests the OTR AKE implementation including DH key exchange,
 * signature verification, and key derivation.
>>>>>>> origin/feature/browser-extension
 */

import {
  createDHCommit,
  createDHKey,
  createRevealSignature,
  createSignature,
  processDHCommit,
  processDHKey,
  processRevealSignature,
  processSignature,
  startAKE
} from '../../../src/core/protocol/ake.js';
import { generateDSAKeyPair } from '../../../src/core/crypto/dsa.js';
import { STATE } from '../../../src/core/protocol/state.js';

describe('Authenticated Key Exchange (AKE)', () => {
  let dsaAlice, dsaBob;
  let mockStateAlice, mockStateBob;

  beforeEach(async () => {
    // Create DSA key pairs for testing
    dsaAlice = await generateDSAKeyPair();
    dsaBob = await generateDSAKeyPair();

    // Create mock state objects
    mockStateAlice = {
      dhKeyPair: null,
      dsaKeyPair: dsaAlice,
      state: STATE.PLAINTEXT,
      setState: jest.fn(),
      getState: jest.fn().mockReturnValue(STATE.PLAINTEXT)
    };

    mockStateBob = {
      dhKeyPair: null,
      dsaKeyPair: dsaBob,
      state: STATE.PLAINTEXT,
      setState: jest.fn(),
      getState: jest.fn().mockReturnValue(STATE.PLAINTEXT)
    };
  });

  describe('AKE Initialization', () => {
    test('should initialize with correct default state', () => {
      expect(mockStateAlice.state).toBe(STATE.PLAINTEXT);
      expect(mockStateAlice.dsaKeyPair).toBeDefined();
      expect(mockStateAlice.dsaKeyPair.privateKey).toBeDefined();
      expect(mockStateAlice.dsaKeyPair.publicKey).toBeDefined();
    });

    test('should start AKE process', async () => {
      const result = await startAKE(mockStateAlice);

      expect(result).toBeDefined();
      expect(result.state).toBeDefined();
      expect(result.message).toBeDefined();
    });

    test('should create valid DH commit message', async () => {
<<<<<<< HEAD
      // Generate a proper DH key pair for testing
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();

      const commitMessage = await createDHCommit(dhKeyPair, 3, 1, 0);

      expect(commitMessage).toBeDefined();
      expect(typeof commitMessage).toBe('object');
      expect(commitMessage.protocolVersion).toBeDefined();
      expect(commitMessage.messageType).toBeDefined();
      expect(commitMessage.encryptedPublicKey).toBeDefined();
      expect(commitMessage.publicKeyHash).toBeDefined();
=======
      const commitMessage = await createDHCommit();

      expect(commitMessage).toBeDefined();
      expect(typeof commitMessage).toBe('string');
      expect(commitMessage.length).toBeGreaterThan(0);
>>>>>>> origin/feature/browser-extension
    });
  });

  describe('DH Key Exchange', () => {
    test('should process DH commit message', async () => {
<<<<<<< HEAD
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const dhCommit = await createDHCommit(dhKeyPair, 3, 1, 0);
=======
      const dhCommit = await createDHCommit();
>>>>>>> origin/feature/browser-extension
      const result = await processDHCommit(dhCommit, mockStateBob);

      expect(result).toBeDefined();
      expect(result.state).toBeDefined();
<<<<<<< HEAD
      expect(result.response).toBe('?OTR:DHKEY');
    });

    test('should create DH key message', async () => {
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const dhKey = await createDHKey(dhKeyPair, 3, 1, 0);

      expect(dhKey).toBeDefined();
      expect(typeof dhKey).toBe('object');
      expect(dhKey.protocolVersion).toBeDefined();
      expect(dhKey.messageType).toBeDefined();
      expect(dhKey.publicKey).toBeDefined();
    });

    test('should process DH key message', async () => {
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const dhKey = await createDHKey(dhKeyPair, 3, 1, 0);

      // Mock the state to have required properties
      mockStateAlice.dhKeyPair = dhKeyPair;
      mockStateAlice.dsaKeyPair = dsaAlice;
      mockStateAlice.dhCommitMessage = { aesKey: new Uint8Array(16), iv: new Uint8Array(16) };

=======
      expect(result.message).toBeDefined();
    });

    test('should create DH key message', async () => {
      const dhKey = await createDHKey();

      expect(dhKey).toBeDefined();
      expect(typeof dhKey).toBe('string');
    });

    test('should process DH key message', async () => {
      const dhKey = await createDHKey();
>>>>>>> origin/feature/browser-extension
      const result = await processDHKey(dhKey, mockStateAlice);

      expect(result).toBeDefined();
      expect(result.state).toBeDefined();
<<<<<<< HEAD
      expect(result.response).toBe('?OTR:REVEALSIG');
    });

    test('should create reveal signature message', async () => {
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const sharedSecret = new Uint8Array(32);
      const aesKey = new Uint8Array(16);
      const iv = new Uint8Array(16);

      const revealSig = await createRevealSignature(dhKeyPair, dsaAlice, sharedSecret, 3, 1, 0, aesKey, iv);

      expect(revealSig).toBeDefined();
      expect(typeof revealSig).toBe('object');
      expect(revealSig.protocolVersion).toBeDefined();
      expect(revealSig.messageType).toBeDefined();
      expect(revealSig.encryptedSignature).toBeDefined();
    });

    test('should create signature message', async () => {
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const sharedSecret = new Uint8Array(32);
      const sessionKey = new Uint8Array(16);

      const signature = await createSignature(dhKeyPair, dsaAlice, sharedSecret, 3, 1, 0, sessionKey);

      expect(signature).toBeDefined();
      expect(typeof signature).toBe('object');
      expect(signature.protocolVersion).toBeDefined();
      expect(signature.messageType).toBeDefined();
      expect(signature.encryptedSignature).toBeDefined();
=======
    });

    test('should create reveal signature message', async () => {
      const revealSig = await createRevealSignature();

      expect(revealSig).toBeDefined();
      expect(typeof revealSig).toBe('string');
    });

    test('should create signature message', async () => {
      const signature = await createSignature();

      expect(signature).toBeDefined();
      expect(typeof signature).toBe('string');
>>>>>>> origin/feature/browser-extension
    });
  });

  describe('Message Processing', () => {
    test('should process reveal signature message', async () => {
<<<<<<< HEAD
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const bobDHKeyPair = await generateDHKeyPair();
      const sharedSecret = new Uint8Array(32).fill(42); // Use consistent test data
      const aesKey = new Uint8Array(16).fill(1);
      const iv = new Uint8Array(16).fill(0);

      // Create a proper reveal signature message
      const revealSig = await createRevealSignature(dhKeyPair, dsaAlice, sharedSecret, 3, 1, 0, aesKey, iv);

      // Mock the state to have required properties for processing
      const testState = {
        dhKeyPair: bobDHKeyPair,
        dsaKeyPair: dsaBob,
        theirPublicKey: dhKeyPair.publicKey,
        sharedSecret: sharedSecret,
        theirDSAPublicKey: dsaAlice.publicKey,
        protocolVersion: 3,
        instanceTag: 0,
        handleRevealSignature: jest.fn()
      };

      // Skip signature verification for this test by mocking the verify function
      const originalVerify = require('../../../src/core/crypto/dsa').verify;
      require('../../../src/core/crypto/dsa').verify = jest.fn().mockResolvedValue(true);

      try {
        const result = await processRevealSignature(revealSig, testState);

        expect(result).toBeDefined();
        expect(result.state).toBeDefined();
        expect(result.response).toBe('?OTR:SIG');
      } finally {
        // Restore original function
        require('../../../src/core/crypto/dsa').verify = originalVerify;
      }
    });

    test('should process signature message', async () => {
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const aliceDHKeyPair = await generateDHKeyPair();
      const sharedSecret = new Uint8Array(32).fill(42); // Use consistent test data
      const sessionKey = new Uint8Array(16).fill(2);

      // Create a proper signature message
      const signature = await createSignature(dhKeyPair, dsaBob, sharedSecret, 3, 1, 0, sessionKey);

      // Mock the state to have required properties for processing
      const testState = {
        dhKeyPair: aliceDHKeyPair,
        dsaKeyPair: dsaAlice,
        receivingAESKey: sessionKey,
        theirPublicKey: dhKeyPair.publicKey,
        sharedSecret: sharedSecret,
        theirDSAPublicKey: dsaBob.publicKey,
        goEncrypted: jest.fn(),
        handleSignature: jest.fn(),
        STATE: { ENCRYPTED: 4 }
      };

      // Skip signature verification for this test by mocking the verify function
      const originalVerify = require('../../../src/core/crypto/dsa').verify;
      require('../../../src/core/crypto/dsa').verify = jest.fn().mockResolvedValue(true);

      try {
        const result = await processSignature(signature, testState);

        expect(result).toBeDefined();
        expect(result.state).toBeDefined();
        expect(result.response).toBeNull(); // AKE complete
        expect(testState.goEncrypted).toHaveBeenCalled();
      } finally {
        // Restore original function
        require('../../../src/core/crypto/dsa').verify = originalVerify;
      }
=======
      const revealSig = await createRevealSignature();
      const result = await processRevealSignature(revealSig, mockStateBob);

      expect(result).toBeDefined();
      expect(result.state).toBeDefined();
    });

    test('should process signature message', async () => {
      const signature = await createSignature();
      const result = await processSignature(signature, mockStateAlice);

      expect(result).toBeDefined();
      expect(result.state).toBeDefined();
>>>>>>> origin/feature/browser-extension
    });
  });

  describe('Error Handling', () => {
    test('should handle malformed messages gracefully', async () => {
      const malformedMessage = 'invalid_message';

      try {
        await processDHCommit(malformedMessage, mockStateBob);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('should handle null state gracefully', async () => {
<<<<<<< HEAD
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const dhCommit = await createDHCommit(dhKeyPair, 3, 1, 0);
=======
      const dhCommit = await createDHCommit();
>>>>>>> origin/feature/browser-extension

      try {
        await processDHCommit(dhCommit, null);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    test('should handle invalid state objects', async () => {
<<<<<<< HEAD
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const dhCommit = await createDHCommit(dhKeyPair, 3, 1, 0);
=======
      const dhCommit = await createDHCommit();
>>>>>>> origin/feature/browser-extension
      const invalidState = {};

      try {
        await processDHCommit(dhCommit, invalidState);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('Function Availability', () => {
    test('should have all required AKE functions available', () => {
      expect(typeof createDHCommit).toBe('function');
      expect(typeof createDHKey).toBe('function');
      expect(typeof createRevealSignature).toBe('function');
      expect(typeof createSignature).toBe('function');
      expect(typeof processDHCommit).toBe('function');
      expect(typeof processDHKey).toBe('function');
      expect(typeof processRevealSignature).toBe('function');
      expect(typeof processSignature).toBe('function');
      expect(typeof startAKE).toBe('function');
    });

    test('should have DSA key generation available', () => {
      expect(typeof generateDSAKeyPair).toBe('function');
      expect(dsaAlice).toBeDefined();
      expect(dsaBob).toBeDefined();
      expect(dsaAlice.privateKey).toBeDefined();
      expect(dsaAlice.publicKey).toBeDefined();
    });

    test('should handle async operations', async () => {
      const startTime = Date.now();

<<<<<<< HEAD
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const sharedSecret = new Uint8Array(32);
      const aesKey = new Uint8Array(16);
      const iv = new Uint8Array(16);
      const sessionKey = new Uint8Array(16);

      await createDHCommit(dhKeyPair, 3, 1, 0);
      await createDHKey(dhKeyPair, 3, 1, 0);
      await createRevealSignature(dhKeyPair, dsaAlice, sharedSecret, 3, 1, 0, aesKey, iv);
      await createSignature(dhKeyPair, dsaAlice, sharedSecret, 3, 1, 0, sessionKey);
=======
      await createDHCommit();
      await createDHKey();
      await createRevealSignature();
      await createSignature();
>>>>>>> origin/feature/browser-extension

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(2000);
    });
  });
<<<<<<< HEAD

  describe('Protocol Flow Validation', () => {
    test('should initialize AKE and return a message', async () => {
      const initialState = {
        setState: jest.fn(),
        getState: jest.fn().mockReturnValue(STATE.PLAINTEXT)
      };

      const result = await startAKE(initialState);

      expect(result).toBeDefined();
      expect(result.state).toBe(initialState);
      expect(result.message).toBe('?OTR:AKESTART');
    });

    test('should handle errors gracefully', async () => {
      const initialState = null;

      await expect(startAKE(initialState)).rejects.toThrow();
    });

    test('should process DH Commit message and return a response', async () => {
      const initialState = {
        setState: jest.fn(),
        getState: jest.fn().mockReturnValue(STATE.PLAINTEXT)
      };
      const data = { /* mock DH Commit data */ };

      const result = await processDHCommit(data, initialState);

      expect(result).toBeDefined();
      expect(result.state).toBe(initialState);
      expect(result.response).toBe('?OTR:DHKEY');
    });

    test('should process DH Key message and return a response', async () => {
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const aliceDHKeyPair = await generateDHKeyPair();

      // Create a real DH Key message
      const dhKeyMessage = await createDHKey(dhKeyPair, 3, 1, 0);

      const initialState = {
        setState: jest.fn(),
        getState: jest.fn().mockReturnValue(STATE.AWAITING_DHKEY),
        dhKeyPair: aliceDHKeyPair,
        dsaKeyPair: dsaAlice,
        dhCommitMessage: { aesKey: new Uint8Array(16).fill(1), iv: new Uint8Array(16).fill(0) },
        protocolVersion: 3,
        instanceTag: 0
      };

      const result = await processDHKey(dhKeyMessage, initialState);

      expect(result).toBeDefined();
      expect(result.state).toBe(initialState);
      expect(result.response).toBe('?OTR:REVEALSIG');
    });

    test('should process Reveal Signature message and return a response', async () => {
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const bobDHKeyPair = await generateDHKeyPair();
      const sharedSecret = new Uint8Array(32).fill(42);
      const aesKey = new Uint8Array(16).fill(1);
      const iv = new Uint8Array(16).fill(0);

      // Create a real Reveal Signature message
      const revealSigMessage = await createRevealSignature(dhKeyPair, dsaAlice, sharedSecret, 3, 1, 0, aesKey, iv);

      const initialState = {
        setState: jest.fn(),
        getState: jest.fn().mockReturnValue(STATE.AWAITING_REVEALSIG),
        dhKeyPair: bobDHKeyPair,
        dsaKeyPair: dsaBob,
        theirPublicKey: dhKeyPair.publicKey,
        sharedSecret: sharedSecret,
        theirDSAPublicKey: dsaAlice.publicKey,
        protocolVersion: 3,
        instanceTag: 0,
        handleRevealSignature: jest.fn()
      };

      // Mock signature verification to pass
      const originalVerify = require('../../../src/core/crypto/dsa').verify;
      require('../../../src/core/crypto/dsa').verify = jest.fn().mockResolvedValue(true);

      try {
        const result = await processRevealSignature(revealSigMessage, initialState);

        expect(result).toBeDefined();
        expect(result.state).toBe(initialState);
        expect(result.response).toBe('?OTR:SIG');
      } finally {
        require('../../../src/core/crypto/dsa').verify = originalVerify;
      }
    });

    test('should process Signature message and update state to ENCRYPTED', async () => {
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');
      const dhKeyPair = await generateDHKeyPair();
      const aliceDHKeyPair = await generateDHKeyPair();
      const sharedSecret = new Uint8Array(32).fill(42);
      const sessionKey = new Uint8Array(16).fill(2);

      // Create a real Signature message
      const signatureMessage = await createSignature(dhKeyPair, dsaBob, sharedSecret, 3, 1, 0, sessionKey);

      const initialState = {
        setState: jest.fn(),
        getState: jest.fn().mockReturnValue(STATE.AWAITING_SIG),
        dhKeyPair: aliceDHKeyPair,
        dsaKeyPair: dsaAlice,
        receivingAESKey: sessionKey,
        theirPublicKey: dhKeyPair.publicKey,
        sharedSecret: sharedSecret,
        theirDSAPublicKey: dsaBob.publicKey,
        handleSignature: jest.fn(),
        STATE: { ENCRYPTED: 4 }
      };

      // Mock signature verification to pass
      const originalVerify = require('../../../src/core/crypto/dsa').verify;
      require('../../../src/core/crypto/dsa').verify = jest.fn().mockResolvedValue(true);

      try {
        const result = await processSignature(signatureMessage, initialState);

        expect(result).toBeDefined();
        expect(result.state).toBe(initialState);
        expect(result.response).toBeNull();
        expect(initialState.setState).toHaveBeenCalledWith(STATE.ENCRYPTED);
      } finally {
        require('../../../src/core/crypto/dsa').verify = originalVerify;
      }
    });
  });

  describe('Complete AKE Flow', () => {
    test('should complete full AKE flow successfully', async () => {
      const { generateDHKeyPair } = await import('../../../src/core/crypto/dh.js');

      // Create mock state that tracks state changes
      let currentState = STATE.PLAINTEXT;
      const aliceState = {
        setState: jest.fn((newState) => { currentState = newState; }),
        getState: jest.fn(() => currentState),
        dsaKeyPair: dsaAlice,
        protocolVersion: 3,
        instanceTag: 1,
        handleDHCommit: jest.fn(),
        handleDHKey: jest.fn(),
        handleRevealSignature: jest.fn(),
        handleSignature: jest.fn(),
        STATE: { ENCRYPTED: 4 }
      };

      const bobState = {
        setState: jest.fn(),
        getState: jest.fn(),
        dsaKeyPair: dsaBob,
        protocolVersion: 3,
        instanceTag: 2,
        handleDHCommit: jest.fn(),
        handleDHKey: jest.fn(),
        handleRevealSignature: jest.fn(),
        handleSignature: jest.fn(),
        STATE: { ENCRYPTED: 4 }
      };

      // Mock signature verification to pass for this integration test
      const originalVerify = require('../../../src/core/crypto/dsa').verify;
      require('../../../src/core/crypto/dsa').verify = jest.fn().mockResolvedValue(true);

      try {
        // Step 1: Alice starts AKE
        const startResult = await startAKE(aliceState);
        expect(startResult.message).toBe('?OTR:AKESTART');
        expect(aliceState.dhKeyPair).toBeDefined();

        // Step 2: Bob processes DH Commit and responds with DH Key
        const dhCommitResult = await processDHCommit(startResult.dhCommit, bobState);
        expect(dhCommitResult.response).toBe('?OTR:DHKEY');
        expect(bobState.dhKeyPair).toBeDefined();

        // Step 3: Alice processes DH Key and responds with Reveal Signature
        aliceState.dhCommitMessage = { aesKey: new Uint8Array(16).fill(1), iv: new Uint8Array(16).fill(0) };
        const dhKeyResult = await processDHKey(dhCommitResult.dhKeyMessage, aliceState);
        expect(dhKeyResult.response).toBe('?OTR:REVEALSIG');

        // Step 4: Bob processes Reveal Signature and responds with Signature
        bobState.theirPublicKey = aliceState.dhKeyPair.publicKey;
        bobState.sharedSecret = new Uint8Array(32).fill(42);
        bobState.theirDSAPublicKey = dsaAlice.publicKey;
        const revealSigResult = await processRevealSignature(dhKeyResult.revealSignatureMessage, bobState);
        expect(revealSigResult.response).toBe('?OTR:SIG');

        // Step 5: Alice processes Signature and completes AKE
        aliceState.receivingAESKey = new Uint8Array(16).fill(2);
        aliceState.theirPublicKey = bobState.dhKeyPair.publicKey;
        aliceState.sharedSecret = new Uint8Array(32).fill(42);
        aliceState.theirDSAPublicKey = dsaBob.publicKey;
        const sigResult = await processSignature(revealSigResult.signatureMessage, aliceState);
        expect(sigResult.response).toBeNull();

        // Verify final state is ENCRYPTED
        expect(aliceState.setState).toHaveBeenCalledWith(STATE.ENCRYPTED);
      } finally {
        require('../../../src/core/crypto/dsa').verify = originalVerify;
      }
    });
  });
=======
>>>>>>> origin/feature/browser-extension
});
