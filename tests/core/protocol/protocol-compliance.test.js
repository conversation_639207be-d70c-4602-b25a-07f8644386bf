/**
 * Protocol Compliance Tests - Based on libOTR/coyim test patterns
 * Tests OTR v3 protocol compliance and edge cases
 */

import { OtrSession } from '../../../src/core/session';
import { createDataMessage, parseOTRDataMessage } from '../../../src/core/protocol/message';
import { STATE } from '../../../src/core/state';

describe('OTR Protocol Compliance Tests', () => {
  let aliceSession, bobSession;
  let aliceMessages = [];
  let bobMessages = [];

  const aliceSendMessage = jest.fn(message => bobMessages.push(message));
  const bobSendMessage = jest.fn(message => aliceMessages.push(message));

  beforeEach(async () => {
    aliceMessages = [];
    bobMessages = [];

    aliceSession = new OtrSession('bob', {
      sendMessage: aliceSendMessage,
      testing: true
    });

    bobSession = new OtrSession('alice', {
      sendMessage: bobSendMessage,
      testing: true
    });

    await Promise.all([
      aliceSession.init(),
      bobSession.init()
    ]);

    // Establish encrypted session
    await aliceSession.startOtr();
    await bobSession.startOtr();
  });

  afterEach(() => {
    if (aliceSession) aliceSession.destroy();
    if (bobSession) bobSession.destroy();
  });

  describe('Message Format Validation', () => {
    test('should reject messages with wrong protocol version', async () => {
      // Create a message with wrong version
      const malformedMessage = '?OTR:AAECAAAAxInvalidProtocolVersion.';
      
      await expect(
        bobSession.processIncoming(malformedMessage)
      ).rejects.toThrow(/protocol version/i);
    });

    test('should handle empty OTR messages gracefully', async () => {
      const emptyMessage = '?OTR:.';
      
      const result = await bobSession.processIncoming(emptyMessage);
      expect(result).toBeNull(); // Should be ignored, not crash
    });

    test('should validate OTR data message structure', async () => {
      const plaintext = 'Test message';
      const encryptedMessage = await aliceSession.encryptMessage(plaintext);
      
      // Parse the message to verify structure
      const parsed = parseOTRDataMessage(encryptedMessage);
      expect(parsed).toHaveProperty('encryptedMessage');
      expect(parsed).toHaveProperty('authenticator');
      expect(parsed).toHaveProperty('counter');
      expect(typeof parsed.counter).toBe('number');
    });

    test('should reject messages with invalid base64 encoding', async () => {
      const invalidBase64 = '?OTR:InvalidBase64Characters!@#$.';
      
      await expect(
        bobSession.processIncoming(invalidBase64)
      ).rejects.toThrow();
    });
  });

  describe('Counter and Replay Protection', () => {
    test('should reject messages with regressed counters', async () => {
      // Send a message to establish counter
      const message1 = await aliceSession.encryptMessage('First message');
      await bobSession.decryptMessage(message1);

      // Try to replay the same message (same counter)
      await expect(
        bobSession.decryptMessage(message1)
      ).rejects.toThrow(/counter/i);
    });

    test('should handle counter overflow gracefully', async () => {
      // Simulate high counter value
      const highCounterMessage = await aliceSession.encryptMessage('High counter test');
      
      // Should process normally
      const decrypted = await bobSession.decryptMessage(highCounterMessage);
      expect(decrypted).toBe('High counter test');
    });

    test('should maintain separate counters for each direction', async () => {
      // Alice -> Bob
      const aliceToBob = await aliceSession.encryptMessage('Alice to Bob');
      const decrypted1 = await bobSession.decryptMessage(aliceToBob);
      expect(decrypted1).toBe('Alice to Bob');

      // Bob -> Alice (should have independent counter)
      const bobToAlice = await bobSession.encryptMessage('Bob to Alice');
      const decrypted2 = await aliceSession.decryptMessage(bobToAlice);
      expect(decrypted2).toBe('Bob to Alice');
    });
  });

  describe('Error Message Handling', () => {
    test('should not display error messages to user', async () => {
      const errorMessage = '?OTR Error:You are doing something wrong';
      
      const result = await bobSession.processIncoming(errorMessage);
      expect(result).toBeNull(); // Should not return error content to user
    });

    test('should handle malformed error messages', async () => {
      const malformedError = '?OTR Error:';
      
      const result = await bobSession.processIncoming(malformedError);
      expect(result).toBeNull();
    });
  });

  describe('State Validation', () => {
    test('should reject data messages when not encrypted', async () => {
      // Create a new session that's not encrypted
      const newSession = new OtrSession('test', {
        sendMessage: jest.fn(),
        testing: true
      });
      await newSession.init();

      const encryptedMessage = await aliceSession.encryptMessage('Test');
      
      await expect(
        newSession.decryptMessage(encryptedMessage)
      ).rejects.toThrow(/not.*encrypted/i);
    });

    test('should handle state transitions correctly', async () => {
      expect(aliceSession.state.getState()).toBe(STATE.ENCRYPTED);
      expect(bobSession.state.getState()).toBe(STATE.ENCRYPTED);

      // End session
      await aliceSession.endSession();
      expect(aliceSession.state.getState()).toBe(STATE.PLAINTEXT);
    });
  });

  describe('Message Size and Limits', () => {
    test('should handle maximum message size', async () => {
      // Test with large message (but within reasonable limits)
      const largeMessage = 'A'.repeat(8192); // 8KB
      
      const encrypted = await aliceSession.encryptMessage(largeMessage);
      const decrypted = await bobSession.decryptMessage(encrypted);
      
      expect(decrypted).toBe(largeMessage);
    });

    test('should handle empty messages', async () => {
      const emptyMessage = '';
      
      const encrypted = await aliceSession.encryptMessage(emptyMessage);
      const decrypted = await bobSession.decryptMessage(encrypted);
      
      expect(decrypted).toBe(emptyMessage);
    });

    test('should handle unicode messages', async () => {
      const unicodeMessage = '🔒 Secure message with émojis and spëcial chars! 中文 العربية';
      
      const encrypted = await aliceSession.encryptMessage(unicodeMessage);
      const decrypted = await bobSession.decryptMessage(encrypted);
      
      expect(decrypted).toBe(unicodeMessage);
    });
  });

  describe('Cryptographic Properties', () => {
    test('should produce different ciphertexts for same plaintext', async () => {
      const plaintext = 'Same message';
      
      const encrypted1 = await aliceSession.encryptMessage(plaintext);
      const encrypted2 = await aliceSession.encryptMessage(plaintext);
      
      // Should be different due to counter increment
      expect(encrypted1).not.toBe(encrypted2);
      
      // But both should decrypt to same plaintext
      const decrypted1 = await bobSession.decryptMessage(encrypted1);
      const decrypted2 = await bobSession.decryptMessage(encrypted2);
      
      expect(decrypted1).toBe(plaintext);
      expect(decrypted2).toBe(plaintext);
    });

    test('should detect message tampering', async () => {
      const plaintext = 'Original message';
      const encrypted = await aliceSession.encryptMessage(plaintext);
      
      // Tamper with the message
      const tamperedMessage = encrypted.slice(0, -10) + 'TAMPERED==';
      
      await expect(
        bobSession.decryptMessage(tamperedMessage)
      ).rejects.toThrow();
    });
  });

  describe('Session Management', () => {
    test('should handle session refresh', async () => {
      // Send a message
      const message1 = await aliceSession.encryptMessage('Before refresh');
      await bobSession.decryptMessage(message1);

      // Refresh session (simulate reconnection)
      await aliceSession.refreshSession();
      await bobSession.refreshSession();

      // Should still work after refresh
      const message2 = await aliceSession.encryptMessage('After refresh');
      const decrypted = await bobSession.decryptMessage(message2);
      
      expect(decrypted).toBe('After refresh');
    });

    test('should clean up resources on session end', async () => {
      await aliceSession.endSession();
      
      // Verify session is cleaned up
      expect(aliceSession.state.getState()).toBe(STATE.PLAINTEXT);
      
      // Should not be able to encrypt after ending
      await expect(
        aliceSession.encryptMessage('After end')
      ).rejects.toThrow();
    });
  });
});
