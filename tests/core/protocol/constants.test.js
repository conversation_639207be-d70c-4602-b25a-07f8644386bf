/**
 * Tests for OTR protocol constants
 */

import { PROTOCOL_VERSION } from "../../../src/core/protocol/state";
import { MESSAGE_PREFIX } from "../../../src/core/protocol/constants";
import { MESSAGE_TYPE } from "../../../src/core/protocol/state";

describe("OTR Protocol Constants", () => {
  // Test protocol versions
  describe("Protocol Versions", () => {
    test("should define the correct protocol versions", () => {
      // V1 is not defined in the current implementation
      // expect(PROTOCOL_VERSION.V1).toBe(1);
      expect(PROTOCOL_VERSION.V2).toBe(2);
      expect(PROTOCOL_VERSION.V3).toBe(3);
      // V4 is not defined in the current implementation
      // expect(PROTOCOL_VERSION.V4).toBe(4);
      // CURRENT is not explicitly defined, but V3 is the default
      // expect(PROTOCOL_VERSION.CURRENT).toBe(3);
    });

    test("should have valid protocol versions", () => {
      const validVersions = [2, 3];
      expect(validVersions).toContain(PROTOCOL_VERSION.V2);
      expect(validVersions).toContain(PROTOCOL_VERSION.V3);
    });
  });

  // Test message types
  describe("Message Types", () => {
    test("should define the correct message types", () => {
      expect(MESSAGE_TYPE.QUERY).toBe("QUERY");
      expect(MESSAGE_TYPE.DH_COMMIT).toBe("DH_COMMIT");
      expect(MESSAGE_TYPE.DH_KEY).toBe("DH_KEY");
      expect(MESSAGE_TYPE.REVEAL_SIGNATURE).toBe("REVEAL_SIGNATURE");
      expect(MESSAGE_TYPE.SIGNATURE).toBe("SIGNATURE");
      expect(MESSAGE_TYPE.DATA).toBe("DATA");
      expect(MESSAGE_TYPE.ERROR).toBe("ERROR");
      expect(MESSAGE_TYPE.UNKNOWN).toBe("UNKNOWN");
    });

    test("should have unique message type values", () => {
      const values = Object.values(MESSAGE_TYPE);
      const uniqueValues = new Set(values);
      expect(uniqueValues.size).toBe(values.length);
    });
  });

  // Test message prefixes
  describe("Message Prefixes", () => {
    test("should define the correct message prefixes", () => {
      expect(MESSAGE_PREFIX.QUERY).toBe("?OTR?");
      expect(MESSAGE_PREFIX.ERROR).toBe("?OTR ERROR:");
      expect(MESSAGE_PREFIX.DATA).toBe("?OTR:");
      expect(MESSAGE_PREFIX.WHITESPACE_TAG).toBe(
        " \t  \t\t\t\t \t \t \t    \t\t  \t"
      );
    });

    test("should have unique message prefixes", () => {
      const values = Object.values(MESSAGE_PREFIX);
      const uniqueValues = new Set(values);
      expect(uniqueValues.size).toBe(values.length);
    });

    test("should have prefixes that can be distinguished from each other", () => {
      // QUERY prefix should not be a prefix of DATA prefix
      expect(MESSAGE_PREFIX.DATA.startsWith(MESSAGE_PREFIX.QUERY)).toBe(false);

      // ERROR prefix should not be a prefix of DATA prefix
      expect(MESSAGE_PREFIX.DATA.startsWith(MESSAGE_PREFIX.ERROR)).toBe(false);

      // WHITESPACE_TAG should be distinguishable from normal whitespace
      expect(MESSAGE_PREFIX.WHITESPACE_TAG).not.toBe("   ");
    });
  });

  // Test relationships between constants
  describe("Relationships Between Constants", () => {
    test("should have message types that correspond to protocol flow", () => {
      // Since we're using string constants, we can't test numeric ordering
      // Instead, verify that all required message types are defined
      expect(MESSAGE_TYPE.QUERY).toBeDefined();
      expect(MESSAGE_TYPE.DH_COMMIT).toBeDefined();
      expect(MESSAGE_TYPE.DH_KEY).toBeDefined();
      expect(MESSAGE_TYPE.REVEAL_SIGNATURE).toBeDefined();
      expect(MESSAGE_TYPE.SIGNATURE).toBeDefined();
      expect(MESSAGE_TYPE.DATA).toBeDefined();
    });

    test("should have error message type for error handling", () => {
      expect(MESSAGE_TYPE.ERROR).toBeDefined();
    });

    test("should have whitespace tag for discovery", () => {
      // WHITESPACE_TAG is not defined in the current implementation
      // expect(MESSAGE_TYPE.WHITESPACE_TAG).toBeDefined();
      expect(MESSAGE_PREFIX.WHITESPACE_TAG).toBeDefined();
    });
  });

  // Test usage in protocol
  describe("Usage in Protocol", () => {
    test("should be able to create a query message", () => {
      const version = PROTOCOL_VERSION.V3;
      const queryMessage = `${MESSAGE_PREFIX.QUERY}v${version}?`;

      expect(queryMessage).toBe("?OTR?v3?");
    });

    test("should be able to create a data message", () => {
      const content = "encrypted-data";
      const dataMessage = `${MESSAGE_PREFIX.DATA}${content}`;

      expect(dataMessage).toBe("?OTR:encrypted-data");
    });

    test("should be able to create an error message", () => {
      const errorText = "An error occurred";
      const errorMessage = `${MESSAGE_PREFIX.ERROR} ${errorText}`;

      expect(errorMessage).toBe("?OTR ERROR: An error occurred");
    });
  });
});
