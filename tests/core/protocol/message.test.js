/**
 * Tests for OTR message protocol implementation
 */

import {
  parseMessage,
  createMessage,
  createQueryMessage,
  createDataMessage,
  createErrorMessage
} from '../../../src/core/protocol/message';

import { encrypt, decrypt } from '../../../src/core/crypto/aes';
import { MESSAGE_TYPE } from '../../../src/core/protocol/state';

describe('OTR Message Protocol', () => {
  // Test message creation
  describe('createMessage', () => {
    test('should create a plaintext message', () => {
      const content = 'This is a plaintext message';
      const type = MESSAGE_TYPE.PLAINTEXT;
      
      const message = createMessage(content, type);
      
      expect(message).toBeDefined();
      expect(message.content).toBe(content);
      expect(message.type).toBe(type);
      expect(message.encrypted).toBe(false);
      expect(message.timestamp).toBeDefined();
    });
    
    test('should create a query message', () => {
      const content = '?OTRv3?';
      const type = MESSAGE_TYPE.QUERY;
      
      const message = createMessage(content, type);
      
      expect(message).toBeDefined();
      expect(message.content).toBe(content);
      expect(message.type).toBe(type);
      expect(message.encrypted).toBe(false);
    });
    
    test('should create a DH commit message', () => {
      const content = { 
        gx: 'encrypted-gx',
        publicKey: 'public-key-data'
      };
      const type = MESSAGE_TYPE.DH_COMMIT;
      
      const message = createMessage(content, type);
      
      expect(message).toBeDefined();
      expect(message.content).toEqual(content);
      expect(message.type).toBe(type);
      expect(message.encrypted).toBe(false);
    });
    
    test('should create a DH key message', () => {
      const content = { 
        gy: 'public-key-data'
      };
      const type = MESSAGE_TYPE.DH_KEY;
      
      const message = createMessage(content, type);
      
      expect(message).toBeDefined();
      expect(message.content).toEqual(content);
      expect(message.type).toBe(type);
      expect(message.encrypted).toBe(false);
    });
    
    test('should create a reveal signature message', () => {
      const content = { 
        r: 'random-value',
        encryptedSignature: 'encrypted-signature-data',
        mac: 'mac-value'
      };
      const type = MESSAGE_TYPE.REVEAL_SIGNATURE;
      
      const message = createMessage(content, type);
      
      expect(message).toBeDefined();
      expect(message.content).toEqual(content);
      expect(message.type).toBe(type);
      expect(message.encrypted).toBe(false);
    });
    
    test('should create a signature message', () => {
      const content = { 
        encryptedSignature: 'encrypted-signature-data',
        mac: 'mac-value'
      };
      const type = MESSAGE_TYPE.SIGNATURE;
      
      const message = createMessage(content, type);
      
      expect(message).toBeDefined();
      expect(message.content).toEqual(content);
      expect(message.type).toBe(type);
      expect(message.encrypted).toBe(false);
    });
    
    test('should create a data message', () => {
      const content = { 
        sender_keyid: 1,
        recipient_keyid: 2,
        next_dh: 'next-dh-value',
        ctr: 0,
        encryptedMessage: 'encrypted-message-data',
        mac: 'mac-value'
      };
      const type = MESSAGE_TYPE.DATA;
      
      const message = createMessage(content, type, true);
      
      expect(message).toBeDefined();
      expect(message.content).toEqual(content);
      expect(message.type).toBe(type);
      expect(message.encrypted).toBe(true);
    });
    
    test('should throw error with invalid type', () => {
      const content = 'This is a test message';
      const type = 'invalid-type';
      
      expect(() => createMessage(content, type)).toThrow();
    });
  });
  
  // Test message parsing
  describe('parseMessage', () => {
    test('should parse a plaintext message', () => {
      const messageStr = 'This is a plaintext message';
      
      const message = parseMessage(messageStr);
      
      expect(message).toBeDefined();
      expect(message.content).toBe(messageStr);
      expect(message.type).toBe(MESSAGE_TYPE.PLAINTEXT);
      expect(message.encrypted).toBe(false);
    });
    
    test('should parse a query message', () => {
      const messageStr = '?OTRv3?';
      
      const message = parseMessage(messageStr);
      
      expect(message).toBeDefined();
      expect(message.content).toBe(messageStr);
      expect(message.type).toBe(MESSAGE_TYPE.QUERY);
      expect(message.encrypted).toBe(false);
    });
    
    test('should parse an OTR encoded message', () => {
      const messageStr = '?OTR:AAMC';

      const message = parseMessage(messageStr);

      expect(message).toBeDefined();
      expect(message.type).toBe(MESSAGE_TYPE.DH_COMMIT);
      expect(message.encrypted).toBe(false);
    });
    
    test('should handle empty message', () => {
      const messageStr = '';
      
      const message = parseMessage(messageStr);
      
      expect(message).toBeDefined();
      expect(message.content).toBe('');
      expect(message.type).toBe(MESSAGE_TYPE.PLAINTEXT);
      expect(message.encrypted).toBe(false);
    });
    
    test('should handle null message', () => {
      const messageStr = null;

      expect(() => parseMessage(messageStr)).toThrow('Message cannot be null or undefined');
    });
  });
  
  // Test message encryption/decryption
  describe('Message Encryption/Decryption', () => {
    test('should encrypt and decrypt a message', async () => {
      const plaintext = 'This is a secret message';
      const key = 'encryption-key-12345';
      
      // Encrypt the message
      const encrypted = await encrypt(plaintext, key);
      expect(encrypted).toBeDefined();
      expect(encrypted).not.toBe(plaintext);

      // Decrypt the message
      const decrypted = await decrypt(encrypted, key);
      expect(decrypted).toBe(plaintext);
    });
    
    test('should fail decryption with wrong key', async () => {
      const plaintext = 'This is a secret message';
      const encryptKey = 'correct-key';
      const decryptKey = 'wrong-key';
      
      // Encrypt with correct key
      const encrypted = await encrypt(plaintext, encryptKey);

      // Try to decrypt with wrong key - should produce different result
      const decrypted = await decrypt(encrypted, decryptKey);
      expect(decrypted).not.toBe(plaintext);
    });
    
    test('should handle empty message', async () => {
      const plaintext = '';
      const key = 'encryption-key-12345';
      
      // Encrypt the empty message
      const encrypted = await encrypt(plaintext, key);
      expect(encrypted).toBeDefined();

      // Decrypt the message
      const decrypted = await decrypt(encrypted, key);
      expect(decrypted).toBe(plaintext);
    });
    
    test('should throw error with invalid inputs', async () => {
      const plaintext = 'This is a secret message';
      const key = 'encryption-key-12345';
      
      // Test with null plaintext
      await expect(encrypt(null, key)).rejects.toThrow();

      // Test with null key
      await expect(encrypt(plaintext, null)).rejects.toThrow();

      // Test decryption with null ciphertext
      await expect(decrypt(null, key)).rejects.toThrow();
      
      // Test decryption with null key
      const encrypted = await encrypt(plaintext, key);
      await expect(decrypt(encrypted, null)).rejects.toThrow();
    });
  });
});
