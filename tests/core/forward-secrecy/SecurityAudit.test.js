/**
 * Security Audit Tests for Forward Secrecy
 * 
 * Comprehensive security validation to ensure the Forward Secrecy
 * implementation meets cryptographic security requirements and
 * is resistant to various attack vectors.
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { ForwardSecrecyManager } from '../../../src/core/forward-secrecy/ForwardSecrecyManager.js';
import { KeyRotationEngine } from '../../../src/core/forward-secrecy/KeyRotationEngine.js';
import { SecureDeletionManager } from '../../../src/core/forward-secrecy/SecureDeletionManager.js';
import { ZeroKnowledgeVerifier } from '../../../src/core/forward-secrecy/ZeroKnowledgeVerifier.js';
import { SecureRandom } from '../../../src/core/crypto/SecureRandom.js';
import { KeyDerivation } from '../../../src/core/crypto/KeyDerivation.js';

// Security testing utilities
class SecurityAnalyzer {
  static analyzeRandomness(data) {
    const bytes = new Uint8Array(data);
    
    // Frequency analysis
    const frequencies = new Array(256).fill(0);
    bytes.forEach(byte => frequencies[byte]++);
    
    // Chi-square test
    const expected = bytes.length / 256;
    const chiSquare = frequencies.reduce((sum, freq) => {
      const diff = freq - expected;
      return sum + (diff * diff) / expected;
    }, 0);
    
    // Entropy calculation
    const entropy = frequencies.reduce((sum, freq) => {
      if (freq === 0) return sum;
      const p = freq / bytes.length;
      return sum - p * Math.log2(p);
    }, 0);
    
    return {
      entropy,
      chiSquare,
      maxEntropy: 8.0,
      chiSquareCritical: 293.25, // 255 degrees of freedom, 95% confidence
      passesChiSquare: chiSquare < 293.25,
      entropyRatio: entropy / 8.0
    };
  }
  
  static detectPatterns(data) {
    const bytes = new Uint8Array(data);
    const patterns = {
      repeatingBytes: 0,
      sequences: 0,
      zeroBytes: 0
    };
    
    // Count repeating bytes
    for (let i = 1; i < bytes.length; i++) {
      if (bytes[i] === bytes[i - 1]) {
        patterns.repeatingBytes++;
      }
    }
    
    // Count sequential patterns
    for (let i = 2; i < bytes.length; i++) {
      if (bytes[i] === bytes[i - 1] + 1 && bytes[i - 1] === bytes[i - 2] + 1) {
        patterns.sequences++;
      }
    }
    
    // Count zero bytes
    patterns.zeroBytes = bytes.filter(byte => byte === 0).length;
    
    return patterns;
  }
  
  static validateKeyUniqueness(keys) {
    const fingerprints = new Set();
    const duplicates = [];
    
    keys.forEach((key, index) => {
      const fingerprint = Array.from(key).join(',');
      if (fingerprints.has(fingerprint)) {
        duplicates.push(index);
      }
      fingerprints.add(fingerprint);
    });
    
    return {
      totalKeys: keys.length,
      uniqueKeys: fingerprints.size,
      duplicates: duplicates.length,
      uniquenessRatio: fingerprints.size / keys.length
    };
  }
}

describe('Forward Secrecy Security Audit', () => {
  let forwardSecrecyManager;
  let keyRotationEngine;
  let secureDeletionManager;
  let zeroKnowledgeVerifier;
  let secureRandom;
  let keyDerivation;

  beforeEach(async () => {
    secureRandom = new SecureRandom();
    keyDerivation = new KeyDerivation();
    
    await secureRandom.initialize();
    await keyDerivation.initialize();
    
    forwardSecrecyManager = new ForwardSecrecyManager({
      autoRotation: false,
      fipsCompliance: true,
      auditTrails: true
    });
    
    keyRotationEngine = new KeyRotationEngine();
    secureDeletionManager = new SecureDeletionManager();
    zeroKnowledgeVerifier = new ZeroKnowledgeVerifier();
    
    await forwardSecrecyManager.initialize();
    await keyRotationEngine.initialize();
    await secureDeletionManager.initialize();
    await zeroKnowledgeVerifier.initialize();
  });

  afterEach(async () => {
    await forwardSecrecyManager.shutdown();
    await keyRotationEngine.shutdown();
    await secureDeletionManager.shutdown();
    await zeroKnowledgeVerifier.shutdown();
    secureRandom.shutdown();
    keyDerivation.shutdown();
  });

  describe('Cryptographic Randomness Validation', () => {
    test('should generate cryptographically secure random keys', async () => {
      const keyCount = 10;
      const keys = [];
      
      for (let i = 0; i < keyCount; i++) {
        const keySet = await keyRotationEngine.generateKeySet(i);
        keys.push(keySet.encryptionKey);
        keys.push(keySet.macKey);
      }
      
      // Analyze each key for randomness
      const analyses = keys.map(key => SecurityAnalyzer.analyzeRandomness(key));
      
      analyses.forEach((analysis, index) => {
        expect(analysis.passesChiSquare).toBe(true);
        expect(analysis.entropyRatio).toBeGreaterThan(0.95); // >95% of max entropy
        
        console.log(`Key ${index}: Entropy ${analysis.entropy.toFixed(2)}/8.0, Chi-square ${analysis.chiSquare.toFixed(2)}`);
      });
      
      // Validate key uniqueness
      const uniquenessCheck = SecurityAnalyzer.validateKeyUniqueness(keys);
      expect(uniquenessCheck.duplicates).toBe(0);
      expect(uniquenessCheck.uniquenessRatio).toBe(1.0);
    });

    test('should not exhibit detectable patterns', async () => {
      const largeKey = await secureRandom.generateBytes(1024);
      const patterns = SecurityAnalyzer.detectPatterns(largeKey);
      
      // Should have minimal patterns
      expect(patterns.repeatingBytes).toBeLessThan(largeKey.length * 0.1); // <10% repeating
      expect(patterns.sequences).toBeLessThan(5); // Very few sequences
      expect(patterns.zeroBytes).toBeLessThan(largeKey.length * 0.05); // <5% zeros
      
      console.log(`Pattern Analysis:
        Repeating bytes: ${patterns.repeatingBytes}/${largeKey.length}
        Sequences: ${patterns.sequences}
        Zero bytes: ${patterns.zeroBytes}/${largeKey.length}`);
    });

    test('should pass NIST randomness tests', async () => {
      const testData = await secureRandom.generateBytes(10000);
      const analysis = SecurityAnalyzer.analyzeRandomness(testData);
      
      // NIST SP 800-22 inspired tests
      expect(analysis.entropy).toBeGreaterThan(7.8); // High entropy
      expect(analysis.passesChiSquare).toBe(true);
      expect(analysis.entropyRatio).toBeGreaterThan(0.975);
      
      console.log(`NIST-style Analysis:
        Entropy: ${analysis.entropy.toFixed(3)}/8.0
        Chi-square: ${analysis.chiSquare.toFixed(2)} (critical: ${analysis.chiSquareCritical})
        Entropy ratio: ${(analysis.entropyRatio * 100).toFixed(1)}%`);
    });
  });

  describe('Key Derivation Security', () => {
    test('should produce independent keys from same input', async () => {
      const masterKey = await secureRandom.generateBytes(32);
      const derivedKeys = [];
      
      // Derive multiple keys with different contexts
      for (let i = 0; i < 10; i++) {
        const key = await keyDerivation.deriveKey(
          masterKey,
          `context-${i}`,
          32
        );
        derivedKeys.push(key);
      }
      
      // Verify all keys are different
      const uniquenessCheck = SecurityAnalyzer.validateKeyUniqueness(derivedKeys);
      expect(uniquenessCheck.duplicates).toBe(0);
      expect(uniquenessCheck.uniquenessRatio).toBe(1.0);
      
      // Verify keys appear random
      derivedKeys.forEach((key, index) => {
        const analysis = SecurityAnalyzer.analyzeRandomness(key);
        expect(analysis.entropyRatio).toBeGreaterThan(0.9);
      });
    });

    test('should be deterministic with same inputs', async () => {
      const masterKey = await secureRandom.generateBytes(32);
      const salt = await secureRandom.generateBytes(16);
      const context = 'test-context';
      
      const key1 = await keyDerivation.deriveKey(masterKey, context, 32, salt);
      const key2 = await keyDerivation.deriveKey(masterKey, context, 32, salt);
      
      expect(key1).toEqual(key2);
    });

    test('should produce different keys with different salts', async () => {
      const masterKey = await secureRandom.generateBytes(32);
      const context = 'test-context';
      
      const salt1 = await secureRandom.generateBytes(16);
      const salt2 = await secureRandom.generateBytes(16);
      
      const key1 = await keyDerivation.deriveKey(masterKey, context, 32, salt1);
      const key2 = await keyDerivation.deriveKey(masterKey, context, 32, salt2);
      
      expect(key1).not.toEqual(key2);
    });
  });

  describe('Forward Secrecy Validation', () => {
    test('should ensure past keys cannot be recovered', async () => {
      const generations = [];
      
      // Generate multiple key generations
      for (let i = 0; i < 5; i++) {
        await forwardSecrecyManager.rotateKeysManually();
        const currentKeys = forwardSecrecyManager.getCurrentKeys();
        generations.push({
          generation: currentKeys.generation,
          encryptionKey: new Uint8Array(currentKeys.encryptionKey),
          macKey: new Uint8Array(currentKeys.macKey)
        });
      }
      
      // Verify each generation is unique
      const allKeys = [];
      generations.forEach(gen => {
        allKeys.push(gen.encryptionKey);
        allKeys.push(gen.macKey);
      });
      
      const uniquenessCheck = SecurityAnalyzer.validateKeyUniqueness(allKeys);
      expect(uniquenessCheck.duplicates).toBe(0);
      
      // Verify no mathematical relationship between consecutive keys
      for (let i = 1; i < generations.length; i++) {
        const prev = generations[i - 1];
        const curr = generations[i];
        
        // XOR analysis - should not reveal patterns
        const xorResult = new Uint8Array(32);
        for (let j = 0; j < 32; j++) {
          xorResult[j] = prev.encryptionKey[j] ^ curr.encryptionKey[j];
        }
        
        const xorAnalysis = SecurityAnalyzer.analyzeRandomness(xorResult);
        expect(xorAnalysis.entropyRatio).toBeGreaterThan(0.8); // XOR should appear random
      }
    });

    test('should securely delete old key material', async () => {
      const testKeys = {
        encryptionKey: new Uint8Array(32).fill(0x42),
        macKey: new Uint8Array(32).fill(0x33),
        generation: 1
      };
      
      // Store original values for verification
      const originalEncryption = new Uint8Array(testKeys.encryptionKey);
      const originalMac = new Uint8Array(testKeys.macKey);
      
      // Perform secure deletion
      const deletionResult = await secureDeletionManager.deleteKeys({
        keyGeneration: 1,
        keys: testKeys
      });
      
      expect(deletionResult.verified).toBe(true);
      
      // Verify keys are completely zeroed
      expect(testKeys.encryptionKey.every(byte => byte === 0)).toBe(true);
      expect(testKeys.macKey.every(byte => byte === 0)).toBe(true);
      
      // Verify original data is not recoverable
      expect(testKeys.encryptionKey).not.toEqual(originalEncryption);
      expect(testKeys.macKey).not.toEqual(originalMac);
    });

    test('should validate key integrity', async () => {
      await forwardSecrecyManager.rotateKeysManually();
      
      const integrityResult = await forwardSecrecyManager.validateKeyIntegrity();
      expect(integrityResult.valid).toBe(true);
      
      // Simulate key corruption
      const currentKeys = forwardSecrecyManager.getCurrentKeys();
      const corruptedManager = new ForwardSecrecyManager();
      await corruptedManager.initialize();
      
      // Manually corrupt the key fingerprint
      corruptedManager.keyRotationEngine.state.currentKeys.keyFingerprint = 'corrupted';
      
      const corruptedIntegrityResult = await corruptedManager.validateKeyIntegrity();
      expect(corruptedIntegrityResult.valid).toBe(false);
      
      await corruptedManager.shutdown();
    });
  });

  describe('Zero-Knowledge Proof Security', () => {
    test('should not leak key information in proofs', async () => {
      const oldKeys = {
        generation: 1,
        keyFingerprint: 'old-fingerprint',
        encryptionKey: await secureRandom.generateBytes(32)
      };
      
      const newKeys = {
        generation: 2,
        keyFingerprint: 'new-fingerprint',
        encryptionKey: await secureRandom.generateBytes(32)
      };
      
      const proof = await zeroKnowledgeVerifier.generateRotationProof({
        oldKeyGeneration: 1,
        newKeyGeneration: 2,
        rotationData: { oldKeys, newKeys }
      });
      
      // Verify proof doesn't contain key material
      const proofString = JSON.stringify(proof);
      
      // Check that actual key bytes are not present in proof
      const oldKeyHex = Array.from(oldKeys.encryptionKey).map(b => b.toString(16).padStart(2, '0')).join('');
      const newKeyHex = Array.from(newKeys.encryptionKey).map(b => b.toString(16).padStart(2, '0')).join('');
      
      expect(proofString).not.toContain(oldKeyHex);
      expect(proofString).not.toContain(newKeyHex);
      
      // Verify proof is verifiable
      const isValid = await zeroKnowledgeVerifier.verifyRotationProof(proof);
      expect(isValid).toBe(true);
    });

    test('should reject invalid proofs', async () => {
      const validProof = await zeroKnowledgeVerifier.generateRotationProof({
        oldKeyGeneration: 1,
        newKeyGeneration: 2,
        rotationData: {
          oldKeys: { generation: 1, keyFingerprint: 'old-fp' },
          newKeys: { generation: 2, keyFingerprint: 'new-fp' }
        }
      });
      
      // Test with corrupted proof
      const corruptedProof = { ...validProof };
      corruptedProof.proofHash = 'corrupted-hash';
      
      const isValidCorrupted = await zeroKnowledgeVerifier.verifyRotationProof(corruptedProof);
      expect(isValidCorrupted).toBe(false);
      
      // Test with mismatched generations
      const mismatchedProof = { ...validProof };
      mismatchedProof.oldKeyGeneration = 999;
      
      const isValidMismatched = await zeroKnowledgeVerifier.verifyRotationProof(mismatchedProof);
      expect(isValidMismatched).toBe(false);
    });
  });

  describe('Attack Resistance', () => {
    test('should resist timing attacks', async () => {
      const iterations = 50;
      const times = [];
      
      // Measure key generation times
      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        await keyRotationEngine.generateKeySet(i);
        const end = performance.now();
        times.push(end - start);
      }
      
      // Calculate timing statistics
      const mean = times.reduce((sum, time) => sum + time, 0) / times.length;
      const variance = times.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / times.length;
      const standardDeviation = Math.sqrt(variance);
      const coefficientOfVariation = standardDeviation / mean;
      
      console.log(`Timing Analysis:
        Mean: ${mean.toFixed(2)}ms
        Std Dev: ${standardDeviation.toFixed(2)}ms
        Coefficient of Variation: ${coefficientOfVariation.toFixed(3)}`);
      
      // Timing should be relatively consistent (low coefficient of variation)
      expect(coefficientOfVariation).toBeLessThan(0.5); // <50% variation
    });

    test('should resist memory analysis attacks', async () => {
      const sensitiveData = await secureRandom.generateBytes(1024);
      const originalData = new Uint8Array(sensitiveData);
      
      // Simulate memory clearing
      await secureDeletionManager.clearMemory(sensitiveData);
      
      // Verify data is completely overwritten
      expect(sensitiveData.every(byte => byte === 0)).toBe(true);
      expect(sensitiveData).not.toEqual(originalData);
      
      // Verify no traces remain in different memory locations
      const memoryCheck = new Uint8Array(1024);
      crypto.getRandomValues(memoryCheck);
      
      // Should not match original pattern
      let matches = 0;
      for (let i = 0; i <= memoryCheck.length - originalData.length; i++) {
        let isMatch = true;
        for (let j = 0; j < originalData.length && j + i < memoryCheck.length; j++) {
          if (memoryCheck[i + j] !== originalData[j]) {
            isMatch = false;
            break;
          }
        }
        if (isMatch) matches++;
      }
      
      expect(matches).toBe(0); // No accidental matches
    });

    test('should handle concurrent access securely', async () => {
      const concurrentOperations = 10;
      const results = [];
      
      // Perform concurrent key rotations
      const promises = Array.from({ length: concurrentOperations }, async (_, i) => {
        const manager = new ForwardSecrecyManager({
          autoRotation: false,
          fipsCompliance: true
        });
        await manager.initialize();
        
        await manager.rotateKeysManually();
        const keys = manager.getCurrentKeys();
        
        await manager.shutdown();
        return keys;
      });
      
      const concurrentResults = await Promise.all(promises);
      
      // Verify all results are unique
      const allKeys = concurrentResults.map(result => result.encryptionKey);
      const uniquenessCheck = SecurityAnalyzer.validateKeyUniqueness(allKeys);
      
      expect(uniquenessCheck.duplicates).toBe(0);
      expect(uniquenessCheck.uniquenessRatio).toBe(1.0);
    });
  });

  describe('Compliance Validation', () => {
    test('should meet FIPS 140-2 requirements', async () => {
      // Test key generation meets FIPS requirements
      const key = await secureRandom.generateBytes(32);
      const analysis = SecurityAnalyzer.analyzeRandomness(key);
      
      // FIPS 140-2 Level 1 requirements
      expect(analysis.passesChiSquare).toBe(true);
      expect(analysis.entropyRatio).toBeGreaterThan(0.95);
      
      // Test key derivation uses approved algorithms
      const derivedKey = await keyDerivation.deriveKey(key, 'FIPS-test', 32);
      expect(derivedKey.length).toBe(32);
      
      const derivedAnalysis = SecurityAnalyzer.analyzeRandomness(derivedKey);
      expect(derivedAnalysis.entropyRatio).toBeGreaterThan(0.9);
    });

    test('should maintain audit trail integrity', async () => {
      // Perform operations that should be audited
      await forwardSecrecyManager.rotateKeysManually();
      await forwardSecrecyManager.emergencyRotation('TEST');
      
      const auditTrail = forwardSecrecyManager.getAuditTrail();
      expect(auditTrail.events.length).toBeGreaterThan(0);
      
      // Verify audit trail integrity
      const integrityCheck = await forwardSecrecyManager.verifyAuditTrailIntegrity();
      expect(integrityCheck.valid).toBe(true);
      expect(integrityCheck.chainIntegrity).toBe(true);
      
      // Verify all required fields are present
      auditTrail.events.forEach(event => {
        expect(event.type).toBeDefined();
        expect(event.timestamp).toBeDefined();
        expect(event.hash).toBeDefined();
      });
    });
  });
});
