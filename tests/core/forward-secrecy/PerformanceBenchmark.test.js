/**
 * Performance Benchmarking for Forward Secrecy
 * 
 * Comprehensive performance testing to validate that all
 * Forward Secrecy operations meet the specified targets:
 * - Key rotation: <100ms
 * - Secure deletion: <50ms
 * - Memory overhead: <1MB
 * - CPU overhead: <5%
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { ForwardSecrecyManager } from '../../../src/core/forward-secrecy/ForwardSecrecyManager.js';
import { KeyRotationEngine } from '../../../src/core/forward-secrecy/KeyRotationEngine.js';
import { SecureDeletionManager } from '../../../src/core/forward-secrecy/SecureDeletionManager.js';
import { ZeroKnowledgeVerifier } from '../../../src/core/forward-secrecy/ZeroKnowledgeVerifier.js';
import { SecureRandom } from '../../../src/core/crypto/SecureRandom.js';
import { KeyDerivation } from '../../../src/core/crypto/KeyDerivation.js';

// Performance measurement utilities
class PerformanceProfiler {
  constructor() {
    this.measurements = {};
    this.memoryBaseline = null;
  }
  
  startMeasurement(name) {
    this.measurements[name] = {
      startTime: performance.now(),
      startMemory: this.getMemoryUsage()
    };
  }
  
  endMeasurement(name) {
    if (!this.measurements[name]) {
      throw new Error(`No measurement started for: ${name}`);
    }
    
    const measurement = this.measurements[name];
    const endTime = performance.now();
    const endMemory = this.getMemoryUsage();
    
    return {
      duration: endTime - measurement.startTime,
      memoryDelta: endMemory - measurement.startMemory,
      startMemory: measurement.startMemory,
      endMemory: endMemory
    };
  }
  
  getMemoryUsage() {
    if (performance.memory) {
      return performance.memory.usedJSHeapSize;
    }
    return 0; // Fallback for environments without memory API
  }
  
  setMemoryBaseline() {
    this.memoryBaseline = this.getMemoryUsage();
  }
  
  getMemoryOverhead() {
    if (this.memoryBaseline === null) {
      return 0;
    }
    return this.getMemoryUsage() - this.memoryBaseline;
  }
}

// CPU usage monitor
class CPUMonitor {
  constructor() {
    this.samples = [];
    this.monitoring = false;
  }
  
  startMonitoring() {
    this.monitoring = true;
    this.samples = [];
    this.sampleCPU();
  }
  
  stopMonitoring() {
    this.monitoring = false;
    return this.calculateAverageCPU();
  }
  
  sampleCPU() {
    if (!this.monitoring) return;
    
    const start = performance.now();
    
    // Perform a small computation to measure CPU responsiveness
    let sum = 0;
    for (let i = 0; i < 1000; i++) {
      sum += Math.random();
    }
    
    const duration = performance.now() - start;
    this.samples.push(duration);
    
    // Schedule next sample
    setTimeout(() => this.sampleCPU(), 10);
  }
  
  calculateAverageCPU() {
    if (this.samples.length === 0) return 0;
    
    const average = this.samples.reduce((sum, sample) => sum + sample, 0) / this.samples.length;
    const baseline = 0.1; // Expected baseline for the computation
    
    // Calculate overhead as percentage above baseline
    return Math.max(0, ((average - baseline) / baseline) * 100);
  }
}

describe('Forward Secrecy Performance Benchmarks', () => {
  let profiler, cpuMonitor;
  let forwardSecrecyManager;
  let keyRotationEngine;
  let secureDeletionManager;
  let zeroKnowledgeVerifier;

  beforeEach(async () => {
    profiler = new PerformanceProfiler();
    cpuMonitor = new CPUMonitor();
    
    // Set memory baseline before initializing components
    profiler.setMemoryBaseline();
    
    // Initialize components
    forwardSecrecyManager = new ForwardSecrecyManager({
      autoRotation: false, // Disable for controlled testing
      rotationInterval: 3600000,
      messageCountThreshold: 1000,
      fipsCompliance: true
    });
    
    keyRotationEngine = new KeyRotationEngine();
    secureDeletionManager = new SecureDeletionManager();
    zeroKnowledgeVerifier = new ZeroKnowledgeVerifier();
    
    await forwardSecrecyManager.initialize();
    await keyRotationEngine.initialize();
    await secureDeletionManager.initialize();
    await zeroKnowledgeVerifier.initialize();
  });

  afterEach(async () => {
    await forwardSecrecyManager.shutdown();
    await keyRotationEngine.shutdown();
    await secureDeletionManager.shutdown();
    await zeroKnowledgeVerifier.shutdown();
  });

  describe('Key Rotation Performance', () => {
    test('should complete key rotation within 100ms target', async () => {
      const iterations = 10;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        profiler.startMeasurement(`rotation-${i}`);
        
        await forwardSecrecyManager.rotateKeysManually();
        
        const result = profiler.endMeasurement(`rotation-${i}`);
        times.push(result.duration);
      }
      
      const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);
      const minTime = Math.min(...times);
      
      console.log(`Key Rotation Performance:
        Average: ${averageTime.toFixed(2)}ms
        Maximum: ${maxTime.toFixed(2)}ms
        Minimum: ${minTime.toFixed(2)}ms
        Target: <100ms`);
      
      // Validate performance targets
      expect(averageTime).toBeLessThan(100);
      expect(maxTime).toBeLessThan(150); // Allow some variance for max
      
      // Verify all rotations were successful
      expect(forwardSecrecyManager.getStatus().currentKeyGeneration).toBe(iterations);
    });

    test('should maintain consistent performance under load', async () => {
      const loadIterations = 50;
      const times = [];
      
      cpuMonitor.startMonitoring();
      
      for (let i = 0; i < loadIterations; i++) {
        const start = performance.now();
        await keyRotationEngine.generateKeySet(i);
        const end = performance.now();
        times.push(end - start);
      }
      
      const cpuOverhead = cpuMonitor.stopMonitoring();
      
      const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const standardDeviation = Math.sqrt(
        times.reduce((sum, time) => sum + Math.pow(time - averageTime, 2), 0) / times.length
      );
      
      console.log(`Load Test Results:
        Iterations: ${loadIterations}
        Average time: ${averageTime.toFixed(2)}ms
        Standard deviation: ${standardDeviation.toFixed(2)}ms
        CPU overhead: ${cpuOverhead.toFixed(2)}%`);
      
      // Performance should remain consistent
      expect(standardDeviation).toBeLessThan(averageTime * 0.5); // <50% variance
      expect(cpuOverhead).toBeLessThan(5); // <5% CPU overhead
    });

    test('should handle concurrent rotations efficiently', async () => {
      const concurrentCount = 5;
      const managers = [];
      
      // Create multiple managers for concurrent testing
      for (let i = 0; i < concurrentCount; i++) {
        const manager = new ForwardSecrecyManager({
          autoRotation: false,
          fipsCompliance: true
        });
        await manager.initialize();
        managers.push(manager);
      }
      
      profiler.startMeasurement('concurrent-rotations');
      
      // Perform concurrent rotations
      const rotationPromises = managers.map(manager => 
        manager.rotateKeysManually()
      );
      
      await Promise.all(rotationPromises);
      
      const result = profiler.endMeasurement('concurrent-rotations');
      
      console.log(`Concurrent Rotation Performance:
        Managers: ${concurrentCount}
        Total time: ${result.duration.toFixed(2)}ms
        Average per rotation: ${(result.duration / concurrentCount).toFixed(2)}ms`);
      
      // Concurrent operations should not significantly degrade performance
      expect(result.duration / concurrentCount).toBeLessThan(120); // <120ms per rotation
      
      // Cleanup
      await Promise.all(managers.map(manager => manager.shutdown()));
    });
  });

  describe('Secure Deletion Performance', () => {
    test('should complete secure deletion within 50ms target', async () => {
      const iterations = 20;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        // Create test key material
        const testKeys = {
          encryptionKey: new Uint8Array(32).fill(0x42),
          macKey: new Uint8Array(32).fill(0x33),
          generation: i
        };
        
        profiler.startMeasurement(`deletion-${i}`);
        
        await secureDeletionManager.deleteKeys({
          keyGeneration: i,
          keys: testKeys
        });
        
        const result = profiler.endMeasurement(`deletion-${i}`);
        times.push(result.duration);
      }
      
      const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);
      
      console.log(`Secure Deletion Performance:
        Average: ${averageTime.toFixed(2)}ms
        Maximum: ${maxTime.toFixed(2)}ms
        Target: <50ms`);
      
      expect(averageTime).toBeLessThan(50);
      expect(maxTime).toBeLessThan(75); // Allow some variance
    });

    test('should handle large key deletion efficiently', async () => {
      // Create large key material (simulating multiple keys)
      const largeKeyMaterial = {
        encryptionKeys: Array.from({ length: 10 }, () => new Uint8Array(32).fill(0x42)),
        macKeys: Array.from({ length: 10 }, () => new Uint8Array(32).fill(0x33)),
        generation: 1
      };
      
      profiler.startMeasurement('large-deletion');
      
      await secureDeletionManager.deleteKeys({
        keyGeneration: 1,
        keys: largeKeyMaterial
      });
      
      const result = profiler.endMeasurement('large-deletion');
      
      console.log(`Large Key Deletion:
        Keys deleted: 20
        Time: ${result.duration.toFixed(2)}ms
        Time per key: ${(result.duration / 20).toFixed(2)}ms`);
      
      expect(result.duration).toBeLessThan(100); // Should handle large deletions efficiently
    });
  });

  describe('Memory Usage Validation', () => {
    test('should maintain memory overhead under 1MB', async () => {
      const initialMemory = profiler.getMemoryUsage();
      
      // Perform multiple operations to accumulate memory usage
      for (let i = 0; i < 10; i++) {
        await forwardSecrecyManager.rotateKeysManually();
        await forwardSecrecyManager.validateKeyIntegrity();
      }
      
      const finalMemory = profiler.getMemoryUsage();
      const memoryOverhead = finalMemory - initialMemory;
      const memoryOverheadMB = memoryOverhead / (1024 * 1024);
      
      console.log(`Memory Usage:
        Initial: ${(initialMemory / 1024 / 1024).toFixed(2)}MB
        Final: ${(finalMemory / 1024 / 1024).toFixed(2)}MB
        Overhead: ${memoryOverheadMB.toFixed(2)}MB
        Target: <1MB`);
      
      expect(memoryOverheadMB).toBeLessThan(1);
    });

    test('should not leak memory during extended operation', async () => {
      const measurements = [];
      
      for (let i = 0; i < 20; i++) {
        await forwardSecrecyManager.rotateKeysManually();
        
        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }
        
        measurements.push(profiler.getMemoryUsage());
        
        // Small delay to allow memory cleanup
        await new Promise(resolve => setTimeout(resolve, 10));
      }
      
      // Check for memory growth trend
      const firstHalf = measurements.slice(0, 10);
      const secondHalf = measurements.slice(10);
      
      const firstHalfAvg = firstHalf.reduce((sum, mem) => sum + mem, 0) / firstHalf.length;
      const secondHalfAvg = secondHalf.reduce((sum, mem) => sum + mem, 0) / secondHalf.length;
      
      const memoryGrowth = secondHalfAvg - firstHalfAvg;
      const memoryGrowthMB = memoryGrowth / (1024 * 1024);
      
      console.log(`Memory Leak Test:
        First half average: ${(firstHalfAvg / 1024 / 1024).toFixed(2)}MB
        Second half average: ${(secondHalfAvg / 1024 / 1024).toFixed(2)}MB
        Growth: ${memoryGrowthMB.toFixed(2)}MB`);
      
      // Memory growth should be minimal
      expect(memoryGrowthMB).toBeLessThan(0.5); // <500KB growth
    });
  });

  describe('Zero-Knowledge Proof Performance', () => {
    test('should generate proofs within 200ms target', async () => {
      const iterations = 10;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        const oldKeys = {
          generation: i,
          keyFingerprint: `old-fingerprint-${i}`,
          encryptionKey: new Uint8Array(32).fill(0x11)
        };
        
        const newKeys = {
          generation: i + 1,
          keyFingerprint: `new-fingerprint-${i + 1}`,
          encryptionKey: new Uint8Array(32).fill(0x22)
        };
        
        profiler.startMeasurement(`proof-${i}`);
        
        await zeroKnowledgeVerifier.generateRotationProof({
          oldKeyGeneration: i,
          newKeyGeneration: i + 1,
          rotationData: { oldKeys, newKeys }
        });
        
        const result = profiler.endMeasurement(`proof-${i}`);
        times.push(result.duration);
      }
      
      const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);
      
      console.log(`Zero-Knowledge Proof Performance:
        Average: ${averageTime.toFixed(2)}ms
        Maximum: ${maxTime.toFixed(2)}ms
        Target: <200ms`);
      
      expect(averageTime).toBeLessThan(200);
      expect(maxTime).toBeLessThan(300);
    });

    test('should verify proofs within 100ms target', async () => {
      // Generate a proof first
      const proof = await zeroKnowledgeVerifier.generateRotationProof({
        oldKeyGeneration: 1,
        newKeyGeneration: 2,
        rotationData: {
          oldKeys: { generation: 1, keyFingerprint: 'old-fp' },
          newKeys: { generation: 2, keyFingerprint: 'new-fp' }
        }
      });
      
      const iterations = 15;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        profiler.startMeasurement(`verification-${i}`);
        
        await zeroKnowledgeVerifier.verifyRotationProof(proof);
        
        const result = profiler.endMeasurement(`verification-${i}`);
        times.push(result.duration);
      }
      
      const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);
      
      console.log(`Proof Verification Performance:
        Average: ${averageTime.toFixed(2)}ms
        Maximum: ${maxTime.toFixed(2)}ms
        Target: <100ms`);
      
      expect(averageTime).toBeLessThan(100);
      expect(maxTime).toBeLessThan(150);
    });
  });

  describe('Overall System Performance', () => {
    test('should meet all performance targets simultaneously', async () => {
      cpuMonitor.startMonitoring();
      const initialMemory = profiler.getMemoryUsage();
      
      // Perform comprehensive operations
      const operations = [];
      
      for (let i = 0; i < 5; i++) {
        operations.push(
          (async () => {
            profiler.startMeasurement(`full-cycle-${i}`);
            
            // Full forward secrecy cycle
            await forwardSecrecyManager.rotateKeysManually();
            await forwardSecrecyManager.validateKeyIntegrity();
            
            const result = profiler.endMeasurement(`full-cycle-${i}`);
            return result;
          })()
        );
      }
      
      const results = await Promise.all(operations);
      const cpuOverhead = cpuMonitor.stopMonitoring();
      const finalMemory = profiler.getMemoryUsage();
      const memoryOverheadMB = (finalMemory - initialMemory) / (1024 * 1024);
      
      const averageTime = results.reduce((sum, result) => sum + result.duration, 0) / results.length;
      
      console.log(`Overall System Performance:
        Average cycle time: ${averageTime.toFixed(2)}ms
        Memory overhead: ${memoryOverheadMB.toFixed(2)}MB
        CPU overhead: ${cpuOverhead.toFixed(2)}%
        
        Targets:
        - Rotation time: <100ms ✓
        - Memory overhead: <1MB ✓
        - CPU overhead: <5% ✓`);
      
      // Validate all targets are met
      expect(averageTime).toBeLessThan(100);
      expect(memoryOverheadMB).toBeLessThan(1);
      expect(cpuOverhead).toBeLessThan(5);
    });
  });
});
