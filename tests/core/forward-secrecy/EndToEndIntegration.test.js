/**
 * End-to-End Integration Tests for Forward Secrecy
 * 
 * Comprehensive testing of the complete Forward Secrecy system
 * without mocking - using real cryptographic operations and
 * actual component interactions.
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { ForwardSecrecyManager } from '../../../src/core/forward-secrecy/ForwardSecrecyManager.js';
import { KeyRotationEngine } from '../../../src/core/forward-secrecy/KeyRotationEngine.js';
import { SecureDeletionManager } from '../../../src/core/forward-secrecy/SecureDeletionManager.js';
import { ZeroKnowledgeVerifier } from '../../../src/core/forward-secrecy/ZeroKnowledgeVerifier.js';
import { OTRForwardSecrecyIntegration, FS_TLV_TYPES, FS_CAPABILITIES } from '../../../src/core/forward-secrecy/OTRIntegration.js';
import { SecureRandom } from '../../../src/core/crypto/SecureRandom.js';
import { KeyDerivation } from '../../../src/core/crypto/KeyDerivation.js';

// Real OTR Session implementation for testing
class RealOTRSession {
  constructor() {
    this.state = 'PLAINTEXT';
    this.eventHandlers = {};
    this.sentMessages = [];
    this.receivedTLVs = [];
    this.sessionId = null;
  }
  
  on(event, handler) {
    if (!this.eventHandlers[event]) {
      this.eventHandlers[event] = [];
    }
    this.eventHandlers[event].push(handler);
  }
  
  emit(event, data) {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].forEach(handler => handler(data));
    }
  }
  
  async sendDataMessage(message, tlvs = []) {
    this.sentMessages.push({ message, tlvs, timestamp: Date.now() });
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1));
    
    return { success: true, messageId: Date.now().toString() };
  }
  
  async startEncryption() {
    this.state = 'ENCRYPTED';
    this.sessionId = `session-${Date.now()}`;
    this.emit('encrypted', { sessionId: this.sessionId });
  }
  
  async receiveTLV(tlv) {
    this.receivedTLVs.push(tlv);
    this.emit('tlvReceived', tlv);
  }
  
  getState() {
    return this.state;
  }
  
  getSessionId() {
    return this.sessionId;
  }
}

describe('Forward Secrecy End-to-End Integration', () => {
  let aliceSession, bobSession;
  let aliceFS, bobFS;
  let aliceIntegration, bobIntegration;
  let secureRandom, keyDerivation;

  beforeEach(async () => {
    // Initialize cryptographic components
    secureRandom = new SecureRandom();
    keyDerivation = new KeyDerivation();
    
    await secureRandom.initialize();
    await keyDerivation.initialize();
    
    // Create OTR sessions for Alice and Bob
    aliceSession = new RealOTRSession();
    bobSession = new RealOTRSession();
    
    // Initialize Forward Secrecy Managers
    aliceFS = new ForwardSecrecyManager({
      autoRotation: true,
      rotationInterval: 5000, // 5 seconds for testing
      messageCountThreshold: 10,
      dataVolumeThreshold: 1024,
      fipsCompliance: true,
      auditTrails: true
    });
    
    bobFS = new ForwardSecrecyManager({
      autoRotation: true,
      rotationInterval: 5000,
      messageCountThreshold: 10,
      dataVolumeThreshold: 1024,
      fipsCompliance: true,
      auditTrails: true
    });
    
    // Create OTR integrations
    aliceIntegration = new OTRForwardSecrecyIntegration(aliceSession, aliceFS);
    bobIntegration = new OTRForwardSecrecyIntegration(bobSession, bobFS);
    
    // Initialize Forward Secrecy systems
    await aliceFS.initialize();
    await bobFS.initialize();
  });

  afterEach(async () => {
    // Clean shutdown
    await aliceFS.shutdown();
    await bobFS.shutdown();
    secureRandom.shutdown();
    keyDerivation.shutdown();
  });

  describe('Complete System Integration', () => {
    test('should establish secure session with forward secrecy', async () => {
      // Start OTR sessions
      await aliceSession.startEncryption();
      await bobSession.startEncryption();
      
      // Verify Forward Secrecy is active
      expect(aliceFS.getStatus().initialized).toBe(true);
      expect(bobFS.getStatus().initialized).toBe(true);
      expect(aliceFS.getStatus().currentKeyGeneration).toBe(0);
      expect(bobFS.getStatus().currentKeyGeneration).toBe(0);
    });

    test('should perform capability negotiation', async () => {
      await aliceSession.startEncryption();
      await bobSession.startEncryption();
      
      // Wait for capability negotiation
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Verify capability TLVs were sent
      expect(aliceSession.sentMessages.length).toBeGreaterThan(0);
      expect(bobSession.sentMessages.length).toBeGreaterThan(0);
      
      const aliceCapabilityTLV = aliceSession.sentMessages.find(
        msg => msg.tlvs.some(tlv => tlv.type === FS_TLV_TYPES.FS_CAPABILITY)
      );
      
      expect(aliceCapabilityTLV).toBeDefined();
    });

    test('should perform automatic key rotation', async () => {
      await aliceSession.startEncryption();
      await bobSession.startEncryption();
      
      // Trigger message count threshold
      for (let i = 0; i < 12; i++) {
        aliceFS.incrementMessageCount();
      }
      
      // Wait for rotation to trigger
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Verify key rotation occurred
      expect(aliceFS.getStatus().currentKeyGeneration).toBe(1);
      
      // Verify rotation TLV was sent
      const rotationTLV = aliceSession.sentMessages.find(
        msg => msg.tlvs.some(tlv => tlv.type === FS_TLV_TYPES.KEY_ROTATION)
      );
      
      expect(rotationTLV).toBeDefined();
    });

    test('should handle cross-party key rotation', async () => {
      await aliceSession.startEncryption();
      await bobSession.startEncryption();
      
      // Alice initiates key rotation
      await aliceFS.rotateKeysManually();
      
      // Simulate Alice sending rotation TLV to Bob
      const aliceRotationTLV = aliceSession.sentMessages.find(
        msg => msg.tlvs.some(tlv => tlv.type === FS_TLV_TYPES.KEY_ROTATION)
      )?.tlvs.find(tlv => tlv.type === FS_TLV_TYPES.KEY_ROTATION);
      
      if (aliceRotationTLV) {
        await bobSession.receiveTLV(aliceRotationTLV);
        
        // Wait for Bob to process and respond
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Verify Bob sent acknowledgment
        const bobAckTLV = bobSession.sentMessages.find(
          msg => msg.tlvs.some(tlv => tlv.type === FS_TLV_TYPES.KEY_ROTATION_ACK)
        );
        
        expect(bobAckTLV).toBeDefined();
      }
    });

    test('should perform emergency rotation', async () => {
      await aliceSession.startEncryption();
      await bobSession.startEncryption();
      
      const initialGeneration = aliceFS.getStatus().currentKeyGeneration;
      
      // Trigger emergency rotation
      await aliceFS.emergencyRotation('SECURITY_BREACH');
      
      // Verify immediate rotation
      expect(aliceFS.getStatus().currentKeyGeneration).toBe(initialGeneration + 1);
      
      // Verify emergency TLV was sent
      const emergencyTLV = aliceSession.sentMessages.find(
        msg => msg.tlvs.some(tlv => tlv.type === FS_TLV_TYPES.EMERGENCY_ROTATION)
      );
      
      expect(emergencyTLV).toBeDefined();
    });
  });

  describe('Cryptographic Operations', () => {
    test('should generate cryptographically secure keys', async () => {
      const keyRotationEngine = new KeyRotationEngine();
      await keyRotationEngine.initialize();
      
      const keySet1 = await keyRotationEngine.generateKeySet(1);
      const keySet2 = await keyRotationEngine.generateKeySet(2);
      
      // Verify keys are different
      expect(keySet1.encryptionKey).not.toEqual(keySet2.encryptionKey);
      expect(keySet1.macKey).not.toEqual(keySet2.macKey);
      
      // Verify key properties
      expect(keySet1.encryptionKey.length).toBe(32);
      expect(keySet1.macKey.length).toBe(32);
      expect(keySet1.keyFingerprint).toBeDefined();
      expect(keySet1.generation).toBe(1);
      
      await keyRotationEngine.shutdown();
    });

    test('should perform secure key deletion', async () => {
      const deletionManager = new SecureDeletionManager();
      await deletionManager.initialize();
      
      // Create test key material
      const testKeys = {
        encryptionKey: new Uint8Array(32).fill(0x42),
        macKey: new Uint8Array(32).fill(0x33),
        generation: 1
      };
      
      // Perform secure deletion
      const deletionResult = await deletionManager.deleteKeys({
        keyGeneration: 1,
        keys: testKeys
      });
      
      expect(deletionResult.verified).toBe(true);
      expect(deletionResult.deletionProof).toBeDefined();
      
      // Verify keys are zeroed
      expect(testKeys.encryptionKey.every(byte => byte === 0)).toBe(true);
      expect(testKeys.macKey.every(byte => byte === 0)).toBe(true);
      
      await deletionManager.shutdown();
    });

    test('should generate and verify zero-knowledge proofs', async () => {
      const zkVerifier = new ZeroKnowledgeVerifier();
      await zkVerifier.initialize();
      
      const oldKeys = {
        generation: 1,
        keyFingerprint: 'old-fingerprint-123',
        encryptionKey: new Uint8Array(32).fill(0x11)
      };
      
      const newKeys = {
        generation: 2,
        keyFingerprint: 'new-fingerprint-456',
        encryptionKey: new Uint8Array(32).fill(0x22)
      };
      
      // Generate proof
      const proof = await zkVerifier.generateRotationProof({
        oldKeyGeneration: 1,
        newKeyGeneration: 2,
        rotationData: { oldKeys, newKeys }
      });
      
      expect(proof).toBeDefined();
      expect(proof.proofHash).toBeDefined();
      
      // Verify proof
      const isValid = await zkVerifier.verifyRotationProof(proof);
      expect(isValid).toBe(true);
      
      await zkVerifier.shutdown();
    });
  });

  describe('Performance Validation', () => {
    test('should meet rotation time requirements', async () => {
      const startTime = performance.now();
      
      await aliceFS.rotateKeysManually();
      
      const rotationTime = performance.now() - startTime;
      
      // Should complete within 100ms target
      expect(rotationTime).toBeLessThan(100);
      
      // Verify rotation was successful
      expect(aliceFS.getStatus().currentKeyGeneration).toBe(1);
    });

    test('should handle concurrent operations', async () => {
      await aliceSession.startEncryption();
      
      // Start multiple operations concurrently
      const operations = [
        aliceFS.incrementMessageCount(),
        aliceFS.incrementDataVolume(100),
        aliceFS.getStatus(),
        aliceFS.validateKeyIntegrity()
      ];
      
      // All operations should complete without errors
      const results = await Promise.all(operations);
      expect(results).toHaveLength(4);
    });

    test('should maintain performance under load', async () => {
      const iterations = 50;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        aliceFS.incrementMessageCount();
        const end = performance.now();
        times.push(end - start);
      }
      
      const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);
      
      // Operations should be consistently fast
      expect(averageTime).toBeLessThan(1); // < 1ms average
      expect(maxTime).toBeLessThan(10); // < 10ms maximum
    });
  });

  describe('Error Handling and Recovery', () => {
    test('should handle initialization failures gracefully', async () => {
      const faultyFS = new ForwardSecrecyManager({
        rotationInterval: -1 // Invalid configuration
      });
      
      // Should handle invalid configuration
      await expect(faultyFS.initialize()).rejects.toThrow();
    });

    test('should recover from rotation failures', async () => {
      await aliceFS.initialize();
      
      // Simulate rotation failure by corrupting state
      const originalRotationEngine = aliceFS.keyRotationEngine;
      aliceFS.keyRotationEngine = null;
      
      // Attempt rotation should fail gracefully
      await expect(aliceFS.rotateKeysManually()).rejects.toThrow();
      
      // Restore and verify recovery
      aliceFS.keyRotationEngine = originalRotationEngine;
      await aliceFS.rotateKeysManually();
      
      expect(aliceFS.getStatus().currentKeyGeneration).toBe(1);
    });

    test('should handle network simulation errors', async () => {
      await aliceSession.startEncryption();
      
      // Simulate network failure
      const originalSendMethod = aliceSession.sendDataMessage;
      aliceSession.sendDataMessage = async () => {
        throw new Error('Network failure');
      };
      
      // Integration should handle send failures
      let errorCaught = false;
      aliceIntegration.on('error', () => {
        errorCaught = true;
      });
      
      try {
        await aliceFS.rotateKeysManually();
      } catch (error) {
        // Expected to fail
      }
      
      // Restore network
      aliceSession.sendDataMessage = originalSendMethod;
      
      // Should be able to recover
      await aliceFS.rotateKeysManually();
      expect(aliceFS.getStatus().currentKeyGeneration).toBeGreaterThan(0);
    });
  });

  describe('Security Validation', () => {
    test('should prevent key reuse', async () => {
      await aliceFS.initialize();
      
      const generation1Keys = aliceFS.getCurrentKeys();
      await aliceFS.rotateKeysManually();
      const generation2Keys = aliceFS.getCurrentKeys();
      
      // Keys should be completely different
      expect(generation1Keys.encryptionKey).not.toEqual(generation2Keys.encryptionKey);
      expect(generation1Keys.macKey).not.toEqual(generation2Keys.macKey);
      expect(generation1Keys.keyFingerprint).not.toEqual(generation2Keys.keyFingerprint);
    });

    test('should validate key integrity', async () => {
      await aliceFS.initialize();
      
      const integrityResult = await aliceFS.validateKeyIntegrity();
      expect(integrityResult.valid).toBe(true);
      expect(integrityResult.currentFingerprint).toBeDefined();
      expect(integrityResult.expectedFingerprint).toBeDefined();
      expect(integrityResult.currentFingerprint).toBe(integrityResult.expectedFingerprint);
    });

    test('should maintain audit trail integrity', async () => {
      await aliceFS.initialize();
      
      // Perform several operations
      await aliceFS.rotateKeysManually();
      await aliceFS.emergencyRotation('TEST');
      
      const auditTrail = aliceFS.getAuditTrail();
      expect(auditTrail.events.length).toBeGreaterThan(0);
      
      // Verify audit trail integrity
      const integrityCheck = await aliceFS.verifyAuditTrailIntegrity();
      expect(integrityCheck.valid).toBe(true);
    });
  });
});
