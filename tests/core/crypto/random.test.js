/**
 * Tests for secure random number generation
 */

import { getRandomValues, random, randomInt } from '../../../src/core/crypto/random';

describe('Secure Random Number Generation', () => {
  // Mock crypto.getRandomValues
  const originalCrypto = global.crypto;
  
  beforeEach(() => {
    // Create a mock implementation of crypto.getRandomValues
    global.crypto = {
      getRandomValues: jest.fn(array => {
        for (let i = 0; i < array.length; i++) {
          array[i] = i % 256; // Deterministic pattern for testing
        }
        return array;
      })
    };
  });
  
  afterEach(() => {
    // Restore original crypto
    global.crypto = originalCrypto;
  });
  
  // Test getRandomValues function
  describe('getRandomValues', () => {
    test('should fill array with random values', () => {
      const array = new Uint8Array(10);
      
      const result = getRandomValues(array);
      
      // Verify crypto.getRandomValues was called
      expect(global.crypto.getRandomValues).toHaveBeenCalledWith(array);
      
      // Verify array was filled
      for (let i = 0; i < array.length; i++) {
        expect(array[i]).toBe(i % 256);
      }
      
      // Verify result is the same array
      expect(result).toBe(array);
    });
    
    test('should throw error if no secure RNG is available', () => {
      // Remove crypto
      global.crypto = undefined;
      
      const array = new Uint8Array(10);
      
      expect(() => getRandomValues(array)).toThrow('No secure random number generator available');
    });
  });
  
  // Test random function
  describe('random', () => {
    test('should generate random byte array of specified length', () => {
      const length = 16;
      
      const result = random(length);
      
      // Verify result is a Uint8Array
      expect(result).toBeInstanceOf(Uint8Array);
      
      // Verify length is correct
      expect(result.length).toBe(length);
      
      // Verify crypto.getRandomValues was called
      expect(global.crypto.getRandomValues).toHaveBeenCalled();
    });
    
    test('should generate empty array for length 0', () => {
      const result = random(0);
      
      expect(result).toBeInstanceOf(Uint8Array);
      expect(result.length).toBe(0);
    });
    
    test('should throw error for negative length', () => {
      expect(() => random(-1)).toThrow();
    });
  });
  
  // Test randomInt function
  describe('randomInt', () => {
    test('should generate random integer within specified range', () => {
      const min = 5;
      const max = 10;
      
      // Call multiple times to test distribution
      const results = [];
      for (let i = 0; i < 100; i++) {
        results.push(randomInt(min, max));
      }
      
      // Verify all results are within range
      for (const result of results) {
        expect(result).toBeGreaterThanOrEqual(min);
        expect(result).toBeLessThanOrEqual(max);
        expect(Number.isInteger(result)).toBe(true);
      }
    });
    
    test('should handle min equal to max', () => {
      const value = 42;
      
      const result = randomInt(value, value);
      
      expect(result).toBe(value);
    });
    
    test('should throw error if min > max', () => {
      expect(() => randomInt(10, 5)).toThrow();
    });
    
    test('should handle large ranges', () => {
      const min = 0;
      const max = 1000000;
      
      const result = randomInt(min, max);
      
      expect(result).toBeGreaterThanOrEqual(min);
      expect(result).toBeLessThanOrEqual(max);
      expect(Number.isInteger(result)).toBe(true);
    });
    
    test('should handle negative ranges', () => {
      const min = -100;
      const max = -50;
      
      const result = randomInt(min, max);
      
      expect(result).toBeGreaterThanOrEqual(min);
      expect(result).toBeLessThanOrEqual(max);
      expect(Number.isInteger(result)).toBe(true);
    });
    
    test('should handle ranges crossing zero', () => {
      const min = -10;
      const max = 10;
      
      const result = randomInt(min, max);
      
      expect(result).toBeGreaterThanOrEqual(min);
      expect(result).toBeLessThanOrEqual(max);
      expect(Number.isInteger(result)).toBe(true);
    });
  });
  
  // Test randomness properties
  describe('Randomness Properties', () => {
    test('should generate different values on each call', () => {
      // Restore real crypto for this test
      global.crypto = originalCrypto;
      
      if (!global.crypto || !global.crypto.getRandomValues) {
        // Skip test if real crypto is not available
        return;
      }
      
      const length = 16;
      
      const result1 = random(length);
      const result2 = random(length);
      
      // Convert to strings for comparison
      const str1 = Array.from(result1).join(',');
      const str2 = Array.from(result2).join(',');
      
      // Verify results are different
      expect(str1).not.toBe(str2);
    });
  });
});
