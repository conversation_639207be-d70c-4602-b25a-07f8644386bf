/**
 * Forward Secrecy and Key Management Tests
 * Based on libOTR/coyim key rotation and security patterns
 */

import { OtrSession } from '../../../src/core/session';
import { generateDHKeyPair, dhExchange } from '../../../src/core/crypto/dh';
import { deriveKeys } from '../../../src/core/crypto/keys';

describe('Forward Secrecy and Key Management Tests', () => {
  let aliceSession, bobSession;
  let aliceMessages = [];
  let bobMessages = [];

  const aliceSendMessage = jest.fn(message => bobMessages.push(message));
  const bobSendMessage = jest.fn(message => aliceMessages.push(message));

  beforeEach(async () => {
    aliceMessages = [];
    bobMessages = [];

    aliceSession = new OtrSession('bob', {
      sendMessage: aliceSendMessage,
      testing: true
    });

    bobSession = new OtrSession('alice', {
      sendMessage: bobSendMessage,
      testing: true
    });

    await Promise.all([
      aliceSession.init(),
      bobSession.init()
    ]);

    // Establish encrypted session
    await aliceSession.startOtr();
    await bobSession.startOtr();
  });

  afterEach(() => {
    if (aliceSession) aliceSession.destroy();
    if (bobSession) bobSession.destroy();
  });

  describe('Key Rotation', () => {
    test('should rotate keys after message threshold', async () => {
      // Get initial key IDs
      const initialAliceKeyId = aliceSession.keys?.ourKeyID || 0;
      const initialBobKeyId = bobSession.keys?.ourKeyID || 0;

      // Send multiple messages to trigger key rotation
      const messages = [];
      for (let i = 0; i < 10; i++) {
        const message = `Message ${i}`;
        const encrypted = await aliceSession.encryptMessage(message);
        const decrypted = await bobSession.decryptMessage(encrypted);
        messages.push({ encrypted, decrypted });
        expect(decrypted).toBe(message);
      }

      // Keys should have rotated (implementation dependent)
      // At minimum, verify messages still work after potential rotation
      const finalMessage = await aliceSession.encryptMessage('Final message');
      const finalDecrypted = await bobSession.decryptMessage(finalMessage);
      expect(finalDecrypted).toBe('Final message');
    });

    test('should maintain separate key pairs for each direction', async () => {
      // Alice -> Bob
      const aliceToBob = await aliceSession.encryptMessage('Alice to Bob');
      const decrypted1 = await bobSession.decryptMessage(aliceToBob);
      expect(decrypted1).toBe('Alice to Bob');

      // Bob -> Alice
      const bobToAlice = await bobSession.encryptMessage('Bob to Alice');
      const decrypted2 = await aliceSession.decryptMessage(bobToAlice);
      expect(decrypted2).toBe('Bob to Alice');

      // Both directions should work independently
      expect(decrypted1).toBe('Alice to Bob');
      expect(decrypted2).toBe('Bob to Alice');
    });

    test('should handle key rotation during active conversation', async () => {
      // Send messages before rotation
      const message1 = await aliceSession.encryptMessage('Before rotation');
      const decrypted1 = await bobSession.decryptMessage(message1);
      expect(decrypted1).toBe('Before rotation');

      // Force key rotation (if supported)
      if (aliceSession.rotateKeys) {
        await aliceSession.rotateKeys();
        await bobSession.rotateKeys();
      }

      // Send messages after rotation
      const message2 = await aliceSession.encryptMessage('After rotation');
      const decrypted2 = await bobSession.decryptMessage(message2);
      expect(decrypted2).toBe('After rotation');
    });
  });

  describe('Forward Secrecy Properties', () => {
    test('should not decrypt old messages after key rotation', async () => {
      // Send and store a message
      const oldMessage = await aliceSession.encryptMessage('Old message');
      const decrypted = await bobSession.decryptMessage(oldMessage);
      expect(decrypted).toBe('Old message');

      // Force key rotation and send new messages
      for (let i = 0; i < 5; i++) {
        const newMsg = await aliceSession.encryptMessage(`New message ${i}`);
        await bobSession.decryptMessage(newMsg);
      }

      // Old message should not be decryptable with new keys
      // (This test depends on implementation - some may cache old keys)
      if (bobSession.clearOldKeys) {
        await bobSession.clearOldKeys();
        
        await expect(
          bobSession.decryptMessage(oldMessage)
        ).rejects.toThrow();
      }
    });

    test('should securely delete old key material', async () => {
      const message = await aliceSession.encryptMessage('Test message');
      await bobSession.decryptMessage(message);

      // Verify that old keys are cleared (implementation dependent)
      if (aliceSession.getOldKeys) {
        const oldKeys = aliceSession.getOldKeys();
        expect(oldKeys.length).toBe(0);
      }
    });

    test('should generate unique session keys', async () => {
      // Create multiple sessions and verify they have different keys
      const session1 = new OtrSession('test1', { sendMessage: jest.fn(), testing: true });
      const session2 = new OtrSession('test2', { sendMessage: jest.fn(), testing: true });

      await session1.init();
      await session2.init();

      await session1.startOtr();
      await session2.startOtr();

      // Sessions should have different key material
      if (session1.getSessionId && session2.getSessionId) {
        expect(session1.getSessionId()).not.toBe(session2.getSessionId());
      }

      session1.destroy();
      session2.destroy();
    });
  });

  describe('Key Derivation Security', () => {
    test('should derive keys from shared secret correctly', async () => {
      // Generate DH key pairs
      const aliceKeyPair = await generateDHKeyPair();
      const bobKeyPair = await generateDHKeyPair();

      // Compute shared secrets
      const aliceSharedSecret = await dhExchange(aliceKeyPair.privateKey, bobKeyPair.publicKey);
      const bobSharedSecret = await dhExchange(bobKeyPair.privateKey, aliceKeyPair.publicKey);

      // Shared secrets should be identical
      expect(Array.from(aliceSharedSecret)).toEqual(Array.from(bobSharedSecret));

      // Derive keys from shared secret
      const aliceKeys = await deriveKeys(aliceSharedSecret);
      const bobKeys = await deriveKeys(bobSharedSecret);

      // Derived keys should be identical
      expect(Array.from(aliceKeys.sendingAESKey)).toEqual(Array.from(bobKeys.sendingAESKey));
      expect(Array.from(aliceKeys.receivingAESKey)).toEqual(Array.from(bobKeys.receivingAESKey));
      expect(Array.from(aliceKeys.sendingMACKey)).toEqual(Array.from(bobKeys.sendingMACKey));
      expect(Array.from(aliceKeys.receivingMACKey)).toEqual(Array.from(bobKeys.receivingMACKey));
    });

    test('should produce different keys for different shared secrets', async () => {
      const secret1 = new Uint8Array(32);
      const secret2 = new Uint8Array(32);
      
      // Fill with different values
      secret1.fill(1);
      secret2.fill(2);

      const keys1 = await deriveKeys(secret1);
      const keys2 = await deriveKeys(secret2);

      // Keys should be different
      expect(Array.from(keys1.sendingAESKey)).not.toEqual(Array.from(keys2.sendingAESKey));
      expect(Array.from(keys1.receivingAESKey)).not.toEqual(Array.from(keys2.receivingAESKey));
      expect(Array.from(keys1.sendingMACKey)).not.toEqual(Array.from(keys2.sendingMACKey));
      expect(Array.from(keys1.receivingMACKey)).not.toEqual(Array.from(keys2.receivingMACKey));
    });

    test('should handle key derivation edge cases', async () => {
      // Test with all-zero secret
      const zeroSecret = new Uint8Array(32);
      const zeroKeys = await deriveKeys(zeroSecret);
      
      expect(zeroKeys.sendingAESKey).toBeDefined();
      expect(zeroKeys.receivingAESKey).toBeDefined();
      expect(zeroKeys.sendingMACKey).toBeDefined();
      expect(zeroKeys.receivingMACKey).toBeDefined();

      // Test with all-ones secret
      const onesSecret = new Uint8Array(32);
      onesSecret.fill(255);
      const onesKeys = await deriveKeys(onesSecret);
      
      expect(onesKeys.sendingAESKey).toBeDefined();
      expect(onesKeys.receivingAESKey).toBeDefined();
      expect(onesKeys.sendingMACKey).toBeDefined();
      expect(onesKeys.receivingMACKey).toBeDefined();

      // Should be different from zero keys
      expect(Array.from(zeroKeys.sendingAESKey)).not.toEqual(Array.from(onesKeys.sendingAESKey));
    });
  });

  describe('Memory Security', () => {
    test('should clear sensitive key material on session end', async () => {
      // Send a message to establish keys
      const message = await aliceSession.encryptMessage('Test message');
      await bobSession.decryptMessage(message);

      // End sessions
      await aliceSession.endSession();
      await bobSession.endSession();

      // Verify sessions are cleaned up
      expect(aliceSession.state.getState()).toBe(0); // PLAINTEXT
      expect(bobSession.state.getState()).toBe(0);

      // Should not be able to encrypt after ending
      await expect(
        aliceSession.encryptMessage('After end')
      ).rejects.toThrow();
    });

    test('should overwrite key material in memory', async () => {
      // This test verifies that sensitive data is properly cleared
      // Implementation dependent - may require specific memory clearing functions
      
      const message = await aliceSession.encryptMessage('Sensitive message');
      await bobSession.decryptMessage(message);

      // If the implementation provides memory clearing functions, test them
      if (aliceSession.clearSensitiveData) {
        await aliceSession.clearSensitiveData();
      }

      if (bobSession.clearSensitiveData) {
        await bobSession.clearSensitiveData();
      }

      // Verify cleanup was successful (implementation dependent)
      expect(true).toBe(true); // Placeholder - actual test depends on implementation
    });
  });

  describe('Key Exchange Security', () => {
    test('should validate DH public keys', async () => {
      // Test with invalid DH public key (should be rejected)
      const invalidKey = new Uint8Array(192); // All zeros
      
      await expect(
        dhExchange(new Uint8Array(192), invalidKey)
      ).rejects.toThrow();
    });

    test('should handle DH key exchange edge cases', async () => {
      const keyPair1 = await generateDHKeyPair();
      const keyPair2 = await generateDHKeyPair();

      // Normal case should work
      const sharedSecret = await dhExchange(keyPair1.privateKey, keyPair2.publicKey);
      expect(sharedSecret).toBeDefined();
      expect(sharedSecret.length).toBeGreaterThan(0);

      // Test with same key pair (should still work but not recommended)
      const selfSharedSecret = await dhExchange(keyPair1.privateKey, keyPair1.publicKey);
      expect(selfSharedSecret).toBeDefined();
    });

    test('should generate cryptographically strong DH keys', async () => {
      const keyPair = await generateDHKeyPair();
      
      // Keys should be proper length
      expect(keyPair.privateKey.length).toBeGreaterThan(0);
      expect(keyPair.publicKey.length).toBeGreaterThan(0);

      // Keys should not be all zeros or ones
      const privateKeySum = Array.from(keyPair.privateKey).reduce((a, b) => a + b, 0);
      const publicKeySum = Array.from(keyPair.publicKey).reduce((a, b) => a + b, 0);
      
      expect(privateKeySum).toBeGreaterThan(0);
      expect(publicKeySum).toBeGreaterThan(0);
      expect(privateKeySum).toBeLessThan(keyPair.privateKey.length * 255);
      expect(publicKeySum).toBeLessThan(keyPair.publicKey.length * 255);
    });
  });
});
