/**
 * Tests for key generation and management
 */

import { 
  generateKeys, 
  deriveKeys, 
  generateInstanceTag 
} from '../../../src/core/crypto/keys';

// Mock dependencies
jest.mock('../../../src/core/crypto/random', () => ({
  random: jest.fn(length => {
    const array = new Uint8Array(length);
    for (let i = 0; i < length; i++) {
      array[i] = i % 256;
    }
    return array;
  })
}));

jest.mock('../../../src/core/crypto/hmac', () => ({
  hmacSha256: jest.fn(async (data, key) => {
    // Simple mock that returns a deterministic value based on input length
    const result = new Uint8Array(32);
    for (let i = 0; i < 32; i++) {
      result[i] = (data.length + key.length + i) % 256;
    }
    return result;
  })
}));

jest.mock('../../../src/core/crypto/dh', () => ({
  generateDHKeyPair: jest.fn(async () => ({
    privateKey: 'mock-private-key',
    publicKey: 'mock-public-key'
  }))
}));

describe('Key Generation and Management', () => {
  // Test generateKeys function
  describe('generateKeys', () => {
    test('should generate DH and DSA key pairs', async () => {
      const keys = await generateKeys();
      
      // Verify structure
      expect(keys).toBeDefined();
      expect(keys.dh).toBeDefined();
      expect(keys.dsa).toBeDefined();
      
      // Verify DH keys
      expect(keys.dh.privateKey).toBe('mock-private-key');
      expect(keys.dh.publicKey).toBe('mock-public-key');
      
      // Verify DSA keys
      expect(keys.dsa.privateKey).toBeDefined();
      expect(keys.dsa.publicKey).toBeDefined();
    });
  });
  
  // Test deriveKeys function
  describe('deriveKeys', () => {
    test('should derive session keys from shared secret', async () => {
      const secret = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8]);
      
      const keys = await deriveKeys(secret);
      
      // Verify structure
      expect(keys).toBeDefined();
      expect(keys.sessionId).toBeDefined();
      expect(keys.sendingAESKey).toBeDefined();
      expect(keys.receivingAESKey).toBeDefined();
      expect(keys.sendingMACKey).toBeDefined();
      expect(keys.receivingMACKey).toBeDefined();
      
      // Verify sessionId is half of the secret
      expect(keys.sessionId.length).toBe(secret.length / 2);
      expect(Array.from(keys.sessionId)).toEqual([1, 2, 3, 4]);
      
      // Verify AES keys are 16 bytes (128 bits)
      expect(keys.sendingAESKey.length).toBe(16);
      expect(keys.receivingAESKey.length).toBe(16);
      
      // Verify MAC keys are 32 bytes (256 bits)
      expect(keys.sendingMACKey.length).toBe(32);
      expect(keys.receivingMACKey.length).toBe(32);
    });
    
    test('should derive different keys for different secrets', async () => {
      const secret1 = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8]);
      const secret2 = new Uint8Array([8, 7, 6, 5, 4, 3, 2, 1]);
      
      const keys1 = await deriveKeys(secret1);
      const keys2 = await deriveKeys(secret2);
      
      // Convert to strings for comparison
      const sendingAES1 = Array.from(keys1.sendingAESKey).join(',');
      const sendingAES2 = Array.from(keys2.sendingAESKey).join(',');
      
      // Verify keys are different
      expect(sendingAES1).not.toBe(sendingAES2);
    });
    
    test('should handle empty secret', async () => {
      const secret = new Uint8Array(0);
      
      await expect(deriveKeys(secret)).rejects.toThrow();
    });
  });
  
  // Test generateInstanceTag function
  describe('generateInstanceTag', () => {
    test('should generate a valid instance tag', () => {
      const tag = generateInstanceTag();
      
      // Verify tag is a number
      expect(typeof tag).toBe('number');
      
      // Verify tag is a 32-bit integer
      expect(tag).toBeLessThanOrEqual(0xFFFFFFFF);
      expect(tag).toBeGreaterThanOrEqual(0);
      
      // Verify tag is > 0x00000100 (as per OTRv3 spec)
      expect(tag).toBeGreaterThan(0x00000100);
    });
    
    test('should generate different tags on each call', () => {
      // Unmock random for this test
      jest.unmock('../../../src/core/crypto/random');
      
      const tag1 = generateInstanceTag();
      const tag2 = generateInstanceTag();
      
      // Verify tags are different
      expect(tag1).not.toBe(tag2);
      
      // Restore mock
      jest.mock('../../../src/core/crypto/random');
    });
  });
  
  // Test key derivation internals
  describe('Key Derivation Internals', () => {
    test('should derive AES keys with different types', async () => {
      const secret = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8]);
      
      const keys = await deriveKeys(secret);
      
      // Convert to strings for comparison
      const sendingAES = Array.from(keys.sendingAESKey).join(',');
      const receivingAES = Array.from(keys.receivingAESKey).join(',');
      
      // Verify sending and receiving keys are different
      expect(sendingAES).not.toBe(receivingAES);
    });
    
    test('should derive MAC keys from AES keys', async () => {
      const secret = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8]);
      
      const keys = await deriveKeys(secret);
      
      // Verify MAC keys are derived from AES keys
      expect(keys.sendingMACKey).toBeDefined();
      expect(keys.receivingMACKey).toBeDefined();
      
      // Convert to strings for comparison
      const sendingMAC = Array.from(keys.sendingMACKey).join(',');
      const receivingMAC = Array.from(keys.receivingMACKey).join(',');
      
      // Verify sending and receiving MAC keys are different
      expect(sendingMAC).not.toBe(receivingMAC);
    });
  });
});
