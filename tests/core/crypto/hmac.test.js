/**
 * Tests for HMAC (Hash-based Message Authentication Code) implementation
 */

import { 
  generateHMAC, 
  verifyHMAC 
} from '../../../src/core/crypto/hmac';

describe('HMAC Authentication', () => {
  // Test HMAC generation
  describe('generateHMAC', () => {
    test('should generate a valid HMAC for a message', async () => {
      const message = 'This is a test message';
      const key = 'test-key-12345678';
      
      const hmac = await generateHMAC(message, key);
      
      expect(hmac).toBeDefined();
      expect(typeof hmac).toBe('string');
      expect(hmac.length).toBeGreaterThan(0);
    });
    
    test('should generate the same HMAC for the same message and key', async () => {
      const message = 'This is a test message';
      const key = 'test-key-12345678';
      
      const hmac1 = await generateHMAC(message, key);
      const hmac2 = await generateHMAC(message, key);
      
      expect(hmac1).toBe(hmac2);
    });
    
    test('should generate different HMACs for different messages', async () => {
      const message1 = 'This is message 1';
      const message2 = 'This is message 2';
      const key = 'test-key-12345678';
      
      const hmac1 = await generateHMAC(message1, key);
      const hmac2 = await generateHMAC(message2, key);
      
      expect(hmac1).not.toBe(hmac2);
    });
    
    test('should generate different HMACs for different keys', async () => {
      const message = 'This is a test message';
      const key1 = 'test-key-1';
      const key2 = 'test-key-2';
      
      const hmac1 = await generateHMAC(message, key1);
      const hmac2 = await generateHMAC(message, key2);
      
      expect(hmac1).not.toBe(hmac2);
    });
    
    test('should handle empty message', async () => {
      const message = '';
      const key = 'test-key-12345678';
      
      const hmac = await generateHMAC(message, key);
      
      expect(hmac).toBeDefined();
      expect(typeof hmac).toBe('string');
      expect(hmac.length).toBeGreaterThan(0);
    });
    
    test('should throw error with invalid key', async () => {
      const message = 'This is a test message';
      const key = null;
      
      await expect(generateHMAC(message, key)).rejects.toThrow();
    });
  });
  
  // Test HMAC verification
  describe('verifyHMAC', () => {
    test('should verify a valid HMAC', async () => {
      const message = 'This is a test message';
      const key = 'test-key-12345678';
      
      const hmac = await generateHMAC(message, key);
      const isValid = await verifyHMAC(message, hmac, key);
      
      expect(isValid).toBe(true);
    });
    
    test('should reject an invalid HMAC', async () => {
      const message = 'This is a test message';
      const key = 'test-key-12345678';
      const invalidHmac = 'invalid-hmac-value';
      
      const isValid = await verifyHMAC(message, invalidHmac, key);
      
      expect(isValid).toBe(false);
    });
    
    test('should reject if message is tampered', async () => {
      const originalMessage = 'This is the original message';
      const tamperedMessage = 'This is the tampered message';
      const key = 'test-key-12345678';
      
      const hmac = await generateHMAC(originalMessage, key);
      const isValid = await verifyHMAC(tamperedMessage, hmac, key);
      
      expect(isValid).toBe(false);
    });
    
    test('should reject if key is incorrect', async () => {
      const message = 'This is a test message';
      const correctKey = 'correct-key';
      const wrongKey = 'wrong-key';
      
      const hmac = await generateHMAC(message, correctKey);
      const isValid = await verifyHMAC(message, hmac, wrongKey);
      
      expect(isValid).toBe(false);
    });
    
    test('should throw error with invalid inputs', async () => {
      const message = 'This is a test message';
      const key = 'test-key-12345678';
      const hmac = await generateHMAC(message, key);
      
      // Test with null message
      await expect(verifyHMAC(null, hmac, key)).rejects.toThrow();
      
      // Test with null HMAC
      await expect(verifyHMAC(message, null, key)).rejects.toThrow();
      
      // Test with null key
      await expect(verifyHMAC(message, hmac, null)).rejects.toThrow();
    });
  });
  
  // Test with various data types
  describe('Data Type Handling', () => {
    test('should handle Unicode characters', async () => {
      const message = 'こんにちは世界 😀🔒💻 Привет мир';
      const key = 'test-key-12345678';
      
      const hmac = await generateHMAC(message, key);
      const isValid = await verifyHMAC(message, hmac, key);
      
      expect(isValid).toBe(true);
    });
    
    test('should handle long text', async () => {
      const message = 'a'.repeat(10000);
      const key = 'test-key-12345678';
      
      const hmac = await generateHMAC(message, key);
      const isValid = await verifyHMAC(message, hmac, key);
      
      expect(isValid).toBe(true);
    });
    
    test('should handle special characters', async () => {
      const message = '!"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~';
      const key = 'test-key-12345678';
      
      const hmac = await generateHMAC(message, key);
      const isValid = await verifyHMAC(message, hmac, key);
      
      expect(isValid).toBe(true);
    });
  });
  
  // Test security properties
  describe('Security Properties', () => {
    test('should detect single bit changes in message', async () => {
      const message = 'This is a test message';
      const key = 'test-key-12345678';
      
      // Generate HMAC for original message
      const hmac = await generateHMAC(message, key);
      
      // Change a single character in the message
      const alteredMessage = message.substring(0, 5) + 'X' + message.substring(6);
      
      // Verify HMAC fails for altered message
      const isValid = await verifyHMAC(alteredMessage, hmac, key);
      expect(isValid).toBe(false);
    });
    
    test('should detect single bit changes in key', async () => {
      const message = 'This is a test message';
      const key = 'test-key-12345678';
      
      // Generate HMAC with original key
      const hmac = await generateHMAC(message, key);
      
      // Change a single character in the key
      const alteredKey = key.substring(0, 5) + 'X' + key.substring(6);
      
      // Verify HMAC fails with altered key
      const isValid = await verifyHMAC(message, hmac, alteredKey);
      expect(isValid).toBe(false);
    });
  });
});
