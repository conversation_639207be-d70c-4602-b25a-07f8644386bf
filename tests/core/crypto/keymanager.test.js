/**
 * Tests for the KeyManager and KeyStore
 */
import KeyManager from '../../../src/core/crypto/keymanager';
import KeyStore from '../../../src/core/crypto/keystore';

// Mock IndexedDB
const mockIndexedDB = {
  open: jest.fn(),
};

const mockObjectStore = {
  put: jest.fn(),
  get: jest.fn(),
  delete: jest.fn(),
  getAll: jest.fn(),
};

const mockTransaction = {
  objectStore: jest.fn().mockReturnValue(mockObjectStore),
};

const mockDB = {
  transaction: jest.fn().mockReturnValue(mockTransaction),
  close: jest.fn(),
  objectStoreNames: {
    contains: jest.fn().mockReturnValue(true),
  },
  createObjectStore: jest.fn(),
};

// Mock for crypto.subtle
const mockCryptoSubtle = {
  encrypt: jest.fn(),
  decrypt: jest.fn(),
  importKey: jest.fn(),
  deriveKey: jest.fn(),
};

// Mock for window.crypto
const mockCrypto = {
  subtle: mockCryptoSubtle,
  getRandomValues: jest.fn(arr => arr), // Return the array as is
};

// Setup mocks before tests
beforeAll(() => {
  // Setup IndexedDB mock
  global.indexedDB = mockIndexedDB;
  
  // Setup crypto mock
  global.window = {
    crypto: mockCrypto,
  };
  
  // Mock TextEncoder/TextDecoder
  global.TextEncoder = jest.fn().mockImplementation(() => ({
    encode: jest.fn(text => new Uint8Array([...text].map(c => c.charCodeAt(0)))),
  }));
  
  global.TextDecoder = jest.fn().mockImplementation(() => ({
    decode: jest.fn(bytes => String.fromCharCode(...bytes)),
  }));
});

// Reset mocks between tests
beforeEach(() => {
  jest.clearAllMocks();
});

describe('KeyStore', () => {
  let keyStore;
  
  beforeEach(() => {
    keyStore = new KeyStore();
    
    // Setup mock for indexedDB.open
    const mockRequest = {
      onerror: null,
      onsuccess: null,
      onupgradeneeded: null,
      result: mockDB,
    };
    
    mockIndexedDB.open.mockImplementation(() => {
      setTimeout(() => {
        mockRequest.onsuccess({ target: mockRequest });
      }, 0);
      return mockRequest;
    });
  });
  
  test('initialize creates database connection', async () => {
    await keyStore.initialize();
    
    expect(mockIndexedDB.open).toHaveBeenCalledWith('webOTR_keystore', 1);
    expect(keyStore.initialized).toBe(true);
    expect(keyStore.db).toBe(mockDB);
  });
  
  test('storeKeyPair stores key pair and metadata', async () => {
    // Setup
    keyStore.initialized = true;
    keyStore.db = mockDB;
    
    // Mock for store operations
    mockObjectStore.put.mockImplementation((item) => {
      const request = { onsuccess: null };
      setTimeout(() => {
        request.onsuccess();
      }, 0);
      return request;
    });
    
    // Test
    const keyPair = { public: 'public-key', private: 'private-key' };
    const metadata = { algo: 'RSA' };
    
    await keyStore.storeKeyPair('test-key', keyPair, metadata);
    
    // Verify
    expect(mockDB.transaction).toHaveBeenCalledWith(['keys'], 'readwrite');
    expect(mockDB.transaction).toHaveBeenCalledWith(['metadata'], 'readwrite');
    expect(mockObjectStore.put).toHaveBeenCalledTimes(2);
  });
  
  test('getKeyPair retrieves key pair', async () => {
    // Setup
    keyStore.initialized = true;
    keyStore.db = mockDB;
    
    const mockKeyData = {
      id: 'test-key',
      data: { public: 'public-key', private: 'private-key' },
    };
    
    // Mock for get operation
    mockObjectStore.get.mockImplementation(() => {
      const request = { 
        onsuccess: null,
        result: mockKeyData,
      };
      setTimeout(() => {
        request.onsuccess();
      }, 0);
      return request;
    });
    
    // Test
    const result = await keyStore.getKeyPair('test-key');
    
    // Verify
    expect(mockDB.transaction).toHaveBeenCalledWith(['keys'], 'readonly');
    expect(mockObjectStore.get).toHaveBeenCalledWith('test-key');
    expect(result).toEqual(mockKeyData.data);
  });
  
  test('listKeyPairs returns all metadata', async () => {
    // Setup
    keyStore.initialized = true;
    keyStore.db = mockDB;
    
    const mockMetadata = [
      { id: 'key1', algo: 'RSA' },
      { id: 'key2', algo: 'DSA' },
    ];
    
    // Mock for getAll operation
    mockObjectStore.getAll.mockImplementation(() => {
      const request = { 
        onsuccess: null,
        result: mockMetadata,
      };
      setTimeout(() => {
        request.onsuccess();
      }, 0);
      return request;
    });
    
    // Test
    const result = await keyStore.listKeyPairs();
    
    // Verify
    expect(mockDB.transaction).toHaveBeenCalledWith(['metadata'], 'readonly');
    expect(mockObjectStore.getAll).toHaveBeenCalled();
    expect(result).toEqual(mockMetadata);
  });
  
  test('deleteKeyPair removes key and metadata', async () => {
    // Setup
    keyStore.initialized = true;
    keyStore.db = mockDB;
    
    // Mock for delete operation
    mockObjectStore.delete.mockImplementation(() => {
      const request = { onsuccess: null };
      setTimeout(() => {
        request.onsuccess();
      }, 0);
      return request;
    });
    
    // Test
    await keyStore.deleteKeyPair('test-key');
    
    // Verify
    expect(mockDB.transaction).toHaveBeenCalledWith(['keys'], 'readwrite');
    expect(mockDB.transaction).toHaveBeenCalledWith(['metadata'], 'readwrite');
    expect(mockObjectStore.delete).toHaveBeenCalledTimes(2);
    expect(mockObjectStore.delete).toHaveBeenCalledWith('test-key');
  });
  
  test('close closes the database connection', async () => {
    // Setup
    keyStore.initialized = true;
    keyStore.db = mockDB;
    
    // Test
    await keyStore.close();
    
    // Verify
    expect(mockDB.close).toHaveBeenCalled();
    expect(keyStore.initialized).toBe(false);
    expect(keyStore.db).toBe(null);
  });
});

describe('KeyManager', () => {
  let keyManager;
  
  beforeEach(() => {
    keyManager = new KeyManager();
    
    // Mock KeyStore methods
    keyManager.keyStore.initialize = jest.fn().mockResolvedValue();
    keyManager.keyStore.storeKeyPair = jest.fn().mockResolvedValue();
    keyManager.keyStore.getKeyPair = jest.fn().mockResolvedValue({
      dh: { publicKey: 'mock-dh-public', privateKey: 'mock-dh-private' },
      dsa: { publicKey: 'mock-dsa-public', privateKey: 'mock-dsa-private' },
    });
    keyManager.keyStore.listKeyPairs = jest.fn().mockResolvedValue([]);
    keyManager.keyStore.updateMetadata = jest.fn().mockResolvedValue();
    keyManager.keyStore.deleteKeyPair = jest.fn().mockResolvedValue();
    keyManager.keyStore._getItem = jest.fn().mockResolvedValue(null);
    keyManager.keyStore._storeItem = jest.fn().mockResolvedValue();
    
    // Mock timer
    jest.useFakeTimers();
  });
  
  afterEach(() => {
    jest.useRealTimers();
  });
  
  test('initialize sets up key manager', async () => {
    // Override listKeyPairs to return empty array
    keyManager.keyStore.listKeyPairs.mockResolvedValue([]);
    
    // Mock rotateKey to avoid side effects
    keyManager.rotateKey = jest.fn().mockResolvedValue('new-key-id');
    
    // Test
    await keyManager.initialize();
    
    // Verify
    expect(keyManager.keyStore.initialize).toHaveBeenCalled();
    expect(keyManager.rotateKey).toHaveBeenCalled();
    expect(keyManager.initialized).toBe(true);
    expect(keyManager.expiryTime).toBe(30 * 24 * 60 * 60 * 1000); // 30 days
  });
  
  test('getActiveKeyPair returns active key pair', async () => {
    // Setup
    keyManager.initialized = true;
    keyManager.activeKeyId = 'active-key';
    
    // Test
    const result = await keyManager.getActiveKeyPair();
    
    // Verify
    expect(keyManager.keyStore.getKeyPair).toHaveBeenCalledWith('active-key');
    expect(result).toEqual({
      dh: { publicKey: 'mock-dh-public', privateKey: 'mock-dh-private' },
      dsa: { publicKey: 'mock-dsa-public', privateKey: 'mock-dsa-private' },
    });
  });
  
  test('rotateKey generates new key and updates active key', async () => {
    // Setup
    keyManager.initialized = true;
    keyManager.activeKeyId = 'old-key';
    
    // Mock Date.now to return consistent timestamp
    const mockTimestamp = 1617185200000; // 2021-03-31T12:00:00.000Z
    jest.spyOn(Date, 'now').mockReturnValue(mockTimestamp);
    
    // Test
    const result = await keyManager.rotateKey();
    
    // Verify
    expect(keyManager.keyStore.storeKeyPair).toHaveBeenCalledWith(
      `key-${mockTimestamp}`,
      expect.any(Object),
      {
        active: true,
        created: mockTimestamp,
        expires: mockTimestamp + keyManager.expiryTime,
      }
    );
    expect(keyManager.keyStore.updateMetadata).toHaveBeenCalledWith(
      'old-key',
      { active: false }
    );
    expect(keyManager.activeKeyId).toBe(`key-${mockTimestamp}`);
    expect(result).toBe(`key-${mockTimestamp}`);
  });
  
  test('cleanupExpiredKeys deletes expired keys', async () => {
    // Setup
    keyManager.initialized = true;
    keyManager.activeKeyId = 'active-key';
    
    const now = Date.now();
    const mockKeys = [
      { id: 'active-key', expires: now + 1000 },
      { id: 'expired-key-1', expires: now - 1000 },
      { id: 'expired-key-2', expires: now - 2000 },
      { id: 'valid-key', expires: now + 2000 },
    ];
    
    keyManager.keyStore.listKeyPairs.mockResolvedValue(mockKeys);
    
    // Test
    const result = await keyManager.cleanupExpiredKeys();
    
    // Verify
    expect(keyManager.keyStore.deleteKeyPair).toHaveBeenCalledTimes(2);
    expect(keyManager.keyStore.deleteKeyPair).toHaveBeenCalledWith('expired-key-1');
    expect(keyManager.keyStore.deleteKeyPair).toHaveBeenCalledWith('expired-key-2');
    expect(result).toBe(2); // 2 keys deleted
  });
  
  test('setExpiryTime updates expiry time and resets timer', async () => {
    // Setup
    keyManager.initialized = true;
    keyManager.activeKeyId = 'active-key';
    keyManager._resetRotationTimer = jest.fn();
    
    // Test
    keyManager.setExpiryTime(60000); // 1 minute
    
    // Verify
    expect(keyManager.expiryTime).toBe(60000);
    expect(keyManager.keyStore.updateMetadata).toHaveBeenCalledWith(
      'active-key',
      { expires: expect.any(Number) }
    );
    expect(keyManager._resetRotationTimer).toHaveBeenCalled();
  });
  
  test('_setupRotationTimer sets timer for key rotation', async () => {
    // Setup
    keyManager.rotateKey = jest.fn().mockResolvedValue();
    keyManager.cleanupExpiredKeys = jest.fn().mockResolvedValue();
    
    // Test
    keyManager._setupRotationTimer();
    
    // Fast-forward timer
    jest.runOnlyPendingTimers();
    
    // Verify
    expect(setTimeout).toHaveBeenCalledWith(expect.any(Function), keyManager.expiryTime);
    // Allow the async operation in the timeout to complete
    await new Promise(resolve => setImmediate(resolve));
    expect(keyManager.rotateKey).toHaveBeenCalled();
    expect(keyManager.cleanupExpiredKeys).toHaveBeenCalled();
  });
  
  test('_initializeActiveKey uses existing valid key', async () => {
    // Setup
    const now = Date.now();
    const mockKeys = [
      { id: 'key-1', active: true, expires: now + 1000 },
      { id: 'key-2', active: false, expires: now + 2000 },
    ];
    
    keyManager.keyStore.listKeyPairs.mockResolvedValue(mockKeys);
    keyManager.rotateKey = jest.fn();
    
    // Test
    await keyManager._initializeActiveKey();
    
    // Verify
    expect(keyManager.activeKeyId).toBe('key-1');
    expect(keyManager.rotateKey).not.toHaveBeenCalled();
  });
  
  test('_initializeActiveKey creates new key when no valid key exists', async () => {
    // Setup
    const now = Date.now();
    const mockKeys = [
      { id: 'key-1', active: true, expires: now - 1000 }, // Expired
      { id: 'key-2', active: false, expires: now + 2000 },
    ];
    
    keyManager.keyStore.listKeyPairs.mockResolvedValue(mockKeys);
    keyManager.rotateKey = jest.fn().mockResolvedValue('new-key');
    
    // Test
    await keyManager._initializeActiveKey();
    
    // Verify
    expect(keyManager.rotateKey).toHaveBeenCalled();
    expect(keyManager.activeKeyId).toBe('new-key');
  });
});

describe('Key Management Integration', () => {
  let keyManager;
  let keyStore;
  
  beforeEach(() => {
    // Create real instances for integration tests
    keyStore = new KeyStore('test_keystore');
    keyManager = new KeyManager();
    keyManager.keyStore = keyStore;
    
    // Mock timer
    jest.useFakeTimers();
  });
  
  afterEach(async () => {
    // Clean up
    await keyManager.close();
    await keyStore.close();
    jest.useRealTimers();
  });
  
  test('complete key lifecycle: initialization to rotation', async () => {
    // 1. Initialize the system
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 1000, // 1 second for testing
      autoRotate: true
    });
    
    // Verify initial state
    expect(keyManager.initialized).toBe(true);
    expect(keyManager.activeKeyId).toBeTruthy();
    
    // 2. Get initial key pair
    const initialKeyPair = await keyManager.getActiveKeyPair();
    expect(initialKeyPair).toBeTruthy();
    expect(initialKeyPair.dh).toBeTruthy();
    expect(initialKeyPair.dsa).toBeTruthy();
    
    // 3. List all keys (should be 1)
    const initialKeys = await keyManager.listKeyPairs();
    expect(initialKeys).toHaveLength(1);
    expect(initialKeys[0].id).toBe(keyManager.activeKeyId);
    
    // 4. Fast-forward time to trigger rotation
    jest.advanceTimersByTime(1000);
    
    // Allow async operations to complete
    await new Promise(resolve => setImmediate(resolve));
    
    // 5. Verify key rotation
    const newKeyPair = await keyManager.getActiveKeyPair();
    expect(newKeyPair).toBeTruthy();
    expect(newKeyPair.dh).toBeTruthy();
    expect(newKeyPair.dsa).toBeTruthy();
    
    // Verify old key is marked as inactive
    const allKeys = await keyManager.listKeyPairs();
    expect(allKeys).toHaveLength(2);
    const oldKey = allKeys.find(k => k.id === initialKeys[0].id);
    expect(oldKey.active).toBe(false);
  });
  
  test('key persistence across sessions', async () => {
    // 1. Initialize and create key
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    const initialKeyId = keyManager.activeKeyId;
    const initialKeyPair = await keyManager.getActiveKeyPair();
    
    // 2. Close and reopen
    await keyManager.close();
    await keyStore.close();
    
    // Create new instances
    const newKeyStore = new KeyStore('test_keystore');
    const newKeyManager = new KeyManager();
    newKeyManager.keyStore = newKeyStore;
    
    // 3. Reinitialize
    await newKeyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 4. Verify key persistence
    expect(newKeyManager.activeKeyId).toBe(initialKeyId);
    const persistedKeyPair = await newKeyManager.getActiveKeyPair();
    expect(persistedKeyPair).toEqual(initialKeyPair);
    
    // Clean up
    await newKeyManager.close();
    await newKeyStore.close();
  });
  
  test('concurrent key operations', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 2. Perform concurrent operations
    const operations = [
      keyManager.getActiveKeyPair(),
      keyManager.rotateKey(),
      keyManager.listKeyPairs(),
      keyManager.rotateKey(),
      keyManager.getActiveKeyPair()
    ];
    
    // 3. Wait for all operations to complete
    const results = await Promise.all(operations);
    
    // 4. Verify results
    expect(results).toHaveLength(5);
    expect(results[0]).toBeTruthy(); // First getActiveKeyPair
    expect(results[1]).toBeTruthy(); // First rotateKey
    expect(results[2]).toBeInstanceOf(Array); // listKeyPairs
    expect(results[3]).toBeTruthy(); // Second rotateKey
    expect(results[4]).toBeTruthy(); // Second getActiveKeyPair
    
    // 5. Verify final state
    const finalKeys = await keyManager.listKeyPairs();
    expect(finalKeys).toHaveLength(3); // Initial + 2 rotations
    expect(finalKeys.filter(k => k.active)).toHaveLength(1);
  });
  
  test('error recovery during key rotation', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 1000,
      autoRotate: true
    });
    
    const initialKeyId = keyManager.activeKeyId;
    
    // 2. Simulate error during rotation
    const originalRotateKey = keyManager.rotateKey;
    keyManager.rotateKey = jest.fn()
      .mockRejectedValueOnce(new Error('Simulated rotation error'))
      .mockImplementation(originalRotateKey);
    
    // 3. Trigger rotation
    jest.advanceTimersByTime(1000);
    
    // Allow async operations to complete
    await new Promise(resolve => setImmediate(resolve));
    
    // 4. Verify system state
    expect(keyManager.activeKeyId).toBe(initialKeyId); // Should still have old key
    const keys = await keyManager.listKeyPairs();
    expect(keys).toHaveLength(1); // No new key should be created
    
    // 5. Verify system is still operational
    const keyPair = await keyManager.getActiveKeyPair();
    expect(keyPair).toBeTruthy();
    
    // 6. Verify next rotation works
    jest.advanceTimersByTime(1000);
    await new Promise(resolve => setImmediate(resolve));
    
    const newKeys = await keyManager.listKeyPairs();
    expect(newKeys).toHaveLength(2); // New key should be created
  });
  
  test('key expiry and cleanup', async () => {
    // 1. Initialize with short expiry
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 1000,
      autoRotate: false
    });
    
    // 2. Create multiple keys
    const keyIds = [];
    for (let i = 0; i < 3; i++) {
      const keyId = await keyManager.rotateKey();
      keyIds.push(keyId);
      jest.advanceTimersByTime(500); // Advance time between rotations
    }
    
    // 3. Verify initial state
    const initialKeys = await keyManager.listKeyPairs();
    expect(initialKeys).toHaveLength(3);
    
    // 4. Advance time past expiry
    jest.advanceTimersByTime(2000);
    
    // 5. Clean up expired keys
    const deletedCount = await keyManager.cleanupExpiredKeys();
    
    // 6. Verify cleanup
    expect(deletedCount).toBe(2); // Two keys should be expired
    const remainingKeys = await keyManager.listKeyPairs();
    expect(remainingKeys).toHaveLength(1);
    expect(remainingKeys[0].id).toBe(keyIds[2]); // Latest key should remain
  });
  
  test('passphrase change and key reencryption', async () => {
    // 1. Initialize with initial passphrase
    await keyManager.initialize({
      passphrase: 'initial-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    const initialKeyId = keyManager.activeKeyId;
    const initialKeyPair = await keyManager.getActiveKeyPair();
    
    // 2. Close and reopen with new passphrase
    await keyManager.close();
    await keyStore.close();
    
    const newKeyStore = new KeyStore('test_keystore');
    const newKeyManager = new KeyManager();
    newKeyManager.keyStore = newKeyStore;
    
    await newKeyManager.initialize({
      passphrase: 'new-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 3. Verify key accessibility
    const newKeyPair = await newKeyManager.getActiveKeyPair();
    expect(newKeyPair).toEqual(initialKeyPair);
    
    // 4. Verify key is reencrypted with new passphrase
    const keyData = await newKeyStore._getItem('keys', initialKeyId);
    expect(keyData.data.encrypted).toBe(true);
    
    // Clean up
    await newKeyManager.close();
    await newKeyStore.close();
  });
});

describe('Security Tests', () => {
  let keyManager;
  let keyStore;
  
  beforeEach(() => {
    keyStore = new KeyStore('test_keystore');
    keyManager = new KeyManager();
    keyManager.keyStore = keyStore;
    jest.useFakeTimers();
  });
  
  afterEach(async () => {
    await keyManager.close();
    await keyStore.close();
    jest.useRealTimers();
  });
  
  test('key isolation between different passphrases', async () => {
    // 1. Initialize with first passphrase
    await keyManager.initialize({
      passphrase: 'passphrase1',
      expiryTime: 5000,
      autoRotate: false
    });
    
    const key1 = await keyManager.getActiveKeyPair();
    
    // 2. Close and reopen with different passphrase
    await keyManager.close();
    await keyStore.close();
    
    const newKeyStore = new KeyStore('test_keystore');
    const newKeyManager = new KeyManager();
    newKeyManager.keyStore = newKeyStore;
    
    await newKeyManager.initialize({
      passphrase: 'passphrase2',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 3. Attempt to access key with wrong passphrase
    await expect(newKeyManager.getActiveKeyPair()).rejects.toThrow();
  });
  
  test('key data encryption verification', async () => {
    // 1. Initialize with passphrase
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 2. Get raw key data from store
    const keyData = await keyStore._getItem('keys', keyManager.activeKeyId);
    
    // 3. Verify encryption
    expect(keyData.data.encrypted).toBe(true);
    expect(keyData.data.ciphertext).toBeDefined();
    expect(keyData.data.iv).toBeDefined();
    
    // 4. Verify ciphertext is different from plaintext
    const plaintext = JSON.stringify({
      dh: { publicKey: 'mock-dh-public', privateKey: 'mock-dh-private' },
      dsa: { publicKey: 'mock-dsa-public', privateKey: 'mock-dsa-private' }
    });
    expect(keyData.data.ciphertext).not.toBe(plaintext);
  });
  
  test('key rotation prevents key reuse', async () => {
    // 1. Initialize and get initial key
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 1000,
      autoRotate: true
    });
    
    const initialKey = await keyManager.getActiveKeyPair();
    
    // 2. Trigger rotation
    jest.advanceTimersByTime(1000);
    await new Promise(resolve => setImmediate(resolve));
    
    const newKey = await keyManager.getActiveKeyPair();
    
    // 3. Verify keys are different
    expect(newKey.dh.publicKey).not.toBe(initialKey.dh.publicKey);
    expect(newKey.dh.privateKey).not.toBe(initialKey.dh.privateKey);
    expect(newKey.dsa.publicKey).not.toBe(initialKey.dsa.publicKey);
    expect(newKey.dsa.privateKey).not.toBe(initialKey.dsa.privateKey);
  });
  
  test('key deletion ensures secure removal', async () => {
    // 1. Initialize and create key
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    const keyId = keyManager.activeKeyId;
    
    // 2. Delete key
    await keyManager.keyStore.deleteKeyPair(keyId);
    
    // 3. Verify key is completely removed
    const keyData = await keyManager.keyStore._getItem('keys', keyId);
    const metadata = await keyManager.keyStore._getItem('metadata', keyId);
    
    expect(keyData).toBeNull();
    expect(metadata).toBeNull();
    
    // 4. Verify key is not recoverable
    await expect(keyManager.keyStore.getKeyPair(keyId)).rejects.toThrow();
  });
  
  test('protection against timing attacks', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 2. Measure operation times
    const startTime = Date.now();
    await keyManager.getActiveKeyPair();
    const endTime = Date.now();
    const operationTime = endTime - startTime;
    
    // 3. Perform multiple operations and verify consistent timing
    const times = [];
    for (let i = 0; i < 10; i++) {
      const start = Date.now();
      await keyManager.getActiveKeyPair();
      const end = Date.now();
      times.push(end - start);
    }
    
    // 4. Verify timing consistency
    const avgTime = times.reduce((a, b) => a + b) / times.length;
    const maxDeviation = Math.max(...times.map(t => Math.abs(t - avgTime)));
    
    // Allow for small timing variations (less than 10ms)
    expect(maxDeviation).toBeLessThan(10);
  });
  
  test('protection against memory exposure', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 2. Get key pair
    const keyPair = await keyManager.getActiveKeyPair();
    
    // 3. Verify private keys are not exposed in memory
    const privateKeys = [
      keyPair.dh.privateKey,
      keyPair.dsa.privateKey
    ];
    
    // 4. Attempt to access private key properties
    privateKeys.forEach(key => {
      expect(() => {
        Object.keys(key);
      }).toThrow();
    });
  });
  
  test('protection against key compromise', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 1000,
      autoRotate: true
    });
    
    const initialKeyId = keyManager.activeKeyId;
    
    // 2. Simulate key compromise by modifying stored key
    const compromisedKey = {
      dh: { publicKey: 'compromised', privateKey: 'compromised' },
      dsa: { publicKey: 'compromised', privateKey: 'compromised' }
    };
    
    await keyManager.keyStore._storeItem('keys', {
      id: initialKeyId,
      data: compromisedKey
    });
    
    // 3. Trigger rotation
    jest.advanceTimersByTime(1000);
    await new Promise(resolve => setImmediate(resolve));
    
    // 4. Verify compromised key is not used
    const newKey = await keyManager.getActiveKeyPair();
    expect(newKey.dh.publicKey).not.toBe('compromised');
    expect(newKey.dh.privateKey).not.toBe('compromised');
    expect(newKey.dsa.publicKey).not.toBe('compromised');
    expect(newKey.dsa.privateKey).not.toBe('compromised');
  });
  
  test('protection against replay attacks', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 2. Capture key data
    const keyData = await keyManager.keyStore._getItem('keys', keyManager.activeKeyId);
    
    // 3. Attempt to replay old key data
    await keyManager.keyStore._storeItem('keys', {
      id: 'replay-attempt',
      data: keyData.data
    });
    
    // 4. Verify replay attempt fails
    await expect(keyManager.keyStore.getKeyPair('replay-attempt')).rejects.toThrow();
  });
  
  test('protection against key tampering', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 2. Attempt to modify key data
    const keyData = await keyManager.keyStore._getItem('keys', keyManager.activeKeyId);
    keyData.data.ciphertext[0] ^= 1; // Flip first bit
    
    await keyManager.keyStore._storeItem('keys', keyData);
    
    // 3. Verify tampered key is detected
    await expect(keyManager.getActiveKeyPair()).rejects.toThrow();
  });
});

describe('Security Error Handling', () => {
  let keyManager;
  let keyStore;
  
  beforeEach(() => {
    keyStore = new KeyStore('test_keystore');
    keyManager = new KeyManager();
    keyManager.keyStore = keyStore;
    jest.useFakeTimers();
  });
  
  afterEach(async () => {
    await keyManager.close();
    await keyStore.close();
    jest.useRealTimers();
  });
  
  test('handles corrupted key data gracefully', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 2. Corrupt key data
    const keyData = await keyStore._getItem('keys', keyManager.activeKeyId);
    keyData.data.ciphertext = new Uint8Array([0, 1, 2, 3]); // Invalid ciphertext
    
    await keyStore._storeItem('keys', keyData);
    
    // 3. Attempt to use corrupted key
    await expect(keyManager.getActiveKeyPair()).rejects.toThrow('Invalid key data');
    
    // 4. Verify system remains operational
    await keyManager.rotateKey();
    const newKey = await keyManager.getActiveKeyPair();
    expect(newKey).toBeTruthy();
  });
  
  test('handles invalid passphrase attempts', async () => {
    // 1. Initialize with correct passphrase
    await keyManager.initialize({
      passphrase: 'correct-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 2. Close and reopen with incorrect passphrase
    await keyManager.close();
    await keyStore.close();
    
    const newKeyStore = new KeyStore('test_keystore');
    const newKeyManager = new KeyManager();
    newKeyManager.keyStore = newKeyStore;
    
    // 3. Attempt initialization with wrong passphrase
    await expect(newKeyManager.initialize({
      passphrase: 'wrong-passphrase',
      expiryTime: 5000,
      autoRotate: false
    })).rejects.toThrow('Invalid passphrase');
    
    // 4. Verify system remains in clean state
    expect(newKeyManager.initialized).toBe(false);
    expect(newKeyManager.activeKeyId).toBeNull();
  });
  
  test('handles database corruption gracefully', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 2. Corrupt database metadata
    const metadata = await keyStore._getItem('metadata', keyManager.activeKeyId);
    metadata.data = { invalid: 'data' };
    
    await keyStore._storeItem('metadata', metadata);
    
    // 3. Attempt to use corrupted database
    await expect(keyManager.listKeyPairs()).rejects.toThrow('Invalid metadata');
    
    // 4. Verify system can recover
    await keyManager.rotateKey();
    const newKey = await keyManager.getActiveKeyPair();
    expect(newKey).toBeTruthy();
  });
  
  test('handles concurrent key access conflicts', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 2. Simulate concurrent access
    const operations = [
      keyManager.getActiveKeyPair(),
      keyManager.rotateKey(),
      keyManager.getActiveKeyPair()
    ];
    
    // 3. Verify operations complete without errors
    const results = await Promise.all(operations);
    expect(results).toHaveLength(3);
    expect(results[0]).toBeTruthy();
    expect(results[1]).toBeTruthy();
    expect(results[2]).toBeTruthy();
  });
  
  test('handles key rotation failures gracefully', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 1000,
      autoRotate: true
    });
    
    const initialKeyId = keyManager.activeKeyId;
    
    // 2. Simulate rotation failure
    const originalRotateKey = keyManager.rotateKey;
    keyManager.rotateKey = jest.fn()
      .mockRejectedValueOnce(new Error('Rotation failed'))
      .mockImplementation(originalRotateKey);
    
    // 3. Trigger rotation
    jest.advanceTimersByTime(1000);
    await new Promise(resolve => setImmediate(resolve));
    
    // 4. Verify system maintains old key
    expect(keyManager.activeKeyId).toBe(initialKeyId);
    const keyPair = await keyManager.getActiveKeyPair();
    expect(keyPair).toBeTruthy();
    
    // 5. Verify next rotation succeeds
    jest.advanceTimersByTime(1000);
    await new Promise(resolve => setImmediate(resolve));
    
    expect(keyManager.activeKeyId).not.toBe(initialKeyId);
  });
  
  test('handles key deletion failures gracefully', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    const keyId = keyManager.activeKeyId;
    
    // 2. Simulate deletion failure
    const originalDeleteKeyPair = keyStore.deleteKeyPair;
    keyStore.deleteKeyPair = jest.fn()
      .mockRejectedValueOnce(new Error('Deletion failed'))
      .mockImplementation(originalDeleteKeyPair);
    
    // 3. Attempt deletion
    await expect(keyStore.deleteKeyPair(keyId)).rejects.toThrow('Deletion failed');
    
    // 4. Verify key still exists
    const keyData = await keyStore._getItem('keys', keyId);
    expect(keyData).toBeTruthy();
    
    // 5. Verify system remains operational
    const keyPair = await keyManager.getActiveKeyPair();
    expect(keyPair).toBeTruthy();
  });
  
  test('handles encryption failures gracefully', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 2. Simulate encryption failure
    const originalStoreKeyPair = keyStore.storeKeyPair;
    keyStore.storeKeyPair = jest.fn()
      .mockRejectedValueOnce(new Error('Encryption failed'))
      .mockImplementation(originalStoreKeyPair);
    
    // 3. Attempt key rotation
    await expect(keyManager.rotateKey()).rejects.toThrow('Encryption failed');
    
    // 4. Verify system maintains old key
    const keyPair = await keyManager.getActiveKeyPair();
    expect(keyPair).toBeTruthy();
    
    // 5. Verify next operation succeeds
    await keyManager.rotateKey();
    const newKey = await keyManager.getActiveKeyPair();
    expect(newKey).toBeTruthy();
  });
  
  test('handles decryption failures gracefully', async () => {
    // 1. Initialize
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    // 2. Corrupt key data to cause decryption failure
    const keyData = await keyStore._getItem('keys', keyManager.activeKeyId);
    keyData.data.ciphertext = new Uint8Array([255, 255, 255, 255]); // Invalid ciphertext
    
    await keyStore._storeItem('keys', keyData);
    
    // 3. Attempt to use corrupted key
    await expect(keyManager.getActiveKeyPair()).rejects.toThrow('Decryption failed');
    
    // 4. Verify system can recover
    await keyManager.rotateKey();
    const newKey = await keyManager.getActiveKeyPair();
    expect(newKey).toBeTruthy();
  });
  
  test('handles database connection failures gracefully', async () => {
    // 1. Simulate database connection failure
    const originalInitialize = keyStore.initialize;
    keyStore.initialize = jest.fn()
      .mockRejectedValueOnce(new Error('Database connection failed'))
      .mockImplementation(originalInitialize);
    
    // 2. Attempt initialization
    await expect(keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    })).rejects.toThrow('Database connection failed');
    
    // 3. Verify system remains in clean state
    expect(keyManager.initialized).toBe(false);
    expect(keyManager.activeKeyId).toBeNull();
    
    // 4. Verify system can recover
    await keyManager.initialize({
      passphrase: 'test-passphrase',
      expiryTime: 5000,
      autoRotate: false
    });
    
    const keyPair = await keyManager.getActiveKeyPair();
    expect(keyPair).toBeTruthy();
  });
}); 