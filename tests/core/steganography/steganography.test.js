/**
 * Steganography Module Tests
 * Tests for WebOTR steganography functionality
 */

import { SteganographyOTR, STEGO_CONSTANTS, STEGO_MESSAGE_TYPE } from '../../../src/core/steganography';

// Mock stego-js since it may not work in test environment
jest.mock('@masknet/stego-js', () => {
  let storedData = null;

  return {
    encode: jest.fn().mockImplementation(async (image, data) => {
      // Store the data for later retrieval
      storedData = data;
      // Mock successful encoding
      return {
        data: new Uint8ClampedArray(1024 * 1024 * 4), // Mock image data
        width: 1024,
        height: 1024,
        _hiddenData: data // Store for testing
      };
    }),
    decode: jest.fn().mockImplementation(async (image) => {
      // Return the stored data if available
      if (image && image._hiddenData) {
        return image._hiddenData;
      }
      // Return null for images without hidden data
      return null;
    })
  };
});

describe('SteganographyOTR', () => {
  let stego;
  let mockImage;

  beforeEach(() => {
    stego = new SteganographyOTR();
    
    // Create mock image data
    mockImage = {
      data: new Uint8ClampedArray(1024 * 1024 * 4),
      width: 1024,
      height: 1024
    };
  });

  describe('Basic Steganography Operations', () => {
    test('should hide and reveal messages successfully', async () => {
      const testMessage = 'Hello, this is a secret OTR message!';
      
      // Hide message
      const stegoImage = await stego.hideMessage(mockImage, testMessage);
      expect(stegoImage).toBeDefined();
      expect(stegoImage.data).toBeInstanceOf(Uint8ClampedArray);
      
      // Reveal message
      const revealedMessage = await stego.revealMessage(stegoImage);
      expect(revealedMessage).toBe(testMessage);
    });

    test('should return null for images without hidden data', async () => {
      // Mock decode to return null (no hidden data)
      const { decode } = require('@masknet/stego-js');
      decode.mockResolvedValueOnce(null);
      
      const result = await stego.revealMessage(mockImage);
      expect(result).toBeNull();
    });

    test('should detect hidden messages correctly', async () => {
      const testMessage = 'Secret message';
      
      // Hide message first
      const stegoImage = await stego.hideMessage(mockImage, testMessage);
      
      // Should detect hidden message
      const hasMessage = await stego.detectMessage(stegoImage);
      expect(hasMessage).toBe(true);
      
      // Mock no hidden data for clean image
      const { decode } = require('@masknet/stego-js');
      decode.mockResolvedValueOnce(null);
      
      const hasMessageClean = await stego.detectMessage(mockImage);
      expect(hasMessageClean).toBe(false);
    });

    test('should calculate image capacity', async () => {
      const capacity = await stego.calculateCapacity(mockImage);
      expect(capacity).toBeGreaterThan(0);
      expect(capacity).toBeLessThanOrEqual(STEGO_CONSTANTS.MAX_MESSAGE_SIZE);
    });
  });

  describe('Message Format Validation', () => {
    test('should create valid steganography message format', () => {
      const testMessage = 'Test OTR message';
      const stegoMessage = stego._createStegoMessage(testMessage, {
        type: STEGO_MESSAGE_TYPE.OTR_MESSAGE
      });
      
      expect(stegoMessage.header).toBe(STEGO_CONSTANTS.MAGIC_HEADER);
      expect(stegoMessage.version).toBe(STEGO_CONSTANTS.VERSION);
      expect(stegoMessage.type).toBe(STEGO_MESSAGE_TYPE.OTR_MESSAGE);
      expect(stegoMessage.payload).toBe(testMessage);
      expect(stegoMessage.checksum).toBeDefined();
    });

    test('should validate steganography message format', () => {
      const validMessage = {
        header: STEGO_CONSTANTS.MAGIC_HEADER,
        version: STEGO_CONSTANTS.VERSION,
        type: STEGO_MESSAGE_TYPE.OTR_MESSAGE,
        payload: 'test message',
        checksum: stego._calculateChecksum('test message')
      };
      
      expect(stego._validateStegoMessage(validMessage)).toBe(true);
      
      // Test invalid messages
      expect(stego._validateStegoMessage(null)).toBe(false);
      expect(stego._validateStegoMessage({})).toBe(false);
      expect(stego._validateStegoMessage({
        header: 'INVALID',
        version: 1,
        type: 1,
        payload: 'test'
      })).toBe(false);
    });

    test('should serialize and deserialize messages correctly', () => {
      const testMessage = 'Test message for serialization';
      const stegoMessage = stego._createStegoMessage(testMessage);
      
      const serialized = stego._serializeMessage(stegoMessage);
      expect(typeof serialized).toBe('string');
      
      const deserialized = stego._deserializeMessage(serialized);
      expect(deserialized).toEqual(stegoMessage);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid image inputs', async () => {
      await expect(stego.hideMessage(null, 'test')).rejects.toThrow();
      // revealMessage returns null for invalid images instead of throwing
      const result = await stego.revealMessage(null);
      expect(result).toBeNull();
    });

    test('should handle invalid message inputs', async () => {
      await expect(stego.hideMessage(mockImage, null)).rejects.toThrow();
      await expect(stego.hideMessage(mockImage, '')).rejects.toThrow();
      await expect(stego.hideMessage(mockImage, 123)).rejects.toThrow();
    });

    test('should handle message too large error', async () => {
      const largeMessage = 'x'.repeat(STEGO_CONSTANTS.MAX_MESSAGE_SIZE + 1);
      await expect(stego.hideMessage(mockImage, largeMessage)).rejects.toThrow();
    });

    test('should handle extraction failures gracefully', async () => {
      // Mock decode to throw error
      const { decode } = require('@masknet/stego-js');
      decode.mockRejectedValueOnce(new Error('Extraction failed'));
      
      const result = await stego.revealMessage(mockImage);
      expect(result).toBeNull();
    });
  });

  describe('Different Message Types', () => {
    test('should handle OTR messages', async () => {
      const otrMessage = '?OTR:AAEDAAAAAQAAAAEAAADAVf3Rg...';
      
      const stegoImage = await stego.hideMessage(mockImage, otrMessage, {
        type: STEGO_MESSAGE_TYPE.OTR_MESSAGE
      });
      
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(otrMessage);
    });

    test('should handle AKE messages', async () => {
      const akeMessage = JSON.stringify({
        type: 'DH_COMMIT',
        instanceTag: 12345,
        dhCommit: 'mock_dh_commit_data'
      });
      
      const stegoImage = await stego.hideMessage(mockImage, akeMessage, {
        type: STEGO_MESSAGE_TYPE.AKE_MESSAGE
      });
      
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(akeMessage);
    });

    test('should handle SMP messages', async () => {
      const smpMessage = JSON.stringify({
        type: 2,
        g2a: 'mock_g2a_value',
        c2: 'mock_c2_value',
        d2: 'mock_d2_value'
      });
      
      const stegoImage = await stego.hideMessage(mockImage, smpMessage, {
        type: STEGO_MESSAGE_TYPE.SMP_MESSAGE
      });
      
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(smpMessage);
    });
  });
});
