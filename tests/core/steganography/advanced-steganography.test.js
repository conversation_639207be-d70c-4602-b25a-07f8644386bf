/**
 * Advanced Steganography Test Suite
 * Comprehensive testing for edge cases, security, and advanced scenarios
 */

const { SteganographyOTR, STEGO_MESSAGE_TYPE } = require('../../../src/core/steganography');

// Mock stego-js with advanced scenarios
jest.mock('@masknet/stego-js', () => {
  const hiddenDataStore = new Map();
  let failureMode = null;
  
  return {
    encode: jest.fn().mockImplementation(async (imageData, data) => {
      if (failureMode === 'encode') {
        throw new Error('Simulated encoding failure');
      }
      
      // Simulate capacity limits
      const capacity = imageData.width * imageData.height * 0.1; // 10% capacity
      if (data.length > capacity) {
        throw new Error('Message too large for image capacity');
      }
      
      const imageKey = `${imageData.width}x${imageData.height}_${Date.now()}_${Math.random()}`;
      hiddenDataStore.set(imageKey, data);
      
      return {
        ...imageData,
        _hiddenData: data,
        _imageKey: imageKey,
        _capacity: capacity
      };
    }),
    decode: jest.fn().mockImplementation(async (imageData) => {
      if (failureMode === 'decode') {
        throw new Error('Simulated decoding failure');
      }
      
      if (imageData && imageData._hiddenData) {
        return imageData._hiddenData;
      }
      
      if (imageData && imageData._imageKey && hiddenDataStore.has(imageData._imageKey)) {
        return hiddenDataStore.get(imageData._imageKey);
      }
      
      return null;
    }),
    // Test utility to control failure modes
    __setFailureMode: (mode) => { failureMode = mode; },
    __clearFailureMode: () => { failureMode = null; },
    __getStoredDataCount: () => hiddenDataStore.size,
    __clearStorage: () => hiddenDataStore.clear()
  };
});

describe('Advanced Steganography Test Suite', () => {
  let stego;
  const mockStego = require('@masknet/stego-js');

  beforeEach(() => {
    stego = new SteganographyOTR({
      quality: 0.95,
      password: 'advanced-test-password',
      compression: true
    });
    mockStego.__clearFailureMode();
    mockStego.__clearStorage();
  });

  describe('Edge Cases and Boundary Conditions', () => {
    test('should handle extremely small images', async () => {
      const tinyImage = {
        data: new Uint8ClampedArray(4 * 4 * 4), // 4x4 image
        width: 4,
        height: 4
      };
      
      // Fill with random data
      for (let i = 0; i < tinyImage.data.length; i++) {
        tinyImage.data[i] = Math.floor(Math.random() * 256);
      }
      
      const message = 'Hi'; // Very short message
      
      try {
        const stegoImage = await stego.hideMessage(tinyImage, message);
        const revealed = await stego.revealMessage(stegoImage);
        expect(revealed).toBe(message);
      } catch (error) {
        // It's acceptable for very small images to fail
        expect(error.message).toContain('capacity');
      }
    });

    test('should handle extremely large images', async () => {
      const largeImage = {
        data: new Uint8ClampedArray(4096 * 4096 * 4), // 4K image
        width: 4096,
        height: 4096
      };
      
      // Fill with pattern data (more realistic than random)
      for (let y = 0; y < 4096; y++) {
        for (let x = 0; x < 4096; x++) {
          const i = (y * 4096 + x) * 4;
          largeImage.data[i] = (x + y) % 256;     // Red
          largeImage.data[i + 1] = (x * 2) % 256; // Green
          largeImage.data[i + 2] = (y * 2) % 256; // Blue
          largeImage.data[i + 3] = 255;           // Alpha
        }
      }
      
      const message = 'Large image test message';
      
      const stegoImage = await stego.hideMessage(largeImage, message);
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(message);
    });

    test('should handle empty messages', async () => {
      const image = createTestImage(512, 512);
      
      const emptyMessage = '';
      const stegoImage = await stego.hideMessage(image, emptyMessage);
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(emptyMessage);
    });

    test('should handle messages at capacity limit', async () => {
      const image = createTestImage(100, 100);
      const capacity = await stego.calculateCapacity(image);
      
      // Create message at exactly capacity limit
      const maxMessage = 'X'.repeat(Math.floor(capacity));
      
      const stegoImage = await stego.hideMessage(image, maxMessage);
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(maxMessage);
    });

    test('should reject messages exceeding capacity', async () => {
      const image = createTestImage(50, 50);
      const capacity = await stego.calculateCapacity(image);
      
      // Create message exceeding capacity
      const oversizedMessage = 'X'.repeat(Math.floor(capacity * 2));
      
      await expect(stego.hideMessage(image, oversizedMessage))
        .rejects.toThrow('too large');
    });
  });

  describe('Security and Robustness Tests', () => {
    test('should handle corrupted image data', async () => {
      const image = createTestImage(256, 256);
      
      // Corrupt some image data
      for (let i = 0; i < 100; i++) {
        const randomIndex = Math.floor(Math.random() * image.data.length);
        image.data[randomIndex] = Math.floor(Math.random() * 256);
      }
      
      const message = 'Test with corrupted image';
      
      try {
        const stegoImage = await stego.hideMessage(image, message);
        const revealed = await stego.revealMessage(stegoImage);
        expect(revealed).toBe(message);
      } catch (error) {
        // Corruption might cause failures, which is acceptable
        expect(error).toBeDefined();
      }
    });

    test('should handle malformed image structures', async () => {
      const malformedImages = [
        { data: null, width: 100, height: 100 },
        { data: new Uint8ClampedArray(100), width: 100, height: 100 }, // Wrong size
        { data: new Uint8ClampedArray(400), width: 0, height: 100 },   // Zero width
        { data: new Uint8ClampedArray(400), width: 100, height: 0 },   // Zero height
        { data: new Uint8ClampedArray(400), width: -10, height: 100 }, // Negative dimensions
      ];
      
      for (const malformedImage of malformedImages) {
        await expect(stego.hideMessage(malformedImage, 'test'))
          .rejects.toThrow();
      }
    });

    test('should handle special characters and unicode', async () => {
      const image = createTestImage(512, 512);
      
      const unicodeMessages = [
        '🔒🌍🚀 Unicode test with emojis! 🎉✨',
        'Ñoño niño español',
        '中文测试消息',
        'Тест на русском языке',
        'العربية اختبار',
        '日本語のテストメッセージ',
        'Ελληνικό τεστ μήνυμα',
        '한국어 테스트 메시지'
      ];
      
      for (const message of unicodeMessages) {
        const stegoImage = await stego.hideMessage(image, message);
        const revealed = await stego.revealMessage(stegoImage);
        expect(revealed).toBe(message);
      }
    });

    test('should handle binary data and control characters', async () => {
      const image = createTestImage(512, 512);
      
      // Create message with binary data and control characters
      const binaryMessage = String.fromCharCode(
        0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,
        127, 128, 129, 255
      ) + 'Mixed binary and text';
      
      const stegoImage = await stego.hideMessage(image, binaryMessage);
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(binaryMessage);
    });

    test('should maintain security with password protection', async () => {
      const image = createTestImage(512, 512);
      const message = 'Password protected message';
      
      // Create stego with password
      const stegoWithPassword = new SteganographyOTR({
        password: 'secret-password-123'
      });
      
      // Create stego without password
      const stegoWithoutPassword = new SteganographyOTR();
      
      // Hide message with password
      const stegoImage = await stegoWithPassword.hideMessage(image, message);
      
      // Try to reveal with correct password
      const revealedCorrect = await stegoWithPassword.revealMessage(stegoImage);
      expect(revealedCorrect).toBe(message);
      
      // Try to reveal without password (should fail or return garbage)
      try {
        const revealedIncorrect = await stegoWithoutPassword.revealMessage(stegoImage);
        // If it doesn't throw, it should return different data
        expect(revealedIncorrect).not.toBe(message);
      } catch (error) {
        // Password mismatch should cause failure
        expect(error).toBeDefined();
      }
    });
  });

  describe('Performance and Stress Tests', () => {
    test('should handle rapid successive operations', async () => {
      const image = createTestImage(256, 256);
      const operations = [];
      
      // Perform 50 rapid hide/reveal operations
      for (let i = 0; i < 50; i++) {
        const message = `Rapid test message ${i}`;
        operations.push(
          stego.hideMessage(image, message).then(stegoImage =>
            stego.revealMessage(stegoImage).then(revealed => ({
              original: message,
              revealed,
              success: revealed === message
            }))
          )
        );
      }
      
      const results = await Promise.all(operations);
      
      // All operations should succeed
      const successCount = results.filter(r => r.success).length;
      expect(successCount).toBe(50);
    });

    test('should handle concurrent operations', async () => {
      const images = Array.from({ length: 10 }, (_, i) => createTestImage(200, 200));
      
      // Perform concurrent operations on different images
      const concurrentOperations = images.map(async (image, i) => {
        const message = `Concurrent message ${i}`;
        const stegoImage = await stego.hideMessage(image, message);
        const revealed = await stego.revealMessage(stegoImage);
        return { original: message, revealed, success: revealed === message };
      });
      
      const results = await Promise.all(concurrentOperations);
      
      // All concurrent operations should succeed
      const successCount = results.filter(r => r.success).length;
      expect(successCount).toBe(10);
    });

    test('should handle memory pressure scenarios', async () => {
      // Create multiple large images to simulate memory pressure
      const largeImages = Array.from({ length: 5 }, () => createTestImage(1024, 1024));
      
      const results = [];
      
      for (let i = 0; i < largeImages.length; i++) {
        const message = `Memory pressure test ${i}`;
        const stegoImage = await stego.hideMessage(largeImages[i], message);
        const revealed = await stego.revealMessage(stegoImage);
        results.push(revealed === message);
        
        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }
      }
      
      // All operations should succeed despite memory pressure
      expect(results.every(success => success)).toBe(true);
    });

    test('should maintain performance with varying image sizes', async () => {
      const imageSizes = [
        [64, 64], [128, 128], [256, 256], [512, 512], [1024, 1024]
      ];
      
      const performanceResults = [];
      
      for (const [width, height] of imageSizes) {
        const image = createTestImage(width, height);
        const message = `Performance test for ${width}x${height}`;
        
        const startTime = Date.now();
        const stegoImage = await stego.hideMessage(image, message);
        const hideTime = Date.now() - startTime;
        
        const revealStart = Date.now();
        const revealed = await stego.revealMessage(stegoImage);
        const revealTime = Date.now() - revealStart;
        
        expect(revealed).toBe(message);
        
        performanceResults.push({
          size: `${width}x${height}`,
          pixels: width * height,
          hideTime,
          revealTime,
          totalTime: hideTime + revealTime
        });
      }
      
      // Performance should scale reasonably with image size
      performanceResults.forEach(result => {
        expect(result.totalTime).toBeLessThan(1000); // Less than 1 second
      });
      
      console.log('Performance Results:', performanceResults);
    });
  });

  describe('Error Recovery and Resilience', () => {
    test('should recover from encoding failures', async () => {
      const image = createTestImage(256, 256);
      const message = 'Recovery test message';
      
      // Simulate encoding failure
      mockStego.__setFailureMode('encode');
      
      await expect(stego.hideMessage(image, message))
        .rejects.toThrow('Simulated encoding failure');
      
      // Clear failure mode and retry
      mockStego.__clearFailureMode();
      
      const stegoImage = await stego.hideMessage(image, message);
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(message);
    });

    test('should recover from decoding failures', async () => {
      const image = createTestImage(256, 256);
      const message = 'Decoding recovery test';
      
      // First, successfully hide the message
      const stegoImage = await stego.hideMessage(image, message);
      
      // Simulate decoding failure
      mockStego.__setFailureMode('decode');
      
      await expect(stego.revealMessage(stegoImage))
        .rejects.toThrow('Simulated decoding failure');
      
      // Clear failure mode and retry
      mockStego.__clearFailureMode();
      
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(message);
    });

    test('should handle partial data corruption gracefully', async () => {
      const image = createTestImage(512, 512);
      const message = 'Corruption resilience test message';
      
      const stegoImage = await stego.hideMessage(image, message);
      
      // Simulate partial corruption by modifying some pixels
      const corruptedImage = { ...stegoImage };
      corruptedImage.data = new Uint8ClampedArray(stegoImage.data);
      
      // Corrupt 1% of pixels
      const corruptionCount = Math.floor(corruptedImage.data.length * 0.01);
      for (let i = 0; i < corruptionCount; i++) {
        const randomIndex = Math.floor(Math.random() * corruptedImage.data.length);
        corruptedImage.data[randomIndex] = Math.floor(Math.random() * 256);
      }
      
      try {
        const revealed = await stego.revealMessage(corruptedImage);
        // Message might be partially corrupted but should not crash
        expect(typeof revealed).toBe('string');
      } catch (error) {
        // Corruption might cause extraction to fail, which is acceptable
        expect(error).toBeDefined();
      }
    });
  });

  describe('Advanced Message Types and Formats', () => {
    test('should handle structured JSON data', async () => {
      const image = createTestImage(512, 512);
      
      const complexData = {
        type: 'secure_message',
        timestamp: Date.now(),
        sender: '<EMAIL>',
        recipient: '<EMAIL>',
        content: {
          text: 'This is a complex structured message',
          attachments: [
            { type: 'image', url: 'https://example.com/image.jpg' },
            { type: 'document', url: 'https://example.com/doc.pdf' }
          ],
          metadata: {
            priority: 'high',
            encrypted: true,
            expires: Date.now() + 86400000 // 24 hours
          }
        },
        signature: 'mock_signature_data_here'
      };
      
      const jsonMessage = JSON.stringify(complexData);
      
      const stegoImage = await stego.hideMessage(image, jsonMessage);
      const revealed = await stego.revealMessage(stegoImage);
      
      expect(revealed).toBe(jsonMessage);
      
      // Verify JSON can be parsed back
      const parsedData = JSON.parse(revealed);
      expect(parsedData).toEqual(complexData);
    });

    test('should handle base64 encoded data', async () => {
      const image = createTestImage(512, 512);
      
      // Create some binary data and encode it
      const binaryData = new Uint8Array(100);
      for (let i = 0; i < binaryData.length; i++) {
        binaryData[i] = Math.floor(Math.random() * 256);
      }
      
      const base64Data = Buffer.from(binaryData).toString('base64');
      
      const stegoImage = await stego.hideMessage(image, base64Data);
      const revealed = await stego.revealMessage(stegoImage);
      
      expect(revealed).toBe(base64Data);
      
      // Verify base64 can be decoded back
      const decodedData = Buffer.from(revealed, 'base64');
      expect(decodedData).toEqual(Buffer.from(binaryData));
    });

    test('should handle URL and URI data', async () => {
      const image = createTestImage(512, 512);
      
      const urls = [
        'https://example.com/path/to/resource?param=value&other=123',
        'ftp://files.example.com/directory/file.txt',
        'mailto:<EMAIL>?subject=Test&body=Hello%20World',
        'data:text/plain;base64,SGVsbG8gV29ybGQ=',
        'file:///path/to/local/file.txt'
      ];
      
      for (const url of urls) {
        const stegoImage = await stego.hideMessage(image, url);
        const revealed = await stego.revealMessage(stegoImage);
        expect(revealed).toBe(url);
      }
    });
  });

  describe('Compatibility and Interoperability', () => {
    test('should maintain consistency across different quality settings', async () => {
      const image = createTestImage(512, 512);
      const message = 'Quality consistency test';
      
      const qualityLevels = [0.5, 0.7, 0.85, 0.95, 1.0];
      
      for (const quality of qualityLevels) {
        const stegoWithQuality = new SteganographyOTR({ quality });
        
        const stegoImage = await stegoWithQuality.hideMessage(image, message);
        const revealed = await stegoWithQuality.revealMessage(stegoImage);
        
        expect(revealed).toBe(message);
      }
    });

    test('should handle different compression settings', async () => {
      const image = createTestImage(512, 512);
      const message = 'Compression test message with some repeated content '.repeat(10);
      
      const compressionSettings = [true, false];
      
      for (const compression of compressionSettings) {
        const stegoWithCompression = new SteganographyOTR({ compression });
        
        const stegoImage = await stegoWithCompression.hideMessage(image, message);
        const revealed = await stegoWithCompression.revealMessage(stegoImage);
        
        expect(revealed).toBe(message);
      }
    });

    test('should work with different image aspect ratios', async () => {
      const aspectRatios = [
        [16, 9],   // Widescreen
        [4, 3],    // Standard
        [1, 1],    // Square
        [3, 4],    // Portrait
        [9, 16],   // Mobile portrait
        [21, 9],   // Ultra-wide
      ];
      
      const message = 'Aspect ratio test message';
      
      for (const [width, height] of aspectRatios) {
        const scaledWidth = width * 50;  // Scale up for reasonable size
        const scaledHeight = height * 50;
        
        const image = createTestImage(scaledWidth, scaledHeight);
        
        const stegoImage = await stego.hideMessage(image, message);
        const revealed = await stego.revealMessage(stegoImage);
        
        expect(revealed).toBe(message);
      }
    });
  });

  // Helper function to create test images
  function createTestImage(width, height, pattern = 'random') {
    const data = new Uint8ClampedArray(width * height * 4);
    
    switch (pattern) {
      case 'gradient':
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const i = (y * width + x) * 4;
            data[i] = (x / width) * 255;     // Red gradient
            data[i + 1] = (y / height) * 255; // Green gradient
            data[i + 2] = 128;               // Blue constant
            data[i + 3] = 255;               // Alpha
          }
        }
        break;
        
      case 'checkerboard':
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const i = (y * width + x) * 4;
            const checker = ((Math.floor(x / 8) + Math.floor(y / 8)) % 2) * 255;
            data[i] = checker;     // Red
            data[i + 1] = checker; // Green
            data[i + 2] = checker; // Blue
            data[i + 3] = 255;     // Alpha
          }
        }
        break;
        
      default: // random
        for (let i = 0; i < data.length; i += 4) {
          data[i] = Math.floor(Math.random() * 256);     // Red
          data[i + 1] = Math.floor(Math.random() * 256); // Green
          data[i + 2] = Math.floor(Math.random() * 256); // Blue
          data[i + 3] = 255;                             // Alpha
        }
    }
    
    return { data, width, height };
  }
});
