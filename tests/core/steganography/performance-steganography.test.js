/**
 * Performance and Stress Testing Suite for Steganography
 * Comprehensive performance benchmarking and stress testing
 */

const { SteganographyOTR, STEGO_MESSAGE_TYPE } = require('../../../src/core/steganography');
const { OTRSteganographySession } = require('../../../src/core/steganography/otr-stego-session');

// Mock stego-js with performance tracking
jest.mock('@masknet/stego-js', () => {
  const performanceMetrics = {
    encodeOperations: 0,
    decodeOperations: 0,
    totalEncodeTime: 0,
    totalDecodeTime: 0,
    peakMemoryUsage: 0
  };
  
  return {
    encode: jest.fn().mockImplementation(async (imageData, data) => {
      const startTime = Date.now();
      performanceMetrics.encodeOperations++;
      
      // Simulate processing time based on image size
      const processingTime = Math.max(1, Math.floor(imageData.data.length / 1000000));
      await new Promise(resolve => setTimeout(resolve, processingTime));
      
      const endTime = Date.now();
      performanceMetrics.totalEncodeTime += (endTime - startTime);
      
      // Simulate memory usage
      const memoryUsage = imageData.data.length + data.length;
      if (memoryUsage > performanceMetrics.peakMemoryUsage) {
        performanceMetrics.peakMemoryUsage = memoryUsage;
      }
      
      const imageKey = `${imageData.width}x${imageData.height}_${Date.now()}_${Math.random()}`;
      
      return {
        ...imageData,
        _hiddenData: data,
        _imageKey: imageKey,
        _processingTime: endTime - startTime
      };
    }),
    
    decode: jest.fn().mockImplementation(async (imageData) => {
      const startTime = Date.now();
      performanceMetrics.decodeOperations++;
      
      // Simulate processing time
      const processingTime = Math.max(1, Math.floor(imageData.data.length / 2000000));
      await new Promise(resolve => setTimeout(resolve, processingTime));
      
      const endTime = Date.now();
      performanceMetrics.totalDecodeTime += (endTime - startTime);
      
      if (imageData && imageData._hiddenData) {
        return imageData._hiddenData;
      }
      
      return null;
    }),
    
    // Performance testing utilities
    __getMetrics: () => ({ ...performanceMetrics }),
    __resetMetrics: () => {
      performanceMetrics.encodeOperations = 0;
      performanceMetrics.decodeOperations = 0;
      performanceMetrics.totalEncodeTime = 0;
      performanceMetrics.totalDecodeTime = 0;
      performanceMetrics.peakMemoryUsage = 0;
    }
  };
});

describe('Performance and Stress Testing Suite', () => {
  let stego;
  const mockStego = require('@masknet/stego-js');

  beforeEach(() => {
    stego = new SteganographyOTR({
      quality: 0.95,
      password: 'performance-test-password'
    });
    mockStego.__resetMetrics();
  });

  describe('Performance Benchmarking', () => {
    test('should benchmark hide/reveal operations across image sizes', async () => {
      const imageSizes = [
        [128, 128],   // Small
        [256, 256],   // Medium
        [512, 512],   // Large
        [1024, 1024], // Very Large
        [2048, 2048], // Huge
      ];
      
      const benchmarkResults = [];
      const message = 'Performance benchmark test message';
      
      for (const [width, height] of imageSizes) {
        const image = createTestImage(width, height);
        const pixelCount = width * height;
        
        // Benchmark hide operation
        const hideStartTime = process.hrtime.bigint();
        const stegoImage = await stego.hideMessage(image, message);
        const hideEndTime = process.hrtime.bigint();
        const hideTime = Number(hideEndTime - hideStartTime) / 1000000; // Convert to ms
        
        // Benchmark reveal operation
        const revealStartTime = process.hrtime.bigint();
        const revealed = await stego.revealMessage(stegoImage);
        const revealEndTime = process.hrtime.bigint();
        const revealTime = Number(revealEndTime - revealStartTime) / 1000000; // Convert to ms
        
        expect(revealed).toBe(message);
        
        const result = {
          size: `${width}x${height}`,
          pixelCount,
          hideTime,
          revealTime,
          totalTime: hideTime + revealTime,
          hideTimePerPixel: hideTime / pixelCount,
          revealTimePerPixel: revealTime / pixelCount
        };
        
        benchmarkResults.push(result);
        
        console.log(`Benchmark ${width}x${height}: Hide ${hideTime.toFixed(2)}ms, Reveal ${revealTime.toFixed(2)}ms`);
      }
      
      // Analyze performance scaling
      const scalingAnalysis = analyzePerfomanceScaling(benchmarkResults);
      console.log('Performance Scaling Analysis:', scalingAnalysis);
      
      // Performance assertions
      benchmarkResults.forEach(result => {
        expect(result.totalTime).toBeLessThan(10000); // Less than 10 seconds
        expect(result.hideTimePerPixel).toBeLessThan(0.01); // Less than 0.01ms per pixel
        expect(result.revealTimePerPixel).toBeLessThan(0.005); // Less than 0.005ms per pixel
      });
    }, 60000);

    test('should benchmark message size impact on performance', async () => {
      const image = createTestImage(512, 512);
      const messageSizes = [10, 50, 100, 500, 1000, 5000, 10000];
      const performanceResults = [];
      
      for (const size of messageSizes) {
        const message = 'A'.repeat(size);
        
        const hideStartTime = process.hrtime.bigint();
        const stegoImage = await stego.hideMessage(image, message);
        const hideEndTime = process.hrtime.bigint();
        const hideTime = Number(hideEndTime - hideStartTime) / 1000000;
        
        const revealStartTime = process.hrtime.bigint();
        const revealed = await stego.revealMessage(stegoImage);
        const revealEndTime = process.hrtime.bigint();
        const revealTime = Number(revealEndTime - revealStartTime) / 1000000;
        
        expect(revealed).toBe(message);
        
        performanceResults.push({
          messageSize: size,
          hideTime,
          revealTime,
          totalTime: hideTime + revealTime,
          hideTimePerByte: hideTime / size,
          revealTimePerByte: revealTime / size
        });
        
        console.log(`Message size ${size}: Hide ${hideTime.toFixed(2)}ms, Reveal ${revealTime.toFixed(2)}ms`);
      }
      
      // Performance should scale reasonably with message size
      performanceResults.forEach(result => {
        expect(result.totalTime).toBeLessThan(5000); // Less than 5 seconds
        expect(result.hideTimePerByte).toBeLessThan(1); // Less than 1ms per byte
      });
    }, 30000);

    test('should benchmark concurrent operations performance', async () => {
      const concurrencyLevels = [1, 2, 5, 10, 20];
      const image = createTestImage(256, 256);
      const message = 'Concurrent performance test';
      
      const concurrencyResults = [];
      
      for (const concurrency of concurrencyLevels) {
        const startTime = process.hrtime.bigint();
        
        // Create concurrent operations
        const operations = Array.from({ length: concurrency }, async (_, i) => {
          const testMessage = `${message} ${i}`;
          const stegoImage = await stego.hideMessage(image, testMessage);
          const revealed = await stego.revealMessage(stegoImage);
          return revealed === testMessage;
        });
        
        const results = await Promise.all(operations);
        const endTime = process.hrtime.bigint();
        const totalTime = Number(endTime - startTime) / 1000000;
        
        // All operations should succeed
        expect(results.every(success => success)).toBe(true);
        
        concurrencyResults.push({
          concurrency,
          totalTime,
          averageTimePerOperation: totalTime / concurrency,
          operationsPerSecond: (concurrency * 1000) / totalTime
        });
        
        console.log(`Concurrency ${concurrency}: ${totalTime.toFixed(2)}ms total, ${(totalTime/concurrency).toFixed(2)}ms avg`);
      }
      
      // Higher concurrency should not dramatically degrade performance
      const baselineTime = concurrencyResults[0].averageTimePerOperation;
      concurrencyResults.forEach(result => {
        const degradationFactor = result.averageTimePerOperation / baselineTime;
        expect(degradationFactor).toBeLessThan(5); // Less than 5x degradation
      });
    }, 45000);
  });

  describe('Stress Testing', () => {
    test('should handle sustained high-volume operations', async () => {
      const image = createTestImage(256, 256);
      const operationCount = 100;
      const message = 'Sustained load test message';
      
      const startTime = Date.now();
      let successCount = 0;
      let errorCount = 0;
      const operationTimes = [];
      
      for (let i = 0; i < operationCount; i++) {
        try {
          const opStartTime = process.hrtime.bigint();
          
          const testMessage = `${message} ${i}`;
          const stegoImage = await stego.hideMessage(image, testMessage);
          const revealed = await stego.revealMessage(stegoImage);
          
          const opEndTime = process.hrtime.bigint();
          const opTime = Number(opEndTime - opStartTime) / 1000000;
          operationTimes.push(opTime);
          
          if (revealed === testMessage) {
            successCount++;
          } else {
            errorCount++;
          }
        } catch (error) {
          errorCount++;
        }
        
        // Log progress every 20 operations
        if ((i + 1) % 20 === 0) {
          console.log(`Completed ${i + 1}/${operationCount} operations`);
        }
      }
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      // Calculate statistics
      const avgOperationTime = operationTimes.reduce((sum, time) => sum + time, 0) / operationTimes.length;
      const minOperationTime = Math.min(...operationTimes);
      const maxOperationTime = Math.max(...operationTimes);
      const throughput = (operationCount * 1000) / totalTime; // Operations per second
      
      console.log(`Stress Test Results:`);
      console.log(`  Total operations: ${operationCount}`);
      console.log(`  Successful: ${successCount}`);
      console.log(`  Errors: ${errorCount}`);
      console.log(`  Total time: ${totalTime}ms`);
      console.log(`  Throughput: ${throughput.toFixed(2)} ops/sec`);
      console.log(`  Avg operation time: ${avgOperationTime.toFixed(2)}ms`);
      console.log(`  Min/Max operation time: ${minOperationTime.toFixed(2)}ms / ${maxOperationTime.toFixed(2)}ms`);
      
      // Stress test assertions
      expect(successCount).toBeGreaterThan(operationCount * 0.95); // 95% success rate
      expect(errorCount).toBeLessThan(operationCount * 0.05); // Less than 5% errors
      expect(throughput).toBeGreaterThan(1); // At least 1 operation per second
      expect(avgOperationTime).toBeLessThan(1000); // Average less than 1 second
    }, 120000);

    test('should handle memory pressure scenarios', async () => {
      const imageSizes = [
        [512, 512],
        [1024, 1024],
        [1024, 1024],
        [1024, 1024],
        [1024, 1024]
      ];
      
      const message = 'Memory pressure test message';
      const results = [];
      
      // Track memory usage (simplified)
      const initialMemory = process.memoryUsage();
      
      for (let i = 0; i < imageSizes.length; i++) {
        const [width, height] = imageSizes[i];
        const image = createTestImage(width, height);
        
        const beforeMemory = process.memoryUsage();
        
        try {
          const stegoImage = await stego.hideMessage(image, `${message} ${i}`);
          const revealed = await stego.revealMessage(stegoImage);
          
          const afterMemory = process.memoryUsage();
          
          results.push({
            iteration: i,
            success: revealed === `${message} ${i}`,
            memoryDelta: afterMemory.heapUsed - beforeMemory.heapUsed,
            totalMemory: afterMemory.heapUsed
          });
          
          // Force garbage collection if available
          if (global.gc) {
            global.gc();
          }
          
        } catch (error) {
          results.push({
            iteration: i,
            success: false,
            error: error.message
          });
        }
      }
      
      const finalMemory = process.memoryUsage();
      
      console.log('Memory Pressure Test Results:');
      console.log(`  Initial memory: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
      console.log(`  Final memory: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
      console.log(`  Memory growth: ${((finalMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024).toFixed(2)}MB`);
      
      // All operations should succeed
      const successCount = results.filter(r => r.success).length;
      expect(successCount).toBe(imageSizes.length);
      
      // Memory growth should be reasonable
      const memoryGrowth = finalMemory.heapUsed - initialMemory.heapUsed;
      expect(memoryGrowth).toBeLessThan(100 * 1024 * 1024); // Less than 100MB growth
    }, 60000);

    test('should handle rapid session creation and destruction', async () => {
      const sessionCount = 50;
      const image = createTestImage(256, 256);
      const message = 'Session lifecycle test';
      
      const startTime = Date.now();
      let successCount = 0;
      
      for (let i = 0; i < sessionCount; i++) {
        try {
          // Create session
          const session = new OTRSteganographySession({
            testing: true,
            steganographyEnabled: true,
            stegoPassword: `password-${i}`
          });
          
          // Use session
          const stegoImage = await session.sendStegoMessage(`${message} ${i}`, image);
          const revealed = await session.processStegoImage(stegoImage);
          
          if (revealed === `${message} ${i}`) {
            successCount++;
          }
          
          // Destroy session
          session.destroy();
          
        } catch (error) {
          console.warn(`Session ${i} failed:`, error.message);
        }
      }
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      console.log(`Session Lifecycle Test:`);
      console.log(`  Created/destroyed ${sessionCount} sessions`);
      console.log(`  Successful operations: ${successCount}`);
      console.log(`  Total time: ${totalTime}ms`);
      console.log(`  Average time per session: ${(totalTime / sessionCount).toFixed(2)}ms`);
      
      // Most sessions should work correctly
      expect(successCount).toBeGreaterThan(sessionCount * 0.9); // 90% success rate
    }, 60000);
  });

  describe('Resource Usage Analysis', () => {
    test('should analyze CPU usage patterns', async () => {
      const image = createTestImage(512, 512);
      const message = 'CPU usage analysis test';
      const iterations = 20;
      
      const cpuTimes = [];
      
      for (let i = 0; i < iterations; i++) {
        const startCpuUsage = process.cpuUsage();
        
        await stego.hideMessage(image, `${message} ${i}`);
        
        const endCpuUsage = process.cpuUsage(startCpuUsage);
        cpuTimes.push({
          user: endCpuUsage.user / 1000, // Convert to milliseconds
          system: endCpuUsage.system / 1000
        });
      }
      
      const avgUserTime = cpuTimes.reduce((sum, time) => sum + time.user, 0) / iterations;
      const avgSystemTime = cpuTimes.reduce((sum, time) => sum + time.system, 0) / iterations;
      
      console.log(`CPU Usage Analysis:`);
      console.log(`  Average user time: ${avgUserTime.toFixed(2)}ms`);
      console.log(`  Average system time: ${avgSystemTime.toFixed(2)}ms`);
      console.log(`  Total average CPU time: ${(avgUserTime + avgSystemTime).toFixed(2)}ms`);
      
      // CPU usage should be reasonable
      expect(avgUserTime + avgSystemTime).toBeLessThan(100); // Less than 100ms CPU time
    }, 30000);

    test('should track mock performance metrics', async () => {
      const image = createTestImage(256, 256);
      const message = 'Metrics tracking test';
      const operationCount = 10;
      
      // Reset metrics
      mockStego.__resetMetrics();
      
      // Perform operations
      for (let i = 0; i < operationCount; i++) {
        const stegoImage = await stego.hideMessage(image, `${message} ${i}`);
        await stego.revealMessage(stegoImage);
      }
      
      // Get metrics
      const metrics = mockStego.__getMetrics();
      
      console.log('Mock Performance Metrics:', metrics);
      
      // Verify metrics
      expect(metrics.encodeOperations).toBe(operationCount);
      expect(metrics.decodeOperations).toBe(operationCount);
      expect(metrics.totalEncodeTime).toBeGreaterThan(0);
      expect(metrics.totalDecodeTime).toBeGreaterThan(0);
      expect(metrics.peakMemoryUsage).toBeGreaterThan(0);
      
      // Calculate averages
      const avgEncodeTime = metrics.totalEncodeTime / metrics.encodeOperations;
      const avgDecodeTime = metrics.totalDecodeTime / metrics.decodeOperations;
      
      expect(avgEncodeTime).toBeLessThan(1000); // Less than 1 second average
      expect(avgDecodeTime).toBeLessThan(500);  // Less than 0.5 second average
    });
  });

  // Helper functions
  function createTestImage(width, height) {
    const data = new Uint8ClampedArray(width * height * 4);
    
    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.floor(Math.random() * 256);     // Red
      data[i + 1] = Math.floor(Math.random() * 256); // Green
      data[i + 2] = Math.floor(Math.random() * 256); // Blue
      data[i + 3] = 255;                             // Alpha
    }
    
    return { data, width, height };
  }

  function analyzePerfomanceScaling(results) {
    if (results.length < 2) return null;
    
    const pixelCounts = results.map(r => r.pixelCount);
    const hideTimes = results.map(r => r.hideTime);
    const revealTimes = results.map(r => r.revealTime);
    
    // Calculate scaling factors
    const hideScaling = calculateScalingFactor(pixelCounts, hideTimes);
    const revealScaling = calculateScalingFactor(pixelCounts, revealTimes);
    
    return {
      hideTimeScaling: hideScaling,
      revealTimeScaling: revealScaling,
      efficiency: hideScaling < 2 && revealScaling < 2 ? 'Good' : 'Poor'
    };
  }

  function calculateScalingFactor(inputs, outputs) {
    if (inputs.length < 2) return 1;
    
    const firstRatio = outputs[1] / outputs[0];
    const inputRatio = inputs[1] / inputs[0];
    
    return firstRatio / inputRatio;
  }
});
