/**
 * Security-Focused Steganography Test Suite
 * Tests for cryptographic security, attack resistance, and privacy protection
 */

const { SteganographyOTR, STEGO_MESSAGE_TYPE } = require('../../../src/core/steganography');
const { OTRSteganographySession } = require('../../../src/core/steganography/otr-stego-session');

// Mock stego-js with security testing capabilities
jest.mock('@masknet/stego-js', () => {
  const hiddenDataStore = new Map();
  const accessLog = [];
  
  return {
    encode: jest.fn().mockImplementation(async (imageData, data) => {
      accessLog.push({ operation: 'encode', timestamp: Date.now(), dataLength: data.length });
      
      const imageKey = `${imageData.width}x${imageData.height}_${Date.now()}_${Math.random()}`;
      hiddenDataStore.set(imageKey, data);
      
      return {
        ...imageData,
        _hiddenData: data,
        _imageKey: imageKey,
        _timestamp: Date.now()
      };
    }),
    decode: jest.fn().mockImplementation(async (imageData) => {
      accessLog.push({ operation: 'decode', timestamp: Date.now() });
      
      if (imageData && imageData._hiddenData) {
        return imageData._hiddenData;
      }
      
      if (imageData && imageData._imageKey && hiddenDataStore.has(imageData._imageKey)) {
        return hiddenDataStore.get(imageData._imageKey);
      }
      
      return null;
    }),
    // Security testing utilities
    __getAccessLog: () => accessLog,
    __clearAccessLog: () => { accessLog.length = 0; },
    __getStoredData: () => Array.from(hiddenDataStore.values()),
    __clearStorage: () => hiddenDataStore.clear()
  };
});

describe('Security-Focused Steganography Tests', () => {
  let stego;
  const mockStego = require('@masknet/stego-js');

  beforeEach(() => {
    stego = new SteganographyOTR({
      quality: 0.95,
      password: 'security-test-password-123',
      compression: true
    });
    mockStego.__clearAccessLog();
    mockStego.__clearStorage();
  });

  describe('Cryptographic Security', () => {
    test('should use different passwords for different instances', async () => {
      const image = createTestImage(512, 512);
      const message = 'Password isolation test';
      
      const stego1 = new SteganographyOTR({ password: 'password1' });
      const stego2 = new SteganographyOTR({ password: 'password2' });
      const stego3 = new SteganographyOTR({ password: 'password1' }); // Same as stego1
      
      // Hide message with password1
      const stegoImage = await stego1.hideMessage(image, message);
      
      // Should reveal correctly with same password
      const revealed1 = await stego1.revealMessage(stegoImage);
      expect(revealed1).toBe(message);
      
      const revealed3 = await stego3.revealMessage(stegoImage);
      expect(revealed3).toBe(message);
      
      // Should fail or return different data with different password
      try {
        const revealed2 = await stego2.revealMessage(stegoImage);
        expect(revealed2).not.toBe(message);
      } catch (error) {
        // Password mismatch should cause failure
        expect(error).toBeDefined();
      }
    });

    test('should generate different outputs for same input with different passwords', async () => {
      const image = createTestImage(256, 256);
      const message = 'Same message, different passwords';
      
      const stego1 = new SteganographyOTR({ password: 'password1' });
      const stego2 = new SteganographyOTR({ password: 'password2' });
      
      const stegoImage1 = await stego1.hideMessage(image, message);
      const stegoImage2 = await stego2.hideMessage(image, message);
      
      // Images should be different (different encryption)
      expect(stegoImage1._hiddenData).not.toBe(stegoImage2._hiddenData);
    });

    test('should resist brute force password attacks', async () => {
      const image = createTestImage(512, 512);
      const message = 'Brute force resistance test';
      const correctPassword = 'correct-password-123';
      
      const stegoWithPassword = new SteganographyOTR({ password: correctPassword });
      const stegoImage = await stegoWithPassword.hideMessage(image, message);
      
      // Try common weak passwords
      const weakPasswords = [
        '', '123', 'password', 'admin', 'test', '12345', 'qwerty',
        'abc123', 'password123', 'admin123', 'letmein', 'welcome'
      ];
      
      let successfulCracks = 0;
      
      for (const weakPassword of weakPasswords) {
        const stegoWithWeak = new SteganographyOTR({ password: weakPassword });
        
        try {
          const revealed = await stegoWithWeak.revealMessage(stegoImage);
          if (revealed === message) {
            successfulCracks++;
          }
        } catch (error) {
          // Failed to crack, which is good
        }
      }
      
      // Should not be crackable with weak passwords
      expect(successfulCracks).toBe(0);
    });

    test('should maintain message confidentiality', async () => {
      const image = createTestImage(512, 512);
      const sensitiveMessage = 'TOP SECRET: Nuclear launch codes are 12345';
      
      const stegoImage = await stego.hideMessage(image, sensitiveMessage);
      
      // Verify that sensitive data is not visible in the stego image structure
      const imageString = JSON.stringify(stegoImage);
      expect(imageString).not.toContain('TOP SECRET');
      expect(imageString).not.toContain('Nuclear');
      expect(imageString).not.toContain('12345');
      
      // Verify that the hidden data is encrypted/obfuscated
      if (stegoImage._hiddenData) {
        expect(stegoImage._hiddenData).not.toContain('TOP SECRET');
        expect(stegoImage._hiddenData).not.toContain('Nuclear');
      }
    });
  });

  describe('Attack Resistance', () => {
    test('should resist statistical analysis attacks', async () => {
      const image = createTestImage(512, 512);
      const messages = [
        'Message 1 with different content',
        'Another message with completely different text',
        'Third message using various words and phrases',
        'Fourth message containing unique information',
        'Fifth message with distinct characteristics'
      ];
      
      const stegoImages = [];
      
      for (const message of messages) {
        const stegoImage = await stego.hideMessage(image, message);
        stegoImages.push(stegoImage);
      }
      
      // Verify that different messages produce different hidden data patterns
      const hiddenDataPatterns = stegoImages.map(img => img._hiddenData);
      const uniquePatterns = new Set(hiddenDataPatterns);
      
      expect(uniquePatterns.size).toBe(messages.length);
    });

    test('should resist timing attacks', async () => {
      const image = createTestImage(256, 256);
      const timings = [];
      
      // Test with messages of different lengths
      const messageLengths = [10, 50, 100, 200, 500];
      
      for (const length of messageLengths) {
        const message = 'A'.repeat(length);
        
        const startTime = process.hrtime.bigint();
        await stego.hideMessage(image, message);
        const endTime = process.hrtime.bigint();
        
        const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
        timings.push({ length, duration });
      }
      
      // Timing should not reveal message length patterns
      // (In a real implementation, constant-time operations would be used)
      console.log('Timing analysis:', timings);
      
      // For now, just verify operations complete in reasonable time
      timings.forEach(timing => {
        expect(timing.duration).toBeLessThan(1000); // Less than 1 second
      });
    });

    test('should resist frequency analysis attacks', async () => {
      const image = createTestImage(512, 512);
      
      // Create messages with different character frequency patterns
      const messages = [
        'aaaaaaaaaa', // High frequency of 'a'
        'eeeeeeeeee', // High frequency of 'e'
        'abcdefghij', // Uniform distribution
        '1234567890', // Numbers only
        '!@#$%^&*()', // Special characters only
      ];
      
      const stegoImages = [];
      
      for (const message of messages) {
        const stegoImage = await stego.hideMessage(image, message);
        stegoImages.push(stegoImage);
      }
      
      // Verify that character frequency doesn't leak through
      const hiddenDataSamples = stegoImages.map(img => img._hiddenData);
      
      // Each hidden data should be different despite similar input patterns
      const uniqueHiddenData = new Set(hiddenDataSamples);
      expect(uniqueHiddenData.size).toBe(messages.length);
    });

    test('should resist known plaintext attacks', async () => {
      const image = createTestImage(512, 512);
      const knownMessage = 'This is a known plaintext message for testing';
      
      // Attacker knows the plaintext and has the stego image
      const stegoImage = await stego.hideMessage(image, knownMessage);
      
      // Attacker tries to use known plaintext to break other messages
      const unknownMessage = 'This is the secret message we want to protect';
      const secretStegoImage = await stego.hideMessage(image, unknownMessage);
      
      // Having the known plaintext/ciphertext pair should not help
      // decrypt other messages (this is a simplified test)
      expect(stegoImage._hiddenData).not.toBe(secretStegoImage._hiddenData);
      
      // Verify the known message can still be decrypted correctly
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(knownMessage);
    });
  });

  describe('Privacy Protection', () => {
    test('should not leak metadata in stego images', async () => {
      const image = createTestImage(512, 512);
      const message = 'Privacy test message';
      
      const stegoImage = await stego.hideMessage(image, message, {
        type: STEGO_MESSAGE_TYPE.OTR_MESSAGE,
        sender: '<EMAIL>',
        recipient: '<EMAIL>',
        timestamp: Date.now()
      });
      
      // Verify that metadata is not exposed in the image structure
      const imageString = JSON.stringify(stegoImage);
      expect(imageString).not.toContain('<EMAIL>');
      expect(imageString).not.toContain('<EMAIL>');
      expect(imageString).not.toContain('OTR_MESSAGE');
    });

    test('should provide plausible deniability', async () => {
      const image = createTestImage(512, 512);
      const message = 'Deniable message content';
      
      const stegoImage = await stego.hideMessage(image, message);
      
      // A stego image should be indistinguishable from a normal image
      // without the password and knowledge of steganography
      
      // Verify basic image properties are preserved
      expect(stegoImage.width).toBe(image.width);
      expect(stegoImage.height).toBe(image.height);
      expect(stegoImage.data.length).toBe(image.data.length);
      
      // Verify no obvious steganography markers
      const imageString = JSON.stringify(stegoImage);
      expect(imageString).not.toContain('stego');
      expect(imageString).not.toContain('hidden');
      expect(imageString).not.toContain('secret');
      expect(imageString).not.toContain('encrypted');
    });

    test('should resist steganalysis detection', async () => {
      const originalImage = createTestImage(512, 512, 'gradient');
      const message = 'Steganalysis resistance test';
      
      const stegoImage = await stego.hideMessage(originalImage, message);
      
      // Simple statistical tests that steganalysis might use
      
      // 1. Chi-square test simulation (simplified)
      const originalStats = calculateImageStats(originalImage);
      const stegoStats = calculateImageStats(stegoImage);
      
      // Statistical properties should be similar
      expect(Math.abs(originalStats.mean - stegoStats.mean)).toBeLessThan(10);
      expect(Math.abs(originalStats.variance - stegoStats.variance)).toBeLessThan(1000);
      
      // 2. Histogram analysis
      const originalHist = calculateHistogram(originalImage);
      const stegoHist = calculateHistogram(stegoImage);
      
      // Histograms should be similar
      const histogramDifference = calculateHistogramDifference(originalHist, stegoHist);
      expect(histogramDifference).toBeLessThan(0.1); // Less than 10% difference
    });

    test('should handle forensic analysis scenarios', async () => {
      const image = createTestImage(512, 512);
      const message = 'Forensic analysis test message';
      
      const stegoImage = await stego.hideMessage(image, message);
      
      // Simulate forensic analysis attempts
      
      // 1. File signature analysis
      // Stego image should still appear as a valid image
      expect(stegoImage.data).toBeInstanceOf(Uint8ClampedArray);
      expect(stegoImage.width).toBeGreaterThan(0);
      expect(stegoImage.height).toBeGreaterThan(0);
      
      // 2. Entropy analysis
      const entropy = calculateEntropy(stegoImage.data);
      expect(entropy).toBeGreaterThan(0);
      expect(entropy).toBeLessThan(8); // Should not be perfectly random
      
      // 3. Pattern analysis
      // Should not have obvious repeating patterns
      const patterns = findRepeatingPatterns(stegoImage.data);
      expect(patterns.length).toBeLessThan(10); // Minimal obvious patterns
    });
  });

  describe('OTR Integration Security', () => {
    test('should maintain OTR security properties with steganography', async () => {
      const alice = new OTRSteganographySession({
        testing: true,
        steganographyEnabled: true,
        stegoPassword: 'shared-secret'
      });
      
      const bob = new OTRSteganographySession({
        testing: true,
        steganographyEnabled: true,
        stegoPassword: 'shared-secret'
      });
      
      const image = createTestImage(512, 512);
      const sensitiveMessage = 'Classified information: Operation Moonlight at 0300 hours';
      
      // Alice sends message to Bob
      const stegoImage = await alice.sendStegoMessage(sensitiveMessage, image);
      
      // Verify message is encrypted in transit
      expect(stegoImage._hiddenData).not.toContain('Classified');
      expect(stegoImage._hiddenData).not.toContain('Moonlight');
      expect(stegoImage._hiddenData).not.toContain('0300');
      
      // Bob receives and decrypts
      const receivedMessage = await bob.processStegoImage(stegoImage);
      expect(receivedMessage).toBe(sensitiveMessage);
      
      // Cleanup
      alice.destroy();
      bob.destroy();
    });

    test('should prevent session key leakage through steganography', async () => {
      const alice = new OTRSteganographySession({
        testing: true,
        steganographyEnabled: true
      });
      
      const image = createTestImage(512, 512);
      const message = 'Key leakage test message';
      
      const stegoImage = await alice.sendStegoMessage(message, image);
      
      // Verify that session keys are not exposed
      const imageString = JSON.stringify(stegoImage);
      expect(imageString).not.toMatch(/[A-Fa-f0-9]{32,}/); // No long hex strings (keys)
      
      alice.destroy();
    });
  });

  // Helper functions for security testing
  function createTestImage(width, height, pattern = 'random') {
    const data = new Uint8ClampedArray(width * height * 4);
    
    if (pattern === 'gradient') {
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const i = (y * width + x) * 4;
          data[i] = (x / width) * 255;
          data[i + 1] = (y / height) * 255;
          data[i + 2] = 128;
          data[i + 3] = 255;
        }
      }
    } else {
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.floor(Math.random() * 256);
        data[i + 1] = Math.floor(Math.random() * 256);
        data[i + 2] = Math.floor(Math.random() * 256);
        data[i + 3] = 255;
      }
    }
    
    return { data, width, height };
  }

  function calculateImageStats(image) {
    let sum = 0;
    let sumSquares = 0;
    const count = image.data.length;
    
    for (let i = 0; i < count; i++) {
      sum += image.data[i];
      sumSquares += image.data[i] * image.data[i];
    }
    
    const mean = sum / count;
    const variance = (sumSquares / count) - (mean * mean);
    
    return { mean, variance };
  }

  function calculateHistogram(image) {
    const histogram = new Array(256).fill(0);
    
    for (let i = 0; i < image.data.length; i++) {
      histogram[image.data[i]]++;
    }
    
    return histogram;
  }

  function calculateHistogramDifference(hist1, hist2) {
    let totalDiff = 0;
    let totalCount = 0;
    
    for (let i = 0; i < 256; i++) {
      totalDiff += Math.abs(hist1[i] - hist2[i]);
      totalCount += hist1[i] + hist2[i];
    }
    
    return totalCount > 0 ? totalDiff / totalCount : 0;
  }

  function calculateEntropy(data) {
    const histogram = new Array(256).fill(0);
    
    for (let i = 0; i < data.length; i++) {
      histogram[data[i]]++;
    }
    
    let entropy = 0;
    for (let i = 0; i < 256; i++) {
      if (histogram[i] > 0) {
        const probability = histogram[i] / data.length;
        entropy -= probability * Math.log2(probability);
      }
    }
    
    return entropy;
  }

  function findRepeatingPatterns(data) {
    const patterns = [];
    const patternLength = 4;
    const patternCounts = new Map();
    
    for (let i = 0; i <= data.length - patternLength; i++) {
      const pattern = Array.from(data.slice(i, i + patternLength)).join(',');
      patternCounts.set(pattern, (patternCounts.get(pattern) || 0) + 1);
    }
    
    for (const [pattern, count] of patternCounts) {
      if (count > 5) { // Pattern repeats more than 5 times
        patterns.push({ pattern, count });
      }
    }
    
    return patterns;
  }
});
