/**
 * Edge Cases and Regression Test Suite for Steganography
 * Comprehensive testing for unusual scenarios and known issues
 */

const { SteganographyOTR, STEGO_MESSAGE_TYPE } = require('../../../src/core/steganography');
const { OTRSteganographySession } = require('../../../src/core/steganography/otr-stego-session');

// Mock stego-js with edge case handling
jest.mock('@masknet/stego-js', () => {
  const hiddenDataStore = new Map();
  let edgeCaseMode = null;
  
  return {
    encode: jest.fn().mockImplementation(async (imageData, data) => {
      // Handle various edge cases
      switch (edgeCaseMode) {
        case 'corruption':
          // Simulate data corruption
          const corruptedData = data.split('').map(char => 
            Math.random() < 0.01 ? String.fromCharCode(Math.floor(Math.random() * 256)) : char
          ).join('');
          data = corruptedData;
          break;
          
        case 'truncation':
          // Simulate data truncation
          data = data.substring(0, Math.floor(data.length * 0.9));
          break;
          
        case 'duplication':
          // Simulate data duplication
          data = data + data.substring(0, Math.floor(data.length * 0.1));
          break;
          
        case 'encoding_error':
          throw new Error('Simulated encoding error');
          
        case 'memory_limit':
          if (data.length > 1000) {
            throw new Error('Memory limit exceeded');
          }
          break;
      }
      
      const imageKey = `${imageData.width}x${imageData.height}_${Date.now()}_${Math.random()}`;
      hiddenDataStore.set(imageKey, data);
      
      return {
        ...imageData,
        _hiddenData: data,
        _imageKey: imageKey,
        _edgeCase: edgeCaseMode
      };
    }),
    
    decode: jest.fn().mockImplementation(async (imageData) => {
      if (edgeCaseMode === 'decoding_error') {
        throw new Error('Simulated decoding error');
      }
      
      if (imageData && imageData._hiddenData) {
        return imageData._hiddenData;
      }
      
      if (imageData && imageData._imageKey && hiddenDataStore.has(imageData._imageKey)) {
        return hiddenDataStore.get(imageData._imageKey);
      }
      
      return null;
    }),
    
    // Edge case testing utilities
    __setEdgeCaseMode: (mode) => { edgeCaseMode = mode; },
    __clearEdgeCaseMode: () => { edgeCaseMode = null; },
    __getStoredDataCount: () => hiddenDataStore.size,
    __clearStorage: () => hiddenDataStore.clear()
  };
});

describe('Edge Cases and Regression Tests', () => {
  let stego;
  const mockStego = require('@masknet/stego-js');

  beforeEach(() => {
    stego = new SteganographyOTR({
      quality: 0.95,
      password: 'edge-case-test-password'
    });
    mockStego.__clearEdgeCaseMode();
    mockStego.__clearStorage();
  });

  describe('Boundary Value Testing', () => {
    test('should handle zero-sized images', async () => {
      const zeroImage = {
        data: new Uint8ClampedArray(0),
        width: 0,
        height: 0
      };
      
      await expect(stego.hideMessage(zeroImage, 'test'))
        .rejects.toThrow();
    });

    test('should handle single pixel images', async () => {
      const singlePixelImage = {
        data: new Uint8ClampedArray([255, 0, 0, 255]), // Red pixel
        width: 1,
        height: 1
      };
      
      const message = 'X'; // Single character
      
      try {
        const stegoImage = await stego.hideMessage(singlePixelImage, message);
        const revealed = await stego.revealMessage(stegoImage);
        expect(revealed).toBe(message);
      } catch (error) {
        // Single pixel might not have enough capacity
        expect(error.message).toContain('capacity');
      }
    });

    test('should handle maximum safe integer dimensions', async () => {
      // Use reasonable large dimensions (not actual MAX_SAFE_INTEGER)
      const largeWidth = 10000;
      const largeHeight = 10000;
      
      // Don't actually create the full array (would use too much memory)
      const mockLargeImage = {
        data: new Uint8ClampedArray(100), // Small data for testing
        width: largeWidth,
        height: largeHeight
      };
      
      const message = 'Large dimension test';
      
      try {
        await stego.hideMessage(mockLargeImage, message);
      } catch (error) {
        // Large dimensions might cause issues
        expect(error).toBeDefined();
      }
    });

    test('should handle negative dimensions', async () => {
      const negativeImage = {
        data: new Uint8ClampedArray(400),
        width: -20,
        height: 20
      };
      
      await expect(stego.hideMessage(negativeImage, 'test'))
        .rejects.toThrow();
    });

    test('should handle mismatched data length and dimensions', async () => {
      const mismatchedImage = {
        data: new Uint8ClampedArray(100), // 100 bytes
        width: 10,
        height: 10 // Should be 400 bytes (10*10*4)
      };
      
      await expect(stego.hideMessage(mismatchedImage, 'test'))
        .rejects.toThrow();
    });
  });

  describe('Data Corruption and Recovery', () => {
    test('should handle data corruption during encoding', async () => {
      const image = createTestImage(256, 256);
      const message = 'Corruption test message';
      
      mockStego.__setEdgeCaseMode('corruption');
      
      const stegoImage = await stego.hideMessage(image, message);
      const revealed = await stego.revealMessage(stegoImage);
      
      // Message might be corrupted
      expect(typeof revealed).toBe('string');
      // Might not match exactly due to corruption
    });

    test('should handle data truncation', async () => {
      const image = createTestImage(256, 256);
      const message = 'This message will be truncated during processing';
      
      mockStego.__setEdgeCaseMode('truncation');
      
      const stegoImage = await stego.hideMessage(image, message);
      const revealed = await stego.revealMessage(stegoImage);
      
      // Message should be truncated
      expect(revealed.length).toBeLessThan(message.length);
      expect(message.startsWith(revealed)).toBe(true);
    });

    test('should handle data duplication', async () => {
      const image = createTestImage(256, 256);
      const message = 'Duplication test';
      
      mockStego.__setEdgeCaseMode('duplication');
      
      const stegoImage = await stego.hideMessage(image, message);
      const revealed = await stego.revealMessage(stegoImage);
      
      // Message might have duplicated content
      expect(revealed).toContain(message);
      expect(revealed.length).toBeGreaterThanOrEqual(message.length);
    });

    test('should recover from encoding errors', async () => {
      const image = createTestImage(256, 256);
      const message = 'Encoding error recovery test';
      
      mockStego.__setEdgeCaseMode('encoding_error');
      
      await expect(stego.hideMessage(image, message))
        .rejects.toThrow('Simulated encoding error');
      
      // Clear error mode and retry
      mockStego.__clearEdgeCaseMode();
      
      const stegoImage = await stego.hideMessage(image, message);
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(message);
    });

    test('should recover from decoding errors', async () => {
      const image = createTestImage(256, 256);
      const message = 'Decoding error recovery test';
      
      // First, successfully hide the message
      const stegoImage = await stego.hideMessage(image, message);
      
      // Then simulate decoding error
      mockStego.__setEdgeCaseMode('decoding_error');
      
      await expect(stego.revealMessage(stegoImage))
        .rejects.toThrow('Simulated decoding error');
      
      // Clear error mode and retry
      mockStego.__clearEdgeCaseMode();
      
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(message);
    });
  });

  describe('Memory and Resource Limits', () => {
    test('should handle memory limit scenarios', async () => {
      const image = createTestImage(256, 256);
      const shortMessage = 'Short';
      const longMessage = 'X'.repeat(2000); // Exceeds mock memory limit
      
      mockStego.__setEdgeCaseMode('memory_limit');
      
      // Short message should work
      const shortStegoImage = await stego.hideMessage(image, shortMessage);
      const shortRevealed = await stego.revealMessage(shortStegoImage);
      expect(shortRevealed).toBe(shortMessage);
      
      // Long message should fail
      await expect(stego.hideMessage(image, longMessage))
        .rejects.toThrow('Memory limit exceeded');
    });

    test('should handle resource exhaustion gracefully', async () => {
      const image = createTestImage(128, 128);
      const message = 'Resource exhaustion test';
      
      // Simulate multiple rapid operations
      const operations = Array.from({ length: 100 }, (_, i) => 
        stego.hideMessage(image, `${message} ${i}`)
      );
      
      try {
        const results = await Promise.all(operations);
        // If all succeed, that's good
        expect(results.length).toBe(100);
      } catch (error) {
        // Some operations might fail due to resource limits
        expect(error).toBeDefined();
      }
    });
  });

  describe('Unicode and Character Encoding Edge Cases', () => {
    test('should handle null characters', async () => {
      const image = createTestImage(256, 256);
      const messageWithNull = 'Before\x00After';
      
      const stegoImage = await stego.hideMessage(image, messageWithNull);
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(messageWithNull);
    });

    test('should handle control characters', async () => {
      const image = createTestImage(256, 256);
      const controlChars = '\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F';
      
      const stegoImage = await stego.hideMessage(image, controlChars);
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(controlChars);
    });

    test('should handle high Unicode characters', async () => {
      const image = createTestImage(256, 256);
      const unicodeMessage = '𝕌𝕟𝕚𝕔𝕠𝕕𝕖 𝕞𝕒𝕥𝕙 𝕤𝕪𝕞𝕓𝕠𝕝𝕤: 𝔸𝔹ℂ𝔻𝔼𝔽𝔾ℍ𝕀𝕁𝕂𝕃𝕄ℕ𝕆ℙℚℝ𝕊𝕋𝕌𝕍𝕎𝕏𝕐ℤ';
      
      const stegoImage = await stego.hideMessage(image, unicodeMessage);
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(unicodeMessage);
    });

    test('should handle mixed encoding scenarios', async () => {
      const image = createTestImage(256, 256);
      const mixedMessage = 'ASCII text 中文 العربية русский 日本語 🌍🚀💻';
      
      const stegoImage = await stego.hideMessage(image, mixedMessage);
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(mixedMessage);
    });

    test('should handle byte order mark (BOM)', async () => {
      const image = createTestImage(256, 256);
      const bomMessage = '\uFEFFBOM test message';
      
      const stegoImage = await stego.hideMessage(image, bomMessage);
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(bomMessage);
    });
  });

  describe('Concurrency and Race Condition Tests', () => {
    test('should handle concurrent access to same image', async () => {
      const image = createTestImage(256, 256);
      const messages = ['Message 1', 'Message 2', 'Message 3'];
      
      // Try to hide different messages in the same image concurrently
      const operations = messages.map(async (message, i) => {
        try {
          const stegoImage = await stego.hideMessage(image, message);
          const revealed = await stego.revealMessage(stegoImage);
          return { success: true, original: message, revealed };
        } catch (error) {
          return { success: false, error: error.message };
        }
      });
      
      const results = await Promise.all(operations);
      
      // At least some operations should succeed
      const successCount = results.filter(r => r.success).length;
      expect(successCount).toBeGreaterThan(0);
    });

    test('should handle rapid session creation/destruction', async () => {
      const image = createTestImage(256, 256);
      const message = 'Rapid session test';
      
      const sessionOperations = Array.from({ length: 20 }, async (_, i) => {
        const session = new OTRSteganographySession({
          testing: true,
          steganographyEnabled: true,
          stegoPassword: `password-${i}`
        });
        
        try {
          const stegoImage = await session.sendStegoMessage(message, image);
          const revealed = await session.processStegoImage(stegoImage);
          session.destroy();
          return revealed === message;
        } catch (error) {
          session.destroy();
          return false;
        }
      });
      
      const results = await Promise.all(sessionOperations);
      const successCount = results.filter(success => success).length;
      
      // Most operations should succeed
      expect(successCount).toBeGreaterThan(15);
    });
  });

  describe('Regression Tests for Known Issues', () => {
    test('should handle empty password edge case', async () => {
      const stegoWithEmptyPassword = new SteganographyOTR({ password: '' });
      const image = createTestImage(256, 256);
      const message = 'Empty password test';
      
      const stegoImage = await stegoWithEmptyPassword.hideMessage(image, message);
      const revealed = await stegoWithEmptyPassword.revealMessage(stegoImage);
      expect(revealed).toBe(message);
    });

    test('should handle undefined password edge case', async () => {
      const stegoWithUndefinedPassword = new SteganographyOTR({ password: undefined });
      const image = createTestImage(256, 256);
      const message = 'Undefined password test';
      
      const stegoImage = await stegoWithUndefinedPassword.hideMessage(image, message);
      const revealed = await stegoWithUndefinedPassword.revealMessage(stegoImage);
      expect(revealed).toBe(message);
    });

    test('should handle quality edge values', async () => {
      const image = createTestImage(256, 256);
      const message = 'Quality edge test';
      const edgeQualities = [0, 0.001, 0.999, 1.0, 1.1, -0.1];
      
      for (const quality of edgeQualities) {
        try {
          const stegoWithQuality = new SteganographyOTR({ quality });
          const stegoImage = await stegoWithQuality.hideMessage(image, message);
          const revealed = await stegoWithQuality.revealMessage(stegoImage);
          expect(revealed).toBe(message);
        } catch (error) {
          // Some edge values might cause errors, which is acceptable
          expect(error).toBeDefined();
        }
      }
    });

    test('should handle circular reference in message data', async () => {
      const image = createTestImage(256, 256);
      
      // Create object with circular reference
      const circularObj = { name: 'test' };
      circularObj.self = circularObj;
      
      try {
        const circularMessage = JSON.stringify(circularObj);
        // This should fail due to circular reference
        expect(false).toBe(true); // Should not reach here
      } catch (error) {
        // Expected to fail
        expect(error.message).toContain('circular');
      }
    });

    test('should handle very long message edge case', async () => {
      const image = createTestImage(1024, 1024);
      const capacity = await stego.calculateCapacity(image);
      
      // Create message just under capacity
      const nearCapacityMessage = 'X'.repeat(Math.floor(capacity * 0.9));
      
      const stegoImage = await stego.hideMessage(image, nearCapacityMessage);
      const revealed = await stego.revealMessage(stegoImage);
      expect(revealed).toBe(nearCapacityMessage);
    });
  });

  describe('Platform-Specific Edge Cases', () => {
    test('should handle different endianness scenarios', async () => {
      const image = createTestImage(256, 256);
      const message = 'Endianness test message';
      
      // Simulate different byte orders by manipulating image data
      const littleEndianImage = { ...image };
      const bigEndianImage = { ...image };
      bigEndianImage.data = new Uint8ClampedArray(image.data);
      
      // Swap bytes to simulate different endianness
      for (let i = 0; i < bigEndianImage.data.length; i += 4) {
        const temp = bigEndianImage.data[i];
        bigEndianImage.data[i] = bigEndianImage.data[i + 3];
        bigEndianImage.data[i + 3] = temp;
      }
      
      const littleEndianStego = await stego.hideMessage(littleEndianImage, message);
      const bigEndianStego = await stego.hideMessage(bigEndianImage, message);
      
      const littleEndianRevealed = await stego.revealMessage(littleEndianStego);
      const bigEndianRevealed = await stego.revealMessage(bigEndianStego);
      
      expect(littleEndianRevealed).toBe(message);
      expect(bigEndianRevealed).toBe(message);
    });

    test('should handle floating point precision edge cases', async () => {
      const image = createTestImage(256, 256);
      const message = 'Floating point precision test';
      
      // Test with various quality values that might cause precision issues
      const precisionQualities = [
        0.1, 0.33333333, 0.66666666, 0.9999999, 
        1/3, 2/3, Math.PI/4, Math.E/3
      ];
      
      for (const quality of precisionQualities) {
        const stegoWithPrecision = new SteganographyOTR({ quality });
        
        const stegoImage = await stegoWithPrecision.hideMessage(image, message);
        const revealed = await stegoWithPrecision.revealMessage(stegoImage);
        expect(revealed).toBe(message);
      }
    });
  });

  // Helper function to create test images
  function createTestImage(width, height) {
    const data = new Uint8ClampedArray(width * height * 4);
    
    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.floor(Math.random() * 256);     // Red
      data[i + 1] = Math.floor(Math.random() * 256); // Green
      data[i + 2] = Math.floor(Math.random() * 256); // Blue
      data[i + 3] = 255;                             // Alpha
    }
    
    return { data, width, height };
  }
});
