/**
 * OTR Steganography Session Tests
 * Tests for integrated OTR + steganography functionality
 */

import { OTRSteganographySession } from '../../../src/core/steganography/otr-stego-session';
import { STEGO_MESSAGE_TYPE } from '../../../src/core/steganography';

// Mock stego-js
jest.mock('@masknet/stego-js', () => {
  let storedData = null;
  
  return {
    encode: jest.fn().mockImplementation(async (image, data) => {
      storedData = data;
      return {
        data: new Uint8ClampedArray(1024 * 1024 * 4),
        width: 1024,
        height: 1024,
        _hiddenData: data
      };
    }),
    decode: jest.fn().mockImplementation(async (image) => {
      if (image && image._hiddenData) {
        return image._hiddenData;
      }
      return null;
    })
  };
});

describe('OTRSteganographySession', () => {
  let stegoSession;
  let mockImage;

  beforeEach(() => {
    stegoSession = new OTRSteganographySession({
      testing: true,
      steganographyEnabled: true
    });
    
    mockImage = {
      data: new Uint8ClampedArray(1024 * 1024 * 4),
      width: 1024,
      height: 1024
    };
  });

  afterEach(() => {
    if (stegoSession) {
      stegoSession.destroy();
    }
  });

  describe('Session Integration', () => {
    test('should create steganography session successfully', () => {
      expect(stegoSession).toBeInstanceOf(OTRSteganographySession);
      expect(stegoSession.stego).toBeDefined();
      expect(stegoSession.stegoState).toBeDefined();
      expect(stegoSession.stegoState.enabled).toBe(true);
    });

    test('should enable and disable steganography', () => {
      stegoSession.disableSteganography();
      expect(stegoSession.stegoState.enabled).toBe(false);
      
      stegoSession.enableSteganography();
      expect(stegoSession.stegoState.enabled).toBe(true);
    });

    test('should provide steganography statistics', () => {
      const stats = stegoSession.getStegoStats();
      expect(stats).toBeDefined();
      expect(stats.enabled).toBe(true);
      expect(stats.autoDetect).toBeDefined();
      expect(stats.cachedImages).toBeDefined();
      expect(stats.quality).toBeDefined();
    });
  });

  describe('Message Steganography', () => {
    test('should send and receive steganography messages', async () => {
      const plaintext = 'Hello, secret world!';
      
      // Send message via steganography
      const stegoImage = await stegoSession.sendStegoMessage(plaintext, mockImage);
      expect(stegoImage).toBeDefined();
      expect(stegoImage.data).toBeInstanceOf(Uint8ClampedArray);
      
      // Receive message from steganography
      const decryptedMessage = await stegoSession.processStegoImage(stegoImage);
      expect(decryptedMessage).toBe(plaintext);
    });

    test('should handle message encryption/decryption in testing mode', async () => {
      const testMessage = 'Test message for steganography';
      
      const stegoImage = await stegoSession.sendStegoMessage(testMessage, mockImage);
      const result = await stegoSession.processStegoImage(stegoImage);
      
      expect(result).toBe(testMessage);
    });

    test('should return null for images without hidden data', async () => {
      const cleanImage = {
        data: new Uint8ClampedArray(512 * 512 * 4),
        width: 512,
        height: 512
      };
      
      const result = await stegoSession.processStegoImage(cleanImage);
      expect(result).toBeNull();
    });

    test('should handle different message types', async () => {
      const messages = [
        'Short message',
        'A much longer message that contains more text and should still work correctly',
        '🔒 Message with emoji and special characters! @#$%^&*()',
        'Message\nwith\nnewlines\nand\ttabs'
      ];
      
      for (const message of messages) {
        const stegoImage = await stegoSession.sendStegoMessage(message, mockImage);
        const result = await stegoSession.processStegoImage(stegoImage);
        expect(result).toBe(message);
      }
    });
  });

  describe('AKE Steganography', () => {
    test('should send and process AKE messages via steganography', async () => {
      const akeMessage = {
        type: 'DH_COMMIT',
        instanceTag: 12345,
        dhCommit: 'mock_commit_data',
        protocolVersion: 3
      };
      
      // Send AKE via steganography
      const stegoImage = await stegoSession.sendStegoAKE(akeMessage, mockImage);
      expect(stegoImage).toBeDefined();
      
      // Process AKE from steganography
      const processedAKE = await stegoSession.processStegoAKE(stegoImage);
      expect(processedAKE).toEqual(akeMessage);
    });

    test('should validate AKE message format', async () => {
      const invalidAKE = {
        invalidField: 'test'
      };
      
      const stegoImage = await stegoSession.sendStegoAKE(invalidAKE, mockImage);
      const result = await stegoSession.processStegoAKE(stegoImage);
      
      // Should return null for invalid AKE message
      expect(result).toBeNull();
    });

    test('should handle AKE processing errors gracefully', async () => {
      const corruptedImage = {
        data: new Uint8ClampedArray(100),
        width: 10,
        height: 10,
        _hiddenData: 'invalid json data'
      };
      
      const result = await stegoSession.processStegoAKE(corruptedImage);
      expect(result).toBeNull();
    });
  });

  describe('Auto-Detection', () => {
    test('should auto-detect and process images with hidden messages', async () => {
      const testMessage = 'Auto-detected message';
      
      // Create stego image
      const stegoImage = await stegoSession.sendStegoMessage(testMessage, mockImage);
      
      // Auto-process should detect and extract the message
      const result = await stegoSession.autoProcessImage(stegoImage);
      expect(result).toBeDefined();
      expect(result.type).toBe('message');
      expect(result.content).toBe(testMessage);
    });

    test('should auto-detect AKE messages', async () => {
      const akeMessage = {
        type: 'DH_KEY',
        instanceTag: 67890
      };
      
      const stegoImage = await stegoSession.sendStegoAKE(akeMessage, mockImage);
      const result = await stegoSession.autoProcessImage(stegoImage);
      
      expect(result).toBeDefined();
      expect(result.type).toBe('ake');
      expect(result.content).toEqual(akeMessage);
    });

    test('should return null when auto-detection is disabled', async () => {
      stegoSession.stegoState.autoDetect = false;
      
      const stegoImage = await stegoSession.sendStegoMessage('test', mockImage);
      const result = await stegoSession.autoProcessImage(stegoImage);
      
      expect(result).toBeNull();
    });

    test('should return null for clean images', async () => {
      const cleanImage = {
        data: new Uint8ClampedArray(512 * 512 * 4),
        width: 512,
        height: 512
      };
      
      const result = await stegoSession.autoProcessImage(cleanImage);
      expect(result).toBeNull();
    });
  });

  describe('Cover Image Generation', () => {
    test('should generate cover images based on message size', async () => {
      const messageSize = 1000; // bytes
      
      const coverImage = await stegoSession.generateCoverImage(messageSize, 'gradient');
      expect(coverImage).toBeDefined();
      expect(coverImage.data).toBeInstanceOf(Uint8ClampedArray);
      expect(coverImage.width).toBeGreaterThanOrEqual(512);
      expect(coverImage.height).toBeGreaterThanOrEqual(512);
    });

    test('should generate different cover image styles', async () => {
      const styles = ['noise', 'gradient', 'pattern'];
      
      for (const style of styles) {
        const coverImage = await stegoSession.generateCoverImage(500, style);
        expect(coverImage).toBeDefined();
        expect(coverImage.data).toBeInstanceOf(Uint8ClampedArray);
      }
    });
  });

  describe('Cache Management', () => {
    test('should cache steganography images', async () => {
      const message = 'Cached message';
      
      expect(stegoSession.stegoState.coverImages.size).toBe(0);
      
      await stegoSession.sendStegoMessage(message, mockImage);
      
      expect(stegoSession.stegoState.coverImages.size).toBe(1);
    });

    test('should clear steganography cache', async () => {
      // Add some cached data
      await stegoSession.sendStegoMessage('test1', mockImage);
      await stegoSession.sendStegoMessage('test2', mockImage);
      
      expect(stegoSession.stegoState.coverImages.size).toBe(2);
      
      stegoSession.clearStegoCache();
      
      expect(stegoSession.stegoState.coverImages.size).toBe(0);
    });

    test('should limit cache size', async () => {
      // Send more than 10 messages to trigger cache cleanup
      for (let i = 0; i < 15; i++) {
        await stegoSession.sendStegoMessage(`message ${i}`, mockImage);
      }
      
      // Cache should be limited to 10 entries
      expect(stegoSession.stegoState.coverImages.size).toBe(10);
    });
  });

  describe('Error Handling', () => {
    test('should handle steganography send failures', async () => {
      // Mock encode to fail
      const { encode } = require('@masknet/stego-js');
      encode.mockRejectedValueOnce(new Error('Encoding failed'));
      
      await expect(stegoSession.sendStegoMessage('test', mockImage))
        .rejects.toThrow('Steganography send failed');
    });

    test('should handle steganography processing failures gracefully', async () => {
      const invalidImage = null;
      
      const result = await stegoSession.processStegoImage(invalidImage);
      expect(result).toBeNull();
    });

    test('should handle auto-processing failures gracefully', async () => {
      const invalidImage = { invalid: 'data' };
      
      const result = await stegoSession.autoProcessImage(invalidImage);
      expect(result).toBeNull();
    });
  });

  describe('Session Lifecycle', () => {
    test('should clean up resources on destroy', () => {
      // Add some cached data
      stegoSession.stegoState.coverImages.set('test', { data: 'test' });
      stegoSession.stegoState.pendingImages.set('test', { data: 'test' });
      
      expect(stegoSession.stegoState.coverImages.size).toBe(1);
      expect(stegoSession.stegoState.pendingImages.size).toBe(1);
      
      stegoSession.destroy();
      
      // Resources should be cleaned up
      expect(stegoSession.stegoState).toBeNull();
      expect(stegoSession.stego).toBeNull();
    });

    test('should handle state changes with steganography context', () => {
      const stateChanges = [];
      
      stegoSession.onStateChange((state) => {
        stateChanges.push(state);
      });
      
      stegoSession.enableSteganography();
      
      // Should have received state change notification
      expect(stateChanges.length).toBeGreaterThan(0);
    });
  });
});
