/**
 * @fileoverview Advanced Steganography Testing
 * Tests security hardening and platform optimization features
 * Part of Steganography PRD Phase 3-4 implementation
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { SteganographyOTR } from '../../src/core/steganography/index.js';
import { SteganographySecurityAnalyzer } from '../../src/core/steganography/security-hardening.js';
import { PlatformOptimizer } from '../../src/core/steganography/platform-optimization.js';

describe('Advanced Steganography Features', () => {
  let stego;
  let securityAnalyzer;
  let testImage;
  let testMessage;

  beforeEach(async () => {
    stego = new SteganographyOTR({
      quality: 0.95,
      compression: true
    });

    securityAnalyzer = new SteganographySecurityAnalyzer({
      strictMode: true,
      compressionResistance: 'MEDIUM'
    });

    // Create test image
    testImage = new ImageData(1024, 1024);
    for (let i = 0; i < testImage.data.length; i += 4) {
      testImage.data[i] = Math.random() * 255;     // Red
      testImage.data[i + 1] = Math.random() * 255; // Green
      testImage.data[i + 2] = Math.random() * 255; // Blue
      testImage.data[i + 3] = 255;                 // Alpha
    }

    testMessage = 'This is a test OTR message for advanced steganography testing.';
  });

  describe('Security Hardening', () => {
    test('should perform comprehensive security analysis', async () => {
      // Create stego image
      const stegoImage = await stego.hideMessage(testImage, testMessage);
      
      // Perform security analysis
      const analysis = await securityAnalyzer.analyzeSecurityProfile(
        testImage, 
        stegoImage
      );

      expect(analysis).toHaveProperty('passed');
      expect(analysis).toHaveProperty('warnings');
      expect(analysis).toHaveProperty('errors');
      expect(analysis).toHaveProperty('metrics');

      // Check metrics structure
      expect(analysis.metrics).toHaveProperty('statistical');
      expect(analysis.metrics).toHaveProperty('visual');
      expect(analysis.metrics).toHaveProperty('entropy');
      expect(analysis.metrics).toHaveProperty('metadata');
      expect(analysis.metrics).toHaveProperty('compression');

      // Validate statistical analysis
      expect(analysis.metrics.statistical).toHaveProperty('chiSquareValue');
      expect(analysis.metrics.statistical).toHaveProperty('chiSquareP');
      expect(analysis.metrics.statistical).toHaveProperty('passed');

      // Validate visual analysis
      expect(analysis.metrics.visual).toHaveProperty('psnr');
      expect(analysis.metrics.visual).toHaveProperty('ssim');
      expect(analysis.metrics.visual).toHaveProperty('visuallyAcceptable');
      expect(analysis.metrics.visual.psnr).toBeGreaterThan(30); // Reasonable PSNR
    });

    test('should detect statistical anomalies', async () => {
      // Create obviously detectable stego image (poor implementation)
      const poorStegoImage = new ImageData(testImage.data.slice(), testImage.width, testImage.height);
      
      // Introduce obvious statistical patterns
      for (let i = 0; i < poorStegoImage.data.length; i += 8) {
        poorStegoImage.data[i] = (poorStegoImage.data[i] & 0xFE) | 1; // Set LSB to 1
      }

      const analysis = await securityAnalyzer.analyzeSecurityProfile(
        testImage,
        poorStegoImage
      );

      // Should detect the obvious pattern
      expect(analysis.warnings.length).toBeGreaterThan(0);
      expect(analysis.metrics.statistical.chiSquareP).toBeLessThan(0.05);
    });

    test('should harden stego images against detection', async () => {
      const stegoImage = await stego.hideMessage(testImage, testMessage);
      
      // Harden the image
      const hardenedImage = await securityAnalyzer.hardenStegoImage(stegoImage, {
        noiseInjection: true,
        histogramNormalization: true,
        adaptiveLSB: true
      });

      expect(hardenedImage).toBeDefined();
      expect(hardenedImage.width).toBe(stegoImage.width);
      expect(hardenedImage.height).toBe(stegoImage.height);

      // Verify message can still be extracted
      const extractedMessage = await stego.revealMessage(hardenedImage);
      expect(extractedMessage).toBe(testMessage);

      // Verify improved security profile
      const hardenedAnalysis = await securityAnalyzer.analyzeSecurityProfile(
        testImage,
        hardenedImage
      );
      
      expect(hardenedAnalysis.warnings.length).toBeLessThanOrEqual(
        (await securityAnalyzer.analyzeSecurityProfile(testImage, stegoImage)).warnings.length
      );
    });

    test('should generate secure cover images', async () => {
      const secureCover = await securityAnalyzer.generateSecureCover(512, 512, {
        naturalTexture: true,
        optimizeCapacity: true
      });

      expect(secureCover).toBeDefined();
      expect(secureCover.width).toBe(512);
      expect(secureCover.height).toBe(512);

      // Verify high entropy
      const entropy = calculateImageEntropy(secureCover);
      expect(entropy).toBeGreaterThan(7.0); // High entropy threshold

      // Verify steganographic capacity
      const capacity = await stego.calculateCapacity(secureCover);
      expect(capacity).toBeGreaterThan(1000); // Reasonable capacity
    });

    test('should validate entropy requirements', async () => {
      // Create low-entropy image (all same color)
      const lowEntropyImage = new ImageData(256, 256);
      lowEntropyImage.data.fill(128); // All pixels same gray

      const entropyAnalysis = await securityAnalyzer._analyzeEntropy(lowEntropyImage);
      expect(entropyAnalysis.entropy).toBeLessThan(1.0);
      expect(entropyAnalysis.sufficient).toBe(false);

      // Create high-entropy image
      const highEntropyImage = await securityAnalyzer.generateSecureCover(256, 256);
      const highEntropyAnalysis = await securityAnalyzer._analyzeEntropy(highEntropyImage);
      expect(highEntropyAnalysis.entropy).toBeGreaterThan(7.0);
      expect(highEntropyAnalysis.sufficient).toBe(true);
    });
  });

  describe('Platform Optimization', () => {
    test('should optimize for Instagram platform', async () => {
      const optimizer = new PlatformOptimizer('INSTAGRAM');
      
      const optimization = await optimizer.optimizeForPlatform(testImage, testMessage);
      
      expect(optimization).toHaveProperty('originalImage');
      expect(optimization).toHaveProperty('optimizedImage');
      expect(optimization).toHaveProperty('platformConfig');
      expect(optimization).toHaveProperty('optimizations');
      expect(optimization).toHaveProperty('capacity');
      expect(optimization).toHaveProperty('estimatedSurvival');

      // Instagram should have aggressive optimization
      expect(optimization.platformConfig.compressionQuality).toBe(0.75);
      expect(optimization.estimatedSurvival).toBeGreaterThan(0);
      expect(optimization.estimatedSurvival).toBeLessThanOrEqual(1);
    });

    test('should optimize for Discord platform', async () => {
      const optimizer = new PlatformOptimizer('DISCORD');
      
      const optimization = await optimizer.optimizeForPlatform(testImage, testMessage);
      
      // Discord should preserve quality better
      expect(optimization.platformConfig.compressionQuality).toBe(0.95);
      expect(optimization.estimatedSurvival).toBeGreaterThan(0.8); // Higher survival rate
    });

    test('should detect optimal embedding strategy', async () => {
      const optimizer = new PlatformOptimizer('TWITTER');
      
      const strategy = await optimizer.detectOptimalStrategy(testImage, testMessage);
      
      expect(strategy).toHaveProperty('recommendedStrategy');
      expect(strategy).toHaveProperty('allStrategies');
      expect(strategy).toHaveProperty('platformOptimized');

      expect(strategy.allStrategies.length).toBeGreaterThan(0);
      expect(strategy.recommendedStrategy).toHaveProperty('name');
      expect(strategy.recommendedStrategy).toHaveProperty('capacity');
      expect(strategy.recommendedStrategy).toHaveProperty('robustness');
      expect(strategy.recommendedStrategy).toHaveProperty('score');
    });

    test('should simulate platform processing', async () => {
      const optimizer = new PlatformOptimizer('FACEBOOK');
      const stegoImage = await stego.hideMessage(testImage, testMessage);
      
      const simulation = await optimizer.simulatePlatformProcessing(stegoImage);
      
      expect(simulation).toHaveProperty('platform');
      expect(simulation).toHaveProperty('originalImage');
      expect(simulation).toHaveProperty('processedImage');
      expect(simulation).toHaveProperty('dataIntegrity');
      expect(simulation).toHaveProperty('processingSteps');

      expect(simulation.platform).toBe('FACEBOOK');
      expect(simulation.dataIntegrity).toBeGreaterThanOrEqual(0);
      expect(simulation.dataIntegrity).toBeLessThanOrEqual(1);
      expect(simulation.processingSteps.length).toBeGreaterThan(0);
    });

    test('should generate platform-specific recommendations', async () => {
      const optimizer = new PlatformOptimizer('SLACK');
      
      const recommendations = await optimizer.generateRecommendations(
        testImage, 
        testMessage.length
      );
      
      expect(recommendations).toHaveProperty('platform');
      expect(recommendations).toHaveProperty('imageAnalysis');
      expect(recommendations).toHaveProperty('recommendations');
      expect(recommendations).toHaveProperty('warnings');
      expect(recommendations).toHaveProperty('estimatedSuccess');

      expect(recommendations.platform).toBe('SLACK');
      expect(recommendations.recommendations.length).toBeGreaterThan(0);
      expect(recommendations.estimatedSuccess).toBeGreaterThanOrEqual(0);
      expect(recommendations.estimatedSuccess).toBeLessThanOrEqual(1);
    });

    test('should handle oversized images', async () => {
      // Create oversized image
      const oversizedImage = new ImageData(8192, 8192); // Very large
      const optimizer = new PlatformOptimizer('INSTAGRAM'); // Has size limits
      
      const optimization = await optimizer.optimizeForPlatform(
        oversizedImage, 
        testMessage
      );
      
      expect(optimization.optimizations).toContain('resized');
      expect(optimization.optimizedImage.width).toBeLessThanOrEqual(1080);
      expect(optimization.optimizedImage.height).toBeLessThanOrEqual(1080);
    });
  });

  describe('Integration Testing', () => {
    test('should integrate security hardening with platform optimization', async () => {
      const optimizer = new PlatformOptimizer('INSTAGRAM');
      const analyzer = new SteganographySecurityAnalyzer();
      
      // Optimize for platform
      const optimization = await optimizer.optimizeForPlatform(testImage, testMessage);
      const optimizedImage = optimization.optimizedImage || testImage;
      
      // Create stego image
      const stegoImage = await stego.hideMessage(optimizedImage, testMessage);
      
      // Harden the image
      const hardenedImage = await analyzer.hardenStegoImage(stegoImage);
      
      // Simulate platform processing
      const simulation = await optimizer.simulatePlatformProcessing(hardenedImage);
      
      // Verify message survives the complete pipeline
      expect(simulation.dataIntegrity).toBeGreaterThan(0.5);
      
      // Try to extract message from processed image
      const extractedMessage = await stego.revealMessage(simulation.processedImage);
      expect(extractedMessage).toBe(testMessage);
    });

    test('should handle multiple platform optimizations', async () => {
      const platforms = ['FACEBOOK', 'TWITTER', 'DISCORD'];
      const results = {};
      
      for (const platform of platforms) {
        const optimizer = new PlatformOptimizer(platform);
        const optimization = await optimizer.optimizeForPlatform(testImage, testMessage);
        results[platform] = optimization;
      }
      
      // Each platform should have different optimization characteristics
      expect(results.FACEBOOK.estimatedSurvival).not.toBe(results.DISCORD.estimatedSurvival);
      expect(results.TWITTER.capacity).toBeGreaterThan(0);
      
      // Discord should generally have better survival rates
      expect(results.DISCORD.estimatedSurvival).toBeGreaterThan(results.FACEBOOK.estimatedSurvival);
    });

    test('should maintain OTR protocol compatibility', async () => {
      // Test with actual OTR message format
      const otrMessage = '?OTR:AAEDAAAAAQAAAAEAAADAVf3Rg3ErZ7K4reDuzgJbZR...';
      
      const optimizer = new PlatformOptimizer('TWITTER');
      const optimization = await optimizer.optimizeForPlatform(testImage, otrMessage);
      
      const stegoImage = await stego.hideMessage(
        optimization.optimizedImage || testImage, 
        otrMessage
      );
      
      const extractedMessage = await stego.revealMessage(stegoImage);
      expect(extractedMessage).toBe(otrMessage);
      
      // Verify OTR message format is preserved
      expect(extractedMessage.startsWith('?OTR:')).toBe(true);
    });
  });

  describe('Performance Testing', () => {
    test('should process images within time limits', async () => {
      const startTime = Date.now();
      
      const optimizer = new PlatformOptimizer('DISCORD');
      await optimizer.optimizeForPlatform(testImage, testMessage);
      
      const processingTime = Date.now() - startTime;
      expect(processingTime).toBeLessThan(5000); // 5 second limit
    });

    test('should handle batch processing efficiently', async () => {
      const images = [];
      for (let i = 0; i < 5; i++) {
        images.push(new ImageData(512, 512));
      }
      
      const startTime = Date.now();
      const optimizer = new PlatformOptimizer('FACEBOOK');
      
      const results = await Promise.all(
        images.map(img => optimizer.optimizeForPlatform(img, testMessage))
      );
      
      const totalTime = Date.now() - startTime;
      expect(totalTime).toBeLessThan(10000); // 10 second limit for 5 images
      expect(results.length).toBe(5);
    });
  });
});

// Utility function for entropy calculation
function calculateImageEntropy(image) {
  const histogram = new Array(256).fill(0);
  const pixels = image.width * image.height;
  
  for (let i = 0; i < image.data.length; i += 4) {
    histogram[image.data[i]]++;
  }
  
  let entropy = 0;
  for (let count of histogram) {
    if (count > 0) {
      const probability = count / pixels;
      entropy -= probability * Math.log2(probability);
    }
  }
  
  return entropy;
}
