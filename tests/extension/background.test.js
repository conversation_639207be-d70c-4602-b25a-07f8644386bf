/**
 * Tests for browser extension background script
 */

// Mock chrome API
global.chrome = {
  webNavigation: {
    onCompleted: {
      addListener: jest.fn()
    }
  },
  runtime: {
    onMessage: {
      addListener: jest.fn()
    },
    sendMessage: jest.fn()
  },
  tabs: {
    query: jest.fn(),
    sendMessage: jest.fn()
  },
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn()
    }
  }
};

// Import the background script
import '../src/extension/background';

describe('Browser Extension Background Script', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  // Test initialization
  describe('Initialization', () => {
    test('should set up event listeners', () => {
      // Verify webNavigation listener was added
      expect(chrome.webNavigation.onCompleted.addListener).toHaveBeenCalled();
      
      // Verify runtime message listener was added
      expect(chrome.runtime.onMessage.addListener).toHaveBeenCalled();
    });
  });
  
  // Test message handling
  describe('Message Handling', () => {
    test('should handle OTR_INITIALIZED message', () => {
      // Get the message listener callback
      const messageListener = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      
      // Create a mock message
      const message = {
        type: 'OTR_INITIALIZED',
        platform: 'discord'
      };
      
      // Create mock sender
      const sender = {
        tab: { id: 123 }
      };
      
      // Create mock response callback
      const sendResponse = jest.fn();
      
      // Call the message listener
      messageListener(message, sender, sendResponse);
      
      // Verify storage was updated
      expect(chrome.storage.local.set).toHaveBeenCalledWith(
        expect.objectContaining({
          otrTabs: expect.objectContaining({
            '123': expect.objectContaining({
              platform: 'discord'
            })
          })
        })
      );
    });
    
    test('should handle START_OTR message', () => {
      // Get the message listener callback
      const messageListener = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      
      // Create a mock message
      const message = {
        type: 'START_OTR',
        tabId: 123
      };
      
      // Create mock sender
      const sender = {};
      
      // Create mock response callback
      const sendResponse = jest.fn();
      
      // Call the message listener
      messageListener(message, sender, sendResponse);
      
      // Verify tab message was sent
      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(
        123,
        { type: 'START_OTR' },
        expect.any(Function)
      );
    });
    
    test('should handle END_OTR message', () => {
      // Get the message listener callback
      const messageListener = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      
      // Create a mock message
      const message = {
        type: 'END_OTR',
        tabId: 123
      };
      
      // Create mock sender
      const sender = {};
      
      // Create mock response callback
      const sendResponse = jest.fn();
      
      // Call the message listener
      messageListener(message, sender, sendResponse);
      
      // Verify tab message was sent
      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(
        123,
        { type: 'END_OTR' },
        expect.any(Function)
      );
    });
    
    test('should handle VERIFY_PEER message', () => {
      // Get the message listener callback
      const messageListener = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      
      // Create a mock message
      const message = {
        type: 'VERIFY_PEER',
        tabId: 123
      };
      
      // Create mock sender
      const sender = {};
      
      // Create mock response callback
      const sendResponse = jest.fn();
      
      // Call the message listener
      messageListener(message, sender, sendResponse);
      
      // Verify tab message was sent
      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(
        123,
        { type: 'VERIFY_PEER' },
        expect.any(Function)
      );
    });
    
    test('should handle GET_OTR_STATUS message', () => {
      // Mock storage.local.get to return tab data
      chrome.storage.local.get.mockImplementation((key, callback) => {
        callback({
          otrTabs: {
            '123': {
              status: 'Encrypted'
            }
          }
        });
      });
      
      // Get the message listener callback
      const messageListener = chrome.runtime.onMessage.addListener.mock.calls[0][0];
      
      // Create a mock message
      const message = {
        type: 'GET_OTR_STATUS',
        tabId: 123
      };
      
      // Create mock sender
      const sender = {};
      
      // Create mock response callback
      const sendResponse = jest.fn();
      
      // Call the message listener
      messageListener(message, sender, sendResponse);
      
      // Verify storage was queried
      expect(chrome.storage.local.get).toHaveBeenCalledWith(
        'otrTabs',
        expect.any(Function)
      );
      
      // Verify response was sent
      expect(sendResponse).toHaveBeenCalledWith({
        status: 'Encrypted'
      });
    });
  });
  
  // Test navigation handling
  describe('Navigation Handling', () => {
    test('should handle navigation to supported platforms', () => {
      // Get the navigation listener callback
      const navigationListener = chrome.webNavigation.onCompleted.addListener.mock.calls[0][0];
      
      // Create a mock navigation details for Discord
      const discordDetails = {
        url: 'https://discord.com/channels/123456789',
        tabId: 123
      };
      
      // Call the navigation listener
      navigationListener(discordDetails);
      
      // Verify tab message was sent
      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(
        123,
        { type: 'CHECK_OTR_STATUS' },
        expect.any(Function)
      );
    });
    
    test('should ignore navigation to unsupported platforms', () => {
      // Get the navigation listener callback
      const navigationListener = chrome.webNavigation.onCompleted.addListener.mock.calls[0][0];
      
      // Create a mock navigation details for unsupported site
      const unsupportedDetails = {
        url: 'https://example.com',
        tabId: 123
      };
      
      // Call the navigation listener
      navigationListener(unsupportedDetails);
      
      // Verify no tab message was sent
      expect(chrome.tabs.sendMessage).not.toHaveBeenCalled();
    });
  });
});
