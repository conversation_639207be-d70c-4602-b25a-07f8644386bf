/**
 * Tests for browser extension popup
 */

// Mock DOM elements
class MockElement {
  constructor() {
    this.classList = {
      add: jest.fn(),
      remove: jest.fn(),
      contains: jest.fn()
    };
    this.style = {};
    this.innerHTML = '';
    this.textContent = '';
    this.appendChild = jest.fn();
    this.querySelector = jest.fn();
    this.querySelectorAll = jest.fn().mockReturnValue([]);
    this.addEventListener = jest.fn();
    this.removeEventListener = jest.fn();
    this.setAttribute = jest.fn();
    this.getAttribute = jest.fn();
    this.dataset = {};
    this.disabled = false;
    this.title = '';
  }
}

// Mock document
global.document = {
  createElement: jest.fn().mockImplementation(() => new MockElement()),
  querySelector: jest.fn().mockImplementation(() => new MockElement()),
  getElementById: jest.fn().mockImplementation(() => new MockElement()),
  addEventListener: jest.fn()
};

// Mock chrome API
global.chrome = {
  runtime: {
    sendMessage: jest.fn(),
    openOptionsPage: jest.fn()
  },
  tabs: {
    query: jest.fn()
  }
};

// Mock DOMContentLoaded event
const mockDOMContentLoaded = () => {
  // Get the event listener callback
  const eventListener = document.addEventListener.mock.calls.find(
    call => call[0] === 'DOMContentLoaded'
  );
  
  // Call the event listener if found
  if (eventListener && eventListener[1]) {
    eventListener[1]();
  }
};

// Import the popup script
import '../src/extension/popup';

describe('Browser Extension Popup', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Set up mock elements
    document.getElementById.mockImplementation((id) => {
      const element = new MockElement();
      element.id = id;
      return element;
    });
  });
  
  // Test initialization
  describe('Initialization', () => {
    test('should set up event listeners on DOMContentLoaded', () => {
      // Trigger DOMContentLoaded
      mockDOMContentLoaded();
      
      // Verify event listeners were added to buttons
      const startOtrButton = document.getElementById('start-otr');
      const endOtrButton = document.getElementById('end-otr');
      const verifyPeerButton = document.getElementById('verify-peer');
      const settingsButton = document.getElementById('settings');
      
      expect(startOtrButton.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
      expect(endOtrButton.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
      expect(verifyPeerButton.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
      expect(settingsButton.addEventListener).toHaveBeenCalledWith('click', expect.any(Function));
    });
    
    test('should query for current tab on initialization', () => {
      // Trigger DOMContentLoaded
      mockDOMContentLoaded();
      
      // Verify tabs.query was called
      expect(chrome.tabs.query).toHaveBeenCalledWith(
        { active: true, currentWindow: true },
        expect.any(Function)
      );
    });
  });
  
  // Test platform detection
  describe('Platform Detection', () => {
    test('should detect Microsoft Teams platform', () => {
      // Trigger DOMContentLoaded
      mockDOMContentLoaded();
      
      // Get the tabs.query callback
      const queryCallback = chrome.tabs.query.mock.calls[0][1];
      
      // Create mock tabs result for Teams
      const tabs = [
        { url: 'https://teams.microsoft.com/_#/conversations/123456789', id: 123 }
      ];
      
      // Call the callback
      queryCallback(tabs);
      
      // Verify platform text was updated
      const platformText = document.getElementById('platform-text');
      expect(platformText.textContent).toBe('Microsoft Teams');
    });
    
    test('should detect Discord platform', () => {
      // Trigger DOMContentLoaded
      mockDOMContentLoaded();
      
      // Get the tabs.query callback
      const queryCallback = chrome.tabs.query.mock.calls[0][1];
      
      // Create mock tabs result for Discord
      const tabs = [
        { url: 'https://discord.com/channels/123456789', id: 123 }
      ];
      
      // Call the callback
      queryCallback(tabs);
      
      // Verify platform text was updated
      const platformText = document.getElementById('platform-text');
      expect(platformText.textContent).toBe('Discord');
    });
    
    test('should detect Slack platform', () => {
      // Trigger DOMContentLoaded
      mockDOMContentLoaded();
      
      // Get the tabs.query callback
      const queryCallback = chrome.tabs.query.mock.calls[0][1];
      
      // Create mock tabs result for Slack
      const tabs = [
        { url: 'https://app.slack.com/client/123456789', id: 123 }
      ];
      
      // Call the callback
      queryCallback(tabs);
      
      // Verify platform text was updated
      const platformText = document.getElementById('platform-text');
      expect(platformText.textContent).toBe('Slack');
    });
    
    test('should handle unsupported platforms', () => {
      // Trigger DOMContentLoaded
      mockDOMContentLoaded();
      
      // Get the tabs.query callback
      const queryCallback = chrome.tabs.query.mock.calls[0][1];
      
      // Create mock tabs result for unsupported site
      const tabs = [
        { url: 'https://example.com', id: 123 }
      ];
      
      // Call the callback
      queryCallback(tabs);
      
      // Verify platform text was updated
      const platformText = document.getElementById('platform-text');
      expect(platformText.textContent).toBe('Unsupported');
      
      // Verify start button was disabled
      const startOtrButton = document.getElementById('start-otr');
      expect(startOtrButton.disabled).toBe(true);
    });
  });
  
  // Test button actions
  describe('Button Actions', () => {
    test('should send START_OTR message when start button is clicked', () => {
      // Trigger DOMContentLoaded
      mockDOMContentLoaded();
      
      // Set up mock tabs
      chrome.tabs.query.mockImplementation((query, callback) => {
        callback([{ id: 123 }]);
      });
      
      // Get the start button click handler
      const startOtrButton = document.getElementById('start-otr');
      const clickHandler = startOtrButton.addEventListener.mock.calls.find(
        call => call[0] === 'click'
      )[1];
      
      // Call the click handler
      clickHandler();
      
      // Verify runtime.sendMessage was called
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        { type: 'START_OTR', tabId: 123 },
        expect.any(Function)
      );
    });
    
    test('should send END_OTR message when end button is clicked', () => {
      // Trigger DOMContentLoaded
      mockDOMContentLoaded();
      
      // Set up mock tabs
      chrome.tabs.query.mockImplementation((query, callback) => {
        callback([{ id: 123 }]);
      });
      
      // Get the end button click handler
      const endOtrButton = document.getElementById('end-otr');
      const clickHandler = endOtrButton.addEventListener.mock.calls.find(
        call => call[0] === 'click'
      )[1];
      
      // Call the click handler
      clickHandler();
      
      // Verify runtime.sendMessage was called
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        { type: 'END_OTR', tabId: 123 },
        expect.any(Function)
      );
    });
    
    test('should send VERIFY_PEER message when verify button is clicked', () => {
      // Trigger DOMContentLoaded
      mockDOMContentLoaded();
      
      // Set up mock tabs
      chrome.tabs.query.mockImplementation((query, callback) => {
        callback([{ id: 123 }]);
      });
      
      // Get the verify button click handler
      const verifyPeerButton = document.getElementById('verify-peer');
      const clickHandler = verifyPeerButton.addEventListener.mock.calls.find(
        call => call[0] === 'click'
      )[1];
      
      // Call the click handler
      clickHandler();
      
      // Verify runtime.sendMessage was called
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
        { type: 'VERIFY_PEER', tabId: 123 },
        expect.any(Function)
      );
    });
    
    test('should open options page when settings button is clicked', () => {
      // Trigger DOMContentLoaded
      mockDOMContentLoaded();
      
      // Get the settings button click handler
      const settingsButton = document.getElementById('settings');
      const clickHandler = settingsButton.addEventListener.mock.calls.find(
        call => call[0] === 'click'
      )[1];
      
      // Call the click handler
      clickHandler();
      
      // Verify openOptionsPage was called
      expect(chrome.runtime.openOptionsPage).toHaveBeenCalled();
    });
  });
});
