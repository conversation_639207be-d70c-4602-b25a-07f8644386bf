/**
 * End-to-end tests for OTR messaging
 */

import { OtrSession } from '../../src/core/session';

describe('OTR E2E Tests', () => {
  let aliceSession;
  let bobSession;
  let aliceMessages = [];
  let bobMessages = [];
  
  // Create send message functions that capture messages for testing
  const aliceSendMessage = jest.fn(message => {
    bobMessages.push(message);
  });
  
  const bobSendMessage = jest.fn(message => {
    aliceMessages.push(message);
  });
  
  beforeEach(async () => {
    // Reset captured messages
    aliceMessages = [];
    bobMessages = [];
    
    // Create sessions
    aliceSession = new OtrSession('bob', {
      sendMessage: aliceSendMessage,
      testing: true
    });
    
    bobSession = new OtrSession('alice', {
      sendMessage: bobSendMessage,
      testing: true
    });
    
    // Initialize both sessions
    await Promise.all([
      aliceSession.init(),
      bobSession.init()
    ]);
    
    // Start OTR
    await aliceSession.startOtr();
    
    // Process all messages to complete the OTR session setup
    let processingComplete = false;
    let iterations = 0;
    const maxIterations = 10;
    
    while (!processingComplete && iterations < maxIterations) {
      iterations++;
      
      // Process Bob's messages
      const bobMessageCount = bobMessages.length;
      if (bobMessageCount > 0) {
        for (let i = 0; i < bobMessageCount; i++) {
          await bobSession.processIncoming(bobMessages.shift());
        }
      }
      
      // Process Alice's messages
      const aliceMessageCount = aliceMessages.length;
      if (aliceMessageCount > 0) {
        for (let i = 0; i < aliceMessageCount; i++) {
          await aliceSession.processIncoming(aliceMessages.shift());
        }
      }
      
      // Check if both sessions are in encrypted state and no messages left
      if ((aliceSession._canSendSMP() && bobSession._canSendSMP()) &&
          aliceMessages.length === 0 && bobMessages.length === 0) {
        processingComplete = true;
      }
    }
  });
  
  afterEach(() => {
    // Clean up
    aliceSession = null;
    bobSession = null;
  });
  
  test('should authenticate using SMP', async () => {
    // Set up SMP callbacks
    const aliceSMPCallback = jest.fn();
    const bobSMPCallback = jest.fn();
    
    aliceSession.registerSMPCallback(aliceSMPCallback);
    bobSession.registerSMPCallback(bobSMPCallback);
    
    // Shared secret for authentication
    const sharedSecret = 'our shared secret';
    
    // Alice initiates SMP
    await aliceSession.initiateSMP(sharedSecret);
    
    // Process messages for SMP
    let iterations = 0;
    const maxIterations = 10;
    
    while (iterations < maxIterations) {
      iterations++;
      
      // Process Bob's messages
      const bobMessageCount = bobMessages.length;
      if (bobMessageCount > 0) {
        for (let i = 0; i < bobMessageCount; i++) {
          await bobSession.processIncoming(bobMessages.shift());
        }
      }
      
      // Bob responds to SMP with the same secret
      if (bobSession.smpHandler.state.stage === 1) {
        await bobSession.respondToSMP(sharedSecret);
      }
      
      // Process Alice's messages
      const aliceMessageCount = aliceMessages.length;
      if (aliceMessageCount > 0) {
        for (let i = 0; i < aliceMessageCount; i++) {
          await aliceSession.processIncoming(aliceMessages.shift());
        }
      }
      
      // Check if SMP is complete and no messages left
      if (aliceMessages.length === 0 && bobMessages.length === 0) {
        // Wait for callbacks to be executed
        await new Promise(resolve => setTimeout(resolve, 100));
        
        if (aliceSMPCallback.mock.calls.length > 0 && bobSMPCallback.mock.calls.length > 0) {
          break;
        }
      }
      
      // Wait a bit between iterations
      await new Promise(resolve => setTimeout(resolve, 10));
    }
    
    // Check that SMP callbacks were called with success result
    expect(aliceSMPCallback).toHaveBeenCalled();
    expect(bobSMPCallback).toHaveBeenCalled();
  });
}); 