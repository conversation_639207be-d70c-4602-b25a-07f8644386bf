/**
 * Basic Extension Functionality E2E Tests
 * 
 * Tests core extension functionality across different browsers
 */

const { test, expect } = require('@playwright/test');
const path = require('path');

test.describe('WebOTR Extension - Basic Functionality', () => {
  test.beforeEach(async ({ page, context }) => {
    // Only run extension tests on browsers that support extensions
    if (!context._options.launchOptions?.args?.some(arg => arg.includes('load-extension'))) {
      test.skip('Extension tests only run on browsers with extension support');
    }
  });

  test('Extension loads successfully', async ({ page, context }) => {
    // Navigate to a test page
    await page.goto('/');
    
    // Wait for extension to load
    await page.waitForTimeout(2000);
    
    // Check if extension is loaded by looking for injected content
    const extensionLoaded = await page.evaluate(() => {
      return window.webOTRExtension !== undefined;
    });
    
    expect(extensionLoaded).toBeTruthy();
  });

  test('Extension popup opens', async ({ page, context }) => {
    // Get extension ID (in real tests, this would be more dynamic)
    const extensionId = await getExtensionId(context);
    
    if (!extensionId) {
      test.skip('Extension ID not found');
    }

    // Open extension popup
    const popupPage = await context.newPage();
    await popupPage.goto(`chrome-extension://${extensionId}/popup/popup.html`);
    
    // Check popup loads
    await expect(popupPage.locator('body')).toBeVisible();
    await expect(popupPage.locator('.webOTR-popup')).toBeVisible();
    
    // Check basic UI elements
    await expect(popupPage.locator('.status-indicator')).toBeVisible();
    await expect(popupPage.locator('.security-status')).toBeVisible();
  });

  test('Content script injection works', async ({ page }) => {
    // Navigate to a supported platform (mock)
    await page.goto('/test-chat');
    
    // Wait for content script injection
    await page.waitForTimeout(3000);
    
    // Check if WebOTR UI elements are injected
    const otrToggle = page.locator('.webOTR-toggle');
    const statusIndicator = page.locator('.webOTR-status');
    
    await expect(otrToggle).toBeVisible();
    await expect(statusIndicator).toBeVisible();
  });

  test('Security status updates correctly', async ({ page }) => {
    await page.goto('/test-chat');
    await page.waitForTimeout(2000);
    
    // Check initial status (should be "Not Encrypted")
    const statusText = page.locator('.webOTR-status .status-text');
    await expect(statusText).toContainText('Not Encrypted');
    
    // Click OTR toggle to start encryption
    await page.click('.webOTR-toggle');
    
    // Wait for status change
    await page.waitForTimeout(1000);
    
    // Status should change to "Connecting..." or "Encrypted"
    await expect(statusText).not.toContainText('Not Encrypted');
  });

  test('Message encryption indicator appears', async ({ page }) => {
    await page.goto('/test-chat');
    await page.waitForTimeout(2000);
    
    // Enable OTR
    await page.click('.webOTR-toggle');
    await page.waitForTimeout(1000);
    
    // Type a test message
    const messageInput = page.locator('.message-input, [data-testid="message-input"]');
    await messageInput.fill('Test encrypted message');
    
    // Check for encryption indicator
    const encryptionIndicator = page.locator('.webOTR-encryption-indicator');
    await expect(encryptionIndicator).toBeVisible();
    
    // Send message
    await page.keyboard.press('Enter');
    
    // Check message appears with encryption indicator
    const lastMessage = page.locator('.message').last();
    await expect(lastMessage.locator('.webOTR-encrypted-badge')).toBeVisible();
  });

  test('Verification dialog opens', async ({ page }) => {
    await page.goto('/test-chat');
    await page.waitForTimeout(2000);
    
    // Enable OTR
    await page.click('.webOTR-toggle');
    await page.waitForTimeout(1000);
    
    // Open verification dialog
    await page.click('.webOTR-verify-button');
    
    // Check verification dialog appears
    const verificationDialog = page.locator('.webOTR-verification-dialog');
    await expect(verificationDialog).toBeVisible();
    
    // Check verification methods are available
    await expect(page.locator('.verification-method-qr')).toBeVisible();
    await expect(page.locator('.verification-method-smp')).toBeVisible();
    await expect(page.locator('.verification-method-manual')).toBeVisible();
  });

  test('QR code verification works', async ({ page }) => {
    await page.goto('/test-chat');
    await page.waitForTimeout(2000);
    
    // Enable OTR and open verification
    await page.click('.webOTR-toggle');
    await page.waitForTimeout(1000);
    await page.click('.webOTR-verify-button');
    
    // Select QR code verification
    await page.click('.verification-method-qr');
    
    // Check QR code is generated
    const qrCode = page.locator('.qr-code-display');
    await expect(qrCode).toBeVisible();
    
    // Check QR code contains data
    const qrImage = page.locator('.qr-code-display img, .qr-code-display canvas');
    await expect(qrImage).toBeVisible();
  });

  test('Settings page loads', async ({ page, context }) => {
    const extensionId = await getExtensionId(context);
    
    if (!extensionId) {
      test.skip('Extension ID not found');
    }

    // Open extension options page
    const optionsPage = await context.newPage();
    await optionsPage.goto(`chrome-extension://${extensionId}/options/options.html`);
    
    // Check options page loads
    await expect(optionsPage.locator('body')).toBeVisible();
    await expect(optionsPage.locator('.webOTR-options')).toBeVisible();
    
    // Check settings sections
    await expect(optionsPage.locator('.security-settings')).toBeVisible();
    await expect(optionsPage.locator('.platform-settings')).toBeVisible();
    await expect(optionsPage.locator('.advanced-settings')).toBeVisible();
  });

  test('Extension persists settings', async ({ page, context }) => {
    const extensionId = await getExtensionId(context);
    
    if (!extensionId) {
      test.skip('Extension ID not found');
    }

    // Open options and change a setting
    const optionsPage = await context.newPage();
    await optionsPage.goto(`chrome-extension://${extensionId}/options/options.html`);
    
    // Toggle a setting
    const autoEncryptToggle = optionsPage.locator('#auto-encrypt-toggle');
    const initialState = await autoEncryptToggle.isChecked();
    await autoEncryptToggle.click();
    
    // Save settings
    await optionsPage.click('.save-settings');
    await optionsPage.waitForTimeout(1000);
    
    // Reload page and check setting persisted
    await optionsPage.reload();
    await optionsPage.waitForTimeout(1000);
    
    const newState = await autoEncryptToggle.isChecked();
    expect(newState).toBe(!initialState);
  });
});

// Helper function to get extension ID
async function getExtensionId(context) {
  try {
    // This is a simplified approach - in real tests you'd need to
    // extract the actual extension ID from the browser
    const pages = context.pages();
    for (const page of pages) {
      const url = page.url();
      if (url.startsWith('chrome-extension://')) {
        return url.split('/')[2];
      }
    }
    return null;
  } catch (error) {
    return null;
  }
}
