/**
 * Global Teardown for E2E Tests
 * 
 * Cleans up testing environment and generates test reports
 */

const fs = require('fs').promises;
const path = require('path');

async function globalTeardown() {
  console.log('🧹 Cleaning up E2E testing environment...');

  try {
    // Generate test summary report
    await generateTestSummary();

    // Clean up temporary files (but keep test results)
    await cleanupTempFiles();

    // Archive test artifacts if in CI
    if (process.env.CI) {
      await archiveTestArtifacts();
    }

    console.log('✅ E2E testing cleanup completed');
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    // Don't throw - cleanup errors shouldn't fail the build
  }
}

async function generateTestSummary() {
  const resultsPath = path.resolve(__dirname, '../../test-results');
  
  try {
    // Read test results if they exist
    const resultsFile = path.join(resultsPath, 'results.json');
    const results = JSON.parse(await fs.readFile(resultsFile, 'utf8'));
    
    const summary = {
      timestamp: new Date().toISOString(),
      totalTests: results.stats?.total || 0,
      passed: results.stats?.passed || 0,
      failed: results.stats?.failed || 0,
      skipped: results.stats?.skipped || 0,
      duration: results.stats?.duration || 0,
      browsers: extractBrowserInfo(results),
      coverage: await getCoverageInfo()
    };

    await fs.writeFile(
      path.join(resultsPath, 'summary.json'),
      JSON.stringify(summary, null, 2)
    );

    console.log('📊 Test Summary:');
    console.log(`   Total: ${summary.totalTests}`);
    console.log(`   Passed: ${summary.passed}`);
    console.log(`   Failed: ${summary.failed}`);
    console.log(`   Duration: ${Math.round(summary.duration / 1000)}s`);

  } catch (error) {
    console.log('ℹ️  No test results found for summary generation');
  }
}

function extractBrowserInfo(results) {
  const browsers = new Set();
  
  if (results.suites) {
    results.suites.forEach(suite => {
      if (suite.title) {
        browsers.add(suite.title);
      }
    });
  }
  
  return Array.from(browsers);
}

async function getCoverageInfo() {
  try {
    const coveragePath = path.resolve(__dirname, '../../coverage/coverage-summary.json');
    const coverage = JSON.parse(await fs.readFile(coveragePath, 'utf8'));
    
    return {
      lines: coverage.total?.lines?.pct || 0,
      functions: coverage.total?.functions?.pct || 0,
      branches: coverage.total?.branches?.pct || 0,
      statements: coverage.total?.statements?.pct || 0
    };
  } catch (error) {
    return null;
  }
}

async function cleanupTempFiles() {
  const tempPaths = [
    path.resolve(__dirname, '../../test-results/browser-data'),
    path.resolve(__dirname, '../../test-data')
  ];

  for (const tempPath of tempPaths) {
    try {
      await fs.rm(tempPath, { recursive: true, force: true });
      console.log(`🗑️  Cleaned up: ${path.basename(tempPath)}`);
    } catch (error) {
      // Ignore cleanup errors
    }
  }
}

async function archiveTestArtifacts() {
  console.log('📦 Archiving test artifacts for CI...');
  
  // In a real CI environment, you might upload to S3, artifact storage, etc.
  // For now, just ensure important files are preserved
  
  const artifactsToPreserve = [
    'test-results/results.json',
    'test-results/results.xml', 
    'test-results/summary.json',
    'test-results/playwright-report',
    'coverage/lcov.info'
  ];

  for (const artifact of artifactsToPreserve) {
    const fullPath = path.resolve(__dirname, '../../', artifact);
    try {
      await fs.access(fullPath);
      console.log(`✅ Preserved: ${artifact}`);
    } catch (error) {
      console.log(`⚠️  Missing: ${artifact}`);
    }
  }
}

module.exports = globalTeardown;
