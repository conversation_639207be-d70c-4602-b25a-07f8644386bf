/**
 * End-to-end tests for OTR protocol flow
 */

import { OtrSession } from '../../src/core/session';
import { STATE } from '../../src/core/protocol/state';

describe('OTR Protocol Flow End-to-End', () => {
  let aliceSession;
  let bobSession;
  let aliceMessages = [];
  let bobMessages = [];
  
  // Create send message functions that capture messages for testing
  const aliceSendMessage = jest.fn(message => {
    bobMessages.push(message);
  });
  
  const bobSendMessage = jest.fn(message => {
    aliceMessages.push(message);
  });
  
  beforeEach(async () => {
    // Reset captured messages
    aliceMessages = [];
    bobMessages = [];
    
    // Create sessions
    aliceSession = new OtrSession('bob', {
      sendMessage: aliceSendMessage,
      testing: true
    });
    
    bobSession = new OtrSession('alice', {
      sendMessage: bobSendMessage,
      testing: true
    });
    
    // Initialize both sessions
    await Promise.all([
      aliceSession.init(),
      bobSession.init()
    ]);
  });
  
  afterEach(() => {
    // Clean up
    aliceSession = null;
    bobSession = null;
  });
  
  test('should complete full OTR protocol flow', async () => {
    // Step 1: Alice initiates OTR with Bob
    await aliceSession.startOtr();
    
    // Verify Alice sent a query message
    expect(aliceMessages.length).toBe(0);
    expect(bobMessages.length).toBe(1);
    expect(bobMessages[0]).toContain('?OTR');
    
    // Step 2: Bob receives Alice's query and responds
    for (const message of bobMessages) {
      await bobSession.processIncoming(message);
    }
    
    // Clear processed messages
    bobMessages = [];
    
    // Verify Bob sent a DH commit message
    expect(aliceMessages.length).toBe(1);
    expect(aliceMessages[0]).toContain('?OTR:');
    
    // Step 3: Alice receives Bob's DH commit and responds
    for (const message of aliceMessages) {
      await aliceSession.processIncoming(message);
    }
    
    // Clear processed messages
    aliceMessages = [];
    
    // Verify Alice sent a DH key message
    expect(bobMessages.length).toBe(1);
    expect(bobMessages[0]).toContain('?OTR:');
    
    // Step 4: Bob receives Alice's DH key and responds
    for (const message of bobMessages) {
      await bobSession.processIncoming(message);
    }
    
    // Clear processed messages
    bobMessages = [];
    
    // Verify Bob sent a reveal signature message
    expect(aliceMessages.length).toBe(1);
    expect(aliceMessages[0]).toContain('?OTR:');
    
    // Step 5: Alice receives Bob's reveal signature and responds
    for (const message of aliceMessages) {
      await aliceSession.processIncoming(message);
    }
    
    // Clear processed messages
    aliceMessages = [];
    
    // Verify Alice sent a signature message
    expect(bobMessages.length).toBe(1);
    expect(bobMessages[0]).toContain('?OTR:');
    
    // Step 6: Bob receives Alice's signature
    for (const message of bobMessages) {
      await bobSession.processIncoming(message);
    }
    
    // Clear processed messages
    bobMessages = [];
    
    // Verify both sessions are now in ENCRYPTED state
    expect(aliceSession.state.getState()).toBe(STATE.ENCRYPTED);
    expect(bobSession.state.getState()).toBe(STATE.ENCRYPTED);
    
    // Step 7: Alice sends an encrypted message to Bob
    const aliceMessage = 'Hello Bob, this is a secret message!';
    await aliceSession.sendMessage(aliceMessage);
    
    // Verify Alice sent an encrypted message
    expect(bobMessages.length).toBe(1);
    expect(bobMessages[0]).toContain('?OTR:');
    expect(bobMessages[0]).not.toContain(aliceMessage); // Message should be encrypted
    
    // Step 8: Bob receives and decrypts Alice's message
    let bobReceivedMessage;
    for (const message of bobMessages) {
      bobReceivedMessage = await bobSession.processIncoming(message);
    }
    
    // Verify Bob received the decrypted message
    expect(bobReceivedMessage.message).toBe(aliceMessage);
    
    // Clear processed messages
    bobMessages = [];
    
    // Step 9: Bob sends an encrypted reply to Alice
    const bobMessage = 'Hello Alice, I received your secret message!';
    await bobSession.sendMessage(bobMessage);
    
    // Verify Bob sent an encrypted message
    expect(aliceMessages.length).toBe(1);
    expect(aliceMessages[0]).toContain('?OTR:');
    expect(aliceMessages[0]).not.toContain(bobMessage); // Message should be encrypted
    
    // Step 10: Alice receives and decrypts Bob's message
    let aliceReceivedMessage;
    for (const message of aliceMessages) {
      aliceReceivedMessage = await aliceSession.processIncoming(message);
    }
    
    // Verify Alice received the decrypted message
    expect(aliceReceivedMessage.message).toBe(bobMessage);
    
    // Clear processed messages
    aliceMessages = [];
    
    // Step 11: Alice ends the OTR session
    await aliceSession.endOtr();
    
    // Verify Alice sent a disconnect message
    expect(bobMessages.length).toBe(1);
    
    // Step 12: Bob receives the disconnect message
    for (const message of bobMessages) {
      await bobSession.processIncoming(message);
    }
    
    // Verify both sessions are back to PLAINTEXT state
    expect(aliceSession.state.getState()).toBe(STATE.PLAINTEXT);
    expect(bobSession.state.getState()).toBe(STATE.PLAINTEXT);
  });
  
  test('should handle concurrent OTR session initiation', async () => {
    // Both Alice and Bob start OTR at the same time
    await Promise.all([
      aliceSession.startOtr(),
      bobSession.startOtr()
    ]);
    
    // Process all messages in both directions
    const processAllMessages = async () => {
      const aliceMessagesCopy = [...aliceMessages];
      const bobMessagesCopy = [...bobMessages];
      
      // Clear message queues
      aliceMessages = [];
      bobMessages = [];
      
      // Process messages
      for (const message of aliceMessagesCopy) {
        await bobSession.processIncoming(message);
      }
      
      for (const message of bobMessagesCopy) {
        await aliceSession.processIncoming(message);
      }
    };
    
    // Process messages multiple times to complete the AKE
    for (let i = 0; i < 5; i++) {
      await processAllMessages();
    }
    
    // Verify both sessions are in ENCRYPTED state
    expect(aliceSession.state.getState()).toBe(STATE.ENCRYPTED);
    expect(bobSession.state.getState()).toBe(STATE.ENCRYPTED);
    
    // Send test messages in both directions
    const aliceMessage = 'Hello from Alice!';
    const bobMessage = 'Hello from Bob!';
    
    await aliceSession.sendMessage(aliceMessage);
    await bobSession.sendMessage(bobMessage);
    
    // Process the encrypted messages
    await processAllMessages();
    
    // End both sessions
    await Promise.all([
      aliceSession.endOtr(),
      bobSession.endOtr()
    ]);
    
    // Process the disconnect messages
    await processAllMessages();
    
    // Verify both sessions are back to PLAINTEXT state
    expect(aliceSession.state.getState()).toBe(STATE.PLAINTEXT);
    expect(bobSession.state.getState()).toBe(STATE.PLAINTEXT);
  });
});
