/**
 * Setup file for WebOTR end-to-end tests
 */

// Set longer timeout for E2E tests
jest.setTimeout(30000);

// Set up any global mocks or configurations
global.TESTING = true;

// Suppress console logs in tests
beforeAll(() => {
  // Save original console methods
  global.originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info
  };
  
  // Silence console during tests unless DEBUG is true
  if (!process.env.DEBUG) {
    console.log = jest.fn();
    console.error = jest.fn();
    console.warn = jest.fn();
    console.info = jest.fn();
  }
});

// Restore console logs after tests
afterAll(() => {
  // Restore original console methods
  console.log = global.originalConsole.log;
  console.error = global.originalConsole.error;
  console.warn = global.originalConsole.warn;
  console.info = global.originalConsole.info;
}); 