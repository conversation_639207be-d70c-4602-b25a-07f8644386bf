/**
 * Global Setup for E2E Tests
 * 
 * Prepares the testing environment for WebOTR extension and web app testing
 */

const { chromium, firefox } = require('@playwright/test');
const path = require('path');
const fs = require('fs').promises;

async function globalSetup() {
  console.log('🚀 Setting up E2E testing environment...');

  // Ensure test results directory exists
  await ensureDirectory('test-results');
  await ensureDirectory('test-results/screenshots');
  await ensureDirectory('test-results/videos');

  // Verify extension build exists
  const distPath = path.resolve(__dirname, '../../dist');
  try {
    await fs.access(distPath);
    console.log('✅ Extension build found at:', distPath);
  } catch (error) {
    console.error('❌ Extension build not found. Run "npm run build:extension" first.');
    throw new Error('Extension build required for E2E tests');
  }

  // Verify manifest.json exists
  const manifestPath = path.join(distPath, 'manifest.json');
  try {
    const manifest = JSON.parse(await fs.readFile(manifestPath, 'utf8'));
    console.log('✅ Extension manifest loaded:', manifest.name, 'v' + manifest.version);
  } catch (error) {
    console.error('❌ Invalid or missing manifest.json');
    throw error;
  }

  // Setup test data
  await setupTestData();

  // Prepare browser contexts for extension testing
  await prepareBrowserContexts();

  console.log('✅ E2E testing environment ready');
}

async function setupTestData() {
  const testDataPath = path.resolve(__dirname, 'test-data');
  await ensureDirectory(testDataPath);

  // Create test user data
  const testUsers = {
    alice: {
      name: 'Alice Test',
      email: '<EMAIL>',
      fingerprint: 'ABCD1234EFGH5678IJKL9012MNOP3456QRST7890'
    },
    bob: {
      name: 'Bob Test', 
      email: '<EMAIL>',
      fingerprint: 'ZYXW9876VUTSRQPO5432NMLKJIHGFEDC1098BA76'
    }
  };

  await fs.writeFile(
    path.join(testDataPath, 'users.json'),
    JSON.stringify(testUsers, null, 2)
  );

  // Create test messages
  const testMessages = [
    'Hello, this is a test message for WebOTR encryption.',
    'Testing special characters: !@#$%^&*()_+-=[]{}|;:,.<>?',
    'Testing Unicode: 🔒🔐🛡️ Secure messaging with emojis! 🚀',
    'Long message test: ' + 'A'.repeat(1000),
    'Multi-line message:\nLine 1\nLine 2\nLine 3'
  ];

  await fs.writeFile(
    path.join(testDataPath, 'messages.json'),
    JSON.stringify(testMessages, null, 2)
  );

  console.log('✅ Test data prepared');
}

async function prepareBrowserContexts() {
  // Create persistent browser contexts for extension testing
  const userDataDir = path.resolve(__dirname, '../../test-results/browser-data');
  await ensureDirectory(userDataDir);

  // Chrome context with extension
  const chromeUserDataDir = path.join(userDataDir, 'chrome');
  await ensureDirectory(chromeUserDataDir);

  // Firefox context with extension
  const firefoxUserDataDir = path.join(userDataDir, 'firefox');
  await ensureDirectory(firefoxUserDataDir);

  console.log('✅ Browser contexts prepared');
}

async function ensureDirectory(dir) {
  try {
    await fs.mkdir(dir, { recursive: true });
  } catch (error) {
    if (error.code !== 'EEXIST') {
      throw error;
    }
  }
}

module.exports = globalSetup;
