/**
 * OTR Protocol Integration Tests
 * Tests the complete OTR protocol flow including AKE, messaging, and SMP
 */

import { OtrSession } from '../../src/core/session/index.js';
import { AKE } from '../../src/core/protocol/ake.js';
import { SMPHandler } from '../../src/core/protocol/smp.js';
import { STATE, SMP_RESULT } from '../../src/core/protocol/index.js';

describe('OTR Protocol Integration', () => {
  let aliceSession, bobSession;
  let messageQueue = [];

  // Mock message transport
  const createMessageTransport = (fromSession, toSession) => {
    return (message) => {
      messageQueue.push({
        from: fromSession,
        to: toSession,
        message: message,
        timestamp: Date.now()
      });
    };
  };

  beforeEach(() => {
    messageQueue = [];
    
    aliceSession = new OtrSession({
      debug: true,
      autoStart: false
    });
    
    bobSession = new OtrSession({
      debug: true,
      autoStart: false
    });

    // Set up message transport
    aliceSession.sendMessage = createMessageTransport(aliceSession, bobSession);
    bobSession.sendMessage = createMessageTransport(bobSession, aliceSession);
  });

  afterEach(() => {
    if (aliceSession) aliceSession.destroy();
    if (bobSession) bobSession.destroy();
  });

  describe('Complete OTR Session Flow', () => {
    test('should complete full AKE handshake', async () => {
      const aliceStateChanges = [];
      const bobStateChanges = [];

      aliceSession.onStateChange((newState, oldState) => {
        aliceStateChanges.push({ newState, oldState });
      });

      bobSession.onStateChange((newState, oldState) => {
        bobStateChanges.push({ newState, oldState });
      });

      // Alice initiates OTR
<<<<<<< HEAD
      await aliceSession.startOtr();
      expect(aliceSession.isEncrypted()).toBe(true); // In testing mode, should be encrypted
=======
      await aliceSession.startAKE();
      expect(aliceSession.state).toBe(STATE.AWAITING_DHKEY);
>>>>>>> origin/feature/browser-extension

      // Process message queue
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        await to.processMessage(message);
      }

      // Both should be encrypted
      expect(aliceSession.state).toBe(STATE.ENCRYPTED);
      expect(bobSession.state).toBe(STATE.ENCRYPTED);
      expect(aliceSession.isEncrypted()).toBe(true);
      expect(bobSession.isEncrypted()).toBe(true);

      // Verify state transitions
      expect(aliceStateChanges.length).toBeGreaterThan(0);
      expect(bobStateChanges.length).toBeGreaterThan(0);
    });

    test('should exchange encrypted messages', async () => {
      // Complete AKE first
      await aliceSession.startAKE();
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        await to.processMessage(message);
      }

      // Alice sends encrypted message
      const aliceMessage = 'Hello from Alice!';
      const encryptedMessage = await aliceSession.encryptMessage(aliceMessage);
      
      // Bob receives and decrypts
      const decryptedMessage = await bobSession.decryptMessage(encryptedMessage);
      
      expect(decryptedMessage).toBe(aliceMessage);
    });

    test('should handle bidirectional messaging', async () => {
      // Complete AKE
      await aliceSession.startAKE();
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        await to.processMessage(message);
      }

      // Exchange multiple messages
      const messages = [
        { sender: aliceSession, receiver: bobSession, text: 'Hello Bob!' },
        { sender: bobSession, receiver: aliceSession, text: 'Hi Alice!' },
        { sender: aliceSession, receiver: bobSession, text: 'How are you?' },
        { sender: bobSession, receiver: aliceSession, text: 'I am fine, thanks!' }
      ];

      for (const { sender, receiver, text } of messages) {
        const encrypted = await sender.encryptMessage(text);
        const decrypted = await receiver.decryptMessage(encrypted);
        expect(decrypted).toBe(text);
      }
    });
  });

  describe('SMP Authentication Integration', () => {
    beforeEach(async () => {
      // Complete AKE first
      await aliceSession.startAKE();
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        await to.processMessage(message);
      }
    });

    test('should complete SMP with matching secrets', async () => {
      const sharedSecret = 'our-shared-secret';
      const aliceSMPResults = [];
      const bobSMPResults = [];

      aliceSession.onSMPResult((result) => {
        aliceSMPResults.push(result);
      });

      bobSession.onSMPResult((result) => {
        bobSMPResults.push(result);
      });

      // Alice initiates SMP
      await aliceSession.initiateSMP(sharedSecret);

      // Process SMP messages
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        if (message.type === 'smp') {
          await to.processSMPMessage(message);
        }
      }

      // Bob responds to SMP
      await bobSession.respondToSMP(sharedSecret);

      // Process remaining SMP messages
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        if (message.type === 'smp') {
          await to.processSMPMessage(message);
        }
      }

      // Both should have successful SMP results
      expect(aliceSMPResults).toContainEqual(
        expect.objectContaining({ result: SMP_RESULT.SUCCESS })
      );
      expect(bobSMPResults).toContainEqual(
        expect.objectContaining({ result: SMP_RESULT.SUCCESS })
      );
    });

    test('should fail SMP with mismatched secrets', async () => {
      const aliceSecret = 'alice-secret';
      const bobSecret = 'bob-secret';
      const aliceSMPResults = [];
      const bobSMPResults = [];

      aliceSession.onSMPResult((result) => {
        aliceSMPResults.push(result);
      });

      bobSession.onSMPResult((result) => {
        bobSMPResults.push(result);
      });

      // Alice initiates SMP
      await aliceSession.initiateSMP(aliceSecret);

      // Process SMP messages
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        if (message.type === 'smp') {
          await to.processSMPMessage(message);
        }
      }

      // Bob responds with different secret
      await bobSession.respondToSMP(bobSecret);

      // Process remaining SMP messages
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        if (message.type === 'smp') {
          await to.processSMPMessage(message);
        }
      }

      // Both should have failed SMP results
      expect(aliceSMPResults).toContainEqual(
        expect.objectContaining({ result: SMP_RESULT.FAILURE })
      );
      expect(bobSMPResults).toContainEqual(
        expect.objectContaining({ result: SMP_RESULT.FAILURE })
      );
    });
  });

  describe('Error Recovery and Edge Cases', () => {
    test('should handle network interruptions during AKE', async () => {
      // Start AKE
      await aliceSession.startAKE();
      
      // Process first message
      const firstMessage = messageQueue.shift();
      await firstMessage.to.processMessage(firstMessage.message);
      
      // Simulate network interruption by clearing queue
      messageQueue.length = 0;
      
      // Alice should timeout and reset
      jest.useFakeTimers();
      jest.advanceTimersByTime(31000); // Timeout after 30s
      
<<<<<<< HEAD
      expect(aliceSession.isEncrypted()).toBe(false);
=======
      expect(aliceSession.state).toBe(STATE.PLAINTEXT);
>>>>>>> origin/feature/browser-extension
      
      jest.useRealTimers();
    });

    test('should handle malformed messages gracefully', async () => {
      const malformedMessages = [
        '?OTR:INVALID_BASE64!',
        '?OTR:',
        '?OTR:AAMG',
        'not-an-otr-message'
      ];

      for (const message of malformedMessages) {
        const result = await aliceSession.processMessage(message);
        expect(result.type).toMatch(/error|plaintext/);
      }
    });

    test('should handle concurrent AKE attempts', async () => {
      // Both Alice and Bob try to start AKE simultaneously
      const alicePromise = aliceSession.startAKE();
      const bobPromise = bobSession.startAKE();

      await Promise.all([alicePromise, bobPromise]);

      // Process all messages
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        await to.processMessage(message);
      }

      // One should succeed in establishing encryption
      const encrypted = aliceSession.isEncrypted() || bobSession.isEncrypted();
      expect(encrypted).toBe(true);
    });
  });

  describe('Session Management', () => {
    test('should handle session termination gracefully', async () => {
      // Complete AKE
      await aliceSession.startAKE();
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        await to.processMessage(message);
      }

      expect(aliceSession.isEncrypted()).toBe(true);
      expect(bobSession.isEncrypted()).toBe(true);

      // Alice ends session
      await aliceSession.endSession();

      expect(aliceSession.state).toBe(STATE.PLAINTEXT);
      expect(aliceSession.isEncrypted()).toBe(false);
    });

    test('should handle session restart', async () => {
      // Complete first session
      await aliceSession.startAKE();
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        await to.processMessage(message);
      }

      const firstSessionKeys = aliceSession.keys;

      // End and restart session
      await aliceSession.endSession();
      await bobSession.endSession();

<<<<<<< HEAD
      await aliceSession.startOtr();
=======
      await aliceSession.startAKE();
>>>>>>> origin/feature/browser-extension
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        await to.processMessage(message);
      }

      // New session should have different keys (forward secrecy)
      expect(aliceSession.keys).not.toEqual(firstSessionKeys);
      expect(aliceSession.isEncrypted()).toBe(true);
      expect(bobSession.isEncrypted()).toBe(true);
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle rapid message exchange', async () => {
      // Complete AKE
      await aliceSession.startAKE();
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        await to.processMessage(message);
      }

      // Send 100 messages rapidly
      const messageCount = 100;
      const startTime = Date.now();

      for (let i = 0; i < messageCount; i++) {
        const message = `Message ${i}`;
        const encrypted = await aliceSession.encryptMessage(message);
        const decrypted = await bobSession.decryptMessage(encrypted);
        expect(decrypted).toBe(message);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should process 100 messages in under 5 seconds
      expect(duration).toBeLessThan(5000);
    });

    test('should handle large messages', async () => {
      // Complete AKE
      await aliceSession.startAKE();
      while (messageQueue.length > 0) {
        const { to, message } = messageQueue.shift();
        await to.processMessage(message);
      }

      // Send a 1MB message
      const largeMessage = 'A'.repeat(1024 * 1024);
      const encrypted = await aliceSession.encryptMessage(largeMessage);
      const decrypted = await bobSession.decryptMessage(encrypted);

      expect(decrypted).toBe(largeMessage);
    });
  });
});
