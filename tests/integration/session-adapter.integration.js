/**
 * Integration tests for OTR Session and Platform Adapters
 */

import { OtrSession } from '../../src/core/session';
import { GenericAdapter } from '../../src/platforms/generic';
import { DiscordAdapter } from '../../src/platforms/discord';
import { TeamsAdapter } from '../../src/platforms/teams';
import { SlackAdapter } from '../../src/platforms/slack';

// Mock DOM elements for platform adapters
class MockElement {
  constructor() {
    this.classList = {
      add: jest.fn(),
      remove: jest.fn(),
      contains: jest.fn()
    };
    this.style = {};
    this.innerHTML = '';
    this.appendChild = jest.fn();
    this.querySelector = jest.fn();
    this.querySelectorAll = jest.fn().mockReturnValue([]);
    this.addEventListener = jest.fn();
    this.removeEventListener = jest.fn();
  }
}

// Mock document
global.document = {
  createElement: jest.fn().mockImplementation(() => new MockElement()),
  querySelector: jest.fn().mockImplementation(() => new MockElement()),
  body: { appendChild: jest.fn() }
};

describe('OTR Session and Platform Adapter Integration', () => {
  // Test with Generic Adapter
  describe('Generic Adapter Integration', () => {
    let adapter;
    let aliceSession;
    let bobSession;
    
    beforeEach(async () => {
      // Create adapter
      adapter = new GenericAdapter();
      await adapter.init();
      
      // Create sessions
      aliceSession = await adapter.getSession('bob');
      bobSession = await adapter.getSession('alice');
    });
    
    test('should create sessions through adapter', () => {
      expect(aliceSession).toBeDefined();
      expect(bobSession).toBeDefined();
      expect(adapter.sessions.size).toBe(2);
      expect(adapter.sessions.has('bob')).toBe(true);
      expect(adapter.sessions.has('alice')).toBe(true);
    });
    
    test('should process messages through adapter', async () => {
      // Mock session methods
      aliceSession.processIncoming = jest.fn().mockResolvedValue({ message: 'Hello from Bob' });
      aliceSession.processOutgoing = jest.fn().mockResolvedValue('Encrypted message from Alice');
      
      bobSession.processIncoming = jest.fn().mockResolvedValue({ message: 'Hello from Alice' });
      bobSession.processOutgoing = jest.fn().mockResolvedValue('Encrypted message from Bob');
      
      // Process outgoing message from Alice
      const aliceOutgoing = await adapter.processOutgoing('Hello from Alice', 'bob');
      
      // Process incoming message to Bob
      const bobIncoming = await adapter.processIncoming(aliceOutgoing, 'alice');
      
      // Process outgoing message from Bob
      const bobOutgoing = await adapter.processOutgoing('Hello from Bob', 'alice');
      
      // Process incoming message to Alice
      const aliceIncoming = await adapter.processIncoming(bobOutgoing, 'bob');
      
      // Verify session methods were called
      expect(aliceSession.processOutgoing).toHaveBeenCalledWith('Hello from Alice');
      expect(bobSession.processIncoming).toHaveBeenCalledWith(aliceOutgoing);
      expect(bobSession.processOutgoing).toHaveBeenCalledWith('Hello from Bob');
      expect(aliceSession.processIncoming).toHaveBeenCalledWith(bobOutgoing);
    });
  });
  
  // Test with Discord Adapter
  describe('Discord Adapter Integration', () => {
    let adapter;
    
    beforeEach(async () => {
      // Create adapter
      adapter = new DiscordAdapter();
      
      // Mock Discord-specific methods
      adapter.findMessageInput = jest.fn().mockReturnValue(new MockElement());
      adapter.findMessageContainer = jest.fn().mockReturnValue(new MockElement());
      adapter.findUserInfo = jest.fn().mockReturnValue({ id: 'user123', name: 'TestUser' });
      
      await adapter.init();
    });
    
    test('should initialize Discord adapter', () => {
      expect(adapter).toBeDefined();
      expect(adapter.initialized).toBe(true);
      expect(adapter.platform).toBe('discord');
    });
    
    test('should create session for Discord user', async () => {
      const session = await adapter.getSession('user123');
      
      expect(session).toBeDefined();
      expect(adapter.sessions.has('user123')).toBe(true);
    });
    
    test('should process messages with Discord formatting', async () => {
      // Get session
      const session = await adapter.getSession('user123');
      
      // Mock session methods
      session.processIncoming = jest.fn().mockResolvedValue({ message: 'Hello from Discord' });
      session.processOutgoing = jest.fn().mockResolvedValue('Encrypted Discord message');
      
      // Process outgoing message
      const outgoing = await adapter.processOutgoing('Hello from Discord', 'user123');
      
      // Process incoming message
      const incoming = await adapter.processIncoming(outgoing, 'user123');
      
      // Verify session methods were called
      expect(session.processOutgoing).toHaveBeenCalledWith('Hello from Discord');
      expect(session.processIncoming).toHaveBeenCalledWith(outgoing);
    });
  });
  
  // Test with Teams Adapter
  describe('Teams Adapter Integration', () => {
    let adapter;
    
    beforeEach(async () => {
      // Create adapter
      adapter = new TeamsAdapter();
      
      // Mock Teams-specific methods
      adapter.findMessageInput = jest.fn().mockReturnValue(new MockElement());
      adapter.findMessageContainer = jest.fn().mockReturnValue(new MockElement());
      adapter.findUserInfo = jest.fn().mockReturnValue({ id: 'user456', name: 'TeamsUser' });
      
      await adapter.init();
    });
    
    test('should initialize Teams adapter', () => {
      expect(adapter).toBeDefined();
      expect(adapter.initialized).toBe(true);
      expect(adapter.platform).toBe('teams');
    });
    
    test('should create session for Teams user', async () => {
      const session = await adapter.getSession('user456');
      
      expect(session).toBeDefined();
      expect(adapter.sessions.has('user456')).toBe(true);
    });
    
    test('should process messages with Teams formatting', async () => {
      // Get session
      const session = await adapter.getSession('user456');
      
      // Mock session methods
      session.processIncoming = jest.fn().mockResolvedValue({ message: 'Hello from Teams' });
      session.processOutgoing = jest.fn().mockResolvedValue('Encrypted Teams message');
      
      // Process outgoing message
      const outgoing = await adapter.processOutgoing('Hello from Teams', 'user456');
      
      // Process incoming message
      const incoming = await adapter.processIncoming(outgoing, 'user456');
      
      // Verify session methods were called
      expect(session.processOutgoing).toHaveBeenCalledWith('Hello from Teams');
      expect(session.processIncoming).toHaveBeenCalledWith(outgoing);
    });
  });
  
  // Test with Slack Adapter
  describe('Slack Adapter Integration', () => {
    let adapter;
    
    beforeEach(async () => {
      // Create adapter
      adapter = new SlackAdapter();
      
      // Mock Slack-specific methods
      adapter.findMessageInput = jest.fn().mockReturnValue(new MockElement());
      adapter.findMessageContainer = jest.fn().mockReturnValue(new MockElement());
      adapter.findUserInfo = jest.fn().mockReturnValue({ id: 'user789', name: 'SlackUser' });
      
      await adapter.init();
    });
    
    test('should initialize Slack adapter', () => {
      expect(adapter).toBeDefined();
      expect(adapter.initialized).toBe(true);
      expect(adapter.platform).toBe('slack');
    });
    
    test('should create session for Slack user', async () => {
      const session = await adapter.getSession('user789');
      
      expect(session).toBeDefined();
      expect(adapter.sessions.has('user789')).toBe(true);
    });
    
    test('should process messages with Slack formatting', async () => {
      // Get session
      const session = await adapter.getSession('user789');
      
      // Mock session methods
      session.processIncoming = jest.fn().mockResolvedValue({ message: 'Hello from Slack' });
      session.processOutgoing = jest.fn().mockResolvedValue('Encrypted Slack message');
      
      // Process outgoing message
      const outgoing = await adapter.processOutgoing('Hello from Slack', 'user789');
      
      // Process incoming message
      const incoming = await adapter.processIncoming(outgoing, 'user789');
      
      // Verify session methods were called
      expect(session.processOutgoing).toHaveBeenCalledWith('Hello from Slack');
      expect(session.processIncoming).toHaveBeenCalledWith(outgoing);
    });
  });
});
