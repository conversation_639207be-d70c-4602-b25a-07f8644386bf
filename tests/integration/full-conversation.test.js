/**
 * Full Conversation Integration Tests
 * Based on libOTR/coyim comprehensive conversation testing patterns
 * Tests complete OTR conversations from initiation to completion
 */

import { OtrSession } from '../../src/core/session';
import { SMPHandler, SMP_RESULT } from '../../src/core/protocol/smp';
import { STATE } from '../../src/core/state';

describe('Full OTR Conversation Integration Tests', () => {
  let aliceSession, bobSession;
  let aliceMessages = [];
  let bobMessages = [];
  let aliceEvents = [];
  let bobEvents = [];

  const aliceSendMessage = jest.fn(message => {
    bobMessages.push(message);
  });

  const bobSendMessage = jest.fn(message => {
    aliceMessages.push(message);
  });

  const aliceEventHandler = jest.fn(event => {
    aliceEvents.push(event);
  });

  const bobEventHandler = jest.fn(event => {
    bobEvents.push(event);
  });

  beforeEach(async () => {
    // Reset state
    aliceMessages = [];
    bobMessages = [];
    aliceEvents = [];
    bobEvents = [];

    // Create sessions
    aliceSession = new OtrSession('bob', {
      sendMessage: aliceSendMessage,
      testing: true
    });

    bobSession = new OtrSession('alice', {
      sendMessage: bobSendMessage,
      testing: true
    });

    // Set up event handlers
    aliceSession.on('stateChange', aliceEventHandler);
    aliceSession.on('smpResult', aliceEventHandler);
    aliceSession.on('error', aliceEventHandler);

    bobSession.on('stateChange', bobEventHandler);
    bobSession.on('smpResult', bobEventHandler);
    bobSession.on('error', bobEventHandler);

    await Promise.all([
      aliceSession.init(),
      bobSession.init()
    ]);
  });

  afterEach(() => {
    if (aliceSession) aliceSession.destroy();
    if (bobSession) bobSession.destroy();
  });

  describe('Complete OTR Handshake Flow', () => {
    test('should complete full AKE handshake step by step', async () => {
      // Step 1: Alice initiates OTR
      await aliceSession.startOtr();
      
      expect(aliceSession.state.getState()).toBeGreaterThan(STATE.PLAINTEXT);
      expect(aliceMessages.length).toBeGreaterThan(0);

      // Step 2: Process all messages until handshake is complete
      let iterations = 0;
      const maxIterations = 20;

      while (iterations < maxIterations && 
             (aliceSession.state.getState() !== STATE.ENCRYPTED || 
              bobSession.state.getState() !== STATE.ENCRYPTED)) {
        
        iterations++;

        // Process Bob's messages
        const bobMessageCount = bobMessages.length;
        for (let i = 0; i < bobMessageCount; i++) {
          const message = bobMessages.shift();
          await bobSession.processIncoming(message);
        }

        // Process Alice's messages
        const aliceMessageCount = aliceMessages.length;
        for (let i = 0; i < aliceMessageCount; i++) {
          const message = aliceMessages.shift();
          await aliceSession.processIncoming(message);
        }

        // Small delay to allow async processing
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      // Verify both sessions are encrypted
      expect(aliceSession.state.getState()).toBe(STATE.ENCRYPTED);
      expect(bobSession.state.getState()).toBe(STATE.ENCRYPTED);
      expect(iterations).toBeLessThan(maxIterations);

      // Verify events were fired
      expect(aliceEvents.length).toBeGreaterThan(0);
      expect(bobEvents.length).toBeGreaterThan(0);
    });

    test('should handle simultaneous OTR initiation', async () => {
      // Both parties start OTR simultaneously
      await Promise.all([
        aliceSession.startOtr(),
        bobSession.startOtr()
      ]);

      // Process messages until both are encrypted
      let iterations = 0;
      const maxIterations = 30;

      while (iterations < maxIterations && 
             (aliceSession.state.getState() !== STATE.ENCRYPTED || 
              bobSession.state.getState() !== STATE.ENCRYPTED)) {
        
        iterations++;

        // Process all pending messages
        const allBobMessages = [...bobMessages];
        const allAliceMessages = [...aliceMessages];
        
        bobMessages.length = 0;
        aliceMessages.length = 0;

        // Process Bob's messages
        for (const message of allBobMessages) {
          await bobSession.processIncoming(message);
        }

        // Process Alice's messages
        for (const message of allAliceMessages) {
          await aliceSession.processIncoming(message);
        }

        await new Promise(resolve => setTimeout(resolve, 10));
      }

      // Should eventually reach encrypted state
      expect(aliceSession.state.getState()).toBe(STATE.ENCRYPTED);
      expect(bobSession.state.getState()).toBe(STATE.ENCRYPTED);
    });
  });

  describe('Message Exchange Flow', () => {
    beforeEach(async () => {
      // Establish encrypted session for message tests
      await aliceSession.startOtr();
      await bobSession.startOtr();

      // Process handshake messages
      let iterations = 0;
      while (iterations < 20 && 
             (aliceSession.state.getState() !== STATE.ENCRYPTED || 
              bobSession.state.getState() !== STATE.ENCRYPTED)) {
        
        iterations++;
        
        const bobMsgs = [...bobMessages];
        const aliceMsgs = [...aliceMessages];
        bobMessages.length = 0;
        aliceMessages.length = 0;

        for (const msg of bobMsgs) {
          await bobSession.processIncoming(msg);
        }
        for (const msg of aliceMsgs) {
          await aliceSession.processIncoming(msg);
        }

        await new Promise(resolve => setTimeout(resolve, 5));
      }

      // Clear message buffers
      aliceMessages.length = 0;
      bobMessages.length = 0;
    });

    test('should exchange messages bidirectionally', async () => {
      const testMessages = [
        { sender: 'alice', content: 'Hello Bob!' },
        { sender: 'bob', content: 'Hi Alice!' },
        { sender: 'alice', content: 'How are you?' },
        { sender: 'bob', content: 'I am fine, thanks!' },
        { sender: 'alice', content: 'Great to hear!' }
      ];

      const receivedMessages = [];

      for (const testMsg of testMessages) {
        if (testMsg.sender === 'alice') {
          const encrypted = await aliceSession.encryptMessage(testMsg.content);
          const decrypted = await bobSession.decryptMessage(encrypted);
          receivedMessages.push({ sender: 'alice', content: decrypted });
        } else {
          const encrypted = await bobSession.encryptMessage(testMsg.content);
          const decrypted = await aliceSession.decryptMessage(encrypted);
          receivedMessages.push({ sender: 'bob', content: decrypted });
        }
      }

      // Verify all messages were received correctly
      expect(receivedMessages).toEqual(testMessages);
    });

    test('should handle rapid message exchange', async () => {
      const messageCount = 50;
      const sentMessages = [];
      const receivedMessages = [];

      // Send rapid messages from both sides
      for (let i = 0; i < messageCount; i++) {
        const aliceMsg = `Alice message ${i}`;
        const bobMsg = `Bob message ${i}`;

        sentMessages.push(aliceMsg, bobMsg);

        // Alice sends
        const aliceEncrypted = await aliceSession.encryptMessage(aliceMsg);
        const aliceDecrypted = await bobSession.decryptMessage(aliceEncrypted);
        receivedMessages.push(aliceDecrypted);

        // Bob sends
        const bobEncrypted = await bobSession.encryptMessage(bobMsg);
        const bobDecrypted = await aliceSession.decryptMessage(bobEncrypted);
        receivedMessages.push(bobDecrypted);
      }

      expect(receivedMessages).toEqual(sentMessages);
    });

    test('should handle empty and special messages', async () => {
      const specialMessages = [
        '', // Empty message
        ' ', // Space
        '\n', // Newline
        '🔒🔑💬', // Emojis
        'A'.repeat(1000), // Long message
        '{"json": "data"}', // JSON
        '<xml>data</xml>', // XML
        'Special chars: !@#$%^&*()_+-=[]{}|;:,.<>?'
      ];

      for (const msg of specialMessages) {
        const encrypted = await aliceSession.encryptMessage(msg);
        const decrypted = await bobSession.decryptMessage(encrypted);
        expect(decrypted).toBe(msg);
      }
    });
  });

  describe('Complete SMP Authentication Flow', () => {
    beforeEach(async () => {
      // Establish encrypted session
      await aliceSession.startOtr();
      await bobSession.startOtr();

      let iterations = 0;
      while (iterations < 20 && 
             (aliceSession.state.getState() !== STATE.ENCRYPTED || 
              bobSession.state.getState() !== STATE.ENCRYPTED)) {
        
        iterations++;
        
        const bobMsgs = [...bobMessages];
        const aliceMsgs = [...aliceMessages];
        bobMessages.length = 0;
        aliceMessages.length = 0;

        for (const msg of bobMsgs) {
          await bobSession.processIncoming(msg);
        }
        for (const msg of aliceMsgs) {
          await aliceSession.processIncoming(msg);
        }

        await new Promise(resolve => setTimeout(resolve, 5));
      }

      aliceMessages.length = 0;
      bobMessages.length = 0;
      aliceEvents.length = 0;
      bobEvents.length = 0;
    });

    test('should complete successful SMP authentication', async () => {
      const sharedSecret = 'our shared secret';
      const question = 'What is our shared secret?';

      // Alice initiates SMP
      await aliceSession.initiateSMP(sharedSecret, question);

      // Process SMP messages
      let smpIterations = 0;
      const maxSMPIterations = 10;

      while (smpIterations < maxSMPIterations) {
        smpIterations++;

        // Process any pending messages
        const bobMsgs = [...bobMessages];
        const aliceMsgs = [...aliceMessages];
        bobMessages.length = 0;
        aliceMessages.length = 0;

        for (const msg of bobMsgs) {
          await bobSession.processIncoming(msg);
        }

        // Bob responds to SMP if needed
        if (bobSession.smpHandler && bobSession.smpHandler.getLastReceivedQuestion() === question) {
          await bobSession.respondToSMP(sharedSecret);
        }

        for (const msg of aliceMsgs) {
          await aliceSession.processIncoming(msg);
        }

        await new Promise(resolve => setTimeout(resolve, 10));

        // Check if SMP is complete
        const aliceSMPComplete = aliceEvents.some(e => e.type === 'smpResult');
        const bobSMPComplete = bobEvents.some(e => e.type === 'smpResult');

        if (aliceSMPComplete && bobSMPComplete) {
          break;
        }
      }

      // Verify SMP completed successfully
      const aliceSMPResults = aliceEvents.filter(e => e.type === 'smpResult');
      const bobSMPResults = bobEvents.filter(e => e.type === 'smpResult');

      expect(aliceSMPResults.length).toBeGreaterThan(0);
      expect(bobSMPResults.length).toBeGreaterThan(0);
    });

    test('should handle SMP with wrong secret', async () => {
      const aliceSecret = 'alice secret';
      const bobSecret = 'bob secret';
      const question = 'What is the secret?';

      // Alice initiates SMP
      await aliceSession.initiateSMP(aliceSecret, question);

      // Process SMP with wrong secret
      let smpIterations = 0;
      while (smpIterations < 10) {
        smpIterations++;

        const bobMsgs = [...bobMessages];
        const aliceMsgs = [...aliceMessages];
        bobMessages.length = 0;
        aliceMessages.length = 0;

        for (const msg of bobMsgs) {
          await bobSession.processIncoming(msg);
        }

        if (bobSession.smpHandler && bobSession.smpHandler.getLastReceivedQuestion() === question) {
          await bobSession.respondToSMP(bobSecret); // Wrong secret
        }

        for (const msg of aliceMsgs) {
          await aliceSession.processIncoming(msg);
        }

        await new Promise(resolve => setTimeout(resolve, 10));

        const aliceSMPComplete = aliceEvents.some(e => e.type === 'smpResult');
        const bobSMPComplete = bobEvents.some(e => e.type === 'smpResult');

        if (aliceSMPComplete && bobSMPComplete) {
          break;
        }
      }

      // Should complete but with failure result
      const aliceSMPResults = aliceEvents.filter(e => e.type === 'smpResult');
      const bobSMPResults = bobEvents.filter(e => e.type === 'smpResult');

      expect(aliceSMPResults.length + bobSMPResults.length).toBeGreaterThan(0);
    });
  });

  describe('Session Lifecycle Management', () => {
    test('should handle complete session lifecycle', async () => {
      // 1. Start session
      await aliceSession.startOtr();
      await bobSession.startOtr();

      // 2. Complete handshake
      let iterations = 0;
      while (iterations < 20 && 
             (aliceSession.state.getState() !== STATE.ENCRYPTED || 
              bobSession.state.getState() !== STATE.ENCRYPTED)) {
        
        iterations++;
        
        const bobMsgs = [...bobMessages];
        const aliceMsgs = [...aliceMessages];
        bobMessages.length = 0;
        aliceMessages.length = 0;

        for (const msg of bobMsgs) {
          await bobSession.processIncoming(msg);
        }
        for (const msg of aliceMsgs) {
          await aliceSession.processIncoming(msg);
        }

        await new Promise(resolve => setTimeout(resolve, 5));
      }

      expect(aliceSession.state.getState()).toBe(STATE.ENCRYPTED);
      expect(bobSession.state.getState()).toBe(STATE.ENCRYPTED);

      // 3. Exchange messages
      const message1 = await aliceSession.encryptMessage('Test message 1');
      const decrypted1 = await bobSession.decryptMessage(message1);
      expect(decrypted1).toBe('Test message 1');

      const message2 = await bobSession.encryptMessage('Test message 2');
      const decrypted2 = await aliceSession.decryptMessage(message2);
      expect(decrypted2).toBe('Test message 2');

      // 4. End session
      await aliceSession.endSession();
      expect(aliceSession.state.getState()).toBe(STATE.PLAINTEXT);

      // 5. Verify session is properly cleaned up
      await expect(
        aliceSession.encryptMessage('After end')
      ).rejects.toThrow();
    });

    test('should handle session refresh/reconnection', async () => {
      // Establish session
      await aliceSession.startOtr();
      await bobSession.startOtr();

      // Complete handshake (simplified)
      let iterations = 0;
      while (iterations < 20 && 
             (aliceSession.state.getState() !== STATE.ENCRYPTED || 
              bobSession.state.getState() !== STATE.ENCRYPTED)) {
        
        iterations++;
        
        const bobMsgs = [...bobMessages];
        const aliceMsgs = [...aliceMessages];
        bobMessages.length = 0;
        aliceMessages.length = 0;

        for (const msg of bobMsgs) {
          await bobSession.processIncoming(msg);
        }
        for (const msg of aliceMsgs) {
          await aliceSession.processIncoming(msg);
        }

        await new Promise(resolve => setTimeout(resolve, 5));
      }

      // Send message before refresh
      const beforeRefresh = await aliceSession.encryptMessage('Before refresh');
      const decrypted1 = await bobSession.decryptMessage(beforeRefresh);
      expect(decrypted1).toBe('Before refresh');

      // Simulate refresh/reconnection
      if (aliceSession.refreshSession) {
        await aliceSession.refreshSession();
      }
      if (bobSession.refreshSession) {
        await bobSession.refreshSession();
      }

      // Send message after refresh
      const afterRefresh = await aliceSession.encryptMessage('After refresh');
      const decrypted2 = await bobSession.decryptMessage(afterRefresh);
      expect(decrypted2).toBe('After refresh');
    });
  });
});
