/**
 * Integration tests for OTR
 */

import { OtrSession } from '../../src/core/session';

describe('OTR Integration Tests', () => {
  let aliceSession;
  let bobSession;
  let aliceMessages = [];
  let bobMessages = [];
  
  // Create send message functions that capture messages for testing
  const aliceSendMessage = jest.fn(message => {
    bobMessages.push(message);
  });
  
  const bobSendMessage = jest.fn(message => {
    aliceMessages.push(message);
  });
  
  beforeEach(async () => {
    // Reset captured messages
    aliceMessages = [];
    bobMessages = [];
    
    // Create sessions
    aliceSession = new OtrSession('bob', {
      sendMessage: aliceSendMessage,
      testing: true
    });
    
    bobSession = new OtrSession('alice', {
      sendMessage: bobSendMessage,
      testing: true
    });
    
    // Initialize both sessions
    await Promise.all([
      aliceSession.init(),
      bobSession.init()
    ]);
  });
  
  afterEach(() => {
    // Clean up
    aliceSession = null;
    bobSession = null;
  });
  
  test('should initialize sessions properly', () => {
    expect(aliceSession).toBeDefined();
    expect(bobSession).toBeDefined();
    expect(aliceSession.peer).toBe('bob');
    expect(bobSession.peer).toBe('alice');
  });
  
  test('should send and receive messages', async () => {
    const aliceMessage = 'Hello from Alice!';
    const bobMessage = 'Hello from Bob!';
    
    // Alice sends a message to Bob
    await aliceSession.processOutgoing(aliceMessage);
    expect(aliceSendMessage).toHaveBeenCalled();
    
    // Bob receives Alice's message
    const processedByBob = await bobSession.processIncoming(aliceMessage);
    expect(processedByBob.message).toBe(aliceMessage);
    
    // Bob sends a message to Alice
    await bobSession.processOutgoing(bobMessage);
    expect(bobSendMessage).toHaveBeenCalled();
    
    // Alice receives Bob's message
    const processedByAlice = await aliceSession.processIncoming(bobMessage);
    expect(processedByAlice.message).toBe(bobMessage);
  });
}); 