/**
 * Realistic Steganography Integration Test
 * Tests with realistic synthetic images that simulate real-world scenarios
 */

const { SteganographyOTR, STEGO_MESSAGE_TYPE } = require('../../src/core/steganography');
const { OTRSteganographySession } = require('../../src/core/steganography/otr-stego-session');

// Mock stego-js for realistic testing
jest.mock('@masknet/stego-js', () => {
  const hiddenDataStore = new Map();
  
  return {
    encode: jest.fn().mockImplementation(async (imageData, data) => {
      const imageKey = `${imageData.width}x${imageData.height}_${Date.now()}_${Math.random()}`;
      hiddenDataStore.set(imageKey, data);
      
      return {
        ...imageData,
        _hiddenData: data,
        _imageKey: imageKey
      };
    }),
    decode: jest.fn().mockImplementation(async (imageData) => {
      if (imageData && imageData._hiddenData) {
        return imageData._hiddenData;
      }
      
      if (imageData && imageData._imageKey && hiddenDataStore.has(imageData._imageKey)) {
        return hiddenDataStore.get(imageData._imageKey);
      }
      
      return null;
    })
  };
});

/**
 * Generate realistic synthetic images that simulate real photos
 */
function generateRealisticImage(width, height, type = 'photo') {
  const data = new Uint8ClampedArray(width * height * 4);
  
  switch (type) {
    case 'photo':
      // Simulate a photo with natural color variations
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const i = (y * width + x) * 4;
          const noise = Math.random() * 50 - 25;
          const baseR = 120 + Math.sin(x * 0.01) * 30 + noise;
          const baseG = 150 + Math.cos(y * 0.01) * 40 + noise;
          const baseB = 100 + Math.sin((x + y) * 0.005) * 50 + noise;
          
          data[i] = Math.max(0, Math.min(255, baseR));
          data[i + 1] = Math.max(0, Math.min(255, baseG));
          data[i + 2] = Math.max(0, Math.min(255, baseB));
          data[i + 3] = 255;
        }
      }
      break;
      
    case 'landscape':
      // Simulate a landscape photo
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const i = (y * width + x) * 4;
          const skyFactor = Math.max(0, 1 - y / height);
          const groundFactor = Math.max(0, y / height - 0.6);
          
          data[i] = skyFactor * 135 + groundFactor * 90 + Math.random() * 20;
          data[i + 1] = skyFactor * 206 + groundFactor * 120 + Math.random() * 20;
          data[i + 2] = skyFactor * 235 + groundFactor * 60 + Math.random() * 20;
          data[i + 3] = 255;
        }
      }
      break;
      
    case 'portrait':
      // Simulate a portrait photo
      const centerX = width / 2;
      const centerY = height / 2;
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const i = (y * width + x) * 4;
          const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
          const faceFactor = Math.max(0, 1 - distance / (Math.min(width, height) * 0.3));
          
          data[i] = 220 * faceFactor + 100 * (1 - faceFactor) + Math.random() * 15;
          data[i + 1] = 180 * faceFactor + 80 * (1 - faceFactor) + Math.random() * 15;
          data[i + 2] = 150 * faceFactor + 60 * (1 - faceFactor) + Math.random() * 15;
          data[i + 3] = 255;
        }
      }
      break;
      
    default:
      // Random realistic image
      for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.random() * 255;
        data[i + 1] = Math.random() * 255;
        data[i + 2] = Math.random() * 255;
        data[i + 3] = 255;
      }
  }
  
  return {
    data,
    width,
    height,
    type,
    size: data.length
  };
}

describe('Realistic Steganography Integration', () => {
  let alice, bob;
  let realisticImages;

  beforeAll(() => {
    console.log('🎨 Generating realistic synthetic images...');
    
    // Generate various realistic images
    realisticImages = [
      generateRealisticImage(1920, 1080, 'landscape'),
      generateRealisticImage(800, 600, 'photo'),
      generateRealisticImage(1024, 768, 'portrait'),
      generateRealisticImage(640, 480, 'photo'),
      generateRealisticImage(1200, 800, 'landscape'),
      generateRealisticImage(512, 512, 'portrait'),
      generateRealisticImage(1600, 900, 'photo'),
      generateRealisticImage(900, 600, 'landscape'),
      generateRealisticImage(1024, 1024, 'portrait'),
      generateRealisticImage(768, 1024, 'photo')
    ];
    
    console.log(`✅ Generated ${realisticImages.length} realistic images:`);
    realisticImages.forEach((img, i) => {
      console.log(`   ${i + 1}. ${img.width}x${img.height} ${img.type} (${(img.size / 1024).toFixed(1)}KB)`);
    });
    console.log('');
  });

  beforeEach(() => {
    alice = new OTRSteganographySession({
      testing: true,
      steganographyEnabled: true,
      stegoPassword: 'realistic-test-password',
      clientName: 'Alice'
    });
    
    bob = new OTRSteganographySession({
      testing: true,
      steganographyEnabled: true,
      stegoPassword: 'realistic-test-password',
      clientName: 'Bob'
    });
  });

  afterEach(() => {
    if (alice) alice.destroy();
    if (bob) bob.destroy();
  });

  describe('Complete OTR Handshake via Steganography', () => {
    test('should perform full AKE handshake using realistic images', async () => {
      console.log('🤝 Testing complete OTR AKE handshake via steganography...');
      
      // Use different image types for each handshake step
      const handshakeSteps = [
        {
          step: 1,
          type: 'DH_COMMIT',
          sender: 'Alice',
          image: realisticImages[0], // landscape
          message: {
            type: 'DH_COMMIT',
            instanceTag: alice.instanceTag,
            protocolVersion: 3,
            dhCommit: 'realistic_dh_commit_' + Date.now(),
            encryptedGx: 'encrypted_gx_data_' + Math.random().toString(36),
            hashedGx: 'hashed_gx_' + Math.random().toString(36)
          }
        },
        {
          step: 2,
          type: 'DH_KEY',
          sender: 'Bob',
          image: realisticImages[1], // photo
          message: {
            type: 'DH_KEY',
            instanceTag: bob.instanceTag,
            protocolVersion: 3,
            dhKey: 'realistic_dh_key_' + Date.now(),
            publicKey: 'bobs_public_key_' + Math.random().toString(36)
          }
        },
        {
          step: 3,
          type: 'REVEAL_SIGNATURE',
          sender: 'Alice',
          image: realisticImages[2], // portrait
          message: {
            type: 'REVEAL_SIGNATURE',
            instanceTag: alice.instanceTag,
            revealedKey: 'revealed_key_' + Date.now(),
            signature: 'alice_signature_' + Math.random().toString(36),
            macKey: 'mac_key_' + Math.random().toString(36)
          }
        },
        {
          step: 4,
          type: 'SIGNATURE',
          sender: 'Bob',
          image: realisticImages[3], // photo
          message: {
            type: 'SIGNATURE',
            instanceTag: bob.instanceTag,
            signature: 'bob_final_signature_' + Date.now(),
            macData: 'mac_verification_' + Math.random().toString(36)
          }
        }
      ];
      
      console.log('📝 Executing 4-step AKE handshake:');
      
      const handshakeResults = [];
      
      for (const step of handshakeSteps) {
        console.log(`   Step ${step.step}: ${step.type} from ${step.sender} via ${step.image.type} image (${step.image.width}x${step.image.height})`);
        
        try {
          let stegoImage, extractedMessage;
          
          if (step.sender === 'Alice') {
            stegoImage = await alice.sendStegoAKE(step.message, step.image);
            extractedMessage = await bob.processStegoAKE(stegoImage);
          } else {
            stegoImage = await bob.sendStegoAKE(step.message, step.image);
            extractedMessage = await alice.processStegoAKE(stegoImage);
          }
          
          expect(extractedMessage).toEqual(step.message);
          
          handshakeResults.push({
            step: step.step,
            type: step.type,
            sender: step.sender,
            imageType: step.image.type,
            success: true
          });
          
          console.log(`   ✅ Step ${step.step}: SUCCESS`);
          
        } catch (error) {
          console.log(`   ❌ Step ${step.step}: FAILED - ${error.message}`);
          handshakeResults.push({
            step: step.step,
            type: step.type,
            sender: step.sender,
            success: false,
            error: error.message
          });
        }
      }
      
      // Verify complete handshake
      const successfulSteps = handshakeResults.filter(r => r.success).length;
      expect(successfulSteps).toBe(4);
      
      console.log(`✅ Complete AKE handshake successful: ${successfulSteps}/4 steps`);
      console.log('🔐 OTR session established via steganography!');
    }, 30000);
  });

  describe('Realistic Message Exchange', () => {
    test('should exchange messages using different image types', async () => {
      console.log('💬 Testing realistic message exchange...');
      
      const conversation = [
        {
          sender: 'Alice',
          message: 'Hey Bob! I\'m sending this via a landscape photo. Can you see it?',
          imageType: 'landscape'
        },
        {
          sender: 'Bob',
          message: 'Yes Alice! Receiving via portrait image. This steganography is incredible! 🤯',
          imageType: 'portrait'
        },
        {
          sender: 'Alice',
          message: 'Let\'s share some sensitive info: Meeting location is 37.7749° N, 122.4194° W',
          imageType: 'photo'
        },
        {
          sender: 'Bob',
          message: 'Got the coordinates! Time: 3:30 PM. Password: "hidden_in_plain_sight"',
          imageType: 'landscape'
        },
        {
          sender: 'Alice',
          message: 'Perfect! This conversation is completely invisible to anyone else! 🕵️‍♀️',
          imageType: 'portrait'
        }
      ];
      
      console.log('📝 Simulating realistic conversation:');
      
      const conversationResults = [];
      
      for (let i = 0; i < conversation.length; i++) {
        const { sender, message, imageType } = conversation[i];
        
        // Find an image of the specified type
        const image = realisticImages.find(img => img.type === imageType) || realisticImages[i % realisticImages.length];
        
        console.log(`   ${sender}: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
        console.log(`   Using ${image.type} image (${image.width}x${image.height})`);
        
        try {
          let stegoImage, extractedMessage;
          
          if (sender === 'Alice') {
            stegoImage = await alice.sendStegoMessage(message, image);
            extractedMessage = await bob.processStegoImage(stegoImage);
          } else {
            stegoImage = await bob.sendStegoMessage(message, image);
            extractedMessage = await alice.processStegoImage(stegoImage);
          }
          
          expect(extractedMessage).toBe(message);
          
          conversationResults.push({
            turn: i + 1,
            sender,
            messageLength: message.length,
            imageType: image.type,
            imageDimensions: `${image.width}x${image.height}`,
            success: true
          });
          
          console.log(`   ✅ Turn ${i + 1}: SUCCESS`);
          
        } catch (error) {
          console.log(`   ❌ Turn ${i + 1}: FAILED - ${error.message}`);
          conversationResults.push({
            turn: i + 1,
            sender,
            success: false,
            error: error.message
          });
        }
      }
      
      // Verify conversation success
      const successfulTurns = conversationResults.filter(r => r.success).length;
      expect(successfulTurns).toBe(conversation.length);
      
      console.log(`✅ Complete conversation successful: ${successfulTurns}/${conversation.length} messages`);
      
      // Display conversation summary
      console.log('📊 Conversation Summary:');
      conversationResults.forEach(result => {
        if (result.success) {
          console.log(`   ✅ ${result.sender}: ${result.messageLength} chars via ${result.imageType} (${result.imageDimensions})`);
        }
      });
    }, 45000);

    test('should handle various message sizes with realistic images', async () => {
      console.log('📏 Testing various message sizes...');
      
      const messageSizes = [
        { size: 'tiny', message: 'Hi!' },
        { size: 'short', message: 'This is a short message for testing.' },
        { size: 'medium', message: 'This is a medium-length message that contains more content to test how our steganography system handles different message sizes with realistic images.' },
        { size: 'long', message: 'This is a very long message that tests the capacity limits of our steganography system. '.repeat(10) + 'It contains repeated content to simulate real-world scenarios where users might send lengthy messages with detailed information, coordinates, instructions, or other sensitive data that needs to be hidden securely within innocent-looking images.' },
        { size: 'structured', message: JSON.stringify({
          type: 'meeting_info',
          location: { lat: 37.7749, lng: -122.4194 },
          time: '2024-01-15T15:30:00Z',
          participants: ['Alice', 'Bob', 'Charlie'],
          agenda: ['Discuss project', 'Review timeline', 'Plan next steps'],
          confidential: true
        }, null, 2) }
      ];
      
      const sizeResults = [];
      
      for (let i = 0; i < messageSizes.length; i++) {
        const { size, message } = messageSizes[i];
        const image = realisticImages[i % realisticImages.length];
        
        console.log(`   Testing ${size} message (${message.length} chars) with ${image.type} image`);
        
        try {
          const startTime = Date.now();
          
          const stegoImage = await alice.sendStegoMessage(message, image);
          const hideTime = Date.now() - startTime;
          
          const revealStart = Date.now();
          const extractedMessage = await bob.processStegoImage(stegoImage);
          const revealTime = Date.now() - revealStart;
          
          expect(extractedMessage).toBe(message);
          
          sizeResults.push({
            size,
            messageLength: message.length,
            imageType: image.type,
            hideTime,
            revealTime,
            totalTime: hideTime + revealTime,
            success: true
          });
          
          console.log(`   ✅ ${size}: ${hideTime}ms hide, ${revealTime}ms reveal`);
          
        } catch (error) {
          console.log(`   ❌ ${size}: FAILED - ${error.message}`);
          sizeResults.push({
            size,
            messageLength: message.length,
            success: false,
            error: error.message
          });
        }
      }
      
      // Verify all sizes worked
      const successfulSizes = sizeResults.filter(r => r.success).length;
      expect(successfulSizes).toBe(messageSizes.length);
      
      console.log('📊 Message Size Performance:');
      sizeResults.forEach(result => {
        if (result.success) {
          console.log(`   ${result.size}: ${result.messageLength} chars, ${result.totalTime}ms total`);
        }
      });
    }, 30000);
  });

  describe('Auto-Detection with Realistic Images', () => {
    test('should auto-detect messages in various image types', async () => {
      console.log('🔍 Testing auto-detection with realistic images...');
      
      const detectionTests = realisticImages.slice(0, 5).map((image, i) => ({
        imageIndex: i + 1,
        image,
        message: `Auto-detection test ${i + 1} in ${image.type} image (${image.width}x${image.height})`
      }));
      
      const detectionResults = [];
      
      for (const test of detectionTests) {
        console.log(`   Testing ${test.image.type} image ${test.imageIndex}: ${test.image.width}x${test.image.height}`);
        
        try {
          // Create stego image
          const stegoImage = await alice.sendStegoMessage(test.message, test.image);
          
          // Test auto-detection
          const autoResult = await bob.autoProcessImage(stegoImage);
          
          expect(autoResult).toBeDefined();
          expect(autoResult.type).toBe('message');
          expect(autoResult.content).toBe(test.message);
          
          detectionResults.push({
            imageIndex: test.imageIndex,
            imageType: test.image.type,
            dimensions: `${test.image.width}x${test.image.height}`,
            detected: true,
            messageLength: test.message.length
          });
          
          console.log(`   ✅ Image ${test.imageIndex}: Auto-detected successfully`);
          
        } catch (error) {
          console.log(`   ❌ Image ${test.imageIndex}: Auto-detection failed - ${error.message}`);
          detectionResults.push({
            imageIndex: test.imageIndex,
            imageType: test.image.type,
            detected: false,
            error: error.message
          });
        }
      }
      
      // Verify detection success
      const detectedCount = detectionResults.filter(r => r.detected).length;
      expect(detectedCount).toBe(detectionTests.length);
      
      console.log(`✅ Auto-detection successful: ${detectedCount}/${detectionTests.length} images`);
      
      // Display detection summary
      console.log('📊 Auto-Detection Summary:');
      detectionResults.forEach(result => {
        if (result.detected) {
          console.log(`   ✅ ${result.imageType} ${result.dimensions}: ${result.messageLength} chars detected`);
        }
      });
    }, 30000);
  });

  describe('Performance with Realistic Images', () => {
    test('should maintain good performance across different image types and sizes', async () => {
      console.log('⚡ Testing performance with realistic images...');
      
      const performanceTests = [
        { image: realisticImages[0], message: 'Performance test with landscape image' },
        { image: realisticImages[1], message: 'Performance test with photo image' },
        { image: realisticImages[2], message: 'Performance test with portrait image' },
        { image: realisticImages[6], message: 'Performance test with large photo' },
        { image: realisticImages[8], message: 'Performance test with square image' }
      ];
      
      const performanceResults = [];
      
      for (let i = 0; i < performanceTests.length; i++) {
        const { image, message } = performanceTests[i];
        
        console.log(`   Testing ${image.type} ${image.width}x${image.height} (${(image.size / 1024).toFixed(1)}KB)`);
        
        const startTime = Date.now();
        
        // Hide message
        const hideStart = Date.now();
        const stegoImage = await alice.sendStegoMessage(message, image);
        const hideTime = Date.now() - hideStart;
        
        // Reveal message
        const revealStart = Date.now();
        const revealed = await bob.processStegoImage(stegoImage);
        const revealTime = Date.now() - revealStart;
        
        const totalTime = Date.now() - startTime;
        
        expect(revealed).toBe(message);
        
        performanceResults.push({
          imageType: image.type,
          dimensions: `${image.width}x${image.height}`,
          imageSize: image.size,
          hideTime,
          revealTime,
          totalTime,
          pixelCount: image.width * image.height
        });
        
        console.log(`   ✅ ${image.type}: Hide ${hideTime}ms, Reveal ${revealTime}ms, Total ${totalTime}ms`);
      }
      
      // Calculate performance metrics
      const avgHideTime = performanceResults.reduce((sum, r) => sum + r.hideTime, 0) / performanceResults.length;
      const avgRevealTime = performanceResults.reduce((sum, r) => sum + r.revealTime, 0) / performanceResults.length;
      const avgTotalTime = performanceResults.reduce((sum, r) => sum + r.totalTime, 0) / performanceResults.length;
      
      console.log('📊 Performance Summary:');
      console.log(`   Average Hide Time: ${avgHideTime.toFixed(1)}ms`);
      console.log(`   Average Reveal Time: ${avgRevealTime.toFixed(1)}ms`);
      console.log(`   Average Total Time: ${avgTotalTime.toFixed(1)}ms`);
      
      // Performance expectations
      expect(avgTotalTime).toBeLessThan(1000); // Less than 1 second total
      expect(avgHideTime).toBeLessThan(600);   // Less than 600ms to hide
      expect(avgRevealTime).toBeLessThan(400); // Less than 400ms to reveal
      
      console.log('✅ All performance benchmarks met!');
    }, 30000);
  });
});
