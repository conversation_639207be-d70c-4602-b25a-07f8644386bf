/**
 * Real-World Steganography Integration Test
 * Tests with actual images downloaded from the internet
 */

const fetch = require('node-fetch');
const sharp = require('sharp');
const { SteganographyOTR, STEGO_MESSAGE_TYPE } = require('../../src/core/steganography');
const { OTRSteganographySession } = require('../../src/core/steganography/otr-stego-session');

// Test image URLs from various sources (free to use)
const TEST_IMAGE_URLS = [
  'https://picsum.photos/800/600?random=1',
  'https://picsum.photos/1024/768?random=2', 
  'https://picsum.photos/640/480?random=3',
  'https://picsum.photos/1200/800?random=4',
  'https://picsum.photos/512/512?random=5',
  'https://picsum.photos/1920/1080?random=6',
  'https://picsum.photos/600/400?random=7',
  'https://picsum.photos/900/600?random=8',
  'https://picsum.photos/1024/1024?random=9',
  'https://picsum.photos/768/1024?random=10'
];

// Mock stego-js for testing with real image data
jest.mock('@masknet/stego-js', () => {
  const hiddenDataStore = new Map();
  
  return {
    encode: jest.fn().mockImplementation(async (imageData, data) => {
      // Create a unique key for this image
      const imageKey = `${imageData.width}x${imageData.height}_${Date.now()}_${Math.random()}`;
      
      // Store the hidden data
      hiddenDataStore.set(imageKey, data);
      
      // Return the image with hidden data marker
      return {
        ...imageData,
        _hiddenData: data,
        _imageKey: imageKey
      };
    }),
    decode: jest.fn().mockImplementation(async (imageData) => {
      // Return hidden data if it exists
      if (imageData && imageData._hiddenData) {
        return imageData._hiddenData;
      }
      
      // Check if this image has stored data
      if (imageData && imageData._imageKey && hiddenDataStore.has(imageData._imageKey)) {
        return hiddenDataStore.get(imageData._imageKey);
      }
      
      return null;
    })
  };
});

describe('Real-World Steganography Integration', () => {
  let downloadedImages = [];
  let alice, bob;

  beforeAll(async () => {
    console.log('🌐 Downloading real images from the internet...');
    
    // Download and process real images
    for (let i = 0; i < TEST_IMAGE_URLS.length; i++) {
      try {
        const url = TEST_IMAGE_URLS[i];
        console.log(`📥 Downloading image ${i + 1}/10: ${url}`);
        
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }
        
        const buffer = await response.buffer();
        
        // Convert to ImageData format using Sharp
        const image = sharp(buffer);
        const metadata = await image.metadata();
        const { data, info } = await image
          .ensureAlpha()
          .raw()
          .toBuffer({ resolveWithObject: true });
        
        const imageData = {
          data: new Uint8ClampedArray(data),
          width: info.width,
          height: info.height,
          source: url,
          size: buffer.length,
          format: metadata.format
        };
        
        downloadedImages.push(imageData);
        console.log(`✅ Image ${i + 1}: ${info.width}x${info.height} (${metadata.format}, ${buffer.length} bytes)`);
        
      } catch (error) {
        console.warn(`⚠️ Failed to download image ${i + 1}: ${error.message}`);
        
        // Create a fallback synthetic image
        const fallbackImage = {
          data: new Uint8ClampedArray(800 * 600 * 4),
          width: 800,
          height: 600,
          source: `fallback-${i + 1}`,
          size: 800 * 600 * 4,
          format: 'synthetic'
        };
        
        // Fill with random data
        for (let j = 0; j < fallbackImage.data.length; j++) {
          fallbackImage.data[j] = Math.floor(Math.random() * 256);
        }
        
        downloadedImages.push(fallbackImage);
        console.log(`🔄 Using fallback image ${i + 1}: 800x600 (synthetic)`);
      }
    }
    
    console.log(`✅ Successfully prepared ${downloadedImages.length} images for testing\n`);
  }, 30000); // 30 second timeout for downloads

  beforeEach(() => {
    // Create fresh sessions for each test
    alice = new OTRSteganographySession({
      testing: true,
      steganographyEnabled: true,
      stegoPassword: 'test-password-123',
      clientName: 'Alice'
    });
    
    bob = new OTRSteganographySession({
      testing: true,
      steganographyEnabled: true,
      stegoPassword: 'test-password-123',
      clientName: 'Bob'
    });
  });

  afterEach(() => {
    if (alice) alice.destroy();
    if (bob) bob.destroy();
  });

  describe('Real Image Steganography Tests', () => {
    test('should hide and extract messages from all downloaded images', async () => {
      const testMessage = 'Hello from Alice! This is a secret message hidden in a real image downloaded from the internet! 🔒🌐';
      const results = [];
      
      console.log('🧪 Testing steganography with all downloaded images...');
      
      for (let i = 0; i < downloadedImages.length; i++) {
        const image = downloadedImages[i];
        console.log(`📝 Testing image ${i + 1}: ${image.width}x${image.height} (${image.format})`);
        
        try {
          // Hide message in real image
          const stegoImage = await alice.sendStegoMessage(testMessage, image);
          expect(stegoImage).toBeDefined();
          
          // Extract message from stego image
          const extractedMessage = await bob.processStegoImage(stegoImage);
          expect(extractedMessage).toBe(testMessage);
          
          results.push({
            imageIndex: i + 1,
            source: image.source,
            dimensions: `${image.width}x${image.height}`,
            format: image.format,
            success: true,
            messageLength: testMessage.length
          });
          
          console.log(`✅ Image ${i + 1}: SUCCESS`);
          
        } catch (error) {
          console.log(`❌ Image ${i + 1}: FAILED - ${error.message}`);
          results.push({
            imageIndex: i + 1,
            source: image.source,
            dimensions: `${image.width}x${image.height}`,
            format: image.format,
            success: false,
            error: error.message
          });
        }
      }
      
      // Verify results
      const successCount = results.filter(r => r.success).length;
      const successRate = (successCount / results.length) * 100;
      
      console.log(`\n📊 Results Summary:`);
      console.log(`   Successful: ${successCount}/${results.length} (${successRate.toFixed(1)}%)`);
      
      // Expect at least 80% success rate
      expect(successRate).toBeGreaterThanOrEqual(80);
      
      // Log detailed results
      results.forEach(result => {
        if (result.success) {
          console.log(`✅ ${result.imageIndex}: ${result.dimensions} ${result.format} - SUCCESS`);
        } else {
          console.log(`❌ ${result.imageIndex}: ${result.dimensions} ${result.format} - ${result.error}`);
        }
      });
    }, 60000);

    test('should perform complete OTR handshake via steganography', async () => {
      console.log('🤝 Testing complete OTR handshake via steganography...');
      
      // Use the first few images for handshake simulation
      const handshakeImages = downloadedImages.slice(0, 4);
      
      // Simulate AKE handshake messages
      const handshakeMessages = [
        {
          type: 'DH_COMMIT',
          instanceTag: alice.instanceTag,
          protocolVersion: 3,
          dhCommit: 'mock_dh_commit_data_' + Date.now(),
          step: 1
        },
        {
          type: 'DH_KEY',
          instanceTag: bob.instanceTag,
          protocolVersion: 3,
          dhKey: 'mock_dh_key_data_' + Date.now(),
          step: 2
        },
        {
          type: 'REVEAL_SIGNATURE',
          instanceTag: alice.instanceTag,
          revealedKey: 'mock_revealed_key_' + Date.now(),
          signature: 'mock_signature_data',
          step: 3
        },
        {
          type: 'SIGNATURE',
          instanceTag: bob.instanceTag,
          signature: 'mock_final_signature_' + Date.now(),
          step: 4
        }
      ];
      
      console.log('📝 Simulating 4-step AKE handshake...');
      
      const handshakeResults = [];
      
      for (let i = 0; i < handshakeMessages.length; i++) {
        const message = handshakeMessages[i];
        const image = handshakeImages[i];
        
        console.log(`   Step ${i + 1}: ${message.type} via ${image.width}x${image.height} image`);
        
        try {
          // Hide AKE message in image
          const stegoImage = await alice.sendStegoAKE(message, image);
          
          // Extract AKE message
          const extractedAKE = await bob.processStegoAKE(stegoImage);
          
          expect(extractedAKE).toEqual(message);
          
          handshakeResults.push({
            step: i + 1,
            type: message.type,
            success: true
          });
          
          console.log(`   ✅ Step ${i + 1}: SUCCESS`);
          
        } catch (error) {
          console.log(`   ❌ Step ${i + 1}: FAILED - ${error.message}`);
          handshakeResults.push({
            step: i + 1,
            type: message.type,
            success: false,
            error: error.message
          });
        }
      }
      
      // Verify handshake completion
      const successfulSteps = handshakeResults.filter(r => r.success).length;
      expect(successfulSteps).toBe(4);
      
      console.log(`✅ Complete AKE handshake successful: ${successfulSteps}/4 steps`);
    }, 30000);

    test('should handle conversation flow with real images', async () => {
      console.log('💬 Testing conversation flow with real images...');
      
      const conversation = [
        { sender: 'Alice', message: 'Hey Bob! Can you see this hidden message?' },
        { sender: 'Bob', message: 'Yes Alice! This steganography is amazing! 🤯' },
        { sender: 'Alice', message: 'Let\'s share some secrets: The password is "stego123"' },
        { sender: 'Bob', message: 'Got it! Meeting at the usual place at 3pm.' },
        { sender: 'Alice', message: 'Perfect! This conversation is completely hidden! 🕵️‍♀️' }
      ];
      
      const conversationResults = [];
      
      for (let i = 0; i < conversation.length; i++) {
        const { sender, message } = conversation[i];
        const image = downloadedImages[i % downloadedImages.length];
        
        console.log(`   ${sender}: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
        
        try {
          let stegoImage, extractedMessage;
          
          if (sender === 'Alice') {
            stegoImage = await alice.sendStegoMessage(message, image);
            extractedMessage = await bob.processStegoImage(stegoImage);
          } else {
            stegoImage = await bob.sendStegoMessage(message, image);
            extractedMessage = await alice.processStegoImage(stegoImage);
          }
          
          expect(extractedMessage).toBe(message);
          
          conversationResults.push({
            turn: i + 1,
            sender,
            messageLength: message.length,
            imageSize: `${image.width}x${image.height}`,
            success: true
          });
          
          console.log(`   ✅ Turn ${i + 1}: SUCCESS`);
          
        } catch (error) {
          console.log(`   ❌ Turn ${i + 1}: FAILED - ${error.message}`);
          conversationResults.push({
            turn: i + 1,
            sender,
            success: false,
            error: error.message
          });
        }
      }
      
      // Verify conversation success
      const successfulTurns = conversationResults.filter(r => r.success).length;
      expect(successfulTurns).toBe(conversation.length);
      
      console.log(`✅ Complete conversation successful: ${successfulTurns}/${conversation.length} messages`);
    }, 45000);

    test('should auto-detect hidden messages in real images', async () => {
      console.log('🔍 Testing auto-detection with real images...');
      
      const testImages = downloadedImages.slice(0, 5);
      const detectionResults = [];
      
      for (let i = 0; i < testImages.length; i++) {
        const image = testImages[i];
        const testMessage = `Auto-detection test message ${i + 1} in real image!`;
        
        console.log(`   Testing auto-detection with image ${i + 1}: ${image.width}x${image.height}`);
        
        try {
          // Create stego image
          const stegoImage = await alice.sendStegoMessage(testMessage, image);
          
          // Test auto-detection
          const autoResult = await bob.autoProcessImage(stegoImage);
          
          expect(autoResult).toBeDefined();
          expect(autoResult.type).toBe('message');
          expect(autoResult.content).toBe(testMessage);
          
          detectionResults.push({
            imageIndex: i + 1,
            detected: true,
            messageLength: testMessage.length
          });
          
          console.log(`   ✅ Image ${i + 1}: Auto-detected successfully`);
          
        } catch (error) {
          console.log(`   ❌ Image ${i + 1}: Auto-detection failed - ${error.message}`);
          detectionResults.push({
            imageIndex: i + 1,
            detected: false,
            error: error.message
          });
        }
      }
      
      // Verify detection success
      const detectedCount = detectionResults.filter(r => r.detected).length;
      expect(detectedCount).toBe(testImages.length);
      
      console.log(`✅ Auto-detection successful: ${detectedCount}/${testImages.length} images`);
    }, 30000);
  });

  describe('Performance with Real Images', () => {
    test('should process real images efficiently', async () => {
      console.log('⚡ Testing performance with real images...');
      
      const performanceResults = [];
      const testMessage = 'Performance test message with real image data!';
      
      for (let i = 0; i < Math.min(5, downloadedImages.length); i++) {
        const image = downloadedImages[i];
        
        console.log(`   Testing performance with ${image.width}x${image.height} image (${image.format})`);
        
        const startTime = Date.now();
        
        // Hide message
        const hideStart = Date.now();
        const stegoImage = await alice.sendStegoMessage(testMessage, image);
        const hideTime = Date.now() - hideStart;
        
        // Reveal message
        const revealStart = Date.now();
        const revealed = await bob.processStegoImage(stegoImage);
        const revealTime = Date.now() - revealStart;
        
        const totalTime = Date.now() - startTime;
        
        expect(revealed).toBe(testMessage);
        
        performanceResults.push({
          imageIndex: i + 1,
          dimensions: `${image.width}x${image.height}`,
          format: image.format,
          hideTime,
          revealTime,
          totalTime,
          pixelCount: image.width * image.height
        });
        
        console.log(`   ✅ Image ${i + 1}: Hide ${hideTime}ms, Reveal ${revealTime}ms, Total ${totalTime}ms`);
      }
      
      // Calculate averages
      const avgHideTime = performanceResults.reduce((sum, r) => sum + r.hideTime, 0) / performanceResults.length;
      const avgRevealTime = performanceResults.reduce((sum, r) => sum + r.revealTime, 0) / performanceResults.length;
      const avgTotalTime = performanceResults.reduce((sum, r) => sum + r.totalTime, 0) / performanceResults.length;
      
      console.log(`📊 Performance Summary:`);
      console.log(`   Average Hide Time: ${avgHideTime.toFixed(1)}ms`);
      console.log(`   Average Reveal Time: ${avgRevealTime.toFixed(1)}ms`);
      console.log(`   Average Total Time: ${avgTotalTime.toFixed(1)}ms`);
      
      // Performance expectations (should be reasonable for real images)
      expect(avgTotalTime).toBeLessThan(5000); // Less than 5 seconds total
      expect(avgHideTime).toBeLessThan(3000);  // Less than 3 seconds to hide
      expect(avgRevealTime).toBeLessThan(2000); // Less than 2 seconds to reveal
    }, 60000);
  });
});
