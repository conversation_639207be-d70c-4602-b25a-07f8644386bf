/**
 * Multi-Platform Steganography Integration Tests
 * Tests for cross-platform compatibility and real-world scenarios
 */

const { SteganographyOTR, STEGO_MESSAGE_TYPE } = require('../../src/core/steganography');
const { OTRSteganographySession } = require('../../src/core/steganography/otr-stego-session');

// Mock stego-js with platform-specific behaviors
jest.mock('@masknet/stego-js', () => {
  const hiddenDataStore = new Map();
  let platformMode = 'standard';
  
  return {
    encode: jest.fn().mockImplementation(async (imageData, data) => {
      // Simulate platform-specific encoding variations
      let processedData = data;
      
      switch (platformMode) {
        case 'mobile':
          // Mobile platforms might have memory constraints
          if (imageData.data.length > 2000000) { // 2MB limit
            throw new Error('Image too large for mobile platform');
          }
          break;
          
        case 'web':
          // Web platforms might have different encoding
          processedData = `web:${data}`;
          break;
          
        case 'desktop':
          // Desktop platforms might support larger images
          processedData = `desktop:${data}`;
          break;
      }
      
      const imageKey = `${imageData.width}x${imageData.height}_${Date.now()}_${Math.random()}`;
      hiddenDataStore.set(imageKey, processedData);
      
      return {
        ...imageData,
        _hiddenData: processedData,
        _imageKey: imageKey,
        _platform: platformMode
      };
    }),
    
    decode: jest.fn().mockImplementation(async (imageData) => {
      if (imageData && imageData._hiddenData) {
        return imageData._hiddenData;
      }
      
      if (imageData && imageData._imageKey && hiddenDataStore.has(imageData._imageKey)) {
        return hiddenDataStore.get(imageData._imageKey);
      }
      
      return null;
    }),
    
    // Platform testing utilities
    __setPlatformMode: (mode) => { platformMode = mode; },
    __getPlatformMode: () => platformMode,
    __clearStorage: () => hiddenDataStore.clear()
  };
});

describe('Multi-Platform Steganography Integration', () => {
  let stego;
  const mockStego = require('@masknet/stego-js');

  beforeEach(() => {
    stego = new SteganographyOTR({
      quality: 0.95,
      password: 'multi-platform-test'
    });
    mockStego.__clearStorage();
    mockStego.__setPlatformMode('standard');
  });

  describe('Cross-Platform Compatibility', () => {
    test('should work across different platform modes', async () => {
      const image = createTestImage(512, 512);
      const message = 'Cross-platform compatibility test';
      const platforms = ['standard', 'mobile', 'web', 'desktop'];
      
      const results = [];
      
      for (const platform of platforms) {
        mockStego.__setPlatformMode(platform);
        
        try {
          const stegoImage = await stego.hideMessage(image, message);
          const revealed = await stego.revealMessage(stegoImage);
          
          results.push({
            platform,
            success: true,
            revealed,
            platformData: stegoImage._platform
          });
          
        } catch (error) {
          results.push({
            platform,
            success: false,
            error: error.message
          });
        }
      }
      
      console.log('Cross-Platform Results:', results);
      
      // Most platforms should work
      const successCount = results.filter(r => r.success).length;
      expect(successCount).toBeGreaterThan(platforms.length * 0.75); // 75% success rate
      
      // Successful platforms should preserve message content
      results.filter(r => r.success).forEach(result => {
        // Handle platform-specific encoding
        if (result.revealed.startsWith('web:') || result.revealed.startsWith('desktop:')) {
          expect(result.revealed.substring(result.revealed.indexOf(':') + 1)).toBe(message);
        } else {
          expect(result.revealed).toBe(message);
        }
      });
    });

    test('should handle mobile platform constraints', async () => {
      mockStego.__setPlatformMode('mobile');
      
      const smallImage = createTestImage(256, 256); // Should work
      const largeImage = createTestImage(2048, 2048); // Might fail on mobile
      const message = 'Mobile platform test';
      
      // Small image should work
      const smallStegoImage = await stego.hideMessage(smallImage, message);
      const smallRevealed = await stego.revealMessage(smallStegoImage);
      expect(smallRevealed).toBe(message);
      
      // Large image might fail
      try {
        await stego.hideMessage(largeImage, message);
        // If it succeeds, that's fine too
      } catch (error) {
        expect(error.message).toContain('too large for mobile');
      }
    });

    test('should maintain compatibility between web and desktop platforms', async () => {
      const image = createTestImage(512, 512);
      const message = 'Web-Desktop compatibility test';
      
      // Hide message on web platform
      mockStego.__setPlatformMode('web');
      const webStegoImage = await stego.hideMessage(image, message);
      
      // Try to reveal on desktop platform
      mockStego.__setPlatformMode('desktop');
      const desktopRevealed = await stego.revealMessage(webStegoImage);
      
      // Should be able to extract the original message
      expect(desktopRevealed).toContain(message);
    });
  });

  describe('Social Media Platform Simulation', () => {
    test('should simulate Facebook image sharing', async () => {
      const facebookImage = createSocialMediaImage('facebook');
      const message = 'Secret message shared via Facebook photo';
      
      // Simulate Facebook's image processing (compression, resizing)
      const processedImage = simulateFacebookProcessing(facebookImage);
      
      const stegoImage = await stego.hideMessage(processedImage, message);
      const revealed = await stego.revealMessage(stegoImage);
      
      expect(revealed).toBe(message);
    });

    test('should simulate Instagram image sharing', async () => {
      const instagramImage = createSocialMediaImage('instagram');
      const message = 'Hidden message in Instagram story';
      
      // Simulate Instagram's square format and filters
      const processedImage = simulateInstagramProcessing(instagramImage);
      
      const stegoImage = await stego.hideMessage(processedImage, message);
      const revealed = await stego.revealMessage(stegoImage);
      
      expect(revealed).toBe(message);
    });

    test('should simulate Twitter image sharing', async () => {
      const twitterImage = createSocialMediaImage('twitter');
      const message = 'Covert communication via Twitter image';
      
      // Simulate Twitter's image compression
      const processedImage = simulateTwitterProcessing(twitterImage);
      
      const stegoImage = await stego.hideMessage(processedImage, message);
      const revealed = await stego.revealMessage(stegoImage);
      
      expect(revealed).toBe(message);
    });

    test('should simulate Discord image sharing', async () => {
      const discordImage = createSocialMediaImage('discord');
      const message = 'Secret gaming coordination message';
      
      // Discord typically preserves image quality better
      const processedImage = simulateDiscordProcessing(discordImage);
      
      const stegoImage = await stego.hideMessage(processedImage, message);
      const revealed = await stego.revealMessage(stegoImage);
      
      expect(revealed).toBe(message);
    });
  });

  describe('Real-World Communication Scenarios', () => {
    test('should simulate journalist-source communication', async () => {
      const journalist = new OTRSteganographySession({
        testing: true,
        steganographyEnabled: true,
        stegoPassword: 'press-freedom-2024',
        clientName: 'Journalist'
      });
      
      const source = new OTRSteganographySession({
        testing: true,
        steganographyEnabled: true,
        stegoPassword: 'press-freedom-2024',
        clientName: 'Source'
      });
      
      // Source sends sensitive information via innocent photo
      const newsPhoto = createTestImage(800, 600, 'news');
      const sensitiveInfo = JSON.stringify({
        type: 'leak',
        subject: 'Government Contract Irregularities',
        documents: ['contract_a.pdf', 'email_thread.txt'],
        meetingLocation: 'Central Park, Bethesda Fountain',
        meetingTime: '2024-01-15T14:00:00Z',
        safetyCode: 'blue-bird-sings'
      });
      
      const stegoImage = await source.sendStegoMessage(sensitiveInfo, newsPhoto);
      const receivedInfo = await journalist.processStegoImage(stegoImage);
      
      expect(receivedInfo).toBe(sensitiveInfo);
      
      // Verify the information can be parsed
      const parsedInfo = JSON.parse(receivedInfo);
      expect(parsedInfo.type).toBe('leak');
      expect(parsedInfo.safetyCode).toBe('blue-bird-sings');
      
      journalist.destroy();
      source.destroy();
    });

    test('should simulate activist coordination', async () => {
      const activists = [
        new OTRSteganographySession({
          testing: true,
          steganographyEnabled: true,
          stegoPassword: 'freedom-march-2024',
          clientName: 'Alice'
        }),
        new OTRSteganographySession({
          testing: true,
          steganographyEnabled: true,
          stegoPassword: 'freedom-march-2024',
          clientName: 'Bob'
        }),
        new OTRSteganographySession({
          testing: true,
          steganographyEnabled: true,
          stegoPassword: 'freedom-march-2024',
          clientName: 'Charlie'
        })
      ];
      
      // Coordinate protest via innocent nature photos
      const naturePhoto = createTestImage(1024, 768, 'nature');
      const coordinationMessage = JSON.stringify({
        event: 'peaceful_protest',
        location: 'City Hall Steps',
        time: '2024-01-20T12:00:00Z',
        supplies: ['signs', 'water', 'first_aid'],
        emergencyContact: '+1-555-LAWYER',
        dispersalPlan: 'Metro stations A, B, C'
      });
      
      // Alice sends coordination message
      const stegoImage = await activists[0].sendStegoMessage(coordinationMessage, naturePhoto);
      
      // Bob and Charlie receive the message
      const bobReceived = await activists[1].processStegoImage(stegoImage);
      const charlieReceived = await activists[2].processStegoImage(stegoImage);
      
      expect(bobReceived).toBe(coordinationMessage);
      expect(charlieReceived).toBe(coordinationMessage);
      
      // Verify coordination details
      const bobParsed = JSON.parse(bobReceived);
      const charlieParsed = JSON.parse(charlieReceived);
      
      expect(bobParsed.event).toBe('peaceful_protest');
      expect(charlieParsed.emergencyContact).toBe('+1-555-LAWYER');
      
      activists.forEach(activist => activist.destroy());
    });

    test('should simulate corporate whistleblowing', async () => {
      const whistleblower = new OTRSteganographySession({
        testing: true,
        steganographyEnabled: true,
        stegoPassword: 'corporate-ethics-2024'
      });
      
      const investigator = new OTRSteganographySession({
        testing: true,
        steganographyEnabled: true,
        stegoPassword: 'corporate-ethics-2024'
      });
      
      // Whistleblower shares evidence via company event photo
      const companyPhoto = createTestImage(1200, 800, 'corporate');
      const evidence = JSON.stringify({
        type: 'financial_misconduct',
        company: 'MegaCorp Industries',
        violations: [
          'Accounting fraud in Q3 2023',
          'Illegal offshore tax avoidance',
          'Environmental regulation violations'
        ],
        evidence_files: [
          'financial_records_q3.xlsx',
          'email_ceo_cfo.pdf',
          'environmental_report.doc'
        ],
        witness_protection_needed: true,
        contact_method: 'encrypted_email_only'
      });
      
      const stegoImage = await whistleblower.sendStegoMessage(evidence, companyPhoto);
      const receivedEvidence = await investigator.processStegoImage(stegoImage);
      
      expect(receivedEvidence).toBe(evidence);
      
      const parsedEvidence = JSON.parse(receivedEvidence);
      expect(parsedEvidence.type).toBe('financial_misconduct');
      expect(parsedEvidence.witness_protection_needed).toBe(true);
      
      whistleblower.destroy();
      investigator.destroy();
    });
  });

  describe('Network and Transport Simulation', () => {
    test('should handle network latency simulation', async () => {
      const image = createTestImage(512, 512);
      const message = 'Network latency test message';
      
      // Simulate network delays
      const networkDelays = [0, 50, 100, 500, 1000]; // milliseconds
      
      for (const delay of networkDelays) {
        const startTime = Date.now();
        
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, delay));
        
        const stegoImage = await stego.hideMessage(image, message);
        const revealed = await stego.revealMessage(stegoImage);
        
        const totalTime = Date.now() - startTime;
        
        expect(revealed).toBe(message);
        expect(totalTime).toBeGreaterThanOrEqual(delay);
        
        console.log(`Network delay ${delay}ms: Total time ${totalTime}ms`);
      }
    });

    test('should simulate packet loss and retransmission', async () => {
      const image = createTestImage(256, 256);
      const message = 'Packet loss simulation test';
      
      // Simulate packet loss scenarios
      const lossRates = [0, 0.01, 0.05, 0.1]; // 0%, 1%, 5%, 10%
      
      for (const lossRate of lossRates) {
        let attempts = 0;
        let success = false;
        const maxAttempts = 5;
        
        while (!success && attempts < maxAttempts) {
          attempts++;
          
          try {
            // Simulate packet loss
            if (Math.random() < lossRate) {
              throw new Error('Simulated packet loss');
            }
            
            const stegoImage = await stego.hideMessage(image, message);
            const revealed = await stego.revealMessage(stegoImage);
            
            if (revealed === message) {
              success = true;
            }
            
          } catch (error) {
            // Retry on failure
            console.log(`Attempt ${attempts} failed for loss rate ${lossRate}: ${error.message}`);
          }
        }
        
        expect(success).toBe(true);
        console.log(`Loss rate ${(lossRate * 100).toFixed(1)}%: Success after ${attempts} attempts`);
      }
    });
  });

  // Helper functions for creating platform-specific images
  function createTestImage(width, height, type = 'random') {
    const data = new Uint8ClampedArray(width * height * 4);
    
    switch (type) {
      case 'news':
        // Simulate news photo with text overlay areas
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const i = (y * width + x) * 4;
            // Create realistic news photo colors
            data[i] = 120 + Math.sin(x * 0.01) * 50;     // Red
            data[i + 1] = 140 + Math.cos(y * 0.01) * 60; // Green
            data[i + 2] = 100 + Math.sin((x + y) * 0.005) * 40; // Blue
            data[i + 3] = 255; // Alpha
          }
        }
        break;
        
      case 'nature':
        // Simulate nature photo with green tones
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const i = (y * width + x) * 4;
            data[i] = 60 + Math.random() * 100;      // Red (lower for green)
            data[i + 1] = 120 + Math.random() * 135; // Green (higher)
            data[i + 2] = 40 + Math.random() * 80;   // Blue (lower)
            data[i + 3] = 255; // Alpha
          }
        }
        break;
        
      case 'corporate':
        // Simulate corporate photo with blue/gray tones
        for (let y = 0; y < height; y++) {
          for (let x = 0; x < width; x++) {
            const i = (y * width + x) * 4;
            data[i] = 100 + Math.random() * 80;      // Red
            data[i + 1] = 110 + Math.random() * 90;  // Green
            data[i + 2] = 140 + Math.random() * 115; // Blue (higher for corporate)
            data[i + 3] = 255; // Alpha
          }
        }
        break;
        
      default:
        // Random image
        for (let i = 0; i < data.length; i += 4) {
          data[i] = Math.floor(Math.random() * 256);
          data[i + 1] = Math.floor(Math.random() * 256);
          data[i + 2] = Math.floor(Math.random() * 256);
          data[i + 3] = 255;
        }
    }
    
    return { data, width, height };
  }

  function createSocialMediaImage(platform) {
    switch (platform) {
      case 'facebook':
        return createTestImage(1200, 630); // Facebook recommended size
      case 'instagram':
        return createTestImage(1080, 1080); // Instagram square
      case 'twitter':
        return createTestImage(1024, 512); // Twitter card size
      case 'discord':
        return createTestImage(800, 600); // Discord embed size
      default:
        return createTestImage(512, 512);
    }
  }

  function simulateFacebookProcessing(image) {
    // Facebook typically compresses images
    return {
      ...image,
      _processed: 'facebook',
      _compression: 0.8
    };
  }

  function simulateInstagramProcessing(image) {
    // Instagram applies filters and square cropping
    return {
      ...image,
      _processed: 'instagram',
      _filter: 'valencia',
      _aspectRatio: '1:1'
    };
  }

  function simulateTwitterProcessing(image) {
    // Twitter compresses images significantly
    return {
      ...image,
      _processed: 'twitter',
      _compression: 0.6
    };
  }

  function simulateDiscordProcessing(image) {
    // Discord preserves quality better
    return {
      ...image,
      _processed: 'discord',
      _compression: 0.95
    };
  }
});
