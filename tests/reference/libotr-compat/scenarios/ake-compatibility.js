/**
 * AKE (Authenticated Key Exchange) Compatibility Tests
 * 
 * Tests webOTR AKE implementation against libOTR reference for compatibility.
 * Based on libOTR Protocol-v3.html AKE specification.
 */

const { WebOTRDummyClient, MESSAGE_TYPES, OTR_CONSTANTS } = require('../dummy-client');
const { OTRMessageBridge } = require('../message-bridge');

/**
 * AKE Test Scenarios
 */
class AKECompatibilityTests {
  constructor() {
    this.messageBridge = new OTRMessageBridge();
  }

  /**
   * Test OTR Query Message parsing and version negotiation
   */
  async testOTRQueryMessages() {
    const testCases = [
      {
        name: 'OTR v3 only',
        query: '?OTRv3?',
        expectedVersions: [3]
      },
      {
        name: 'OTR v2 and v3',
        query: '?OTRv23?',
        expectedVersions: [2, 3]
      },
      {
        name: 'OTR v1 only (legacy)',
        query: '?OTR?',
        expectedVersions: [1]
      },
      {
        name: 'OTR v1, v2, and v3',
        query: '?OTR?v23?',
        expectedVersions: [1, 2, 3]
      },
      {
        name: 'Invalid version string',
        query: '?OTRv?',
        expectedVersions: [],
        shouldError: true
      }
    ];

    const results = [];

    for (const testCase of testCases) {
      const client = new WebOTRDummyClient('<EMAIL>', 'test');
      
      try {
        // Parse the query message
        const versions = client.parseOTRVersions(testCase.query);
        
        if (testCase.shouldError) {
          results.push({
            ...testCase,
            status: 'FAILED',
            error: 'Expected error but parsing succeeded',
            actualVersions: versions
          });
        } else {
          // Check if versions match expected
          const versionsMatch = JSON.stringify(versions.sort()) === JSON.stringify(testCase.expectedVersions.sort());
          
          results.push({
            ...testCase,
            status: versionsMatch ? 'PASSED' : 'FAILED',
            actualVersions: versions,
            error: versionsMatch ? null : `Version mismatch: expected ${testCase.expectedVersions}, got ${versions}`
          });
        }
      } catch (error) {
        if (testCase.shouldError) {
          results.push({
            ...testCase,
            status: 'PASSED',
            error: null,
            actualVersions: null
          });
        } else {
          results.push({
            ...testCase,
            status: 'FAILED',
            error: error.message,
            actualVersions: null
          });
        }
      }
    }

    return {
      testName: 'OTR Query Message Parsing',
      results,
      summary: {
        total: results.length,
        passed: results.filter(r => r.status === 'PASSED').length,
        failed: results.filter(r => r.status === 'FAILED').length
      }
    };
  }

  /**
   * Test OTR message format validation
   */
  async testOTRMessageFormats() {
    const testCases = [
      {
        name: 'Valid DH Commit Message',
        messageType: 'DH_COMMIT',
        messageData: {
          senderInstanceTag: 0x12345678,
          receiverInstanceTag: 0x87654321,
          encryptedGx: Buffer.from('encrypted_gx_data_here', 'utf8'),
          hashedGx: Buffer.from('hashed_gx_data_here_32_bytes_long', 'utf8')
        },
        shouldPass: true
      },
      {
        name: 'Valid DH Key Message',
        messageType: 'DH_KEY',
        messageData: {
          senderInstanceTag: 0x12345678,
          receiverInstanceTag: 0x87654321,
          gy: Buffer.from('0123456789abcdef', 'hex')
        },
        shouldPass: true
      },
      {
        name: 'Invalid Instance Tag (too small)',
        messageType: 'DH_COMMIT',
        messageData: {
          senderInstanceTag: 0x000000FF, // Invalid: < 0x00000100
          receiverInstanceTag: 0x87654321,
          encryptedGx: Buffer.from('encrypted_gx_data_here', 'utf8'),
          hashedGx: Buffer.from('hashed_gx_data_here_32_bytes_long', 'utf8')
        },
        shouldPass: false
      },
      {
        name: 'Missing Required Field',
        messageType: 'DH_KEY',
        messageData: {
          senderInstanceTag: 0x12345678,
          receiverInstanceTag: 0x87654321
          // Missing 'gy' field
        },
        shouldPass: false
      }
    ];

    const results = [];

    for (const testCase of testCases) {
      try {
        // Test serialization
        const serialized = this.messageBridge.serializeMessage(testCase.messageType, testCase.messageData);
        
        // Test deserialization
        const deserialized = this.messageBridge.deserializeMessage(serialized);
        
        // Test validation
        const isValid = this.messageBridge.validateMessage(testCase.messageType, testCase.messageData);
        
        if (testCase.shouldPass) {
          results.push({
            ...testCase,
            status: 'PASSED',
            serializedLength: serialized.length,
            deserializedCorrectly: deserialized.messageType === testCase.messageType
          });
        } else {
          results.push({
            ...testCase,
            status: 'FAILED',
            error: 'Expected validation to fail but it passed'
          });
        }
        
      } catch (error) {
        if (testCase.shouldPass) {
          results.push({
            ...testCase,
            status: 'FAILED',
            error: error.message
          });
        } else {
          results.push({
            ...testCase,
            status: 'PASSED',
            expectedError: error.message
          });
        }
      }
    }

    return {
      testName: 'OTR Message Format Validation',
      results,
      summary: {
        total: results.length,
        passed: results.filter(r => r.status === 'PASSED').length,
        failed: results.filter(r => r.status === 'FAILED').length
      }
    };
  }

  /**
   * Test instance tag validation
   */
  async testInstanceTagValidation() {
    const testCases = [
      { tag: 0x00000100, valid: true, name: 'Minimum valid tag' },
      { tag: 0x12345678, valid: true, name: 'Normal valid tag' },
      { tag: 0xFFFFFFFF, valid: true, name: 'Maximum valid tag' },
      { tag: 0x000000FF, valid: false, name: 'Too small tag' },
      { tag: 0x00000000, valid: false, name: 'Zero tag (special case)' },
      { tag: 0x00000001, valid: false, name: 'Very small tag' }
    ];

    const results = testCases.map(testCase => {
      const actualValid = this.messageBridge.validateInstanceTag(testCase.tag);
      const passed = actualValid === testCase.valid;
      
      return {
        ...testCase,
        status: passed ? 'PASSED' : 'FAILED',
        actualValid,
        error: passed ? null : `Expected ${testCase.valid}, got ${actualValid}`
      };
    });

    return {
      testName: 'Instance Tag Validation',
      results,
      summary: {
        total: results.length,
        passed: results.filter(r => r.status === 'PASSED').length,
        failed: results.filter(r => r.status === 'FAILED').length
      }
    };
  }

  /**
   * Test OTR message string encoding/decoding
   */
  async testOTRMessageStringEncoding() {
    const testMessage = {
      messageType: 'DH_KEY',
      messageData: {
        senderInstanceTag: 0x12345678,
        receiverInstanceTag: 0x87654321,
        gy: Buffer.from('0123456789abcdef0123456789abcdef', 'hex')
      }
    };

    try {
      // Create OTR message string
      const otrString = this.messageBridge.createOTRMessageString(
        testMessage.messageType, 
        testMessage.messageData
      );

      // Verify format
      if (!otrString.startsWith('?OTR:') || !otrString.endsWith('.')) {
        throw new Error('Invalid OTR message string format');
      }

      // Parse back
      const parsed = this.messageBridge.parseOTRMessageString(otrString);

      // Verify round-trip
      const matches = (
        parsed.messageType === testMessage.messageType &&
        parsed.senderInstanceTag === testMessage.messageData.senderInstanceTag &&
        parsed.receiverInstanceTag === testMessage.messageData.receiverInstanceTag &&
        Buffer.compare(parsed.gy, testMessage.messageData.gy) === 0
      );

      return {
        testName: 'OTR Message String Encoding',
        status: matches ? 'PASSED' : 'FAILED',
        otrString,
        originalData: testMessage.messageData,
        parsedData: parsed,
        error: matches ? null : 'Round-trip data mismatch'
      };

    } catch (error) {
      return {
        testName: 'OTR Message String Encoding',
        status: 'FAILED',
        error: error.message
      };
    }
  }

  /**
   * Test whitespace tag handling
   */
  async testWhitespaceTagHandling() {
    const client = new WebOTRDummyClient('<EMAIL>', 'test');
    
    const testMessage = 'Hello, this is a test message';
    const queryId = client.sendMessage('<EMAIL>', 'test', testMessage);
    
    try {
      const processedMessage = await client.getQueryResult(queryId);
      
      // Check if OTR v3 whitespace tag was added
      const hasV3Tag = processedMessage.includes(OTR_CONSTANTS.OTR_V3_WHITESPACE_TAG);
      
      return {
        testName: 'Whitespace Tag Handling',
        status: hasV3Tag ? 'PASSED' : 'FAILED',
        originalMessage: testMessage,
        processedMessage: processedMessage,
        hasV3Tag: hasV3Tag,
        error: hasV3Tag ? null : 'OTR v3 whitespace tag not added'
      };
      
    } catch (error) {
      return {
        testName: 'Whitespace Tag Handling',
        status: 'FAILED',
        error: error.message
      };
    }
  }

  /**
   * Run all AKE compatibility tests
   */
  async runAllTests() {
    const testResults = [];
    
    console.log('🔄 Running AKE Compatibility Tests...');
    
    // Run each test
    testResults.push(await this.testOTRQueryMessages());
    testResults.push(await this.testOTRMessageFormats());
    testResults.push(await this.testInstanceTagValidation());
    testResults.push(await this.testOTRMessageStringEncoding());
    testResults.push(await this.testWhitespaceTagHandling());
    
    // Calculate overall summary
    const overallSummary = testResults.reduce((acc, test) => {
      if (test.summary) {
        acc.total += test.summary.total;
        acc.passed += test.summary.passed;
        acc.failed += test.summary.failed;
      } else {
        acc.total += 1;
        if (test.status === 'PASSED') acc.passed += 1;
        else acc.failed += 1;
      }
      return acc;
    }, { total: 0, passed: 0, failed: 0 });
    
    console.log(`📊 AKE Compatibility Tests Summary:`);
    console.log(`   Total: ${overallSummary.total}`);
    console.log(`   Passed: ${overallSummary.passed}`);
    console.log(`   Failed: ${overallSummary.failed}`);
    console.log(`   Success Rate: ${((overallSummary.passed / overallSummary.total) * 100).toFixed(1)}%`);
    
    return {
      testSuite: 'AKE Compatibility Tests',
      results: testResults,
      summary: overallSummary,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = {
  AKECompatibilityTests
};
