/**
 * OTR Protocol Message Bridge
 * 
 * Handles serialization/deserialization of OTR messages for compatibility
 * with libOTR reference implementation.
 * 
 * Based on libOTR Protocol-v3.html specification and binary format definitions.
 */

const crypto = require('crypto');

/**
 * OTR Data Types (from Protocol-v3.html)
 */
const DATA_TYPES = {
  BYTE: 1,
  SHORT: 2, 
  INT: 4,
  // MPI and DATA have variable length
};

/**
 * OTR Message Format Definitions
 * Based on libOTR Protocol-v3.html specification
 */
const OTR_MESSAGE_FORMATS = {
  DH_COMMIT: {
    protocolVersion: 0x0003,
    messageType: 0x02,
    fields: [
      { name: 'senderInstanceTag', type: 'INT' },
      { name: 'receiverInstanceTag', type: 'INT' },
      { name: 'encryptedGx', type: 'DATA' },
      { name: 'hashedGx', type: 'DATA' }
    ]
  },
  
  DH_KEY: {
    protocolVersion: 0x0003,
    messageType: 0x0a,
    fields: [
      { name: 'senderInstanceTag', type: 'INT' },
      { name: 'receiverInstanceT<PERSON>', type: 'INT' },
      { name: 'gy', type: 'MPI' }
    ]
  },
  
  REVE<PERSON>_SIGNATURE: {
    protocolVersion: 0x0003,
    messageType: 0x11,
    fields: [
      { name: 'senderInstanceTag', type: 'INT' },
      { name: 'receiverInstanceTag', type: 'INT' },
      { name: 'revealedKey', type: 'DATA' },
      { name: 'encryptedSignature', type: 'DATA' },
      { name: 'mac', type: 'MAC' }
    ]
  },
  
  SIGNATURE: {
    protocolVersion: 0x0003,
    messageType: 0x12,
    fields: [
      { name: 'senderInstanceTag', type: 'INT' },
      { name: 'receiverInstanceTag', type: 'INT' },
      { name: 'encryptedSignature', type: 'DATA' },
      { name: 'mac', type: 'MAC' }
    ]
  },
  
  DATA: {
    protocolVersion: 0x0003,
    messageType: 0x03,
    fields: [
      { name: 'flags', type: 'BYTE' },
      { name: 'senderKeyId', type: 'INT' },
      { name: 'recipientKeyId', type: 'INT' },
      { name: 'dhY', type: 'MPI' },
      { name: 'ctr', type: 'CTR' },
      { name: 'encryptedMessage', type: 'DATA' },
      { name: 'mac', type: 'MAC' },
      { name: 'oldMacKeys', type: 'DATA' }
    ]
  }
};

/**
 * OTR Message Serializer/Deserializer
 */
class OTRMessageBridge {
  constructor() {
    this.messageFormats = OTR_MESSAGE_FORMATS;
  }

  /**
   * Serialize an OTR message to binary format
   * @param {string} messageType - Type of message (DH_COMMIT, DH_KEY, etc.)
   * @param {Object} messageData - Message data object
   * @returns {Buffer} Serialized binary message
   */
  serializeMessage(messageType, messageData) {
    const format = this.messageFormats[messageType];
    if (!format) {
      throw new Error(`Unknown message type: ${messageType}`);
    }

    const buffers = [];
    
    // Add protocol version and message type
    buffers.push(this.serializeShort(format.protocolVersion));
    buffers.push(this.serializeByte(format.messageType));
    
    // Serialize each field according to its type
    for (const field of format.fields) {
      const value = messageData[field.name];
      if (value === undefined) {
        throw new Error(`Missing required field: ${field.name}`);
      }
      
      buffers.push(this.serializeField(value, field.type));
    }
    
    return Buffer.concat(buffers);
  }

  /**
   * Deserialize a binary OTR message
   * @param {Buffer} binaryData - Binary message data
   * @returns {Object} Parsed message object
   */
  deserializeMessage(binaryData) {
    let offset = 0;
    
    // Parse header
    const protocolVersion = binaryData.readUInt16BE(offset);
    offset += 2;
    
    const messageType = binaryData.readUInt8(offset);
    offset += 1;
    
    // Find message format
    const messageTypeName = this.getMessageTypeName(messageType);
    const format = this.messageFormats[messageTypeName];
    
    if (!format) {
      throw new Error(`Unknown message type: 0x${messageType.toString(16)}`);
    }
    
    if (protocolVersion !== format.protocolVersion) {
      throw new Error(`Protocol version mismatch: expected ${format.protocolVersion}, got ${protocolVersion}`);
    }
    
    // Parse fields
    const messageData = {
      messageType: messageTypeName,
      protocolVersion: protocolVersion
    };
    
    for (const field of format.fields) {
      const { value, newOffset } = this.deserializeField(binaryData, offset, field.type);
      messageData[field.name] = value;
      offset = newOffset;
    }
    
    return messageData;
  }

  /**
   * Get message type name from numeric type
   */
  getMessageTypeName(messageType) {
    for (const [name, format] of Object.entries(this.messageFormats)) {
      if (format.messageType === messageType) {
        return name;
      }
    }
    throw new Error(`Unknown message type: 0x${messageType.toString(16)}`);
  }

  /**
   * Serialize a field based on its type
   */
  serializeField(value, type) {
    switch (type) {
      case 'BYTE':
        return this.serializeByte(value);
      case 'SHORT':
        return this.serializeShort(value);
      case 'INT':
        return this.serializeInt(value);
      case 'MPI':
        return this.serializeMPI(value);
      case 'DATA':
        return this.serializeData(value);
      case 'CTR':
        return this.serializeCTR(value);
      case 'MAC':
        return this.serializeMAC(value);
      default:
        throw new Error(`Unknown field type: ${type}`);
    }
  }

  /**
   * Deserialize a field based on its type
   */
  deserializeField(buffer, offset, type) {
    switch (type) {
      case 'BYTE':
        return this.deserializeByte(buffer, offset);
      case 'SHORT':
        return this.deserializeShort(buffer, offset);
      case 'INT':
        return this.deserializeInt(buffer, offset);
      case 'MPI':
        return this.deserializeMPI(buffer, offset);
      case 'DATA':
        return this.deserializeData(buffer, offset);
      case 'CTR':
        return this.deserializeCTR(buffer, offset);
      case 'MAC':
        return this.deserializeMAC(buffer, offset);
      default:
        throw new Error(`Unknown field type: ${type}`);
    }
  }

  // Basic type serialization methods
  serializeByte(value) {
    const buffer = Buffer.allocUnsafe(1);
    buffer.writeUInt8(value, 0);
    return buffer;
  }

  serializeShort(value) {
    const buffer = Buffer.allocUnsafe(2);
    buffer.writeUInt16BE(value, 0);
    return buffer;
  }

  serializeInt(value) {
    const buffer = Buffer.allocUnsafe(4);
    buffer.writeUInt32BE(value, 0);
    return buffer;
  }

  serializeMPI(value) {
    // Multi-precision integer: 4-byte length + data
    let mpiData;
    
    if (Buffer.isBuffer(value)) {
      mpiData = value;
    } else if (typeof value === 'string') {
      // Assume hex string
      mpiData = Buffer.from(value.replace(/^0x/, ''), 'hex');
    } else if (typeof value === 'bigint' || (value && typeof value.toString === 'function')) {
      // Convert to hex and then to buffer
      const hexStr = value.toString(16);
      mpiData = Buffer.from(hexStr.length % 2 ? '0' + hexStr : hexStr, 'hex');
    } else {
      throw new Error('Invalid MPI value type');
    }
    
    // Remove leading zeros (minimum-length encoding requirement)
    while (mpiData.length > 1 && mpiData[0] === 0) {
      mpiData = mpiData.slice(1);
    }
    
    const lengthBuffer = this.serializeInt(mpiData.length);
    return Buffer.concat([lengthBuffer, mpiData]);
  }

  serializeData(value) {
    // Variable-length data: 4-byte length + data
    const dataBuffer = Buffer.isBuffer(value) ? value : Buffer.from(value);
    const lengthBuffer = this.serializeInt(dataBuffer.length);
    return Buffer.concat([lengthBuffer, dataBuffer]);
  }

  serializeCTR(value) {
    // 8-byte counter value
    if (Buffer.isBuffer(value) && value.length === 8) {
      return value;
    } else if (typeof value === 'number' || typeof value === 'bigint') {
      const buffer = Buffer.allocUnsafe(8);
      buffer.writeBigUInt64BE(BigInt(value), 0);
      return buffer;
    } else {
      throw new Error('Invalid CTR value');
    }
  }

  serializeMAC(value) {
    // 20-byte MAC
    const macBuffer = Buffer.isBuffer(value) ? value : Buffer.from(value, 'hex');
    if (macBuffer.length !== 20) {
      throw new Error(`MAC must be 20 bytes, got ${macBuffer.length}`);
    }
    return macBuffer;
  }

  // Basic type deserialization methods
  deserializeByte(buffer, offset) {
    if (offset + 1 > buffer.length) {
      throw new Error('Buffer underrun reading BYTE');
    }
    return {
      value: buffer.readUInt8(offset),
      newOffset: offset + 1
    };
  }

  deserializeShort(buffer, offset) {
    if (offset + 2 > buffer.length) {
      throw new Error('Buffer underrun reading SHORT');
    }
    return {
      value: buffer.readUInt16BE(offset),
      newOffset: offset + 2
    };
  }

  deserializeInt(buffer, offset) {
    if (offset + 4 > buffer.length) {
      throw new Error('Buffer underrun reading INT');
    }
    return {
      value: buffer.readUInt32BE(offset),
      newOffset: offset + 4
    };
  }

  deserializeMPI(buffer, offset) {
    const { value: length, newOffset: lengthOffset } = this.deserializeInt(buffer, offset);
    
    if (lengthOffset + length > buffer.length) {
      throw new Error('Buffer underrun reading MPI data');
    }
    
    const mpiData = buffer.slice(lengthOffset, lengthOffset + length);
    
    return {
      value: mpiData,
      newOffset: lengthOffset + length
    };
  }

  deserializeData(buffer, offset) {
    const { value: length, newOffset: lengthOffset } = this.deserializeInt(buffer, offset);
    
    if (lengthOffset + length > buffer.length) {
      throw new Error('Buffer underrun reading DATA');
    }
    
    const data = buffer.slice(lengthOffset, lengthOffset + length);
    
    return {
      value: data,
      newOffset: lengthOffset + length
    };
  }

  deserializeCTR(buffer, offset) {
    if (offset + 8 > buffer.length) {
      throw new Error('Buffer underrun reading CTR');
    }
    
    const ctrData = buffer.slice(offset, offset + 8);
    
    return {
      value: ctrData,
      newOffset: offset + 8
    };
  }

  deserializeMAC(buffer, offset) {
    if (offset + 20 > buffer.length) {
      throw new Error('Buffer underrun reading MAC');
    }
    
    const macData = buffer.slice(offset, offset + 20);
    
    return {
      value: macData,
      newOffset: offset + 20
    };
  }

  /**
   * Create an OTR message string (base64 encoded with OTR wrapper)
   */
  createOTRMessageString(messageType, messageData) {
    const binaryMessage = this.serializeMessage(messageType, messageData);
    const base64Message = binaryMessage.toString('base64');
    return `?OTR:${base64Message}.`;
  }

  /**
   * Parse an OTR message string
   */
  parseOTRMessageString(otrMessageString) {
    if (!otrMessageString.startsWith('?OTR:') || !otrMessageString.endsWith('.')) {
      throw new Error('Invalid OTR message format');
    }
    
    const base64Data = otrMessageString.slice(5, -1); // Remove "?OTR:" and "."
    const binaryData = Buffer.from(base64Data, 'base64');
    
    return this.deserializeMessage(binaryData);
  }

  /**
   * Validate instance tags according to OTR v3 specification
   */
  validateInstanceTag(instanceTag) {
    // Instance tags must be >= 0x00000100
    return instanceTag >= 0x00000100;
  }

  /**
   * Validate message structure
   */
  validateMessage(messageType, messageData) {
    const format = this.messageFormats[messageType];
    if (!format) {
      throw new Error(`Unknown message type: ${messageType}`);
    }

    // Check required fields
    for (const field of format.fields) {
      if (messageData[field.name] === undefined) {
        throw new Error(`Missing required field: ${field.name}`);
      }
    }

    // Validate instance tags if present
    if (messageData.senderInstanceTag !== undefined) {
      if (!this.validateInstanceTag(messageData.senderInstanceTag)) {
        throw new Error(`Invalid sender instance tag: ${messageData.senderInstanceTag}`);
      }
    }

    if (messageData.receiverInstanceTag !== undefined && messageData.receiverInstanceTag !== 0) {
      if (!this.validateInstanceTag(messageData.receiverInstanceTag)) {
        throw new Error(`Invalid receiver instance tag: ${messageData.receiverInstanceTag}`);
      }
    }

    return true;
  }
}

module.exports = {
  OTRMessageBridge,
  OTR_MESSAGE_FORMATS,
  DATA_TYPES
};
