/**
 * @fileoverview libOTR Protocol Compliance Testing
 * Tests compatibility with libOTR reference implementation
 * Part of libOTR Testing Framework PRD implementation
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { OtrSession } from '../../../src/core/session/index.js';
import { MESSAGE_TYPE, STATE } from '../../../src/core/protocol/state.js';
import { generateKeyPair } from '../../../src/core/crypto/dsa.js';

// libOTR Constants (from libOTR source)
const LIBOTR_CONSTANTS = {
  // SMP Constants from sm.c
  MODULUS: "0xFFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD1" +
           "29024E088A67CC74020BBEA63B139B22514A08798E3404DD" +
           "EF9519B3CD3A431B302B0A6DF25F14374FE1356D6D51C245" +
           "E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7ED" +
           "EE386BFB5A899FA5AE9F24117C4B1FE649286651ECE45B3D" +
           "C2007CB8A163BF0598DA48361C55D39A69163FA8FD24CF5F" +
           "83655D23DCA3AD961C62F356208552BB9ED529077096966D" +
           "670C354E4ABC9804F1746C08CA237327FFFFFFFFFFFFFFFF",

  ORDER: "0x7FFFFFFFFFFFFFFFE487ED5110B4611A62633145C06E0E68" +
         "948127044533E63A0105DF531D89CD9128A5043CC71A026E" +
         "F7CA8CD9E69D218D98158536F92F8A1BA7F09AB6B6A8E122" +
         "F242DABB312F3F637A262174D31BF6B585FFAE5B7A035BF6" +
         "F71C35FDAD44CFD2D74F9208BE258FF324943328F6722D9E" +
         "E1003E5C50B1DF82CC6D241B0E2AE9CD348B1FD47E9267AF" +
         "C1B2AE91EE51D6CB0E3179AB1042A95DCF6A9483B84B4B36" +
         "B3861AA7255E4C0278BA36046511B993FFFFFFFFFFFFFFFF",

  GENERATOR: "0x02",
  MOD_LEN_BITS: 1536,
  MOD_LEN_BYTES: 192,

  // Protocol version
  PROTOCOL_VERSION: 0x0003,

  // Instance tag constraints
  MIN_INSTANCE_TAG: 0x00000100,
  MAX_INSTANCE_TAG: 0xFFFFFFFF
};

// OTR Message Format Definitions (from Protocol-v3.html)
const OTR_MESSAGE_FORMATS = {
  DH_COMMIT: {
    protocolVersion: 0x0003,
    messageType: 0x02,
    fields: ['senderInstanceTag', 'receiverInstanceTag', 'encryptedGx', 'hashedGx']
  },
  DH_KEY: {
    protocolVersion: 0x0003,
    messageType: 0x0a,
    fields: ['senderInstanceTag', 'receiverInstanceTag', 'gy']
  },
  REVEAL_SIGNATURE: {
    protocolVersion: 0x0003,
    messageType: 0x11,
    fields: ['senderInstanceTag', 'receiverInstanceTag', 'revealedKey', 'encryptedSignature', 'mac']
  },
  SIGNATURE: {
    protocolVersion: 0x0003,
    messageType: 0x12,
    fields: ['senderInstanceTag', 'receiverInstanceTag', 'encryptedSignature', 'mac']
  },
  DATA: {
    protocolVersion: 0x0003,
    messageType: 0x03,
    fields: ['flags', 'senderKeyId', 'recipientKeyId', 'dhY', 'ctr', 'encryptedMessage', 'mac', 'oldMacKeys']
  }
};

// SMP State Machine (from sm.h)
const SMP_STATES = {
  EXPECT1: 1,  // Expecting SMP1 message
  EXPECT2: 2,  // Expecting SMP2 message
  EXPECT3: 3,  // Expecting SMP3 message
  EXPECT4: 4,  // Expecting SMP4 message
  EXPECT5: 5   // SMP complete, expecting potential new SMP1
};

const SMP_PROGRESS_STATES = {
  OK: 0,        // SMP proceeding normally
  CHEATED: 1,   // Verification failed - potential cheating
  FAILED: 2,    // SMP failed due to error
  SUCCEEDED: 3  // SMP completed successfully
};

describe('libOTR Protocol Compliance Testing', () => {
  let aliceSession, bobSession;
  let aliceKeys, bobKeys;

  beforeEach(async () => {
    // Generate test key pairs
    aliceKeys = await generateKeyPair();
    bobKeys = await generateKeyPair();

    // Create test sessions with libOTR-compatible settings
    aliceSession = new OtrSession({
      privateKey: aliceKeys.privateKey,
      publicKey: aliceKeys.publicKey,
      instanceTag: 0x12345678, // Valid instance tag
      protocolVersion: LIBOTR_CONSTANTS.PROTOCOL_VERSION
    });

    bobSession = new OtrSession({
      privateKey: bobKeys.privateKey,
      publicKey: bobKeys.publicKey,
      instanceTag: 0x87654321, // Valid instance tag
      protocolVersion: LIBOTR_CONSTANTS.PROTOCOL_VERSION
    });
  });

  afterEach(() => {
    aliceSession?.cleanup();
    bobSession?.cleanup();
  });

  describe('OTR v3 Protocol Specification Compliance', () => {
    test('should use correct DH group parameters', () => {
      // Validate 1536-bit prime modulus (RFC 3526)
      const dhParams = aliceSession.getDHParameters();
      
      expect(dhParams.modulus.toString(16).toUpperCase()).toBe(
        LIBOTR_CONSTANTS.MODULUS.substring(2) // Remove 0x prefix
      );
      expect(dhParams.generator.toString(16)).toBe(
        LIBOTR_CONSTANTS.GENERATOR.substring(2)
      );
      expect(dhParams.modulusLength).toBe(LIBOTR_CONSTANTS.MOD_LEN_BITS);
    });

    test('should validate instance tag constraints', () => {
      // Instance tags must be >= 0x00000100
      expect(aliceSession.getInstanceTag()).toBeGreaterThanOrEqual(
        LIBOTR_CONSTANTS.MIN_INSTANCE_TAG
      );
      expect(bobSession.getInstanceTag()).toBeGreaterThanOrEqual(
        LIBOTR_CONSTANTS.MIN_INSTANCE_TAG
      );

      // Test invalid instance tags
      expect(() => {
        new OtrSession({
          privateKey: aliceKeys.privateKey,
          publicKey: aliceKeys.publicKey,
          instanceTag: 0x000000FF // Invalid - too small
        });
      }).toThrow(/invalid.*instance.*tag/i);
    });

    test('should use correct protocol version', () => {
      expect(aliceSession.getProtocolVersion()).toBe(LIBOTR_CONSTANTS.PROTOCOL_VERSION);
      expect(bobSession.getProtocolVersion()).toBe(LIBOTR_CONSTANTS.PROTOCOL_VERSION);
    });

    test('should handle message fragmentation correctly', async () => {
      // Test message fragmentation according to OTR v3 spec
      const largeMessage = 'x'.repeat(2000); // Large message requiring fragmentation
      
      const fragments = await aliceSession.fragmentMessage(largeMessage);
      
      expect(fragments.length).toBeGreaterThan(1);
      
      fragments.forEach((fragment, index) => {
        // Each fragment should match OTR fragmentation format
        expect(fragment).toMatch(/^\?OTR\|[0-9a-f]{8}\|[0-9a-f]{8}\|/);
        
        // Validate fragment structure
        const parts = fragment.split('|');
        expect(parts[0]).toBe('?OTR');
        expect(parts[1]).toHaveLength(8); // Instance tag (hex)
        expect(parts[2]).toHaveLength(8); // Fragment identifier (hex)
        expect(parts[3]).toMatch(/^\d+$/);  // Fragment index
        expect(parts[4]).toMatch(/^\d+$/);  // Total fragments
      });

      // Test reassembly
      const reassembled = await bobSession.reassembleFragments(fragments);
      expect(reassembled).toBe(largeMessage);
    });
  });

  describe('Message Format Compatibility', () => {
    test('should generate libOTR-compatible DH_COMMIT messages', async () => {
      const dhCommitMsg = await aliceSession.initiateAKE();
      
      // Validate message structure
      expect(dhCommitMsg.type).toBe(MESSAGE_TYPE.DH_COMMIT);
      expect(dhCommitMsg.protocolVersion).toBe(LIBOTR_CONSTANTS.PROTOCOL_VERSION);
      
      // Validate required fields
      const format = OTR_MESSAGE_FORMATS.DH_COMMIT;
      format.fields.forEach(field => {
        expect(dhCommitMsg).toHaveProperty(field);
      });

      // Validate field formats
      expect(dhCommitMsg.senderInstanceTag).toBe(aliceSession.getInstanceTag());
      expect(dhCommitMsg.receiverInstanceTag).toBe(0x00000000); // Unknown initially
      expect(dhCommitMsg.encryptedGx).toBeInstanceOf(Uint8Array);
      expect(dhCommitMsg.hashedGx).toBeInstanceOf(Uint8Array);
      expect(dhCommitMsg.encryptedGx.length).toBe(LIBOTR_CONSTANTS.MOD_LEN_BYTES + 16); // DH key + AES overhead
      expect(dhCommitMsg.hashedGx.length).toBe(32); // SHA-256 hash
    });

    test('should generate libOTR-compatible DH_KEY messages', async () => {
      const dhCommitMsg = await aliceSession.initiateAKE();
      const dhKeyMsg = await bobSession.processMessage(dhCommitMsg);
      
      // Validate message structure
      expect(dhKeyMsg.type).toBe(MESSAGE_TYPE.DH_KEY);
      expect(dhKeyMsg.protocolVersion).toBe(LIBOTR_CONSTANTS.PROTOCOL_VERSION);
      
      // Validate required fields
      const format = OTR_MESSAGE_FORMATS.DH_KEY;
      format.fields.forEach(field => {
        expect(dhKeyMsg).toHaveProperty(field);
      });

      // Validate field formats
      expect(dhKeyMsg.senderInstanceTag).toBe(bobSession.getInstanceTag());
      expect(dhKeyMsg.receiverInstanceTag).toBe(aliceSession.getInstanceTag());
      expect(dhKeyMsg.gy).toBeInstanceOf(Uint8Array);
      expect(dhKeyMsg.gy.length).toBe(LIBOTR_CONSTANTS.MOD_LEN_BYTES);
    });

    test('should generate libOTR-compatible REVEAL_SIGNATURE messages', async () => {
      const dhCommitMsg = await aliceSession.initiateAKE();
      const dhKeyMsg = await bobSession.processMessage(dhCommitMsg);
      const revealSigMsg = await aliceSession.processMessage(dhKeyMsg);
      
      // Validate message structure
      expect(revealSigMsg.type).toBe(MESSAGE_TYPE.REVEAL_SIG);
      expect(revealSigMsg.protocolVersion).toBe(LIBOTR_CONSTANTS.PROTOCOL_VERSION);
      
      // Validate required fields
      const format = OTR_MESSAGE_FORMATS.REVEAL_SIGNATURE;
      format.fields.forEach(field => {
        expect(revealSigMsg).toHaveProperty(field);
      });

      // Validate field formats
      expect(revealSigMsg.revealedKey).toBeInstanceOf(Uint8Array);
      expect(revealSigMsg.encryptedSignature).toBeInstanceOf(Uint8Array);
      expect(revealSigMsg.mac).toBeInstanceOf(Uint8Array);
      expect(revealSigMsg.revealedKey.length).toBe(16); // AES key
      expect(revealSigMsg.mac.length).toBe(20); // HMAC-SHA1
    });

    test('should generate libOTR-compatible SIGNATURE messages', async () => {
      const dhCommitMsg = await aliceSession.initiateAKE();
      const dhKeyMsg = await bobSession.processMessage(dhCommitMsg);
      const revealSigMsg = await aliceSession.processMessage(dhKeyMsg);
      const sigMsg = await bobSession.processMessage(revealSigMsg);
      
      // Validate message structure
      expect(sigMsg.type).toBe(MESSAGE_TYPE.SIGNATURE);
      expect(sigMsg.protocolVersion).toBe(LIBOTR_CONSTANTS.PROTOCOL_VERSION);
      
      // Validate required fields
      const format = OTR_MESSAGE_FORMATS.SIGNATURE;
      format.fields.forEach(field => {
        expect(sigMsg).toHaveProperty(field);
      });

      // Validate field formats
      expect(sigMsg.encryptedSignature).toBeInstanceOf(Uint8Array);
      expect(sigMsg.mac).toBeInstanceOf(Uint8Array);
      expect(sigMsg.mac.length).toBe(20); // HMAC-SHA1
    });
  });

  describe('Cryptographic Parameter Validation', () => {
    test('should validate group elements according to libOTR standards', () => {
      // Test based on libOTR check_group_elem function
      const validator = aliceSession.getCryptoValidator();
      
      // Valid element (2 < element < modulus-2)
      const validElement = BigInt('3');
      expect(validator.checkGroupElement(validElement)).toBe(true);
      
      // Invalid elements
      const invalidLow = BigInt('1');   // <= 2
      const invalidZero = BigInt('0');  // <= 2
      const invalidTwo = BigInt('2');   // <= 2
      
      expect(validator.checkGroupElement(invalidLow)).toBe(false);
      expect(validator.checkGroupElement(invalidZero)).toBe(false);
      expect(validator.checkGroupElement(invalidTwo)).toBe(false);
      
      // Invalid high (>= modulus-2)
      const modulus = BigInt('0x' + LIBOTR_CONSTANTS.MODULUS.substring(2));
      const invalidHigh = modulus - BigInt('1');
      expect(validator.checkGroupElement(invalidHigh)).toBe(false);
    });

    test('should validate exponents according to libOTR standards', () => {
      // Test based on libOTR check_expon function
      const validator = aliceSession.getCryptoValidator();
      
      // Valid exponent (1 <= exponent < order)
      const validExponent = BigInt('100');
      expect(validator.checkExponent(validExponent)).toBe(true);
      
      // Invalid exponents
      const invalidZero = BigInt('0');   // < 1
      const order = BigInt('0x' + LIBOTR_CONSTANTS.ORDER.substring(2));
      const invalidHigh = order;         // >= order
      
      expect(validator.checkExponent(invalidZero)).toBe(false);
      expect(validator.checkExponent(invalidHigh)).toBe(false);
    });

    test('should use libOTR-compatible key derivation', async () => {
      // Complete AKE to establish shared secret
      const dhCommitMsg = await aliceSession.initiateAKE();
      const dhKeyMsg = await bobSession.processMessage(dhCommitMsg);
      const revealSigMsg = await aliceSession.processMessage(dhKeyMsg);
      const sigMsg = await bobSession.processMessage(revealSigMsg);
      await aliceSession.processMessage(sigMsg);
      
      // Both sessions should derive identical keys
      const aliceKeys = aliceSession.getSessionKeys();
      const bobKeys = bobSession.getSessionKeys();
      
      expect(aliceKeys.sendingAESKey).toEqual(bobKeys.receivingAESKey);
      expect(aliceKeys.receivingAESKey).toEqual(bobKeys.sendingAESKey);
      expect(aliceKeys.sendingMACKey).toEqual(bobKeys.receivingMACKey);
      expect(aliceKeys.receivingMACKey).toEqual(bobKeys.sendingMACKey);
      
      // Validate key lengths match libOTR
      expect(aliceKeys.sendingAESKey.length).toBe(16);    // 128-bit AES
      expect(aliceKeys.sendingMACKey.length).toBe(20);    // 160-bit HMAC-SHA1
    });
  });

  describe('SMP Protocol Compliance', () => {
    test('should implement libOTR-compatible SMP state machine', async () => {
      // Establish encrypted session first
      await establishEncryptedSession(aliceSession, bobSession);
      
      // Test SMP state transitions
      expect(aliceSession.getSMPState()).toBe(SMP_STATES.EXPECT1);
      expect(bobSession.getSMPState()).toBe(SMP_STATES.EXPECT1);
      
      // Alice initiates SMP
      const smp1 = await aliceSession.initiateSMP('shared secret');
      expect(aliceSession.getSMPState()).toBe(SMP_STATES.EXPECT2);
      
      // Bob processes SMP1
      const smp2 = await bobSession.processSMPMessage(smp1, 'shared secret');
      expect(bobSession.getSMPState()).toBe(SMP_STATES.EXPECT3);
      
      // Alice processes SMP2
      const smp3 = await aliceSession.processSMPMessage(smp2);
      expect(aliceSession.getSMPState()).toBe(SMP_STATES.EXPECT4);
      
      // Bob processes SMP3
      const smp4 = await bobSession.processSMPMessage(smp3);
      expect(bobSession.getSMPState()).toBe(SMP_STATES.EXPECT5);
      
      // Alice processes SMP4
      await aliceSession.processSMPMessage(smp4);
      expect(aliceSession.getSMPState()).toBe(SMP_STATES.EXPECT5);
      
      // Both should have successful SMP result
      expect(aliceSession.getSMPResult()).toBe(SMP_PROGRESS_STATES.SUCCEEDED);
      expect(bobSession.getSMPResult()).toBe(SMP_PROGRESS_STATES.SUCCEEDED);
    });

    test('should detect SMP cheating like libOTR', async () => {
      await establishEncryptedSession(aliceSession, bobSession);
      
      // Alice initiates SMP with one secret
      const smp1 = await aliceSession.initiateSMP('secret1');
      
      // Bob responds with different secret
      const smp2 = await bobSession.processSMPMessage(smp1, 'secret2');
      
      // Continue SMP protocol
      const smp3 = await aliceSession.processSMPMessage(smp2);
      const smp4 = await bobSession.processSMPMessage(smp3);
      await aliceSession.processSMPMessage(smp4);
      
      // SMP should detect the mismatch
      expect(aliceSession.getSMPResult()).toBe(SMP_PROGRESS_STATES.FAILED);
      expect(bobSession.getSMPResult()).toBe(SMP_PROGRESS_STATES.FAILED);
    });
  });
});

// Helper function to establish encrypted session
async function establishEncryptedSession(alice, bob) {
  const dhCommitMsg = await alice.initiateAKE();
  const dhKeyMsg = await bob.processMessage(dhCommitMsg);
  const revealSigMsg = await alice.processMessage(dhKeyMsg);
  const sigMsg = await bob.processMessage(revealSigMsg);
  await alice.processMessage(sigMsg);
  
  expect(alice.isEncrypted()).toBe(true);
  expect(bob.isEncrypted()).toBe(true);
}
