/**
 * AKE Compatibility Test Suite
 * 
 * Tests AKE (Authenticated Key Exchange) compatibility with libOTR reference implementation.
 */

const { AKECompatibilityTests } = require('./scenarios/ake-compatibility');

describe('AKE Compatibility Tests', () => {
  let akeTests;
  
  beforeEach(() => {
    akeTests = new AKECompatibilityTests();
  });

  describe('OTR Query Message Parsing', () => {
    test('should parse all OTR query message formats correctly', async () => {
      const result = await akeTests.testOTRQueryMessages();
      
      expect(result.testName).toBe('OTR Query Message Parsing');
      expect(result.summary.total).toBeGreaterThan(0);
      expect(result.summary.failed).toBe(0);
      
      // All test cases should pass
      result.results.forEach(testCase => {
        expect(testCase.status).toBe('PASSED');
      });
    });
  });

  describe('OTR Message Format Validation', () => {
    test('should validate all OTR message formats correctly', async () => {
      const result = await akeTests.testOTRMessageFormats();
      
      expect(result.testName).toBe('OTR Message Format Validation');
      expect(result.summary.total).toBeGreaterThan(0);
      expect(result.summary.failed).toBe(0);
      
      // All test cases should pass
      result.results.forEach(testCase => {
        expect(testCase.status).toBe('PASSED');
      });
    });
  });

  describe('Instance Tag Validation', () => {
    test('should validate instance tags according to OTR v3 specification', async () => {
      const result = await akeTests.testInstanceTagValidation();
      
      expect(result.testName).toBe('Instance Tag Validation');
      expect(result.summary.total).toBeGreaterThan(0);
      expect(result.summary.failed).toBe(0);
      
      // All test cases should pass
      result.results.forEach(testCase => {
        expect(testCase.status).toBe('PASSED');
      });
    });
  });

  describe('OTR Message String Encoding', () => {
    test('should encode and decode OTR message strings correctly', async () => {
      const result = await akeTests.testOTRMessageStringEncoding();
      
      expect(result.testName).toBe('OTR Message String Encoding');
      expect(result.status).toBe('PASSED');
      expect(result.otrString).toMatch(/^\?OTR:[A-Za-z0-9+/=]+\.$/);
    });
  });

  describe('Whitespace Tag Handling', () => {
    test('should add OTR whitespace tags to messages', async () => {
      const result = await akeTests.testWhitespaceTagHandling();
      
      expect(result.testName).toBe('Whitespace Tag Handling');
      expect(result.status).toBe('PASSED');
      expect(result.hasV3Tag).toBe(true);
    });
  });

  describe('Complete AKE Compatibility Test Suite', () => {
    test('should pass all AKE compatibility tests', async () => {
      const results = await akeTests.runAllTests();
      
      expect(results.testSuite).toBe('AKE Compatibility Tests');
      expect(results.summary.total).toBeGreaterThan(0);
      expect(results.summary.failed).toBe(0);
      expect(results.summary.passed).toBe(results.summary.total);
      
      // Verify all individual test results
      results.results.forEach(testResult => {
        if (testResult.summary) {
          // Test with multiple sub-tests
          expect(testResult.summary.failed).toBe(0);
        } else {
          // Single test
          expect(testResult.status).toBe('PASSED');
        }
      });
    }, 30000); // Longer timeout for comprehensive test
  });
});
