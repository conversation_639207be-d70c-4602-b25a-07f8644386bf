/**
 * webOTR Dummy Client for libOTR Compatibility Testing
 * 
 * This client implements the same interface as libOTR's dummy clients
 * to enable cross-implementation compatibility testing.
 * 
 * Based on libOTR test_suite/dummy_im.py and otr_subprocess.py
 */

const EventEmitter = require('events');
const { OTRSession } = require('../../../src/core/session');
const { SMPHandler } = require('../../../src/core/protocol/smp');
const { AKEHandler } = require('../../../src/core/protocol/ake');

/**
 * Message types for compatibility with libOTR test framework
 */
const MESSAGE_TYPES = {
  RAW_MSG: 'raw_msg',
  OTR_MSG: 'otr_msg', 
  RAW_DATA_MSG: 'raw_data_msg',
  ERROR: 'error',
  GONE_SECURE: 'gone_secure',
  GONE_INSECURE: 'gone_insecure',
  SMP_REQUEST: 'smp_request',
  SMP_COMPLETE: 'smp_complete',
  SMP_FAILED: 'smp_failed',
  SMP_ABORTED: 'smp_aborted'
};

/**
 * OTR Protocol Constants (matching libOTR)
 */
const OTR_CONSTANTS = {
  PROTOCOL_VERSION_3: 0x0003,
  MESSAGE_TYPE_DH_COMMIT: 0x02,
  MESSAGE_TYPE_DH_KEY: 0x0a,
  MESSAGE_TYPE_REVEAL_SIGNATURE: 0x11,
  MESSAGE_TYPE_SIGNATURE: 0x12,
  MESSAGE_TYPE_DATA: 0x03,
  
  // Instance tag constants
  MIN_VALID_INSTANCE_TAG: 0x00000100,
  
  // OTR message prefixes
  OTR_QUERY_PREFIX: '?OTR',
  OTR_DATA_PREFIX: '?OTR:',
  OTR_ERROR_PREFIX: '?OTR Error:',
  
  // Whitespace tags for OTR v3
  OTR_V3_WHITESPACE_TAG: '\x20\x20\x09\x09\x20\x20\x09\x09'
};

/**
 * webOTR Dummy Client for compatibility testing
 */
class WebOTRDummyClient extends EventEmitter {
  constructor(accountName, protocol, instanceTag = null) {
    super();
    
    this.accountName = accountName;
    this.protocol = protocol;
    this.instanceTag = instanceTag || this.generateInstanceTag();
    
    // Message queues (compatible with libOTR test framework)
    this.messageQueues = {
      [MESSAGE_TYPES.RAW_MSG]: [],
      [MESSAGE_TYPES.OTR_MSG]: [],
      [MESSAGE_TYPES.RAW_DATA_MSG]: [],
      [MESSAGE_TYPES.ERROR]: [],
      [MESSAGE_TYPES.GONE_SECURE]: [],
      [MESSAGE_TYPES.GONE_INSECURE]: [],
      [MESSAGE_TYPES.SMP_REQUEST]: [],
      [MESSAGE_TYPES.SMP_COMPLETE]: [],
      [MESSAGE_TYPES.SMP_FAILED]: [],
      [MESSAGE_TYPES.SMP_ABORTED]: []
    };
    
    // OTR session management
    this.sessions = new Map();
    this.contexts = new Map();
    
    // Query ID counter for async operations
    this.queryIdCounter = 1;
    this.pendingQueries = new Map();
    
    // Initialize OTR components
    this.initializeOTRComponents();
  }

  /**
   * Generate a valid instance tag (>= 0x00000100)
   */
  generateInstanceTag() {
    return Math.floor(Math.random() * 0xFFFFFF00) + OTR_CONSTANTS.MIN_VALID_INSTANCE_TAG;
  }

  /**
   * Initialize OTR protocol components
   */
  initializeOTRComponents() {
    // This will be expanded as we implement more components
    this.smpHandler = new SMPHandler();
    // this.akeHandler = new AKEHandler(); // Will be implemented
  }

  /**
   * Send a message to another client
   * Returns a query ID for tracking the operation
   */
  sendMessage(recipientAccount, recipientProtocol, message) {
    const queryId = this.generateQueryId();
    
    // Process message through OTR if session exists
    const sessionKey = `${recipientAccount}@${recipientProtocol}`;
    const session = this.sessions.get(sessionKey);
    
    if (session && session.isEncrypted()) {
      // Encrypt message
      this.processOutgoingMessage(queryId, message, session);
    } else {
      // Send plaintext (potentially with OTR tags)
      this.processPlaintextMessage(queryId, message, recipientAccount, recipientProtocol);
    }
    
    return queryId;
  }

  /**
   * Process an incoming message
   */
  receiveMessage(senderAccount, senderProtocol, message) {
    const sessionKey = `${senderAccount}@${senderProtocol}`;
    
    // Check if this is an OTR message
    if (this.isOTRMessage(message)) {
      this.processOTRMessage(message, sessionKey);
    } else {
      // Regular plaintext message
      this.queueMessage(MESSAGE_TYPES.RAW_MSG, {
        sender: senderAccount,
        protocol: senderProtocol,
        message: message,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Check if a message is an OTR message
   */
  isOTRMessage(message) {
    return message.startsWith(OTR_CONSTANTS.OTR_QUERY_PREFIX) ||
           message.startsWith(OTR_CONSTANTS.OTR_DATA_PREFIX) ||
           message.startsWith(OTR_CONSTANTS.OTR_ERROR_PREFIX);
  }

  /**
   * Process OTR protocol messages
   */
  processOTRMessage(message, sessionKey) {
    try {
      if (message.startsWith(OTR_CONSTANTS.OTR_QUERY_PREFIX)) {
        this.handleOTRQuery(message, sessionKey);
      } else if (message.startsWith(OTR_CONSTANTS.OTR_DATA_PREFIX)) {
        this.handleOTRData(message, sessionKey);
      } else if (message.startsWith(OTR_CONSTANTS.OTR_ERROR_PREFIX)) {
        this.handleOTRError(message, sessionKey);
      }
    } catch (error) {
      this.queueMessage(MESSAGE_TYPES.ERROR, {
        error: error.message,
        message: message,
        sessionKey: sessionKey
      });
    }
  }

  /**
   * Handle OTR query messages
   */
  handleOTRQuery(message, sessionKey) {
    // Parse OTR version information
    const versions = this.parseOTRVersions(message);
    
    if (versions.includes(3)) {
      // Start AKE for OTR v3
      this.initiateAKE(sessionKey);
    } else {
      this.queueMessage(MESSAGE_TYPES.ERROR, {
        error: 'Unsupported OTR version',
        versions: versions
      });
    }
  }

  /**
   * Parse OTR version information from query message
   */
  parseOTRVersions(queryMessage) {
    const versions = [];

    // Handle complex version combinations like "?OTR?v23?"
    // This means version 1 (from "?OTR?") plus versions 2 and 3 (from "v23?")

    // Check for version 1 indicator "?OTR?"
    if (queryMessage.includes('?OTR?')) {
      versions.push(1);
    }

    // Check for other versions "v[digits]?"
    const versionMatch = queryMessage.match(/v([0-9]+)\?/);
    if (versionMatch && versionMatch[1]) {
      // Parse individual version digits
      for (const char of versionMatch[1]) {
        const version = parseInt(char);
        if (version >= 1 && version <= 9 && !versions.includes(version)) {
          versions.push(version);
        }
      }
    }

    // Handle simple cases like "?OTRv3?" (no version 1)
    if (versions.length === 0) {
      const simpleMatch = queryMessage.match(/\?OTRv([0-9]+)\?/);
      if (simpleMatch && simpleMatch[1]) {
        for (const char of simpleMatch[1]) {
          const version = parseInt(char);
          if (version >= 1 && version <= 9) {
            versions.push(version);
          }
        }
      }
    }

    // Check for malformed queries like "?OTRv?" (claims versions but specifies none)
    if (versions.length === 0 && queryMessage.includes('v') && queryMessage.includes('?')) {
      const malformedMatch = queryMessage.match(/\?OTRv\?/);
      if (malformedMatch) {
        throw new Error('Malformed OTR query: version indicator present but no versions specified');
      }
    }

    return versions.sort();
  }

  /**
   * Initiate Authenticated Key Exchange
   */
  initiateAKE(sessionKey) {
    // This will be implemented when we add AKE support
    this.queueMessage(MESSAGE_TYPES.ERROR, {
      error: 'AKE not yet implemented',
      sessionKey: sessionKey
    });
  }

  /**
   * Handle OTR data messages
   */
  handleOTRData(message, sessionKey) {
    // Decode base64 OTR data message
    const base64Data = message.substring(OTR_CONSTANTS.OTR_DATA_PREFIX.length, message.length - 1);
    
    try {
      const binaryData = Buffer.from(base64Data, 'base64');
      this.processOTRDataMessage(binaryData, sessionKey);
    } catch (error) {
      this.queueMessage(MESSAGE_TYPES.ERROR, {
        error: 'Failed to decode OTR data message',
        details: error.message
      });
    }
  }

  /**
   * Process binary OTR data message
   */
  processOTRDataMessage(binaryData, sessionKey) {
    // Parse message header
    const messageInfo = this.parseOTRMessageHeader(binaryData);
    
    if (messageInfo.protocolVersion !== OTR_CONSTANTS.PROTOCOL_VERSION_3) {
      throw new Error(`Unsupported protocol version: ${messageInfo.protocolVersion}`);
    }

    // Route to appropriate handler based on message type
    switch (messageInfo.messageType) {
      case OTR_CONSTANTS.MESSAGE_TYPE_DH_COMMIT:
        this.handleDHCommit(binaryData, sessionKey);
        break;
      case OTR_CONSTANTS.MESSAGE_TYPE_DH_KEY:
        this.handleDHKey(binaryData, sessionKey);
        break;
      case OTR_CONSTANTS.MESSAGE_TYPE_REVEAL_SIGNATURE:
        this.handleRevealSignature(binaryData, sessionKey);
        break;
      case OTR_CONSTANTS.MESSAGE_TYPE_SIGNATURE:
        this.handleSignature(binaryData, sessionKey);
        break;
      case OTR_CONSTANTS.MESSAGE_TYPE_DATA:
        this.handleDataMessage(binaryData, sessionKey);
        break;
      default:
        throw new Error(`Unknown message type: ${messageInfo.messageType}`);
    }
  }

  /**
   * Parse OTR message header
   */
  parseOTRMessageHeader(binaryData) {
    if (binaryData.length < 6) {
      throw new Error('Message too short for OTR header');
    }

    return {
      protocolVersion: binaryData.readUInt16BE(0),
      messageType: binaryData.readUInt8(2),
      senderInstanceTag: binaryData.readUInt32BE(3),
      receiverInstanceTag: binaryData.readUInt32BE(7)
    };
  }

  /**
   * Generate a unique query ID
   */
  generateQueryId() {
    return `query_${this.queryIdCounter++}`;
  }

  /**
   * Queue a message in the appropriate queue
   */
  queueMessage(messageType, data) {
    if (this.messageQueues[messageType]) {
      this.messageQueues[messageType].push({
        ...data,
        timestamp: Date.now(),
        id: this.generateQueryId()
      });

      // Emit event for listeners (only if not an error to avoid unhandled error events)
      if (messageType !== MESSAGE_TYPES.ERROR) {
        this.emit(messageType, data);
      }
    }
  }

  /**
   * Get messages from a specific queue
   */
  getMessages(messageType, count = null) {
    const queue = this.messageQueues[messageType];
    if (!queue) return [];
    
    if (count === null) {
      return queue.splice(0); // Return all and clear
    } else {
      return queue.splice(0, count); // Return count and remove
    }
  }

  /**
   * Get a specific query result (blocking simulation)
   */
  async getQueryResult(queryId, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const result = this.pendingQueries.get(queryId);
      if (result) {
        this.pendingQueries.delete(queryId);
        resolve(result);
        return;
      }

      // Set up timeout
      const timeoutId = setTimeout(() => {
        reject(new Error(`Query ${queryId} timed out`));
      }, timeout);

      // Check periodically for result
      const checkInterval = setInterval(() => {
        const result = this.pendingQueries.get(queryId);
        if (result) {
          clearTimeout(timeoutId);
          clearInterval(checkInterval);
          this.pendingQueries.delete(queryId);
          resolve(result);
        }
      }, 100);
    });
  }

  /**
   * Set query result
   */
  setQueryResult(queryId, result) {
    this.pendingQueries.set(queryId, result);
  }

  // Placeholder methods for AKE message handling (to be implemented)
  handleDHCommit(binaryData, sessionKey) {
    this.queueMessage(MESSAGE_TYPES.ERROR, {
      error: 'DH Commit handling not yet implemented'
    });
  }

  handleDHKey(binaryData, sessionKey) {
    this.queueMessage(MESSAGE_TYPES.ERROR, {
      error: 'DH Key handling not yet implemented'
    });
  }

  handleRevealSignature(binaryData, sessionKey) {
    this.queueMessage(MESSAGE_TYPES.ERROR, {
      error: 'Reveal Signature handling not yet implemented'
    });
  }

  handleSignature(binaryData, sessionKey) {
    this.queueMessage(MESSAGE_TYPES.ERROR, {
      error: 'Signature handling not yet implemented'
    });
  }

  handleDataMessage(binaryData, sessionKey) {
    this.queueMessage(MESSAGE_TYPES.ERROR, {
      error: 'Data message handling not yet implemented'
    });
  }

  handleOTRError(message, sessionKey) {
    const errorText = message.substring(OTR_CONSTANTS.OTR_ERROR_PREFIX.length);
    this.queueMessage(MESSAGE_TYPES.ERROR, {
      error: 'OTR Error received',
      details: errorText,
      sessionKey: sessionKey
    });
  }

  // Utility methods for testing
  processOutgoingMessage(queryId, message, session) {
    // Placeholder for encrypted message processing
    this.setQueryResult(queryId, `?OTR:${Buffer.from(message).toString('base64')}.`);
  }

  processPlaintextMessage(queryId, message, recipientAccount, recipientProtocol) {
    // Check if we should add OTR whitespace tags
    let processedMessage = message;
    
    // Add OTR v3 whitespace tag if appropriate
    if (this.shouldAddWhitespaceTag(recipientAccount, recipientProtocol)) {
      processedMessage += OTR_CONSTANTS.OTR_V3_WHITESPACE_TAG;
    }
    
    this.setQueryResult(queryId, processedMessage);
  }

  shouldAddWhitespaceTag(recipientAccount, recipientProtocol) {
    // Policy decision - for testing, we'll add tags to indicate OTR capability
    return true;
  }
}

module.exports = {
  WebOTRDummyClient,
  MESSAGE_TYPES,
  OTR_CONSTANTS
};
