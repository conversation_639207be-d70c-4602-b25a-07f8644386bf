/**
 * Setup file for libOTR Reference Implementation Tests
 * 
 * Configures the test environment for compatibility testing with libOTR.
 */

const path = require('path');
const fs = require('fs');

// Extend Jest timeout for compatibility tests
jest.setTimeout(30000);

// Global test configuration
global.TEST_CONFIG = {
  // libOTR paths
  LIBOTR_ROOT: path.join(__dirname, '../../lib/libotr'),
  LIBOTR_SRC: path.join(__dirname, '../../lib/libotr/src'),
  LIBOTR_TEST_SUITE: path.join(__dirname, '../../lib/libotr/test_suite'),
  
  // Test data directories
  TEST_DATA_DIR: path.join(__dirname, 'test-data'),
  FIXTURES_DIR: path.join(__dirname, 'fixtures'),
  
  // Test configuration
  VERBOSE_LOGGING: process.env.VERBOSE_TESTS === 'true',
  SKIP_SLOW_TESTS: process.env.SKIP_SLOW_TESTS === 'true',
  
  // Protocol constants
  OTR_V3_PROTOCOL_VERSION: 0x0003,
  MIN_INSTANCE_TAG: 0x00000100,
  
  // Timeouts
  DEFAULT_TIMEOUT: 5000,
  LONG_TIMEOUT: 15000,
  
  // Test client configuration
  DEFAULT_ACCOUNT: '<EMAIL>',
  DEFAULT_PROTOCOL: 'test'
};

// Global utilities for tests
global.TestUtils = {
  /**
   * Create a random instance tag
   */
  generateInstanceTag() {
    return Math.floor(Math.random() * 0xFFFFFF00) + global.TEST_CONFIG.MIN_INSTANCE_TAG;
  },
  
  /**
   * Create test data buffer
   */
  createTestBuffer(size = 32) {
    const buffer = Buffer.alloc(size);
    for (let i = 0; i < size; i++) {
      buffer[i] = i % 256;
    }
    return buffer;
  },
  
  /**
   * Create hex string of specified length
   */
  createHexString(length = 32) {
    const chars = '0123456789abcdef';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
    return result;
  },
  
  /**
   * Wait for a specified time
   */
  async wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  
  /**
   * Log message if verbose mode is enabled
   */
  log(message) {
    if (global.TEST_CONFIG.VERBOSE_LOGGING) {
      console.log(`[TEST] ${message}`);
    }
  },
  
  /**
   * Create a mock OTR message
   */
  createMockOTRMessage(type = 'DH_KEY') {
    const baseMessage = {
      senderInstanceTag: this.generateInstanceTag(),
      receiverInstanceTag: this.generateInstanceTag()
    };
    
    switch (type) {
      case 'DH_COMMIT':
        return {
          ...baseMessage,
          encryptedGx: this.createTestBuffer(196),
          hashedGx: this.createTestBuffer(32)
        };
        
      case 'DH_KEY':
        return {
          ...baseMessage,
          gy: this.createTestBuffer(192)
        };
        
      case 'REVEAL_SIGNATURE':
        return {
          ...baseMessage,
          revealedKey: this.createTestBuffer(16),
          encryptedSignature: this.createTestBuffer(100),
          mac: this.createTestBuffer(20)
        };
        
      case 'SIGNATURE':
        return {
          ...baseMessage,
          encryptedSignature: this.createTestBuffer(100),
          mac: this.createTestBuffer(20)
        };
        
      case 'DATA':
        return {
          flags: 0x00,
          senderKeyId: 1,
          recipientKeyId: 1,
          dhY: this.createTestBuffer(192),
          ctr: this.createTestBuffer(8),
          encryptedMessage: this.createTestBuffer(50),
          mac: this.createTestBuffer(20),
          oldMacKeys: this.createTestBuffer(0)
        };
        
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  }
};

// Global test matchers
expect.extend({
  /**
   * Check if a value is a valid instance tag
   */
  toBeValidInstanceTag(received) {
    const pass = typeof received === 'number' && 
                 received >= global.TEST_CONFIG.MIN_INSTANCE_TAG &&
                 received <= 0xFFFFFFFF;
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid instance tag`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid instance tag (>= 0x${global.TEST_CONFIG.MIN_INSTANCE_TAG.toString(16)})`,
        pass: false
      };
    }
  },
  
  /**
   * Check if a buffer has the expected length
   */
  toHaveByteLength(received, expected) {
    const pass = Buffer.isBuffer(received) && received.length === expected;
    
    if (pass) {
      return {
        message: () => `expected buffer not to have length ${expected}`,
        pass: true
      };
    } else {
      return {
        message: () => `expected buffer to have length ${expected}, but got ${Buffer.isBuffer(received) ? received.length : 'not a buffer'}`,
        pass: false
      };
    }
  },
  
  /**
   * Check if a string is a valid OTR message
   */
  toBeValidOTRMessage(received) {
    const pass = typeof received === 'string' &&
                 received.startsWith('?OTR:') &&
                 received.endsWith('.') &&
                 received.length > 6;
    
    if (pass) {
      return {
        message: () => `expected "${received}" not to be a valid OTR message`,
        pass: true
      };
    } else {
      return {
        message: () => `expected "${received}" to be a valid OTR message (format: ?OTR:base64data.)`,
        pass: false
      };
    }
  },
  
  /**
   * Check if a value is a valid hex string
   */
  toBeValidHexString(received) {
    const pass = typeof received === 'string' &&
                 /^(0x)?[0-9a-fA-F]+$/.test(received);
    
    if (pass) {
      return {
        message: () => `expected "${received}" not to be a valid hex string`,
        pass: true
      };
    } else {
      return {
        message: () => `expected "${received}" to be a valid hex string`,
        pass: false
      };
    }
  }
});

// Setup test data directories
function setupTestDirectories() {
  const dirs = [
    global.TEST_CONFIG.TEST_DATA_DIR,
    global.TEST_CONFIG.FIXTURES_DIR
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      global.TestUtils.log(`Created test directory: ${dir}`);
    }
  });
}

// Check libOTR availability
function checkLibOTRAvailability() {
  const libOTRExists = fs.existsSync(global.TEST_CONFIG.LIBOTR_ROOT);
  
  if (!libOTRExists) {
    global.TestUtils.log('Warning: libOTR reference implementation not found');
    global.TestUtils.log('Run: git submodule update --init --recursive');
    global.TEST_CONFIG.LIBOTR_AVAILABLE = false;
  } else {
    global.TEST_CONFIG.LIBOTR_AVAILABLE = true;
    global.TestUtils.log('libOTR reference implementation found');
  }
}

// Global setup
beforeAll(() => {
  global.TestUtils.log('Setting up libOTR reference tests...');
  
  setupTestDirectories();
  checkLibOTRAvailability();
  
  global.TestUtils.log('Reference test setup complete');
});

// Global teardown
afterAll(() => {
  global.TestUtils.log('Cleaning up reference tests...');
  
  // Clean up any global resources
  // (Individual tests should clean up their own resources)
  
  global.TestUtils.log('Reference test cleanup complete');
});

// Per-test setup
beforeEach(() => {
  // Reset any global state if needed
  global.TEST_STATE = {
    startTime: Date.now(),
    testName: expect.getState().currentTestName || 'unknown'
  };
  
  global.TestUtils.log(`Starting test: ${global.TEST_STATE.testName}`);
});

// Per-test teardown
afterEach(() => {
  const duration = Date.now() - global.TEST_STATE.startTime;
  global.TestUtils.log(`Completed test: ${global.TEST_STATE.testName} (${duration}ms)`);
  
  // Clean up test state
  delete global.TEST_STATE;
});

// Error handling for unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process in tests, just log the error
});

// Error handling for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Don't exit the process in tests, just log the error
});

global.TestUtils.log('Reference test setup file loaded');
