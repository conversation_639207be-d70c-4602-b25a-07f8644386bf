/**
 * WebOTR Service Worker
 * 
 * Background service worker for WebOTR extension:
 * - Extension lifecycle management
 * - Cross-tab communication coordination
 * - Secure storage management
 * - Performance monitoring and optimization
 */

import { ExtensionFramework } from '../core/ExtensionFramework.js';

class WebOTRServiceWorker {
  constructor() {
    this.framework = new ExtensionFramework();
    this.state = {
      initialized: false,
      activeTabs: new Map(),
      startTime: Date.now()
    };
    
    this.metrics = {
      tabsManaged: 0,
      messagesProcessed: 0,
      storageOperations: 0,
      errors: 0
    };
    
    this.tabStates = new Map();
    this.messageQueue = [];
  }

  /**
   * Initialize service worker
   */
  async initialize() {
    try {
      console.log('WebOTR: Initializing service worker...');
      
      // Initialize extension framework
      await this.framework.initialize();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Set up message handling
      this.setupMessageHandling();
      
      // Set up storage management
      this.setupStorageManagement();
      
      // Set up performance monitoring
      this.setupPerformanceMonitoring();
      
      this.state.initialized = true;
      
      console.log('WebOTR: Service worker initialized successfully');
      
      return { success: true };
      
    } catch (error) {
      console.error('WebOTR: Service worker initialization failed:', error);
      this.metrics.errors++;
      throw error;
    }
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Extension installation/update
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstallation(details);
    });
    
    // Extension startup
    chrome.runtime.onStartup.addListener(() => {
      this.handleStartup();
    });
    
    // Tab events
    chrome.tabs.onCreated.addListener((tab) => {
      this.handleTabCreated(tab);
    });
    
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdated(tabId, changeInfo, tab);
    });
    
    chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
      this.handleTabRemoved(tabId, removeInfo);
    });
    
    // Action button click
    chrome.action.onClicked.addListener((tab) => {
      this.handleActionClick(tab);
    });
  }

  /**
   * Setup message handling
   */
  setupMessageHandling() {
    // Register framework message handlers
    this.framework.registerMessageHandler('tab-status-update', async (data, sender) => {
      return this.handleTabStatusUpdate(data, sender);
    });
    
    this.framework.registerMessageHandler('get-global-status', async () => {
      return this.getGlobalStatus();
    });
    
    this.framework.registerMessageHandler('coordinate-tabs', async (data) => {
      return this.coordinateTabs(data);
    });
    
    this.framework.registerMessageHandler('storage-request', async (data) => {
      return this.handleStorageRequest(data);
    });
  }

  /**
   * Setup storage management
   */
  setupStorageManagement() {
    // Initialize secure storage
    this.secureStorage = {
      get: async (keys) => {
        this.metrics.storageOperations++;
        return await this.framework.api.storage.get(keys);
      },
      
      set: async (items) => {
        this.metrics.storageOperations++;
        return await this.framework.api.storage.set(items);
      },
      
      remove: async (keys) => {
        this.metrics.storageOperations++;
        return await this.framework.api.storage.remove(keys);
      }
    };
    
    // Set up storage change monitoring
    chrome.storage.onChanged.addListener((changes, namespace) => {
      this.handleStorageChange(changes, namespace);
    });
  }

  /**
   * Setup performance monitoring
   */
  setupPerformanceMonitoring() {
    // Monitor service worker performance
    setInterval(() => {
      this.collectPerformanceMetrics();
    }, 60000); // Every minute
    
    // Monitor tab states
    setInterval(() => {
      this.monitorTabStates();
    }, 30000); // Every 30 seconds
  }

  /**
   * Handle extension installation
   */
  async handleInstallation(details) {
    console.log('WebOTR: Extension installed/updated:', details);
    
    if (details.reason === 'install') {
      // First installation
      await this.performFirstTimeSetup();
    } else if (details.reason === 'update') {
      // Extension update
      await this.performUpdateSetup(details.previousVersion);
    }
    
    // Set extension badge
    await this.updateExtensionBadge();
  }

  /**
   * Handle extension startup
   */
  async handleStartup() {
    console.log('WebOTR: Extension startup');
    
    // Restore previous state if needed
    await this.restoreState();
    
    // Update badge
    await this.updateExtensionBadge();
  }

  /**
   * Handle tab created
   */
  handleTabCreated(tab) {
    if (this.isSupportedPlatform(tab.url)) {
      this.state.activeTabs.set(tab.id, {
        id: tab.id,
        url: tab.url,
        platform: this.detectPlatform(tab.url),
        status: 'created',
        timestamp: Date.now()
      });
      
      this.metrics.tabsManaged++;
      console.log(`WebOTR: Managing new tab ${tab.id} for ${this.detectPlatform(tab.url)}`);
    }
  }

  /**
   * Handle tab updated
   */
  handleTabUpdated(tabId, changeInfo, tab) {
    if (this.state.activeTabs.has(tabId)) {
      const tabState = this.state.activeTabs.get(tabId);
      
      if (changeInfo.status === 'complete') {
        tabState.status = 'ready';
        tabState.lastUpdate = Date.now();
        
        // Inject content script if needed
        this.injectContentScript(tabId, tab);
      }
      
      if (changeInfo.url) {
        tabState.url = changeInfo.url;
        tabState.platform = this.detectPlatform(changeInfo.url);
      }
    } else if (this.isSupportedPlatform(tab.url)) {
      // New supported platform detected
      this.handleTabCreated(tab);
    }
  }

  /**
   * Handle tab removed
   */
  handleTabRemoved(tabId, removeInfo) {
    if (this.state.activeTabs.has(tabId)) {
      console.log(`WebOTR: Tab ${tabId} removed`);
      this.state.activeTabs.delete(tabId);
      this.tabStates.delete(tabId);
    }
  }

  /**
   * Handle action button click
   */
  async handleActionClick(tab) {
    console.log('WebOTR: Action button clicked for tab:', tab.id);
    
    // Open popup or perform action based on tab state
    if (this.state.activeTabs.has(tab.id)) {
      // Tab is managed, popup will handle interaction
      return;
    } else if (this.isSupportedPlatform(tab.url)) {
      // Supported platform but not yet managed
      await this.injectContentScript(tab.id, tab);
    } else {
      // Unsupported platform, show information
      await this.showUnsupportedPlatformNotification();
    }
  }

  /**
   * Handle tab status update from content script
   */
  async handleTabStatusUpdate(data, sender) {
    const tabId = sender.tab?.id;
    
    if (!tabId) {
      return { error: 'No tab ID available' };
    }
    
    // Update tab state
    this.tabStates.set(tabId, {
      ...this.tabStates.get(tabId),
      ...data,
      lastUpdate: Date.now()
    });
    
    // Update extension badge
    await this.updateExtensionBadge();
    
    this.metrics.messagesProcessed++;
    
    return { success: true };
  }

  /**
   * Get global extension status
   */
  getGlobalStatus() {
    const activeTabs = Array.from(this.state.activeTabs.values());
    const tabStates = Array.from(this.tabStates.values());
    
    return {
      initialized: this.state.initialized,
      uptime: Date.now() - this.state.startTime,
      activeTabs: activeTabs.length,
      platforms: [...new Set(activeTabs.map(tab => tab.platform))],
      tabStates,
      metrics: this.metrics,
      framework: this.framework.getStatus()
    };
  }

  /**
   * Coordinate between tabs
   */
  async coordinateTabs(data) {
    const { action, targetTabs, payload } = data;
    
    const results = [];
    
    for (const tabId of targetTabs || Array.from(this.state.activeTabs.keys())) {
      try {
        const result = await this.framework.api.tabs.sendMessage(tabId, {
          type: action,
          data: payload
        });
        
        results.push({ tabId, success: true, result });
      } catch (error) {
        results.push({ tabId, success: false, error: error.message });
      }
    }
    
    return { results };
  }

  /**
   * Handle storage requests
   */
  async handleStorageRequest(data) {
    const { operation, key, value } = data;
    
    try {
      switch (operation) {
        case 'get':
          return await this.secureStorage.get(key);
        
        case 'set':
          return await this.secureStorage.set({ [key]: value });
        
        case 'remove':
          return await this.secureStorage.remove(key);
        
        default:
          throw new Error(`Unknown storage operation: ${operation}`);
      }
    } catch (error) {
      this.metrics.errors++;
      throw error;
    }
  }

  /**
   * Handle storage changes
   */
  handleStorageChange(changes, namespace) {
    console.log('WebOTR: Storage changed:', changes, namespace);
    
    // Notify relevant tabs about storage changes
    Object.keys(changes).forEach(key => {
      if (key.startsWith('webottr-')) {
        this.notifyTabsOfStorageChange(key, changes[key]);
      }
    });
  }

  /**
   * Notify tabs of storage changes
   */
  async notifyTabsOfStorageChange(key, change) {
    const message = {
      type: 'storage-changed',
      data: { key, change }
    };
    
    for (const tabId of this.state.activeTabs.keys()) {
      try {
        await this.framework.api.tabs.sendMessage(tabId, message);
      } catch (error) {
        // Tab might not be ready or content script not injected
        console.warn(`Failed to notify tab ${tabId} of storage change:`, error);
      }
    }
  }

  /**
   * Inject content script into tab
   */
  async injectContentScript(tabId, tab) {
    try {
      // Check if content script is already injected
      const response = await this.framework.api.tabs.sendMessage(tabId, {
        type: 'ping'
      });
      
      if (response?.pong) {
        console.log(`WebOTR: Content script already active in tab ${tabId}`);
        return;
      }
    } catch (error) {
      // Content script not injected, proceed with injection
    }
    
    try {
      await this.framework.api.tabs.executeScript(tabId, {
        files: [
          'content/platform-detector.js',
          'content/message-interceptor.js',
          'content/ui-injector.js',
          'content/content-script.js'
        ]
      });
      
      console.log(`WebOTR: Content script injected into tab ${tabId}`);
      
    } catch (error) {
      console.error(`Failed to inject content script into tab ${tabId}:`, error);
      this.metrics.errors++;
    }
  }

  /**
   * Detect platform from URL
   */
  detectPlatform(url) {
    if (!url) return 'unknown';
    
    const hostname = new URL(url).hostname;
    
    if (hostname.includes('discord.com')) return 'discord';
    if (hostname.includes('slack.com')) return 'slack';
    if (hostname.includes('teams.microsoft.com')) return 'teams';
    if (hostname.includes('web.whatsapp.com')) return 'whatsapp';
    if (hostname.includes('web.telegram.org')) return 'telegram';
    if (hostname.includes('app.element.io')) return 'element';
    
    return 'unknown';
  }

  /**
   * Check if platform is supported
   */
  isSupportedPlatform(url) {
    return this.detectPlatform(url) !== 'unknown';
  }

  /**
   * Update extension badge
   */
  async updateExtensionBadge() {
    const activeTabsCount = this.state.activeTabs.size;
    const readyTabsCount = Array.from(this.tabStates.values())
      .filter(state => state.initialized).length;
    
    if (activeTabsCount > 0) {
      await this.framework.api.action.setBadgeText({
        text: readyTabsCount.toString()
      });
      
      await this.framework.api.action.setBadgeBackgroundColor({
        color: readyTabsCount > 0 ? '#00FF00' : '#FF9900'
      });
    } else {
      await this.framework.api.action.setBadgeText({ text: '' });
    }
  }

  /**
   * Show unsupported platform notification
   */
  async showUnsupportedPlatformNotification() {
    await this.framework.api.notifications.create('unsupported-platform', {
      type: 'basic',
      iconUrl: 'icons/webottr-48.png',
      title: 'WebOTR - Unsupported Platform',
      message: 'This platform is not yet supported by WebOTR. Supported platforms: Discord, Slack, Teams, WhatsApp Web, Telegram, Element.'
    });
  }

  /**
   * Perform first time setup
   */
  async performFirstTimeSetup() {
    console.log('WebOTR: Performing first time setup...');
    
    // Set default settings
    await this.secureStorage.set({
      'webottr-settings': {
        encryptionEnabled: true,
        autoKeyRotation: true,
        notificationsEnabled: true,
        version: '1.0.0',
        installedAt: Date.now()
      }
    });
    
    // Show welcome notification
    await this.framework.api.notifications.create('welcome', {
      type: 'basic',
      iconUrl: 'icons/webottr-48.png',
      title: 'Welcome to WebOTR!',
      message: 'WebOTR is now installed and ready to secure your messages. Visit a supported chat platform to get started.'
    });
  }

  /**
   * Perform update setup
   */
  async performUpdateSetup(previousVersion) {
    console.log(`WebOTR: Updating from version ${previousVersion}`);
    
    // Update settings if needed
    const settings = await this.secureStorage.get('webottr-settings');
    if (settings['webottr-settings']) {
      settings['webottr-settings'].version = '1.0.0';
      settings['webottr-settings'].updatedAt = Date.now();
      await this.secureStorage.set(settings);
    }
  }

  /**
   * Restore previous state
   */
  async restoreState() {
    // Restore any persistent state if needed
    console.log('WebOTR: Restoring previous state...');
  }

  /**
   * Collect performance metrics
   */
  collectPerformanceMetrics() {
    // Collect and store performance metrics
    const metrics = {
      ...this.metrics,
      uptime: Date.now() - this.state.startTime,
      activeTabs: this.state.activeTabs.size,
      timestamp: Date.now()
    };
    
    // Store metrics for analysis
    this.secureStorage.set({
      'webottr-metrics': metrics
    });
  }

  /**
   * Monitor tab states
   */
  async monitorTabStates() {
    // Check health of active tabs
    for (const [tabId, tabInfo] of this.state.activeTabs) {
      try {
        const response = await this.framework.api.tabs.sendMessage(tabId, {
          type: 'get-status'
        });
        
        if (response?.initialized) {
          // Tab is healthy
          continue;
        }
      } catch (error) {
        // Tab might be unresponsive
        console.warn(`Tab ${tabId} appears unresponsive:`, error);
      }
    }
  }

  /**
   * Get service worker status
   */
  getStatus() {
    return {
      initialized: this.state.initialized,
      uptime: Date.now() - this.state.startTime,
      activeTabs: this.state.activeTabs.size,
      metrics: this.metrics,
      framework: this.framework.getStatus()
    };
  }

  /**
   * Shutdown service worker
   */
  async shutdown() {
    console.log('WebOTR: Shutting down service worker...');
    
    // Cleanup framework
    await this.framework.shutdown();

    // Clear state
    this.state.activeTabs.clear();
    this.tabStates.clear();

    this.state.initialized = false;
  }

  /**
   * Initialize enhanced platform integration
   */
  async initializeEnhancedPlatformIntegration() {
    try {
      console.log('WebOTR: Initializing enhanced platform integration in service worker...');

      // Register enhanced message handlers
      this.framework.registerMessageHandler('enhanced-platform-info', async (data, sender) => {
        return this.handleEnhancedPlatformInfo(data, sender);
      });

      this.framework.registerMessageHandler('enhanced-encryption-toggle', async (data, sender) => {
        return this.handleEnhancedEncryptionToggle(data, sender);
      });

      this.framework.registerMessageHandler('enhanced-message-decrypt', async (data, sender) => {
        return this.handleEnhancedMessageDecrypt(data, sender);
      });

      console.log('WebOTR: Enhanced platform integration initialized in service worker');
      return true;
    } catch (error) {
      console.error('WebOTR: Enhanced platform integration initialization failed:', error);
      return false;
    }
  }

  /**
   * Handle enhanced encryption toggle
   */
  async handleEnhancedEncryptionToggle(data, sender) {
    const tabId = sender.tab?.id;

    try {
      // Update tab encryption state
      const tabState = this.tabStates.get(tabId) || {};
      tabState.encryptionEnabled = data.enabled;
      tabState.lastEncryptionToggle = Date.now();
      this.tabStates.set(tabId, tabState);

      // Update extension badge
      await this.updateExtensionBadge();

      return {
        success: true,
        encryptionEnabled: data.enabled,
        tabId: tabId
      };
    } catch (error) {
      console.error('WebOTR: Enhanced encryption toggle failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Handle enhanced platform info requests
   */
  async handleEnhancedPlatformInfo(data, sender) {
    const tabId = sender.tab?.id;

    if (!tabId) {
      return { error: 'No tab ID available' };
    }

    // Store enhanced platform info
    const tabState = this.tabStates.get(tabId) || {};
    tabState.enhancedPlatformInfo = data;
    this.tabStates.set(tabId, tabState);

    return { success: true, stored: true };
  }

  /**
   * Handle enhanced message decryption
   */
  async handleEnhancedMessageDecrypt(data, sender) {
    const tabId = sender.tab?.id;

    try {
      // Update metrics
      this.metrics.messagesProcessed++;

      // Store decryption event
      const tabState = this.tabStates.get(tabId) || {};
      tabState.lastDecryption = Date.now();
      tabState.messagesDecrypted = (tabState.messagesDecrypted || 0) + 1;
      this.tabStates.set(tabId, tabState);

      return {
        success: true,
        messageId: data.messageId,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('WebOTR: Enhanced message decryption failed:', error);
      this.metrics.errors++;
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Initialize and start enhanced service worker
const webottrServiceWorker = new WebOTRServiceWorker();

// Initialize with enhanced features
webottrServiceWorker.initialize()
  .then(() => webottrServiceWorker.initializeEnhancedPlatformIntegration())
  .then(() => {
    console.log('WebOTR: Enhanced service worker fully initialized');
  })
  .catch(error => {
    console.error('WebOTR: Enhanced service worker initialization failed:', error);
  });

// Make available for debugging
self.webottrServiceWorker = webottrServiceWorker;
