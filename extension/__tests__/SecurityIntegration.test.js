/**
 * Security Integration Tests
 * 
 * Comprehensive test suite for SecurityIntegration:
 * - Forward Secrecy integration
 * - User Experience coordination
 * - Real-time security operations
 * - Cross-component communication
 */

import { SecurityIntegration } from '../core/SecurityIntegration.js';

// Mock components
const mockForwardSecrecy = {
  initialize: jest.fn().mockResolvedValue({ success: true }),
  rotateKeysManually: jest.fn().mockResolvedValue({
    success: true,
    keyGeneration: 1,
    rotationTime: 50
  }),
  emergencyRotation: jest.fn().mockResolvedValue({
    success: true,
    keyGeneration: 2,
    trigger: 'EMERGENCY'
  }),
  getStatus: jest.fn().mockReturnValue({
    initialized: true,
    currentKeyGeneration: 1,
    autoRotationEnabled: true
  }),
  on: jest.fn(),
  off: jest.fn(),
  shutdown: jest.fn()
};

const mockUserExperience = {
  initialize: jest.fn().mockResolvedValue({ success: true }),
  startSecureMessaging: jest.fn().mockResolvedValue({ success: true }),
  endSecureMessaging: jest.fn().mockResolvedValue({ success: true }),
  rotateSecurityKeys: jest.fn().mockResolvedValue({ success: true }),
  showNotification: jest.fn(),
  updateSecurityStatus: jest.fn(),
  getStatus: jest.fn().mockReturnValue({
    initialized: true,
    secureMessagingActive: false
  }),
  on: jest.fn(),
  off: jest.fn(),
  shutdown: jest.fn()
};

const mockSecureStorage = {
  initialize: jest.fn().mockResolvedValue({ success: true }),
  store: jest.fn().mockResolvedValue({ success: true }),
  retrieve: jest.fn().mockResolvedValue({ success: true, data: 'stored-data' }),
  remove: jest.fn().mockResolvedValue({ success: true }),
  encrypt: jest.fn().mockResolvedValue({ success: true, encrypted: 'encrypted-data' }),
  decrypt: jest.fn().mockResolvedValue({ success: true, decrypted: 'decrypted-data' }),
  getStatus: jest.fn().mockReturnValue({
    initialized: true,
    storageAvailable: true
  }),
  shutdown: jest.fn()
};

describe('SecurityIntegration', () => {
  let securityIntegration;
  
  beforeEach(async () => {
    securityIntegration = new SecurityIntegration({
      forwardSecrecy: mockForwardSecrecy,
      userExperience: mockUserExperience,
      secureStorage: mockSecureStorage,
      autoStart: false,
      debugMode: true
    });
  });
  
  afterEach(async () => {
    if (securityIntegration) {
      await securityIntegration.shutdown();
    }
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should initialize all components', async () => {
      const result = await securityIntegration.initialize();
      
      expect(result.success).toBe(true);
      expect(mockForwardSecrecy.initialize).toHaveBeenCalled();
      expect(mockUserExperience.initialize).toHaveBeenCalled();
      expect(mockSecureStorage.initialize).toHaveBeenCalled();
      expect(securityIntegration.state.initialized).toBe(true);
    });

    test('should handle component initialization failure', async () => {
      mockForwardSecrecy.initialize.mockRejectedValueOnce(new Error('FS init failed'));
      
      await expect(securityIntegration.initialize()).rejects.toThrow('FS init failed');
      expect(securityIntegration.state.initialized).toBe(false);
    });

    test('should setup event listeners between components', async () => {
      await securityIntegration.initialize();
      
      expect(mockForwardSecrecy.on).toHaveBeenCalledWith('keyRotated', expect.any(Function));
      expect(mockUserExperience.on).toHaveBeenCalledWith('securityOperation', expect.any(Function));
    });

    test('should validate component compatibility', async () => {
      const result = await securityIntegration.validateComponents();
      
      expect(result.compatible).toBe(true);
      expect(result.forwardSecrecyVersion).toBeDefined();
      expect(result.userExperienceVersion).toBeDefined();
    });
  });

  describe('Security Operations Coordination', () => {
    beforeEach(async () => {
      await securityIntegration.initialize();
    });

    test('should coordinate OTR session start', async () => {
      const result = await securityIntegration.startOTR();
      
      expect(result.success).toBe(true);
      expect(mockUserExperience.startSecureMessaging).toHaveBeenCalled();
      expect(securityIntegration.state.otrActive).toBe(true);
    });

    test('should coordinate OTR session end', async () => {
      await securityIntegration.startOTR();
      
      const result = await securityIntegration.endOTR();
      
      expect(result.success).toBe(true);
      expect(mockUserExperience.endSecureMessaging).toHaveBeenCalled();
      expect(securityIntegration.state.otrActive).toBe(false);
    });

    test('should coordinate key rotation', async () => {
      await securityIntegration.startOTR();
      
      const result = await securityIntegration.rotateKeys();
      
      expect(result.success).toBe(true);
      expect(result.keyGeneration).toBe(1);
      expect(mockForwardSecrecy.rotateKeysManually).toHaveBeenCalled();
      expect(mockUserExperience.rotateSecurityKeys).toHaveBeenCalled();
    });

    test('should handle emergency rotation', async () => {
      await securityIntegration.startOTR();
      
      const result = await securityIntegration.emergencyRotation('SECURITY_BREACH');
      
      expect(result.success).toBe(true);
      expect(result.trigger).toBe('EMERGENCY');
      expect(mockForwardSecrecy.emergencyRotation).toHaveBeenCalledWith('SECURITY_BREACH');
    });

    test('should prevent operations when not initialized', async () => {
      const uninitializedIntegration = new SecurityIntegration({
        forwardSecrecy: mockForwardSecrecy,
        userExperience: mockUserExperience,
        secureStorage: mockSecureStorage
      });
      
      await expect(uninitializedIntegration.startOTR()).rejects.toThrow('not initialized');
    });
  });

  describe('Real-time Event Coordination', () => {
    beforeEach(async () => {
      await securityIntegration.initialize();
    });

    test('should handle Forward Secrecy key rotation events', async () => {
      // Simulate key rotation event from Forward Secrecy
      const keyRotationHandler = mockForwardSecrecy.on.mock.calls
        .find(call => call[0] === 'keyRotated')[1];
      
      const rotationEvent = {
        keyGeneration: 2,
        trigger: 'TIME_BASED',
        timestamp: Date.now()
      };
      
      keyRotationHandler(rotationEvent);
      
      expect(mockUserExperience.updateSecurityStatus).toHaveBeenCalled();
      expect(mockUserExperience.showNotification).toHaveBeenCalledWith(
        expect.stringContaining('Keys rotated'),
        'success'
      );
    });

    test('should handle User Experience security operations', async () => {
      // Simulate security operation from User Experience
      const securityOpHandler = mockUserExperience.on.mock.calls
        .find(call => call[0] === 'securityOperation')[1];
      
      const operationEvent = {
        operation: 'START_OTR',
        timestamp: Date.now()
      };
      
      await securityOpHandler(operationEvent);
      
      expect(securityIntegration.state.otrActive).toBe(true);
    });

    test('should coordinate security status updates', async () => {
      await securityIntegration.updateSecurityStatus();
      
      expect(mockUserExperience.updateSecurityStatus).toHaveBeenCalled();
    });

    test('should handle security alerts', async () => {
      const alert = {
        level: 'HIGH',
        message: 'Potential security breach detected',
        timestamp: Date.now()
      };
      
      await securityIntegration.handleSecurityAlert(alert);
      
      expect(mockUserExperience.showNotification).toHaveBeenCalledWith(
        alert.message,
        'error'
      );
    });
  });

  describe('Secure Data Management', () => {
    beforeEach(async () => {
      await securityIntegration.initialize();
    });

    test('should store encrypted session data', async () => {
      const sessionData = {
        sessionId: 'test-session-123',
        keyGeneration: 1,
        timestamp: Date.now()
      };
      
      const result = await securityIntegration.storeSessionData(sessionData);
      
      expect(result.success).toBe(true);
      expect(mockSecureStorage.store).toHaveBeenCalledWith(
        'session-test-session-123',
        sessionData
      );
    });

    test('should retrieve and decrypt session data', async () => {
      const sessionId = 'test-session-123';
      
      const result = await securityIntegration.getSessionData(sessionId);
      
      expect(result.success).toBe(true);
      expect(result.data).toBe('stored-data');
      expect(mockSecureStorage.retrieve).toHaveBeenCalledWith(`session-${sessionId}`);
    });

    test('should remove session data securely', async () => {
      const sessionId = 'test-session-123';
      
      const result = await securityIntegration.removeSessionData(sessionId);
      
      expect(result.success).toBe(true);
      expect(mockSecureStorage.remove).toHaveBeenCalledWith(`session-${sessionId}`);
    });

    test('should handle storage encryption/decryption', async () => {
      const sensitiveData = { secret: 'very-secret-data' };
      
      const encryptResult = await securityIntegration.encryptData(sensitiveData);
      expect(encryptResult.success).toBe(true);
      expect(mockSecureStorage.encrypt).toHaveBeenCalledWith(sensitiveData);
      
      const decryptResult = await securityIntegration.decryptData('encrypted-data');
      expect(decryptResult.success).toBe(true);
      expect(mockSecureStorage.decrypt).toHaveBeenCalledWith('encrypted-data');
    });
  });

  describe('Security Monitoring and Metrics', () => {
    beforeEach(async () => {
      await securityIntegration.initialize();
    });

    test('should collect comprehensive security metrics', async () => {
      await securityIntegration.startOTR();
      await securityIntegration.rotateKeys();
      
      const metrics = securityIntegration.getSecurityMetrics();
      
      expect(metrics.otrSessions).toBe(1);
      expect(metrics.keyRotations).toBe(1);
      expect(metrics.securityEvents).toBeGreaterThan(0);
      expect(metrics.uptime).toBeGreaterThan(0);
    });

    test('should track security events', async () => {
      await securityIntegration.startOTR();
      await securityIntegration.rotateKeys();
      await securityIntegration.endOTR();
      
      const events = securityIntegration.getSecurityEvents();
      
      expect(events.some(e => e.type === 'OTR_STARTED')).toBe(true);
      expect(events.some(e => e.type === 'KEY_ROTATED')).toBe(true);
      expect(events.some(e => e.type === 'OTR_ENDED')).toBe(true);
    });

    test('should monitor component health', async () => {
      const health = await securityIntegration.checkComponentHealth();
      
      expect(health.forwardSecrecy.healthy).toBe(true);
      expect(health.userExperience.healthy).toBe(true);
      expect(health.secureStorage.healthy).toBe(true);
      expect(health.overall.healthy).toBe(true);
    });

    test('should detect security anomalies', async () => {
      // Simulate rapid key rotations (potential anomaly)
      for (let i = 0; i < 10; i++) {
        await securityIntegration.rotateKeys();
      }
      
      const anomalies = securityIntegration.detectAnomalies();
      
      expect(anomalies.length).toBeGreaterThan(0);
      expect(anomalies[0].type).toBe('RAPID_KEY_ROTATION');
    });
  });

  describe('Performance Optimization', () => {
    beforeEach(async () => {
      await securityIntegration.initialize();
    });

    test('should measure operation performance', async () => {
      const startTime = performance.now();
      
      await securityIntegration.startOTR();
      
      const metrics = securityIntegration.getPerformanceMetrics();
      
      expect(metrics.otrStartTime).toBeLessThan(100); // Should be fast
      expect(metrics.averageOperationTime).toBeGreaterThan(0);
    });

    test('should optimize component coordination', async () => {
      // Perform multiple operations
      await securityIntegration.startOTR();
      await securityIntegration.rotateKeys();
      await securityIntegration.rotateKeys();
      await securityIntegration.endOTR();
      
      const optimization = securityIntegration.getOptimizationReport();
      
      expect(optimization.coordinationEfficiency).toBeGreaterThan(0.8);
      expect(optimization.suggestions).toBeDefined();
    });

    test('should cache frequently accessed data', async () => {
      // First access
      const status1 = securityIntegration.getStatus();
      
      // Second access (should use cache)
      const status2 = securityIntegration.getStatus();
      
      expect(status1).toEqual(status2);
      expect(securityIntegration.cacheHits).toBeGreaterThan(0);
    });

    test('should batch security events', async () => {
      // Generate multiple events rapidly
      for (let i = 0; i < 5; i++) {
        securityIntegration.logSecurityEvent('TEST_EVENT', { index: i });
      }
      
      // Events should be batched
      expect(securityIntegration.eventBatches.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling and Recovery', () => {
    beforeEach(async () => {
      await securityIntegration.initialize();
    });

    test('should handle Forward Secrecy component failure', async () => {
      mockForwardSecrecy.rotateKeysManually.mockRejectedValueOnce(new Error('FS failure'));
      
      const result = await securityIntegration.rotateKeys();
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('FS failure');
      expect(mockUserExperience.showNotification).toHaveBeenCalledWith(
        expect.stringContaining('failed'),
        'error'
      );
    });

    test('should handle User Experience component failure', async () => {
      mockUserExperience.startSecureMessaging.mockRejectedValueOnce(new Error('UX failure'));
      
      const result = await securityIntegration.startOTR();
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('UX failure');
    });

    test('should recover from component failures', async () => {
      // Simulate failure
      mockForwardSecrecy.rotateKeysManually.mockRejectedValueOnce(new Error('Temporary failure'));
      
      await securityIntegration.rotateKeys();
      
      // Reset mock to succeed
      mockForwardSecrecy.rotateKeysManually.mockResolvedValueOnce({
        success: true,
        keyGeneration: 2
      });
      
      // Should recover on next operation
      const result = await securityIntegration.rotateKeys();
      expect(result.success).toBe(true);
    });

    test('should maintain state consistency during failures', async () => {
      const initialState = { ...securityIntegration.state };
      
      mockUserExperience.startSecureMessaging.mockRejectedValueOnce(new Error('Test failure'));
      
      await securityIntegration.startOTR();
      
      expect(securityIntegration.state.otrActive).toBe(initialState.otrActive);
    });
  });

  describe('Security Compliance and Auditing', () => {
    beforeEach(async () => {
      await securityIntegration.initialize();
    });

    test('should generate compliance report', async () => {
      await securityIntegration.startOTR();
      await securityIntegration.rotateKeys();
      
      const report = await securityIntegration.generateComplianceReport();
      
      expect(report.forwardSecrecyCompliance).toBe(true);
      expect(report.encryptionStandards).toContain('AES-256');
      expect(report.keyRotationCompliance).toBe(true);
      expect(report.auditTrail).toBeDefined();
    });

    test('should maintain audit trail', async () => {
      await securityIntegration.startOTR();
      await securityIntegration.rotateKeys();
      await securityIntegration.endOTR();
      
      const auditTrail = securityIntegration.getAuditTrail();
      
      expect(auditTrail.length).toBeGreaterThan(0);
      expect(auditTrail.every(entry => entry.timestamp)).toBe(true);
      expect(auditTrail.every(entry => entry.action)).toBe(true);
      expect(auditTrail.every(entry => entry.result)).toBe(true);
    });

    test('should verify security integrity', async () => {
      const integrity = await securityIntegration.verifySecurityIntegrity();
      
      expect(integrity.componentsIntact).toBe(true);
      expect(integrity.configurationValid).toBe(true);
      expect(integrity.cryptographicIntegrity).toBe(true);
      expect(integrity.overallScore).toBeGreaterThan(0.9);
    });

    test('should handle security policy enforcement', async () => {
      const policy = {
        maxKeyAge: 3600000, // 1 hour
        mandatoryRotation: true,
        emergencyProcedures: true
      };
      
      securityIntegration.enforceSecurityPolicy(policy);
      
      expect(securityIntegration.securityPolicy).toEqual(policy);
    });
  });

  describe('Integration Testing', () => {
    beforeEach(async () => {
      await securityIntegration.initialize();
    });

    test('should perform complete security workflow', async () => {
      // Start OTR session
      const startResult = await securityIntegration.startOTR();
      expect(startResult.success).toBe(true);
      
      // Rotate keys
      const rotateResult = await securityIntegration.rotateKeys();
      expect(rotateResult.success).toBe(true);
      
      // Store session data
      const storeResult = await securityIntegration.storeSessionData({
        sessionId: 'integration-test',
        data: 'test-data'
      });
      expect(storeResult.success).toBe(true);
      
      // End OTR session
      const endResult = await securityIntegration.endOTR();
      expect(endResult.success).toBe(true);
      
      // Verify all components were coordinated
      expect(mockForwardSecrecy.rotateKeysManually).toHaveBeenCalled();
      expect(mockUserExperience.startSecureMessaging).toHaveBeenCalled();
      expect(mockUserExperience.endSecureMessaging).toHaveBeenCalled();
      expect(mockSecureStorage.store).toHaveBeenCalled();
    });

    test('should handle concurrent operations safely', async () => {
      const operations = [
        securityIntegration.startOTR(),
        securityIntegration.rotateKeys(),
        securityIntegration.storeSessionData({ sessionId: 'test1', data: 'data1' }),
        securityIntegration.storeSessionData({ sessionId: 'test2', data: 'data2' })
      ];
      
      const results = await Promise.allSettled(operations);
      
      // All operations should complete successfully
      expect(results.every(r => r.status === 'fulfilled')).toBe(true);
    });

    test('should maintain security across component restarts', async () => {
      await securityIntegration.startOTR();
      const sessionId = 'restart-test';
      
      await securityIntegration.storeSessionData({
        sessionId,
        data: 'persistent-data'
      });
      
      // Simulate component restart
      await securityIntegration.shutdown();
      
      const newIntegration = new SecurityIntegration({
        forwardSecrecy: mockForwardSecrecy,
        userExperience: mockUserExperience,
        secureStorage: mockSecureStorage
      });
      
      await newIntegration.initialize();
      
      // Should be able to retrieve stored data
      const retrieveResult = await newIntegration.getSessionData(sessionId);
      expect(retrieveResult.success).toBe(true);
      
      await newIntegration.shutdown();
    });
  });
});
