/**
 * Extension Framework Tests
 * 
 * Comprehensive test suite for ExtensionFramework:
 * - Cross-browser compatibility
 * - Message passing and communication
 * - Platform detection and adaptation
 * - Security integration
 */

import { ExtensionFramework } from '../core/ExtensionFramework.js';
import { PlatformDetector } from '../core/PlatformDetector.js';
import { MessageInterceptor } from '../core/MessageInterceptor.js';

// Mock Chrome Extension APIs
global.chrome = {
  runtime: {
    sendMessage: jest.fn(),
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    },
    getManifest: jest.fn(() => ({ version: '1.0.0' })),
    id: 'test-extension-id'
  },
  tabs: {
    query: jest.fn(),
    sendMessage: jest.fn(),
    onUpdated: {
      addListener: jest.fn(),
      removeListener: jest.fn()
    }
  },
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn()
    },
    sync: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn()
    }
  },
  action: {
    setBadgeText: jest.fn(),
    setBadgeBackgroundColor: jest.fn(),
    setIcon: jest.fn()
  }
};

// Mock browser APIs for Firefox compatibility
global.browser = global.chrome;

describe('ExtensionFramework', () => {
  let extensionFramework;
  let mockPlatformDetector;
  let mockMessageInterceptor;
  
  beforeEach(async () => {
    // Create mock platform detector
    mockPlatformDetector = {
      initialize: jest.fn().mockResolvedValue({ success: true }),
      detectPlatform: jest.fn().mockReturnValue({
        platform: 'discord',
        supported: true,
        version: '1.0.0'
      }),
      getPlatformConfig: jest.fn().mockReturnValue({
        messageSelector: '.message-content',
        inputSelector: '.message-input',
        sendButtonSelector: '.send-button'
      }),
      on: jest.fn(),
      off: jest.fn()
    };
    
    // Create mock message interceptor
    mockMessageInterceptor = {
      initialize: jest.fn().mockResolvedValue({ success: true }),
      startInterception: jest.fn().mockResolvedValue({ success: true }),
      stopInterception: jest.fn().mockResolvedValue({ success: true }),
      encryptMessage: jest.fn().mockResolvedValue({
        success: true,
        encryptedMessage: 'encrypted-content'
      }),
      decryptMessage: jest.fn().mockResolvedValue({
        success: true,
        decryptedMessage: 'decrypted-content'
      }),
      on: jest.fn(),
      off: jest.fn(),
      shutdown: jest.fn()
    };
    
    // Initialize ExtensionFramework
    extensionFramework = new ExtensionFramework({
      platformDetector: mockPlatformDetector,
      messageInterceptor: mockMessageInterceptor,
      autoStart: false,
      debugMode: true
    });
  });
  
  afterEach(async () => {
    if (extensionFramework) {
      await extensionFramework.shutdown();
    }
    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should initialize with default configuration', async () => {
      const result = await extensionFramework.initialize();
      
      expect(result.success).toBe(true);
      expect(extensionFramework.state.initialized).toBe(true);
      expect(mockPlatformDetector.initialize).toHaveBeenCalled();
      expect(mockMessageInterceptor.initialize).toHaveBeenCalled();
    });

    test('should detect browser environment', async () => {
      await extensionFramework.initialize();
      
      const browserInfo = extensionFramework.getBrowserInfo();
      
      expect(browserInfo.isChrome).toBe(true);
      expect(browserInfo.isFirefox).toBe(false);
      expect(browserInfo.manifestVersion).toBe(3);
    });

    test('should handle Firefox environment', async () => {
      // Mock Firefox environment
      delete global.chrome;
      global.browser = {
        runtime: {
          sendMessage: jest.fn(),
          onMessage: {
            addListener: jest.fn(),
            removeListener: jest.fn()
          },
          getManifest: jest.fn(() => ({ version: '1.0.0', manifest_version: 2 }))
        }
      };
      
      const firefoxFramework = new ExtensionFramework({
        platformDetector: mockPlatformDetector,
        messageInterceptor: mockMessageInterceptor
      });
      
      await firefoxFramework.initialize();
      
      const browserInfo = firefoxFramework.getBrowserInfo();
      expect(browserInfo.isFirefox).toBe(true);
      expect(browserInfo.manifestVersion).toBe(2);
      
      await firefoxFramework.shutdown();
      
      // Restore Chrome environment
      global.chrome = global.browser;
    });

    test('should handle initialization failure gracefully', async () => {
      mockPlatformDetector.initialize.mockRejectedValueOnce(new Error('Platform init failed'));
      
      await expect(extensionFramework.initialize()).rejects.toThrow('Platform init failed');
      expect(extensionFramework.state.initialized).toBe(false);
    });

    test('should validate required permissions', async () => {
      await extensionFramework.initialize();
      
      const permissions = extensionFramework.getRequiredPermissions();
      
      expect(permissions).toContain('activeTab');
      expect(permissions).toContain('storage');
      expect(permissions).toContain('scripting');
    });
  });

  describe('Platform Detection and Adaptation', () => {
    beforeEach(async () => {
      await extensionFramework.initialize();
    });

    test('should detect supported platforms', async () => {
      const platformInfo = await extensionFramework.detectCurrentPlatform();
      
      expect(platformInfo.platform).toBe('discord');
      expect(platformInfo.supported).toBe(true);
      expect(mockPlatformDetector.detectPlatform).toHaveBeenCalled();
    });

    test('should handle unsupported platforms', async () => {
      mockPlatformDetector.detectPlatform.mockReturnValueOnce({
        platform: 'unknown',
        supported: false,
        reason: 'Platform not supported'
      });
      
      const platformInfo = await extensionFramework.detectCurrentPlatform();
      
      expect(platformInfo.platform).toBe('unknown');
      expect(platformInfo.supported).toBe(false);
      expect(platformInfo.reason).toBe('Platform not supported');
    });

    test('should adapt to different platform configurations', async () => {
      const platforms = ['discord', 'slack', 'teams', 'whatsapp', 'telegram'];
      
      for (const platform of platforms) {
        mockPlatformDetector.detectPlatform.mockReturnValueOnce({
          platform,
          supported: true,
          version: '1.0.0'
        });
        
        mockPlatformDetector.getPlatformConfig.mockReturnValueOnce({
          messageSelector: `.${platform}-message`,
          inputSelector: `.${platform}-input`,
          sendButtonSelector: `.${platform}-send`
        });
        
        const platformInfo = await extensionFramework.detectCurrentPlatform();
        const config = extensionFramework.getPlatformConfig();
        
        expect(platformInfo.platform).toBe(platform);
        expect(config.messageSelector).toBe(`.${platform}-message`);
      }
    });

    test('should cache platform detection results', async () => {
      await extensionFramework.detectCurrentPlatform();
      await extensionFramework.detectCurrentPlatform();
      
      // Should only call detector once due to caching
      expect(mockPlatformDetector.detectPlatform).toHaveBeenCalledTimes(1);
    });
  });

  describe('Message Interception and Processing', () => {
    beforeEach(async () => {
      await extensionFramework.initialize();
      await extensionFramework.detectCurrentPlatform();
    });

    test('should start message interception', async () => {
      const result = await extensionFramework.startMessageInterception();
      
      expect(result.success).toBe(true);
      expect(mockMessageInterceptor.startInterception).toHaveBeenCalled();
      expect(extensionFramework.state.intercepting).toBe(true);
    });

    test('should stop message interception', async () => {
      await extensionFramework.startMessageInterception();
      
      const result = await extensionFramework.stopMessageInterception();
      
      expect(result.success).toBe(true);
      expect(mockMessageInterceptor.stopInterception).toHaveBeenCalled();
      expect(extensionFramework.state.intercepting).toBe(false);
    });

    test('should encrypt outgoing messages', async () => {
      await extensionFramework.startMessageInterception();
      
      const result = await extensionFramework.encryptMessage('Hello, world!');
      
      expect(result.success).toBe(true);
      expect(result.encryptedMessage).toBe('encrypted-content');
      expect(mockMessageInterceptor.encryptMessage).toHaveBeenCalledWith('Hello, world!');
    });

    test('should decrypt incoming messages', async () => {
      await extensionFramework.startMessageInterception();
      
      const result = await extensionFramework.decryptMessage('encrypted-content');
      
      expect(result.success).toBe(true);
      expect(result.decryptedMessage).toBe('decrypted-content');
      expect(mockMessageInterceptor.decryptMessage).toHaveBeenCalledWith('encrypted-content');
    });

    test('should handle encryption errors gracefully', async () => {
      mockMessageInterceptor.encryptMessage.mockRejectedValueOnce(new Error('Encryption failed'));
      
      await extensionFramework.startMessageInterception();
      
      const result = await extensionFramework.encryptMessage('Hello, world!');
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Encryption failed');
    });

    test('should handle decryption errors gracefully', async () => {
      mockMessageInterceptor.decryptMessage.mockRejectedValueOnce(new Error('Decryption failed'));
      
      await extensionFramework.startMessageInterception();
      
      const result = await extensionFramework.decryptMessage('invalid-content');
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Decryption failed');
    });
  });

  describe('Cross-Browser Communication', () => {
    beforeEach(async () => {
      await extensionFramework.initialize();
    });

    test('should send messages to background script', async () => {
      chrome.runtime.sendMessage.mockResolvedValueOnce({ success: true });
      
      const result = await extensionFramework.sendToBackground({
        type: 'GET_STATUS'
      });
      
      expect(result.success).toBe(true);
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'GET_STATUS'
      });
    });

    test('should send messages to content scripts', async () => {
      chrome.tabs.query.mockResolvedValueOnce([{ id: 123 }]);
      chrome.tabs.sendMessage.mockResolvedValueOnce({ success: true });
      
      const result = await extensionFramework.sendToContentScript({
        type: 'START_INTERCEPTION'
      });
      
      expect(result.success).toBe(true);
      expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(123, {
        type: 'START_INTERCEPTION'
      });
    });

    test('should handle message passing errors', async () => {
      chrome.runtime.sendMessage.mockRejectedValueOnce(new Error('Connection error'));
      
      const result = await extensionFramework.sendToBackground({
        type: 'GET_STATUS'
      });
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Connection error');
    });

    test('should register message listeners', () => {
      const handler = jest.fn();
      
      extensionFramework.onMessage('TEST_MESSAGE', handler);
      
      expect(chrome.runtime.onMessage.addListener).toHaveBeenCalled();
    });

    test('should unregister message listeners', () => {
      const handler = jest.fn();
      
      extensionFramework.onMessage('TEST_MESSAGE', handler);
      extensionFramework.offMessage('TEST_MESSAGE', handler);
      
      expect(chrome.runtime.onMessage.removeListener).toHaveBeenCalled();
    });
  });

  describe('Storage Management', () => {
    beforeEach(async () => {
      await extensionFramework.initialize();
    });

    test('should store data locally', async () => {
      chrome.storage.local.set.mockResolvedValueOnce();
      
      await extensionFramework.setStorageData('test-key', { value: 'test-data' });
      
      expect(chrome.storage.local.set).toHaveBeenCalledWith({
        'test-key': { value: 'test-data' }
      });
    });

    test('should retrieve data from local storage', async () => {
      chrome.storage.local.get.mockResolvedValueOnce({
        'test-key': { value: 'test-data' }
      });
      
      const result = await extensionFramework.getStorageData('test-key');
      
      expect(result).toEqual({ value: 'test-data' });
      expect(chrome.storage.local.get).toHaveBeenCalledWith('test-key');
    });

    test('should remove data from storage', async () => {
      chrome.storage.local.remove.mockResolvedValueOnce();
      
      await extensionFramework.removeStorageData('test-key');
      
      expect(chrome.storage.local.remove).toHaveBeenCalledWith('test-key');
    });

    test('should handle storage quota exceeded', async () => {
      chrome.storage.local.set.mockRejectedValueOnce(new Error('QUOTA_EXCEEDED'));
      
      await expect(extensionFramework.setStorageData('large-key', new Array(1000000).fill('data')))
        .rejects.toThrow('QUOTA_EXCEEDED');
    });

    test('should use sync storage when available', async () => {
      chrome.storage.sync.set.mockResolvedValueOnce();
      
      await extensionFramework.setStorageData('sync-key', { value: 'sync-data' }, true);
      
      expect(chrome.storage.sync.set).toHaveBeenCalledWith({
        'sync-key': { value: 'sync-data' }
      });
    });
  });

  describe('Extension UI Integration', () => {
    beforeEach(async () => {
      await extensionFramework.initialize();
    });

    test('should update extension badge', async () => {
      await extensionFramework.updateBadge('ON', '#00FF00');
      
      expect(chrome.action.setBadgeText).toHaveBeenCalledWith({ text: 'ON' });
      expect(chrome.action.setBadgeBackgroundColor).toHaveBeenCalledWith({ color: '#00FF00' });
    });

    test('should update extension icon', async () => {
      await extensionFramework.updateIcon('active');
      
      expect(chrome.action.setIcon).toHaveBeenCalledWith({
        path: {
          16: 'icons/active-16.png',
          32: 'icons/active-32.png',
          48: 'icons/active-48.png',
          128: 'icons/active-128.png'
        }
      });
    });

    test('should handle tab updates', () => {
      const handler = jest.fn();
      
      extensionFramework.onTabUpdated(handler);
      
      expect(chrome.tabs.onUpdated.addListener).toHaveBeenCalledWith(handler);
    });

    test('should get active tab information', async () => {
      chrome.tabs.query.mockResolvedValueOnce([{
        id: 123,
        url: 'https://discord.com/channels/123/456',
        title: 'Discord'
      }]);
      
      const tabInfo = await extensionFramework.getActiveTab();
      
      expect(tabInfo.id).toBe(123);
      expect(tabInfo.url).toBe('https://discord.com/channels/123/456');
      expect(tabInfo.title).toBe('Discord');
    });
  });

  describe('Security and Permissions', () => {
    beforeEach(async () => {
      await extensionFramework.initialize();
    });

    test('should validate content security policy', () => {
      const cspViolation = {
        blockedURI: 'inline',
        violatedDirective: 'script-src'
      };
      
      const isValid = extensionFramework.validateCSP(cspViolation);
      
      expect(isValid).toBe(false);
    });

    test('should sanitize user input', () => {
      const maliciousInput = '<script>alert("xss")</script>Hello';
      const sanitized = extensionFramework.sanitizeInput(maliciousInput);
      
      expect(sanitized).toBe('Hello');
      expect(sanitized).not.toContain('<script>');
    });

    test('should validate message origins', () => {
      const validOrigin = 'https://discord.com';
      const invalidOrigin = 'https://malicious.com';
      
      expect(extensionFramework.validateOrigin(validOrigin)).toBe(true);
      expect(extensionFramework.validateOrigin(invalidOrigin)).toBe(false);
    });

    test('should handle permission requests', async () => {
      // Mock permissions API
      chrome.permissions = {
        request: jest.fn().mockResolvedValueOnce(true),
        contains: jest.fn().mockResolvedValueOnce(false)
      };
      
      const granted = await extensionFramework.requestPermission('tabs');
      
      expect(granted).toBe(true);
      expect(chrome.permissions.request).toHaveBeenCalledWith({ permissions: ['tabs'] });
    });
  });

  describe('Performance and Optimization', () => {
    beforeEach(async () => {
      await extensionFramework.initialize();
    });

    test('should measure operation performance', async () => {
      const startTime = performance.now();
      
      await extensionFramework.measurePerformance('message-encryption', async () => {
        await extensionFramework.encryptMessage('test message');
      });
      
      const metrics = extensionFramework.getPerformanceMetrics();
      
      expect(metrics['message-encryption']).toBeDefined();
      expect(metrics['message-encryption'].averageTime).toBeGreaterThan(0);
    });

    test('should throttle rapid operations', async () => {
      const throttledEncrypt = extensionFramework.throttle(
        extensionFramework.encryptMessage.bind(extensionFramework),
        100
      );
      
      // Rapid calls
      const promises = [
        throttledEncrypt('message1'),
        throttledEncrypt('message2'),
        throttledEncrypt('message3')
      ];
      
      const results = await Promise.allSettled(promises);
      
      // Only first call should succeed immediately
      expect(results[0].status).toBe('fulfilled');
    });

    test('should cleanup resources on shutdown', async () => {
      await extensionFramework.startMessageInterception();
      
      const shutdownResult = await extensionFramework.shutdown();
      
      expect(shutdownResult.success).toBe(true);
      expect(mockMessageInterceptor.shutdown).toHaveBeenCalled();
      expect(extensionFramework.state.initialized).toBe(false);
    });

    test('should monitor memory usage', () => {
      const memoryInfo = extensionFramework.getMemoryUsage();
      
      expect(memoryInfo.usedJSHeapSize).toBeDefined();
      expect(memoryInfo.totalJSHeapSize).toBeDefined();
      expect(memoryInfo.jsHeapSizeLimit).toBeDefined();
    });
  });

  describe('Error Handling and Recovery', () => {
    beforeEach(async () => {
      await extensionFramework.initialize();
    });

    test('should handle component failures gracefully', async () => {
      mockMessageInterceptor.startInterception.mockRejectedValueOnce(new Error('Component failure'));

      const result = await extensionFramework.startMessageInterception();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Component failure');
      expect(extensionFramework.state.intercepting).toBe(false);
    });

    test('should retry failed operations', async () => {
      chrome.runtime.sendMessage
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({ success: true });

      const result = await extensionFramework.sendToBackground(
        { type: 'TEST' },
        { retries: 1, retryDelay: 10 }
      );

      expect(result.success).toBe(true);
      expect(chrome.runtime.sendMessage).toHaveBeenCalledTimes(2);
    });

    test('should maintain state consistency during errors', async () => {
      const initialState = { ...extensionFramework.state };

      mockMessageInterceptor.startInterception.mockRejectedValueOnce(new Error('Test error'));

      await extensionFramework.startMessageInterception();

      expect(extensionFramework.state.intercepting).toBe(initialState.intercepting);
    });

    test('should provide detailed error information', async () => {
      mockPlatformDetector.detectPlatform.mockImplementationOnce(() => {
        throw new Error('Detection failed');
      });

      try {
        await extensionFramework.detectCurrentPlatform();
      } catch (error) {
        const errorInfo = extensionFramework.getLastError();

        expect(errorInfo.message).toBe('Detection failed');
        expect(errorInfo.timestamp).toBeDefined();
        expect(errorInfo.stack).toBeDefined();
      }
    });
  });

  describe('Integration Testing', () => {
    beforeEach(async () => {
      await extensionFramework.initialize();
    });

    test('should perform end-to-end message encryption flow', async () => {
      // Detect platform
      const platformInfo = await extensionFramework.detectCurrentPlatform();
      expect(platformInfo.supported).toBe(true);

      // Start interception
      const startResult = await extensionFramework.startMessageInterception();
      expect(startResult.success).toBe(true);

      // Encrypt message
      const encryptResult = await extensionFramework.encryptMessage('Hello, secure world!');
      expect(encryptResult.success).toBe(true);

      // Decrypt message
      const decryptResult = await extensionFramework.decryptMessage(encryptResult.encryptedMessage);
      expect(decryptResult.success).toBe(true);
      expect(decryptResult.decryptedMessage).toBe('decrypted-content');
    });

    test('should handle platform switching', async () => {
      // Start with Discord
      await extensionFramework.detectCurrentPlatform();
      await extensionFramework.startMessageInterception();

      // Switch to Slack
      mockPlatformDetector.detectPlatform.mockReturnValueOnce({
        platform: 'slack',
        supported: true,
        version: '1.0.0'
      });

      const newPlatform = await extensionFramework.detectCurrentPlatform();
      expect(newPlatform.platform).toBe('slack');

      // Should restart interception for new platform
      const restartResult = await extensionFramework.restartForNewPlatform();
      expect(restartResult.success).toBe(true);
    });

    test('should maintain security across browser restarts', async () => {
      // Simulate browser restart by reinitializing
      await extensionFramework.shutdown();

      const newFramework = new ExtensionFramework({
        platformDetector: mockPlatformDetector,
        messageInterceptor: mockMessageInterceptor
      });

      await newFramework.initialize();

      // Should restore previous state
      const status = newFramework.getStatus();
      expect(status.initialized).toBe(true);

      await newFramework.shutdown();
    });
  });
});
