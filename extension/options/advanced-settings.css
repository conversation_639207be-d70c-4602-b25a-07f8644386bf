/* WebOTR Advanced Settings Styles */

:root {
  --webottr-primary: #3b82f6;
  --webottr-primary-hover: #2563eb;
  --webottr-secondary: #6b7280;
  --webottr-success: #10b981;
  --webottr-warning: #f59e0b;
  --webottr-error: #ef4444;
  --webottr-bg: #ffffff;
  --webottr-bg-secondary: #f9fafb;
  --webottr-border: #e5e7eb;
  --webottr-text: #111827;
  --webottr-text-secondary: #6b7280;
  --webottr-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --webottr-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
  --webottr-bg: #1f2937;
  --webottr-bg-secondary: #111827;
  --webottr-border: #374151;
  --webottr-text: #f9fafb;
  --webottr-text-secondary: #9ca3af;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--webottr-bg-secondary);
  color: var(--webottr-text);
  line-height: 1.6;
}

.webottr-settings {
  max-width: 1200px;
  margin: 0 auto;
  background: var(--webottr-bg);
  min-height: 100vh;
  box-shadow: var(--webottr-shadow-lg);
}

.webottr-settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid var(--webottr-border);
  background: var(--webottr-bg);
  position: sticky;
  top: 0;
  z-index: 10;
}

.webottr-settings-header h1 {
  margin: 0;
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--webottr-text);
}

.webottr-settings-actions {
  display: flex;
  gap: 0.75rem;
}

.webottr-btn-primary,
.webottr-btn-secondary {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.webottr-btn-primary {
  background: var(--webottr-primary);
  color: white;
}

.webottr-btn-primary:hover {
  background: var(--webottr-primary-hover);
}

.webottr-btn-secondary {
  background: var(--webottr-bg-secondary);
  color: var(--webottr-text);
  border: 1px solid var(--webottr-border);
}

.webottr-btn-secondary:hover {
  background: var(--webottr-border);
}

.webottr-settings-body {
  display: flex;
  min-height: calc(100vh - 120px);
}

.webottr-settings-nav {
  width: 250px;
  background: var(--webottr-bg-secondary);
  border-right: 1px solid var(--webottr-border);
  padding: 1rem 0;
}

.webottr-nav-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1.5rem;
  border: none;
  background: none;
  color: var(--webottr-text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.webottr-nav-item:hover {
  background: var(--webottr-border);
  color: var(--webottr-text);
}

.webottr-nav-item.active {
  background: var(--webottr-primary);
  color: white;
  font-weight: 500;
}

.webottr-nav-item span {
  margin-right: 0.5rem;
}

.webottr-settings-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.webottr-settings-category {
  display: none;
}

.webottr-settings-category.active {
  display: block;
}

.webottr-settings-category h2 {
  margin: 0 0 2rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--webottr-text);
}

.webottr-setting-group {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--webottr-bg-secondary);
  border-radius: 0.5rem;
  border: 1px solid var(--webottr-border);
}

.webottr-setting-group h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--webottr-text);
}

.webottr-setting-item {
  margin-bottom: 1.5rem;
}

.webottr-setting-item:last-child {
  margin-bottom: 0;
}

.webottr-setting-item label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--webottr-text);
}

.webottr-setting-item input[type="text"],
.webottr-setting-item input[type="number"],
.webottr-setting-item select {
  width: 100%;
  max-width: 300px;
  padding: 0.5rem;
  border: 1px solid var(--webottr-border);
  border-radius: 0.375rem;
  background: var(--webottr-bg);
  color: var(--webottr-text);
  font-size: 0.875rem;
}

.webottr-setting-item input:focus,
.webottr-setting-item select:focus {
  outline: none;
  border-color: var(--webottr-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.webottr-checkbox-label {
  display: flex !important;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0 !important;
}

.webottr-checkbox-label input[type="checkbox"] {
  display: none;
}

.webottr-checkbox {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--webottr-border);
  border-radius: 0.25rem;
  margin-right: 0.75rem;
  position: relative;
  transition: all 0.2s;
}

.webottr-checkbox-label input[type="checkbox"]:checked + .webottr-checkbox {
  background: var(--webottr-primary);
  border-color: var(--webottr-primary);
}

.webottr-checkbox-label input[type="checkbox"]:checked + .webottr-checkbox::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
}

.webottr-setting-description {
  margin: 0.5rem 0 0 0;
  font-size: 0.75rem;
  color: var(--webottr-text-secondary);
  line-height: 1.4;
}

.webottr-platform-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.webottr-platform-item {
  display: flex !important;
  align-items: center;
  padding: 1rem;
  border: 1px solid var(--webottr-border);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--webottr-bg);
}

.webottr-platform-item:hover {
  border-color: var(--webottr-primary);
  background: rgba(59, 130, 246, 0.05);
}

.webottr-platform-item input[type="checkbox"] {
  margin-right: 0.75rem;
}

.webottr-platform-icon {
  font-size: 1.5rem;
  margin-right: 0.75rem;
}

.webottr-platform-name {
  font-weight: 500;
}

.webottr-notification {
  position: fixed;
  top: 1rem;
  right: 1rem;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  color: white;
  font-weight: 500;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.webottr-notification.success {
  background: var(--webottr-success);
}

.webottr-notification.error {
  background: var(--webottr-error);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .webottr-settings-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .webottr-settings-body {
    flex-direction: column;
  }
  
  .webottr-settings-nav {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 0.5rem;
  }
  
  .webottr-nav-item {
    white-space: nowrap;
    padding: 0.5rem 1rem;
  }
  
  .webottr-settings-content {
    padding: 1rem;
  }
  
  .webottr-platform-grid {
    grid-template-columns: 1fr;
  }
}
