/**
 * Advanced Settings Interface
 * 
 * Comprehensive settings interface for WebOTR browser extension
 * with security configuration, platform customization, and advanced features.
 */

class AdvancedSettings {
  constructor() {
    this.settings = new Map();
    this.settingsCategories = {
      security: 'Security & Encryption',
      platforms: 'Platform Integration',
      ui: 'User Interface',
      advanced: 'Advanced Features',
      privacy: 'Privacy & Data',
      performance: 'Performance'
    };
    
    this.defaultSettings = {
      // Security settings
      encryptionAlgorithm: 'AES-256-GCM',
      keyRotationInterval: 60, // minutes
      forwardSecrecy: true,
      autoKeyRotation: true,
      emergencyRotation: true,
      
      // Platform settings
      autoDetectPlatforms: true,
      enabledPlatforms: ['discord', 'slack', 'teams', 'whatsapp', 'telegram', 'element'],
      platformSpecificFeatures: true,
      
      // UI settings
      theme: 'auto',
      compactMode: false,
      showEncryptionIndicators: true,
      showSecurityBadges: true,
      animationsEnabled: true,
      
      // Advanced features
      developerMode: false,
      debugLogging: false,
      experimentalFeatures: false,
      betaFeatures: false,
      
      // Privacy settings
      telemetryEnabled: false,
      crashReporting: true,
      usageAnalytics: false,
      
      // Performance settings
      backgroundProcessing: true,
      memoryOptimization: true,
      cpuThrottling: false
    };
  }

  /**
   * Initialize advanced settings interface
   */
  async initialize() {
    console.log('⚙️ Initializing advanced settings interface...');
    
    // Load current settings
    await this.loadSettings();
    
    // Setup settings UI
    this.setupSettingsUI();
    
    // Setup event handlers
    this.setupEventHandlers();
    
    console.log('✅ Advanced settings interface initialized');
  }

  /**
   * Load settings from storage
   */
  async loadSettings() {
    try {
      const stored = await chrome.storage.sync.get(null);
      
      // Merge with defaults
      for (const [key, defaultValue] of Object.entries(this.defaultSettings)) {
        this.settings.set(key, stored[key] !== undefined ? stored[key] : defaultValue);
      }
      
      console.log('Settings loaded:', Object.fromEntries(this.settings));
      
    } catch (error) {
      console.error('Failed to load settings:', error);
      // Use defaults
      for (const [key, value] of Object.entries(this.defaultSettings)) {
        this.settings.set(key, value);
      }
    }
  }

  /**
   * Save settings to storage
   */
  async saveSettings() {
    try {
      const settingsObject = Object.fromEntries(this.settings);
      await chrome.storage.sync.set(settingsObject);
      
      console.log('Settings saved:', settingsObject);
      this.showSaveConfirmation();
      
    } catch (error) {
      console.error('Failed to save settings:', error);
      this.showSaveError();
    }
  }

  /**
   * Setup settings UI
   */
  setupSettingsUI() {
    const container = document.getElementById('advanced-settings-container');
    if (!container) return;

    container.innerHTML = this.createSettingsHTML();
    
    // Populate current values
    this.populateCurrentValues();
    
    // Setup platform checkboxes
    this.setupPlatformCheckboxes();
  }

  /**
   * Create settings HTML
   */
  createSettingsHTML() {
    return `
      <div class="webottr-settings">
        <div class="webottr-settings-header">
          <h1>WebOTR Advanced Settings</h1>
          <div class="webottr-settings-actions">
            <button id="save-settings" class="webottr-btn-primary">Save Settings</button>
            <button id="reset-settings" class="webottr-btn-secondary">Reset to Defaults</button>
            <button id="export-settings" class="webottr-btn-secondary">Export</button>
            <button id="import-settings" class="webottr-btn-secondary">Import</button>
          </div>
        </div>
        
        <div class="webottr-settings-body">
          <nav class="webottr-settings-nav">
            ${this.createCategoryNavigation()}
          </nav>
          
          <main class="webottr-settings-content">
            ${this.createSettingsContent()}
          </main>
        </div>
      </div>
    `;
  }

  /**
   * Create category navigation
   */
  createCategoryNavigation() {
    return Object.entries(this.settingsCategories)
      .map(([key, label]) => `
        <button class="webottr-nav-item ${key === 'security' ? 'active' : ''}" 
                data-category="${key}">
          <span>${this.getCategoryIcon(key)}</span> ${label}
        </button>
      `).join('');
  }

  /**
   * Create settings content
   */
  createSettingsContent() {
    return Object.keys(this.settingsCategories)
      .map(category => `
        <div class="webottr-settings-category ${category === 'security' ? 'active' : ''}" 
             data-category="${category}">
          <h2>${this.settingsCategories[category]}</h2>
          ${this.createCategorySettings(category)}
        </div>
      `).join('');
  }

  /**
   * Create category settings
   */
  createCategorySettings(category) {
    switch (category) {
      case 'security':
        return this.createSecuritySettings();
      case 'platforms':
        return this.createPlatformSettings();
      case 'ui':
        return this.createUISettings();
      case 'advanced':
        return this.createAdvancedSettings();
      case 'privacy':
        return this.createPrivacySettings();
      case 'performance':
        return this.createPerformanceSettings();
      default:
        return '';
    }
  }

  /**
   * Create security settings
   */
  createSecuritySettings() {
    return `
      <div class="webottr-setting-group">
        <h3>Encryption Configuration</h3>
        
        <div class="webottr-setting-item">
          <label for="encryption-algorithm">Encryption Algorithm</label>
          <select id="encryption-algorithm" data-setting="encryptionAlgorithm">
            <option value="AES-256-GCM">AES-256-GCM (Recommended)</option>
            <option value="ChaCha20-Poly1305">ChaCha20-Poly1305</option>
            <option value="AES-256-CBC">AES-256-CBC</option>
          </select>
          <p class="webottr-setting-description">
            Choose the encryption algorithm for message protection.
          </p>
        </div>
        
        <div class="webottr-setting-item">
          <label for="key-rotation-interval">Key Rotation Interval (minutes)</label>
          <input type="number" id="key-rotation-interval" 
                 data-setting="keyRotationInterval" 
                 min="5" max="1440" step="5">
          <p class="webottr-setting-description">
            How often to automatically rotate encryption keys (5-1440 minutes).
          </p>
        </div>
        
        <div class="webottr-setting-item">
          <label class="webottr-checkbox-label">
            <input type="checkbox" id="forward-secrecy" data-setting="forwardSecrecy">
            <span class="webottr-checkbox"></span>
            Enable Perfect Forward Secrecy
          </label>
          <p class="webottr-setting-description">
            Ensures past communications remain secure even if keys are compromised.
          </p>
        </div>
        
        <div class="webottr-setting-item">
          <label class="webottr-checkbox-label">
            <input type="checkbox" id="auto-key-rotation" data-setting="autoKeyRotation">
            <span class="webottr-checkbox"></span>
            Automatic Key Rotation
          </label>
          <p class="webottr-setting-description">
            Automatically rotate encryption keys based on time and usage.
          </p>
        </div>
        
        <div class="webottr-setting-item">
          <label class="webottr-checkbox-label">
            <input type="checkbox" id="emergency-rotation" data-setting="emergencyRotation">
            <span class="webottr-checkbox"></span>
            Emergency Key Rotation
          </label>
          <p class="webottr-setting-description">
            Enable immediate key rotation in case of security incidents.
          </p>
        </div>
      </div>
    `;
  }

  /**
   * Create platform settings
   */
  createPlatformSettings() {
    return `
      <div class="webottr-setting-group">
        <h3>Platform Detection</h3>
        
        <div class="webottr-setting-item">
          <label class="webottr-checkbox-label">
            <input type="checkbox" id="auto-detect-platforms" data-setting="autoDetectPlatforms">
            <span class="webottr-checkbox"></span>
            Auto-detect Chat Platforms
          </label>
          <p class="webottr-setting-description">
            Automatically detect and enable WebOTR on supported chat platforms.
          </p>
        </div>
        
        <div class="webottr-setting-item">
          <label class="webottr-checkbox-label">
            <input type="checkbox" id="platform-specific-features" data-setting="platformSpecificFeatures">
            <span class="webottr-checkbox"></span>
            Enable Platform-Specific Features
          </label>
          <p class="webottr-setting-description">
            Enable advanced features tailored for specific platforms (media encryption, bot integration, etc.).
          </p>
        </div>
      </div>
      
      <div class="webottr-setting-group">
        <h3>Enabled Platforms</h3>
        <div class="webottr-platform-grid">
          <label class="webottr-platform-item">
            <input type="checkbox" data-platform="discord">
            <span class="webottr-platform-icon">🎮</span>
            <span class="webottr-platform-name">Discord</span>
          </label>
          <label class="webottr-platform-item">
            <input type="checkbox" data-platform="slack">
            <span class="webottr-platform-icon">💼</span>
            <span class="webottr-platform-name">Slack</span>
          </label>
          <label class="webottr-platform-item">
            <input type="checkbox" data-platform="teams">
            <span class="webottr-platform-icon">👥</span>
            <span class="webottr-platform-name">Microsoft Teams</span>
          </label>
          <label class="webottr-platform-item">
            <input type="checkbox" data-platform="whatsapp">
            <span class="webottr-platform-icon">📱</span>
            <span class="webottr-platform-name">WhatsApp Web</span>
          </label>
          <label class="webottr-platform-item">
            <input type="checkbox" data-platform="telegram">
            <span class="webottr-platform-icon">✈️</span>
            <span class="webottr-platform-name">Telegram Web</span>
          </label>
          <label class="webottr-platform-item">
            <input type="checkbox" data-platform="element">
            <span class="webottr-platform-icon">🌐</span>
            <span class="webottr-platform-name">Element Matrix</span>
          </label>
        </div>
      </div>
    `;
  }

  /**
   * Get category icon
   */
  getCategoryIcon(category) {
    const icons = {
      security: '🔒',
      platforms: '🌐',
      ui: '🎨',
      advanced: '⚙️',
      privacy: '🛡️',
      performance: '⚡'
    };
    return icons[category] || '📋';
  }

  /**
   * Setup event handlers
   */
  setupEventHandlers() {
    // Save button
    document.getElementById('save-settings')?.addEventListener('click', () => {
      this.collectAndSaveSettings();
    });

    // Reset button
    document.getElementById('reset-settings')?.addEventListener('click', () => {
      this.resetToDefaults();
    });

    // Export button
    document.getElementById('export-settings')?.addEventListener('click', () => {
      this.exportSettings();
    });

    // Import button
    document.getElementById('import-settings')?.addEventListener('click', () => {
      this.importSettings();
    });

    // Category navigation
    document.querySelectorAll('.webottr-nav-item').forEach(item => {
      item.addEventListener('click', (e) => {
        this.switchCategory(e.target.dataset.category);
      });
    });

    // Setting changes
    document.addEventListener('change', (e) => {
      if (e.target.hasAttribute('data-setting')) {
        this.handleSettingChange(e.target);
      } else if (e.target.hasAttribute('data-platform')) {
        this.handlePlatformChange(e.target);
      }
    });
  }

  /**
   * Populate current values
   */
  populateCurrentValues() {
    this.settings.forEach((value, key) => {
      const element = document.querySelector(`[data-setting="${key}"]`);
      if (element) {
        if (element.type === 'checkbox') {
          element.checked = value;
        } else {
          element.value = value;
        }
      }
    });
  }

  /**
   * Setup platform checkboxes
   */
  setupPlatformCheckboxes() {
    const enabledPlatforms = this.settings.get('enabledPlatforms') || [];
    document.querySelectorAll('[data-platform]').forEach(checkbox => {
      const platform = checkbox.dataset.platform;
      checkbox.checked = enabledPlatforms.includes(platform);
    });
  }

  /**
   * Handle setting change
   */
  handleSettingChange(element) {
    const key = element.dataset.setting;
    let value = element.value;

    if (element.type === 'checkbox') {
      value = element.checked;
    } else if (element.type === 'number') {
      value = parseInt(value);
    }

    this.settings.set(key, value);
    console.log(`Setting changed: ${key} = ${value}`);
  }

  /**
   * Handle platform change
   */
  handlePlatformChange(element) {
    const platform = element.dataset.platform;
    const enabledPlatforms = this.settings.get('enabledPlatforms') || [];
    
    if (element.checked) {
      if (!enabledPlatforms.includes(platform)) {
        enabledPlatforms.push(platform);
      }
    } else {
      const index = enabledPlatforms.indexOf(platform);
      if (index > -1) {
        enabledPlatforms.splice(index, 1);
      }
    }
    
    this.settings.set('enabledPlatforms', enabledPlatforms);
    console.log(`Platform ${platform} ${element.checked ? 'enabled' : 'disabled'}`);
  }

  /**
   * Collect and save all settings
   */
  async collectAndSaveSettings() {
    // Collect all current values
    document.querySelectorAll('[data-setting]').forEach(element => {
      this.handleSettingChange(element);
    });

    // Collect platform settings
    const enabledPlatforms = [];
    document.querySelectorAll('[data-platform]:checked').forEach(checkbox => {
      enabledPlatforms.push(checkbox.dataset.platform);
    });
    this.settings.set('enabledPlatforms', enabledPlatforms);

    // Save to storage
    await this.saveSettings();
  }

  /**
   * Switch category
   */
  switchCategory(category) {
    // Update navigation
    document.querySelectorAll('.webottr-nav-item').forEach(item => {
      item.classList.toggle('active', item.dataset.category === category);
    });

    // Update content
    document.querySelectorAll('.webottr-settings-category').forEach(item => {
      item.classList.toggle('active', item.dataset.category === category);
    });
  }

  /**
   * Show save confirmation
   */
  showSaveConfirmation() {
    const notification = document.createElement('div');
    notification.className = 'webottr-notification success';
    notification.textContent = 'Settings saved successfully!';
    document.body.appendChild(notification);
    
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);
  }

  /**
   * Show save error
   */
  showSaveError() {
    const notification = document.createElement('div');
    notification.className = 'webottr-notification error';
    notification.textContent = 'Failed to save settings. Please try again.';
    document.body.appendChild(notification);
    
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);
  }

  // Additional methods for UI, advanced, privacy, and performance settings
  // would be added here...
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  const advancedSettings = new AdvancedSettings();
  advancedSettings.initialize();
});
