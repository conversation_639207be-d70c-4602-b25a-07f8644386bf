/**
 * Universal API Abstraction Layer
 * 
 * Provides a unified interface for browser-specific APIs across
 * Chrome, Firefox, Safari, and Edge extensions
 */

class UniversalAPI {
  constructor() {
    this.browser = this.detectBrowser();
    this.api = this.getBrowserAPI();
    this.manifestVersion = this.getManifestVersion();
    
    // Initialize browser-specific adaptations
    this.initializeBrowserAdaptations();
  }

  /**
   * Detect the current browser environment
   * @returns {string} Browser identifier
   */
  detectBrowser() {
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
      if (navigator.userAgent.includes('Edg/')) {
        return 'edge';
      }
      return 'chrome';
    }
    
    if (typeof browser !== 'undefined' && browser.runtime) {
      return 'firefox';
    }
    
    if (typeof safari !== 'undefined' && safari.extension) {
      return 'safari';
    }
    
    // Fallback detection
    if (typeof chrome !== 'undefined') {
      return 'chrome';
    }
    
    return 'unknown';
  }

  /**
   * Get the appropriate browser API object
   * @returns {object} Browser API object
   */
  getBrowserAPI() {
    switch (this.browser) {
      case 'firefox':
        return typeof browser !== 'undefined' ? browser : chrome;
      case 'safari':
        return typeof safari !== 'undefined' ? safari : chrome;
      case 'chrome':
      case 'edge':
      default:
        return typeof chrome !== 'undefined' ? chrome : {};
    }
  }

  /**
   * Get manifest version
   * @returns {number} Manifest version (2 or 3)
   */
  getManifestVersion() {
    try {
      const manifest = this.api.runtime.getManifest();
      return manifest.manifest_version || 2;
    } catch (error) {
      return 2; // Default to V2 for compatibility
    }
  }

  /**
   * Initialize browser-specific adaptations
   */
  initializeBrowserAdaptations() {
    // Firefox-specific adaptations
    if (this.browser === 'firefox') {
      this.adaptFirefoxAPIs();
    }
    
    // Safari-specific adaptations
    if (this.browser === 'safari') {
      this.adaptSafariAPIs();
    }
    
    // Manifest V2/V3 adaptations
    if (this.manifestVersion === 3) {
      this.adaptManifestV3();
    }
  }

  /**
   * Adapt Firefox-specific API differences
   */
  adaptFirefoxAPIs() {
    // Firefox uses browser.* instead of chrome.*
    if (typeof browser !== 'undefined' && !browser.action && browser.browserAction) {
      browser.action = browser.browserAction;
    }
  }

  /**
   * Adapt Safari-specific API differences
   */
  adaptSafariAPIs() {
    // Safari has different event handling
    if (typeof safari !== 'undefined') {
      // Adapt Safari extension APIs to match Chrome/Firefox pattern
      this.createSafariAdapters();
    }
  }

  /**
   * Adapt for Manifest V3 differences
   */
  adaptManifestV3() {
    // Handle service worker vs background page differences
    if (this.api.action && !this.api.browserAction) {
      this.api.browserAction = this.api.action;
    }
  }

  /**
   * Create Safari-specific API adapters
   */
  createSafariAdapters() {
    // Safari extension API adapters would go here
    // This is a placeholder for Safari-specific implementations
  }

  // ==================== STORAGE API ====================

  /**
   * Universal storage get
   * @param {string|array|object} keys - Keys to retrieve
   * @returns {Promise<object>} Retrieved data
   */
  async storageGet(keys) {
    return new Promise((resolve, reject) => {
      try {
        if (this.browser === 'safari') {
          // Safari storage implementation
          resolve(this.safariStorageGet(keys));
        } else {
          this.api.storage.local.get(keys, (result) => {
            if (this.api.runtime.lastError) {
              reject(new Error(this.api.runtime.lastError.message));
            } else {
              resolve(result);
            }
          });
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Universal storage set
   * @param {object} data - Data to store
   * @returns {Promise<void>}
   */
  async storageSet(data) {
    return new Promise((resolve, reject) => {
      try {
        if (this.browser === 'safari') {
          // Safari storage implementation
          this.safariStorageSet(data);
          resolve();
        } else {
          this.api.storage.local.set(data, () => {
            if (this.api.runtime.lastError) {
              reject(new Error(this.api.runtime.lastError.message));
            } else {
              resolve();
            }
          });
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Universal storage remove
   * @param {string|array} keys - Keys to remove
   * @returns {Promise<void>}
   */
  async storageRemove(keys) {
    return new Promise((resolve, reject) => {
      try {
        if (this.browser === 'safari') {
          // Safari storage implementation
          this.safariStorageRemove(keys);
          resolve();
        } else {
          this.api.storage.local.remove(keys, () => {
            if (this.api.runtime.lastError) {
              reject(new Error(this.api.runtime.lastError.message));
            } else {
              resolve();
            }
          });
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  // ==================== MESSAGING API ====================

  /**
   * Send message to content script or background
   * @param {object} message - Message to send
   * @param {object} options - Send options
   * @returns {Promise<any>} Response from receiver
   */
  async sendMessage(message, options = {}) {
    return new Promise((resolve, reject) => {
      try {
        if (this.browser === 'safari') {
          // Safari messaging implementation
          resolve(this.safariSendMessage(message, options));
        } else {
          const callback = (response) => {
            if (this.api.runtime.lastError) {
              reject(new Error(this.api.runtime.lastError.message));
            } else {
              resolve(response);
            }
          };

          if (options.tabId) {
            this.api.tabs.sendMessage(options.tabId, message, callback);
          } else {
            this.api.runtime.sendMessage(message, callback);
          }
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Add message listener
   * @param {function} callback - Message handler
   */
  addMessageListener(callback) {
    if (this.browser === 'safari') {
      // Safari message listener implementation
      this.safariAddMessageListener(callback);
    } else {
      this.api.runtime.onMessage.addListener(callback);
    }
  }

  // ==================== TABS API ====================

  /**
   * Get active tab
   * @returns {Promise<object>} Active tab information
   */
  async getActiveTab() {
    return new Promise((resolve, reject) => {
      try {
        if (this.browser === 'safari') {
          // Safari tabs implementation
          resolve(this.safariGetActiveTab());
        } else {
          this.api.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (this.api.runtime.lastError) {
              reject(new Error(this.api.runtime.lastError.message));
            } else {
              resolve(tabs[0]);
            }
          });
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Execute script in tab
   * @param {number} tabId - Tab ID
   * @param {object} details - Script details
   * @returns {Promise<any>} Script execution result
   */
  async executeScript(tabId, details) {
    return new Promise((resolve, reject) => {
      try {
        if (this.browser === 'safari') {
          // Safari script execution implementation
          resolve(this.safariExecuteScript(tabId, details));
        } else {
          if (this.manifestVersion === 3) {
            // Manifest V3 scripting API
            this.api.scripting.executeScript({
              target: { tabId },
              ...details
            }, (results) => {
              if (this.api.runtime.lastError) {
                reject(new Error(this.api.runtime.lastError.message));
              } else {
                resolve(results);
              }
            });
          } else {
            // Manifest V2 tabs API
            this.api.tabs.executeScript(tabId, details, (results) => {
              if (this.api.runtime.lastError) {
                reject(new Error(this.api.runtime.lastError.message));
              } else {
                resolve(results);
              }
            });
          }
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  // ==================== SAFARI IMPLEMENTATIONS ====================

  safariStorageGet(keys) {
    // Safari-specific storage implementation
    // This would use Safari's extension storage APIs
    return {};
  }

  safariStorageSet(data) {
    // Safari-specific storage implementation
  }

  safariStorageRemove(keys) {
    // Safari-specific storage implementation
  }

  safariSendMessage(message, options) {
    // Safari-specific messaging implementation
    return null;
  }

  safariAddMessageListener(callback) {
    // Safari-specific message listener implementation
  }

  safariGetActiveTab() {
    // Safari-specific active tab implementation
    return {};
  }

  safariExecuteScript(tabId, details) {
    // Safari-specific script execution implementation
    return null;
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Get browser information
   * @returns {object} Browser information
   */
  getBrowserInfo() {
    return {
      name: this.browser,
      manifestVersion: this.manifestVersion,
      api: !!this.api,
      features: this.getSupportedFeatures()
    };
  }

  /**
   * Get supported features for current browser
   * @returns {object} Supported features
   */
  getSupportedFeatures() {
    return {
      storage: !!this.api.storage,
      tabs: !!this.api.tabs,
      scripting: !!this.api.scripting,
      action: !!(this.api.action || this.api.browserAction),
      contextMenus: !!this.api.contextMenus,
      webRequest: !!this.api.webRequest
    };
  }

  /**
   * Check if feature is supported
   * @param {string} feature - Feature name
   * @returns {boolean} Feature support status
   */
  isFeatureSupported(feature) {
    const features = this.getSupportedFeatures();
    return features[feature] || false;
  }
}

// Create singleton instance
const universalAPI = new UniversalAPI();

// Export for different module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = universalAPI;
} else if (typeof window !== 'undefined') {
  window.UniversalAPI = universalAPI;
}

export default universalAPI;
