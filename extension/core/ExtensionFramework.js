/**
 * WebOTR Extension Framework
 * 
 * Universal browser extension framework providing:
 * - Cross-browser API abstraction
 * - Manifest V3 compatibility
 * - Secure communication between components
 * - Performance optimization and monitoring
 */

export class ExtensionFramework {
  constructor() {
    this.browser = this.detectBrowser();
    this.api = this.createUniversalAPI();
    this.state = {
      initialized: false,
      version: '1.0.0',
      startTime: Date.now()
    };
    
    this.components = new Map();
    this.messageHandlers = new Map();
    this.performanceMetrics = {
      startupTime: 0,
      messageLatency: [],
      memoryUsage: []
    };
  }

  /**
   * Initialize the extension framework
   */
  async initialize() {
    const startTime = performance.now();
    
    try {
      // Detect browser environment
      await this.setupBrowserEnvironment();
      
      // Initialize universal APIs
      await this.initializeUniversalAPIs();
      
      // Set up secure communication
      await this.setupSecureCommunication();
      
      // Initialize performance monitoring
      await this.initializePerformanceMonitoring();
      
      // Register core message handlers
      this.registerCoreMessageHandlers();
      
      this.state.initialized = true;
      this.performanceMetrics.startupTime = performance.now() - startTime;
      
      console.log(`WebOTR Extension Framework initialized in ${this.performanceMetrics.startupTime.toFixed(2)}ms`);
      
      return {
        success: true,
        browser: this.browser,
        startupTime: this.performanceMetrics.startupTime,
        version: this.state.version
      };
      
    } catch (error) {
      console.error('Extension Framework initialization failed:', error);
      throw error;
    }
  }

  /**
   * Detect browser type and version
   */
  detectBrowser() {
    const userAgent = navigator.userAgent;
    
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      if (userAgent.includes('Edg/')) {
        return { name: 'edge', api: chrome, manifestVersion: 3 };
      } else {
        return { name: 'chrome', api: chrome, manifestVersion: 3 };
      }
    } else if (typeof browser !== 'undefined' && browser.runtime) {
      return { name: 'firefox', api: browser, manifestVersion: 3 };
    } else if (typeof safari !== 'undefined' && safari.extension) {
      return { name: 'safari', api: safari, manifestVersion: 2 };
    }
    
    throw new Error('Unsupported browser environment');
  }

  /**
   * Create universal API abstraction layer
   */
  createUniversalAPI() {
    const browserAPI = this.browser.api;
    
    return {
      // Storage API
      storage: {
        get: (keys) => this.promisify(browserAPI.storage.local.get, keys),
        set: (items) => this.promisify(browserAPI.storage.local.set, items),
        remove: (keys) => this.promisify(browserAPI.storage.local.remove, keys),
        clear: () => this.promisify(browserAPI.storage.local.clear)
      },
      
      // Runtime API
      runtime: {
        sendMessage: (message) => this.promisify(browserAPI.runtime.sendMessage, message),
        onMessage: browserAPI.runtime.onMessage,
        getURL: browserAPI.runtime.getURL,
        id: browserAPI.runtime.id
      },
      
      // Tabs API
      tabs: {
        query: (queryInfo) => this.promisify(browserAPI.tabs.query, queryInfo),
        sendMessage: (tabId, message) => this.promisify(browserAPI.tabs.sendMessage, tabId, message),
        executeScript: (tabId, details) => this.promisify(browserAPI.scripting.executeScript, {
          target: { tabId },
          ...details
        })
      },
      
      // Notifications API
      notifications: {
        create: (id, options) => this.promisify(browserAPI.notifications.create, id, options),
        clear: (id) => this.promisify(browserAPI.notifications.clear, id)
      },
      
      // Action API (Manifest V3)
      action: {
        setBadgeText: (details) => this.promisify(browserAPI.action.setBadgeText, details),
        setBadgeBackgroundColor: (details) => this.promisify(browserAPI.action.setBadgeBackgroundColor, details),
        setIcon: (details) => this.promisify(browserAPI.action.setIcon, details)
      }
    };
  }

  /**
   * Promisify browser API calls for consistent async handling
   */
  promisify(fn, ...args) {
    return new Promise((resolve, reject) => {
      fn.call(this.browser.api, ...args, (result) => {
        if (this.browser.api.runtime.lastError) {
          reject(new Error(this.browser.api.runtime.lastError.message));
        } else {
          resolve(result);
        }
      });
    });
  }

  /**
   * Setup browser-specific environment
   */
  async setupBrowserEnvironment() {
    switch (this.browser.name) {
      case 'chrome':
      case 'edge':
        await this.setupChromiumEnvironment();
        break;
      case 'firefox':
        await this.setupFirefoxEnvironment();
        break;
      case 'safari':
        await this.setupSafariEnvironment();
        break;
    }
  }

  /**
   * Setup Chromium-based browser environment
   */
  async setupChromiumEnvironment() {
    // Chrome/Edge specific optimizations
    this.browserFeatures = {
      serviceWorkers: true,
      offscreenDocuments: true,
      declarativeNetRequest: true,
      webAssembly: true
    };
  }

  /**
   * Setup Firefox environment
   */
  async setupFirefoxEnvironment() {
    // Firefox specific optimizations
    this.browserFeatures = {
      serviceWorkers: true,
      offscreenDocuments: false,
      declarativeNetRequest: true,
      webAssembly: true
    };
  }

  /**
   * Setup Safari environment
   */
  async setupSafariEnvironment() {
    // Safari specific optimizations
    this.browserFeatures = {
      serviceWorkers: false,
      offscreenDocuments: false,
      declarativeNetRequest: false,
      webAssembly: true
    };
  }

  /**
   * Initialize universal APIs
   */
  async initializeUniversalAPIs() {
    // Test storage API
    await this.api.storage.set({ 'webottr-test': Date.now() });
    await this.api.storage.remove('webottr-test');
    
    // Verify runtime API
    if (!this.api.runtime.id) {
      throw new Error('Runtime API not available');
    }
    
    console.log('Universal APIs initialized successfully');
  }

  /**
   * Setup secure communication between extension components
   */
  async setupSecureCommunication() {
    // Generate session key for secure communication
    this.sessionKey = await this.generateSessionKey();
    
    // Set up message encryption/decryption
    this.messageEncryption = {
      encrypt: async (message) => await this.encryptMessage(message),
      decrypt: async (encryptedMessage) => await this.decryptMessage(encryptedMessage)
    };
    
    // Set up message routing
    this.api.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
      try {
        const response = await this.handleMessage(message, sender);
        sendResponse(response);
      } catch (error) {
        sendResponse({ error: error.message });
      }
      return true; // Async response
    });
  }

  /**
   * Generate session key for secure communication
   */
  async generateSessionKey() {
    const keyMaterial = new Uint8Array(32);
    crypto.getRandomValues(keyMaterial);
    
    return await crypto.subtle.importKey(
      'raw',
      keyMaterial,
      { name: 'AES-GCM' },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Encrypt message for secure communication
   */
  async encryptMessage(message) {
    const encoder = new TextEncoder();
    const data = encoder.encode(JSON.stringify(message));
    const iv = new Uint8Array(12);
    crypto.getRandomValues(iv);
    
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      this.sessionKey,
      data
    );
    
    return {
      encrypted: Array.from(new Uint8Array(encrypted)),
      iv: Array.from(iv)
    };
  }

  /**
   * Decrypt message for secure communication
   */
  async decryptMessage(encryptedMessage) {
    const { encrypted, iv } = encryptedMessage;
    
    const decrypted = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv: new Uint8Array(iv) },
      this.sessionKey,
      new Uint8Array(encrypted)
    );
    
    const decoder = new TextDecoder();
    return JSON.parse(decoder.decode(decrypted));
  }

  /**
   * Initialize performance monitoring
   */
  async initializePerformanceMonitoring() {
    // Monitor memory usage
    if (performance.memory) {
      setInterval(() => {
        this.performanceMetrics.memoryUsage.push({
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          timestamp: Date.now()
        });
        
        // Keep only last 100 measurements
        if (this.performanceMetrics.memoryUsage.length > 100) {
          this.performanceMetrics.memoryUsage.shift();
        }
      }, 30000); // Every 30 seconds
    }
    
    // Monitor message latency
    this.messageLatencyStart = new Map();
  }

  /**
   * Register core message handlers
   */
  registerCoreMessageHandlers() {
    this.registerMessageHandler('ping', async () => ({ pong: Date.now() }));
    this.registerMessageHandler('getStatus', async () => this.getStatus());
    this.registerMessageHandler('getPerformanceMetrics', async () => this.getPerformanceMetrics());
  }

  /**
   * Register a message handler
   */
  registerMessageHandler(type, handler) {
    this.messageHandlers.set(type, handler);
  }

  /**
   * Handle incoming messages
   */
  async handleMessage(message, sender) {
    const startTime = performance.now();
    
    try {
      // Decrypt message if encrypted
      let decryptedMessage = message;
      if (message.encrypted) {
        decryptedMessage = await this.decryptMessage(message);
      }
      
      const { type, data, id } = decryptedMessage;
      
      // Find and execute handler
      const handler = this.messageHandlers.get(type);
      if (!handler) {
        throw new Error(`No handler for message type: ${type}`);
      }
      
      const result = await handler(data, sender);
      
      // Record latency
      const latency = performance.now() - startTime;
      this.performanceMetrics.messageLatency.push(latency);
      if (this.performanceMetrics.messageLatency.length > 1000) {
        this.performanceMetrics.messageLatency.shift();
      }
      
      return {
        success: true,
        data: result,
        id,
        latency
      };
      
    } catch (error) {
      return {
        success: false,
        error: error.message,
        id: message.id
      };
    }
  }

  /**
   * Send message to content script or background
   */
  async sendMessage(type, data, options = {}) {
    const message = {
      type,
      data,
      id: this.generateMessageId(),
      timestamp: Date.now()
    };
    
    // Encrypt if requested
    if (options.encrypt) {
      return await this.messageEncryption.encrypt(message);
    }
    
    return message;
  }

  /**
   * Generate unique message ID
   */
  generateMessageId() {
    return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Register extension component
   */
  registerComponent(name, component) {
    this.components.set(name, component);
  }

  /**
   * Get extension component
   */
  getComponent(name) {
    return this.components.get(name);
  }

  /**
   * Get extension status
   */
  getStatus() {
    return {
      initialized: this.state.initialized,
      version: this.state.version,
      browser: this.browser.name,
      uptime: Date.now() - this.state.startTime,
      components: Array.from(this.components.keys()),
      features: this.browserFeatures
    };
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    const avgLatency = this.performanceMetrics.messageLatency.length > 0
      ? this.performanceMetrics.messageLatency.reduce((a, b) => a + b) / this.performanceMetrics.messageLatency.length
      : 0;
    
    const currentMemory = this.performanceMetrics.memoryUsage.length > 0
      ? this.performanceMetrics.memoryUsage[this.performanceMetrics.memoryUsage.length - 1]
      : null;
    
    return {
      startupTime: this.performanceMetrics.startupTime,
      averageMessageLatency: avgLatency,
      currentMemoryUsage: currentMemory,
      totalMessages: this.performanceMetrics.messageLatency.length
    };
  }

  /**
   * Initialize platform integration
   */
  async initializePlatformIntegration() {
    try {
      // Import PlatformIntegration dynamically to avoid circular dependencies
      const { default: PlatformIntegration } = await import('./PlatformIntegration.js');

      this.platformIntegration = new PlatformIntegration();
      const success = await this.platformIntegration.initialize();

      if (success) {
        this.registerComponent('platformIntegration', this.platformIntegration);

        // Register platform-specific message handlers
        this.registerMessageHandler('toggleEncryption', async (data) => {
          return await this.handleEncryptionToggle(data);
        });

        this.registerMessageHandler('decryptMessage', async (data) => {
          return await this.handleMessageDecryption(data);
        });

        this.registerMessageHandler('getPlatformInfo', async () => {
          return this.platformIntegration.getPlatformInfo();
        });

        console.log('Platform integration initialized successfully');
        return true;
      }

      return false;
    } catch (error) {
      console.error('Platform integration initialization failed:', error);
      return false;
    }
  }

  /**
   * Handle encryption toggle request
   */
  async handleEncryptionToggle(data) {
    try {
      // Get current encryption state
      const currentState = await this.api.storage.get(['encryptionEnabled']);
      const newState = !currentState.encryptionEnabled;

      // Save new state
      await this.api.storage.set({ encryptionEnabled: newState });

      // Update UI indicators
      await this.updateEncryptionIndicators(newState);

      return {
        success: true,
        encryptionEnabled: newState
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Handle message decryption request
   */
  async handleMessageDecryption(data) {
    try {
      const { message, platform } = data;

      // Import OTR session for decryption
      // This would integrate with the existing OTR implementation
      const decryptedMessage = await this.decryptOTRMessage(message);

      return {
        success: true,
        decryptedMessage
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update encryption indicators across the extension
   */
  async updateEncryptionIndicators(enabled) {
    // Update extension badge
    await this.api.action.setBadgeText({
      text: enabled ? '🔒' : ''
    });

    await this.api.action.setBadgeBackgroundColor({
      color: enabled ? '#00ff00' : '#ff0000'
    });

    // Notify content scripts
    const tabs = await this.api.tabs.query({});
    for (const tab of tabs) {
      try {
        await this.api.tabs.sendMessage(tab.id, {
          type: 'updateEncryptionState',
          data: { enabled }
        });
      } catch (error) {
        // Tab might not have content script, ignore
      }
    }
  }

  /**
   * Decrypt OTR message (placeholder for OTR integration)
   */
  async decryptOTRMessage(encryptedMessage) {
    // This would integrate with the existing OTR implementation
    // For now, return a placeholder
    return `[Decrypted] ${encryptedMessage}`;
  }

  /**
   * Get comprehensive extension information
   */
  getExtensionInfo() {
    const status = this.getStatus();
    const performance = this.getPerformanceMetrics();
    const platformInfo = this.platformIntegration?.getPlatformInfo() || null;

    return {
      ...status,
      performance,
      platform: platformInfo,
      browserFeatures: this.browserFeatures,
      manifestVersion: this.browser.manifestVersion
    };
  }

  /**
   * Shutdown extension framework
   */
  async shutdown() {
    // Cleanup platform integration
    if (this.platformIntegration) {
      this.platformIntegration.cleanup();
    }

    // Clear components
    this.components.clear();
    this.messageHandlers.clear();

    // Clear session key
    this.sessionKey = null;

    this.state.initialized = false;

    console.log('WebOTR Extension Framework shutdown complete');
  }
}
