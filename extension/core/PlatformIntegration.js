/**
 * Platform Integration Engine
 * 
 * Automatically detects and integrates with supported chat platforms
 * Provides unified interface for message interception and UI injection
 */

import universalAPI from './UniversalAPI.js';

class PlatformIntegration {
  constructor() {
    this.currentPlatform = null;
    this.platformConfig = null;
    this.isInitialized = false;
    this.messageObserver = null;
    this.uiElements = new Map();
    
    // Platform configurations
    this.platforms = {
      discord: {
        name: 'Discord',
        domains: ['discord.com', 'discordapp.com'],
        selectors: {
          messageInput: '[data-slate-editor="true"]',
          messageContainer: '[class*="messageContent"]',
          sendButton: '[data-testid="send-button"]',
          chatArea: '[class*="chatContent"]'
        },
        features: ['encryption', 'verification', 'fileSharing'],
        injectionPoints: {
          toolbar: '[class*="toolbar"]',
          messageArea: '[class*="messageContent"]'
        }
      },
      slack: {
        name: 'Slack',
        domains: ['slack.com'],
        selectors: {
          messageInput: '[data-qa="message_input"]',
          messageContainer: '[data-qa="message_content"]',
          sendButton: '[data-qa="send_button"]',
          chatArea: '[data-qa="channel_view"]'
        },
        features: ['encryption', 'verification', 'threading'],
        injectionPoints: {
          toolbar: '[data-qa="message_toolbar"]',
          messageArea: '[data-qa="message_content"]'
        }
      },
      teams: {
        name: 'Microsoft Teams',
        domains: ['teams.microsoft.com', 'teams.live.com'],
        selectors: {
          messageInput: '[data-tid="ckeditor"]',
          messageContainer: '[data-tid="message-body"]',
          sendButton: '[data-tid="send-button"]',
          chatArea: '[data-tid="chat-pane"]'
        },
        features: ['encryption', 'verification', 'enterprise'],
        injectionPoints: {
          toolbar: '[data-tid="message-toolbar"]',
          messageArea: '[data-tid="message-body"]'
        }
      },
      whatsapp: {
        name: 'WhatsApp Web',
        domains: ['web.whatsapp.com'],
        selectors: {
          messageInput: '[data-testid="conversation-compose-box-input"]',
          messageContainer: '[data-testid="conversation-message"]',
          sendButton: '[data-testid="send"]',
          chatArea: '[data-testid="conversation-panel-messages"]'
        },
        features: ['encryption', 'verification', 'media'],
        injectionPoints: {
          toolbar: '[data-testid="compose-toolbar"]',
          messageArea: '[data-testid="conversation-message"]'
        }
      },
      telegram: {
        name: 'Telegram Web',
        domains: ['web.telegram.org'],
        selectors: {
          messageInput: '.input-message-input',
          messageContainer: '.message-content',
          sendButton: '.btn-send',
          chatArea: '.messages-container'
        },
        features: ['encryption', 'verification', 'bots'],
        injectionPoints: {
          toolbar: '.input-message-container',
          messageArea: '.message-content'
        }
      },
      element: {
        name: 'Element (Matrix)',
        domains: ['app.element.io', 'element.io'],
        selectors: {
          messageInput: '.mx_BasicMessageComposer_input',
          messageContainer: '.mx_EventTile_body',
          sendButton: '.mx_MessageComposer_sendButton',
          chatArea: '.mx_RoomView_messagePanel'
        },
        features: ['encryption', 'verification', 'federation'],
        injectionPoints: {
          toolbar: '.mx_MessageComposer_actions',
          messageArea: '.mx_EventTile_body'
        }
      }
    };
  }

  /**
   * Initialize platform integration
   * @returns {Promise<boolean>} Success status
   */
  async initialize() {
    try {
      // Detect current platform
      this.currentPlatform = this.detectPlatform();
      
      if (!this.currentPlatform) {
        console.log('WebOTR: No supported platform detected');
        return false;
      }

      this.platformConfig = this.platforms[this.currentPlatform];
      console.log(`WebOTR: Detected platform: ${this.platformConfig.name}`);

      // Wait for platform to load
      await this.waitForPlatformReady();

      // Initialize platform-specific integration
      await this.initializePlatformIntegration();

      // Set up message observation
      this.setupMessageObserver();

      // Inject WebOTR UI elements
      await this.injectUI();

      this.isInitialized = true;
      console.log(`WebOTR: Successfully initialized for ${this.platformConfig.name}`);
      
      return true;
    } catch (error) {
      console.error('WebOTR: Platform integration failed:', error);
      return false;
    }
  }

  /**
   * Detect current platform based on URL and DOM
   * @returns {string|null} Platform identifier
   */
  detectPlatform() {
    const hostname = window.location.hostname;
    
    for (const [platformId, config] of Object.entries(this.platforms)) {
      if (config.domains.some(domain => hostname.includes(domain))) {
        return platformId;
      }
    }
    
    return null;
  }

  /**
   * Wait for platform to be ready for integration
   * @returns {Promise<void>}
   */
  async waitForPlatformReady() {
    const maxWaitTime = 30000; // 30 seconds
    const checkInterval = 500; // 500ms
    let elapsed = 0;

    return new Promise((resolve, reject) => {
      const checkReady = () => {
        if (elapsed >= maxWaitTime) {
          reject(new Error('Platform ready timeout'));
          return;
        }

        // Check if key elements exist
        const messageInput = document.querySelector(this.platformConfig.selectors.messageInput);
        const chatArea = document.querySelector(this.platformConfig.selectors.chatArea);

        if (messageInput && chatArea) {
          resolve();
        } else {
          elapsed += checkInterval;
          setTimeout(checkReady, checkInterval);
        }
      };

      checkReady();
    });
  }

  /**
   * Initialize platform-specific integration
   * @returns {Promise<void>}
   */
  async initializePlatformIntegration() {
    switch (this.currentPlatform) {
      case 'discord':
        await this.initializeDiscord();
        break;
      case 'slack':
        await this.initializeSlack();
        break;
      case 'teams':
        await this.initializeTeams();
        break;
      case 'whatsapp':
        await this.initializeWhatsApp();
        break;
      case 'telegram':
        await this.initializeTelegram();
        break;
      case 'element':
        await this.initializeElement();
        break;
    }
  }

  /**
   * Discord-specific initialization
   */
  async initializeDiscord() {
    // Discord uses React, so we need to handle dynamic content
    this.setupReactObserver();
    
    // Discord-specific event listeners
    this.addDiscordEventListeners();
  }

  /**
   * Slack-specific initialization
   */
  async initializeSlack() {
    // Slack has workspace-specific configurations
    this.detectSlackWorkspace();
    
    // Slack-specific event listeners
    this.addSlackEventListeners();
  }

  /**
   * Teams-specific initialization
   */
  async initializeTeams() {
    // Teams has enterprise features
    this.detectTeamsEnvironment();
    
    // Teams-specific event listeners
    this.addTeamsEventListeners();
  }

  /**
   * WhatsApp-specific initialization
   */
  async initializeWhatsApp() {
    // WhatsApp Web has specific message handling
    this.setupWhatsAppMessageHandling();
  }

  /**
   * Telegram-specific initialization
   */
  async initializeTelegram() {
    // Telegram has bot integration capabilities
    this.setupTelegramBotHandling();
  }

  /**
   * Element-specific initialization
   */
  async initializeElement() {
    // Element/Matrix has federation features
    this.setupMatrixFederation();
  }

  /**
   * Set up message observation for encryption/decryption
   */
  setupMessageObserver() {
    const chatArea = document.querySelector(this.platformConfig.selectors.chatArea);
    
    if (!chatArea) {
      console.warn('WebOTR: Chat area not found for message observation');
      return;
    }

    this.messageObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            this.processNewMessage(node);
          }
        });
      });
    });

    this.messageObserver.observe(chatArea, {
      childList: true,
      subtree: true
    });
  }

  /**
   * Process new messages for encryption/decryption
   * @param {Element} messageElement - New message element
   */
  async processNewMessage(messageElement) {
    try {
      // Check if message contains encrypted content
      if (this.isEncryptedMessage(messageElement)) {
        await this.decryptMessage(messageElement);
      }
      
      // Add encryption indicators
      this.addEncryptionIndicators(messageElement);
    } catch (error) {
      console.error('WebOTR: Message processing failed:', error);
    }
  }

  /**
   * Check if message is encrypted
   * @param {Element} messageElement - Message element
   * @returns {boolean} Encryption status
   */
  isEncryptedMessage(messageElement) {
    const messageText = messageElement.textContent || '';
    return messageText.includes('?OTR:') || messageText.includes('-----BEGIN PGP MESSAGE-----');
  }

  /**
   * Decrypt encrypted message
   * @param {Element} messageElement - Message element
   */
  async decryptMessage(messageElement) {
    // Send message to background script for decryption
    const messageText = messageElement.textContent;
    
    try {
      const response = await universalAPI.sendMessage({
        action: 'decryptMessage',
        message: messageText,
        platform: this.currentPlatform
      });

      if (response.success) {
        // Replace encrypted content with decrypted content
        this.replaceMessageContent(messageElement, response.decryptedMessage);
        
        // Add decryption indicator
        this.addDecryptionIndicator(messageElement);
      }
    } catch (error) {
      console.error('WebOTR: Message decryption failed:', error);
    }
  }

  /**
   * Inject WebOTR UI elements
   * @returns {Promise<void>}
   */
  async injectUI() {
    // Inject encryption toggle
    await this.injectEncryptionToggle();
    
    // Inject status indicator
    await this.injectStatusIndicator();
    
    // Inject verification button
    await this.injectVerificationButton();
    
    // Inject settings menu
    await this.injectSettingsMenu();
  }

  /**
   * Inject encryption toggle button
   */
  async injectEncryptionToggle() {
    const toolbar = document.querySelector(this.platformConfig.injectionPoints.toolbar);
    
    if (!toolbar) {
      console.warn('WebOTR: Toolbar not found for encryption toggle injection');
      return;
    }

    const toggleButton = this.createEncryptionToggle();
    toolbar.appendChild(toggleButton);
    
    this.uiElements.set('encryptionToggle', toggleButton);
  }

  /**
   * Create encryption toggle button
   * @returns {Element} Toggle button element
   */
  createEncryptionToggle() {
    const button = document.createElement('button');
    button.className = 'webOTR-encryption-toggle';
    button.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"/>
      </svg>
    `;
    button.title = 'Toggle WebOTR Encryption';
    button.style.cssText = `
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      color: #666;
      transition: all 0.2s ease;
    `;
    
    button.addEventListener('click', () => this.toggleEncryption());
    
    return button;
  }

  /**
   * Toggle encryption state
   */
  async toggleEncryption() {
    try {
      const response = await universalAPI.sendMessage({
        action: 'toggleEncryption',
        platform: this.currentPlatform
      });

      if (response.success) {
        this.updateEncryptionToggleState(response.encryptionEnabled);
      }
    } catch (error) {
      console.error('WebOTR: Encryption toggle failed:', error);
    }
  }

  /**
   * Update encryption toggle visual state
   * @param {boolean} enabled - Encryption state
   */
  updateEncryptionToggleState(enabled) {
    const toggle = this.uiElements.get('encryptionToggle');
    if (toggle) {
      toggle.style.color = enabled ? '#00ff00' : '#666';
      toggle.title = enabled ? 'WebOTR Encryption: ON' : 'WebOTR Encryption: OFF';
    }
  }

  /**
   * Inject status indicator
   */
  async injectStatusIndicator() {
    // Implementation for status indicator injection
  }

  /**
   * Inject verification button
   */
  async injectVerificationButton() {
    // Implementation for verification button injection
  }

  /**
   * Inject settings menu
   */
  async injectSettingsMenu() {
    // Implementation for settings menu injection
  }

  // Platform-specific helper methods
  setupReactObserver() {
    // Discord React-specific observer setup
  }

  addDiscordEventListeners() {
    // Discord-specific event listeners
  }

  addSlackEventListeners() {
    // Slack-specific event listeners
  }

  addTeamsEventListeners() {
    // Teams-specific event listeners
  }

  detectSlackWorkspace() {
    // Detect Slack workspace configuration
  }

  detectTeamsEnvironment() {
    // Detect Teams environment (personal/enterprise)
  }

  setupWhatsAppMessageHandling() {
    // WhatsApp-specific message handling
  }

  setupTelegramBotHandling() {
    // Telegram bot integration
  }

  setupMatrixFederation() {
    // Matrix federation handling
  }

  replaceMessageContent(element, newContent) {
    // Replace message content while preserving structure
    const contentElement = element.querySelector(this.platformConfig.selectors.messageContainer);
    if (contentElement) {
      contentElement.textContent = newContent;
    }
  }

  addEncryptionIndicators(element) {
    // Add visual indicators for encrypted messages
  }

  addDecryptionIndicator(element) {
    // Add indicator for successfully decrypted messages
  }

  /**
   * Get platform information
   * @returns {object} Platform information
   */
  getPlatformInfo() {
    return {
      platform: this.currentPlatform,
      config: this.platformConfig,
      initialized: this.isInitialized,
      features: this.platformConfig?.features || []
    };
  }

  /**
   * Cleanup integration
   */
  cleanup() {
    if (this.messageObserver) {
      this.messageObserver.disconnect();
    }
    
    // Remove injected UI elements
    this.uiElements.forEach((element) => {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
    });
    
    this.uiElements.clear();
    this.isInitialized = false;
  }
}

export default PlatformIntegration;
