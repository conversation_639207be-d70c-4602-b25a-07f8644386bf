/**
 * WebOTR Platform Detector
 * 
 * Automatically detects and adapts to different chat platforms:
 * - <PERSON><PERSON>, <PERSON>lack, Teams, WhatsApp Web, Telegram, Element
 * - Platform-specific UI integration points
 * - Message container and input field detection
 * - Real-time platform state monitoring
 */

class PlatformDetector {
  constructor() {
    this.platform = null;
    this.config = null;
    this.observers = [];
    this.state = {
      detected: false,
      ready: false,
      lastCheck: null
    };
    
    this.platformConfigs = this.createPlatformConfigs();
  }

  /**
   * Initialize platform detection
   */
  async initialize() {
    try {
      // Detect current platform
      this.platform = this.detectPlatform();
      
      if (!this.platform) {
        throw new Error('Unsupported platform');
      }
      
      this.config = this.platformConfigs[this.platform];
      this.state.detected = true;
      
      // Wait for platform to be ready
      await this.waitForPlatformReady();
      
      // Set up platform monitoring
      this.setupPlatformMonitoring();
      
      this.state.ready = true;
      this.state.lastCheck = Date.now();
      
      console.log(`WebOTR: Detected platform ${this.platform}`);
      
      return {
        platform: this.platform,
        config: this.config,
        ready: this.state.ready
      };
      
    } catch (error) {
      console.error('Platform detection failed:', error);
      throw error;
    }
  }

  /**
   * Create platform-specific configurations
   */
  createPlatformConfigs() {
    return {
      discord: {
        name: 'Discord',
        selectors: {
          messageContainer: '[data-list-id="chat-messages"]',
          messageInput: '[data-slate-editor="true"]',
          messageItem: '[id^="chat-messages-"]',
          sendButton: '[data-testid="send-button"]',
          userAvatar: '[data-testid="avatar"]',
          username: '.username-h_Y3Us',
          messageContent: '[id^="message-content-"]',
          toolbar: '[class*="toolbar"]'
        },
        features: {
          richText: true,
          fileUpload: true,
          reactions: true,
          threads: true,
          voiceChat: true
        },
        integration: {
          injectLocation: 'toolbar',
          messageInterception: 'input',
          uiStyle: 'discord'
        }
      },
      
      slack: {
        name: 'Slack',
        selectors: {
          messageContainer: '[data-qa="virtual-list"]',
          messageInput: '[data-qa="message_input"]',
          messageItem: '[data-qa="message"]',
          sendButton: '[data-qa="send_button"]',
          userAvatar: '[data-qa="message_sender_avatar"]',
          username: '[data-qa="message_sender_name"]',
          messageContent: '[data-qa="message_content"]',
          toolbar: '[data-qa="message_input_toolbar"]'
        },
        features: {
          richText: true,
          fileUpload: true,
          reactions: true,
          threads: true,
          workspaces: true
        },
        integration: {
          injectLocation: 'toolbar',
          messageInterception: 'input',
          uiStyle: 'slack'
        }
      },
      
      teams: {
        name: 'Microsoft Teams',
        selectors: {
          messageContainer: '[data-tid="chat-pane-list"]',
          messageInput: '[data-tid="ckeditor"]',
          messageItem: '[data-tid="chat-pane-message"]',
          sendButton: '[data-tid="send-button"]',
          userAvatar: '[data-tid="message-avatar"]',
          username: '[data-tid="message-author-name"]',
          messageContent: '[data-tid="message-body-content"]',
          toolbar: '[data-tid="ckeditor-toolbar"]'
        },
        features: {
          richText: true,
          fileUpload: true,
          reactions: true,
          meetings: true,
          enterprise: true
        },
        integration: {
          injectLocation: 'toolbar',
          messageInterception: 'input',
          uiStyle: 'teams'
        }
      },
      
      whatsapp: {
        name: 'WhatsApp Web',
        selectors: {
          messageContainer: '[data-testid="conversation-panel-messages"]',
          messageInput: '[data-testid="message-input"]',
          messageItem: '[data-testid="msg-container"]',
          sendButton: '[data-testid="send"]',
          userAvatar: '[data-testid="avatar"]',
          username: '[data-testid="message-author"]',
          messageContent: '[data-testid="message-text"]',
          toolbar: '[data-testid="compose-panel"]'
        },
        features: {
          richText: false,
          fileUpload: true,
          reactions: true,
          voiceMessages: true,
          endToEnd: true
        },
        integration: {
          injectLocation: 'compose',
          messageInterception: 'send',
          uiStyle: 'whatsapp'
        }
      },
      
      telegram: {
        name: 'Telegram Web',
        selectors: {
          messageContainer: '.messages-container',
          messageInput: '#message-input-text',
          messageItem: '.message',
          sendButton: '.btn-send',
          userAvatar: '.avatar',
          username: '.peer-title',
          messageContent: '.message-content',
          toolbar: '.input-message-container'
        },
        features: {
          richText: true,
          fileUpload: true,
          secretChats: true,
          bots: true,
          channels: true
        },
        integration: {
          injectLocation: 'toolbar',
          messageInterception: 'input',
          uiStyle: 'telegram'
        }
      },
      
      element: {
        name: 'Element (Matrix)',
        selectors: {
          messageContainer: '.mx_RoomView_messagePanel',
          messageInput: '.mx_BasicMessageComposer_input',
          messageItem: '.mx_EventTile',
          sendButton: '.mx_MessageComposer_sendButton',
          userAvatar: '.mx_BaseAvatar',
          username: '.mx_DisambiguatedProfile_displayName',
          messageContent: '.mx_EventTile_body',
          toolbar: '.mx_MessageComposer'
        },
        features: {
          richText: true,
          fileUpload: true,
          encryption: true,
          federation: true,
          rooms: true
        },
        integration: {
          injectLocation: 'composer',
          messageInterception: 'input',
          uiStyle: 'element'
        }
      }
    };
  }

  /**
   * Detect current platform based on URL and DOM
   */
  detectPlatform() {
    const hostname = window.location.hostname;
    const pathname = window.location.pathname;
    
    // Discord detection
    if (hostname.includes('discord.com')) {
      return 'discord';
    }
    
    // Slack detection
    if (hostname.includes('slack.com')) {
      return 'slack';
    }
    
    // Microsoft Teams detection
    if (hostname.includes('teams.microsoft.com')) {
      return 'teams';
    }
    
    // WhatsApp Web detection
    if (hostname.includes('web.whatsapp.com')) {
      return 'whatsapp';
    }
    
    // Telegram Web detection
    if (hostname.includes('web.telegram.org')) {
      return 'telegram';
    }
    
    // Element detection
    if (hostname.includes('app.element.io') || 
        document.querySelector('.mx_MatrixChat')) {
      return 'element';
    }
    
    return null;
  }

  /**
   * Wait for platform to be fully loaded and ready
   */
  async waitForPlatformReady() {
    const maxWaitTime = 30000; // 30 seconds
    const checkInterval = 500; // 500ms
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      if (this.isPlatformReady()) {
        return true;
      }
      
      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }
    
    throw new Error(`Platform ${this.platform} not ready within timeout`);
  }

  /**
   * Check if platform is ready for integration
   */
  isPlatformReady() {
    const config = this.config;
    
    // Check for essential elements
    const messageContainer = document.querySelector(config.selectors.messageContainer);
    const messageInput = document.querySelector(config.selectors.messageInput);
    
    return messageContainer && messageInput;
  }

  /**
   * Setup platform monitoring for dynamic changes
   */
  setupPlatformMonitoring() {
    // Monitor DOM changes
    const observer = new MutationObserver((mutations) => {
      this.handlePlatformChanges(mutations);
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'data-testid', 'data-qa', 'data-tid']
    });
    
    this.observers.push(observer);
    
    // Monitor URL changes (SPA navigation)
    let lastUrl = window.location.href;
    const urlObserver = new MutationObserver(() => {
      if (window.location.href !== lastUrl) {
        lastUrl = window.location.href;
        this.handleUrlChange();
      }
    });
    
    urlObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    this.observers.push(urlObserver);
  }

  /**
   * Handle platform DOM changes
   */
  handlePlatformChanges(mutations) {
    // Check if critical elements are still present
    if (!this.isPlatformReady()) {
      console.warn('WebOTR: Platform elements changed, re-initializing...');
      this.reinitialize();
    }
    
    // Notify other components of changes
    this.notifyPlatformChange('dom-change', { mutations });
  }

  /**
   * Handle URL changes in single-page applications
   */
  handleUrlChange() {
    console.log('WebOTR: URL changed, checking platform state...');
    
    // Re-detect platform if URL changed significantly
    const newPlatform = this.detectPlatform();
    if (newPlatform !== this.platform) {
      console.log(`WebOTR: Platform changed from ${this.platform} to ${newPlatform}`);
      this.platform = newPlatform;
      this.config = this.platformConfigs[newPlatform];
      this.reinitialize();
    }
    
    this.notifyPlatformChange('url-change', { 
      url: window.location.href,
      platform: this.platform 
    });
  }

  /**
   * Reinitialize platform detection
   */
  async reinitialize() {
    try {
      this.state.ready = false;
      await this.waitForPlatformReady();
      this.state.ready = true;
      this.state.lastCheck = Date.now();
      
      this.notifyPlatformChange('reinitialized', {
        platform: this.platform,
        ready: this.state.ready
      });
      
    } catch (error) {
      console.error('Platform reinitialization failed:', error);
      this.notifyPlatformChange('error', { error: error.message });
    }
  }

  /**
   * Notify other components of platform changes
   */
  notifyPlatformChange(type, data) {
    const event = new CustomEvent('webottr-platform-change', {
      detail: { type, data, platform: this.platform, timestamp: Date.now() }
    });
    document.dispatchEvent(event);
  }

  /**
   * Get platform-specific element
   */
  getElement(selectorName) {
    if (!this.config || !this.config.selectors[selectorName]) {
      return null;
    }
    
    return document.querySelector(this.config.selectors[selectorName]);
  }

  /**
   * Get all platform-specific elements
   */
  getElements(selectorName) {
    if (!this.config || !this.config.selectors[selectorName]) {
      return [];
    }
    
    return Array.from(document.querySelectorAll(this.config.selectors[selectorName]));
  }

  /**
   * Check if platform supports feature
   */
  supportsFeature(featureName) {
    return this.config && this.config.features[featureName] === true;
  }

  /**
   * Get platform status
   */
  getStatus() {
    return {
      platform: this.platform,
      detected: this.state.detected,
      ready: this.state.ready,
      lastCheck: this.state.lastCheck,
      config: this.config,
      elements: this.config ? {
        messageContainer: !!this.getElement('messageContainer'),
        messageInput: !!this.getElement('messageInput'),
        toolbar: !!this.getElement('toolbar')
      } : null
    };
  }

  /**
   * Cleanup platform detector
   */
  cleanup() {
    // Disconnect all observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    
    this.state.ready = false;
    this.state.detected = false;
  }
}

// Initialize platform detector
const platformDetector = new PlatformDetector();

// Export for use by other components
window.webottrPlatformDetector = platformDetector;
