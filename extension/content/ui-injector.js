/**
 * WebOTR UI Injector
 * 
 * Seamlessly integrates WebOTR UI components into chat platforms:
 * - Platform-specific UI injection points
 * - Native-looking security controls
 * - Real-time encryption status indicators
 * - Contextual security information
 */

class UIInjector {
  constructor(platformDetector) {
    this.platformDetector = platformDetector;
    this.platform = null;
    this.config = null;
    
    this.state = {
      initialized: false,
      injected: false,
      componentsActive: 0
    };
    
    this.injectedComponents = new Map();
    this.styleSheets = [];
    this.observers = [];
    
    // UI component templates
    this.templates = this.createUITemplates();
  }

  /**
   * Initialize UI injector
   */
  async initialize() {
    try {
      // Wait for platform detection
      if (!this.platformDetector.state.ready) {
        await this.waitForPlatformReady();
      }
      
      this.platform = this.platformDetector.platform;
      this.config = this.platformDetector.config;
      
      // Load platform-specific styles
      await this.loadPlatformStyles();
      
      // Inject UI components
      await this.injectUIComponents();
      
      // Set up dynamic UI monitoring
      this.setupUIMonitoring();
      
      this.state.initialized = true;
      this.state.injected = true;
      
      console.log(`WebOTR: UI injector initialized for ${this.platform}`);
      
      return {
        platform: this.platform,
        initialized: this.state.initialized,
        componentsActive: this.state.componentsActive
      };
      
    } catch (error) {
      console.error('UI injector initialization failed:', error);
      throw error;
    }
  }

  /**
   * Wait for platform to be ready
   */
  async waitForPlatformReady() {
    return new Promise((resolve) => {
      const checkReady = () => {
        if (this.platformDetector.state.ready) {
          resolve();
        } else {
          setTimeout(checkReady, 100);
        }
      };
      checkReady();
    });
  }

  /**
   * Create UI component templates
   */
  createUITemplates() {
    return {
      encryptionToggle: {
        html: `
          <div class="webottr-encryption-toggle" title="Toggle WebOTR Encryption">
            <button class="webottr-toggle-btn" data-webottr-action="toggle-encryption">
              <span class="webottr-icon">🔒</span>
              <span class="webottr-status">ON</span>
            </button>
          </div>
        `,
        events: {
          'click [data-webottr-action="toggle-encryption"]': 'handleEncryptionToggle'
        }
      },
      
      securityIndicator: {
        html: `
          <div class="webottr-security-indicator" title="WebOTR Security Status">
            <span class="webottr-security-icon">🛡️</span>
            <span class="webottr-security-text">Secure</span>
          </div>
        `,
        events: {
          'click': 'showSecurityDetails'
        }
      },
      
      messageStatus: {
        html: `
          <div class="webottr-message-status" title="Message Encryption Status">
            <span class="webottr-status-icon">🔐</span>
          </div>
        `,
        events: {}
      },
      
      keyRotationIndicator: {
        html: `
          <div class="webottr-key-rotation" title="Key Rotation Status">
            <span class="webottr-rotation-icon">🔄</span>
            <span class="webottr-rotation-text">Keys Fresh</span>
          </div>
        `,
        events: {
          'click': 'showKeyRotationInfo'
        }
      },
      
      contactVerification: {
        html: `
          <div class="webottr-contact-verification" title="Contact Verification">
            <span class="webottr-verification-icon">✅</span>
            <span class="webottr-verification-text">Verified</span>
          </div>
        `,
        events: {
          'click': 'showVerificationDetails'
        }
      },
      
      settingsButton: {
        html: `
          <div class="webottr-settings-btn" title="WebOTR Settings">
            <button class="webottr-settings" data-webottr-action="open-settings">
              <span class="webottr-icon">⚙️</span>
            </button>
          </div>
        `,
        events: {
          'click [data-webottr-action="open-settings"]': 'openSettings'
        }
      }
    };
  }

  /**
   * Load platform-specific styles
   */
  async loadPlatformStyles() {
    // Load base WebOTR styles
    await this.loadStyleSheet('webottr-base.css');
    
    // Load platform-specific styles
    await this.loadStyleSheet(`webottr-${this.platform}.css`);
    
    // Inject dynamic styles for better integration
    this.injectDynamicStyles();
  }

  /**
   * Load CSS stylesheet
   */
  async loadStyleSheet(filename) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.type = 'text/css';
    link.href = chrome.runtime.getURL(`content/styles/${filename}`);
    link.className = 'webottr-stylesheet';
    
    document.head.appendChild(link);
    this.styleSheets.push(link);
    
    // Wait for stylesheet to load
    return new Promise((resolve) => {
      link.onload = resolve;
      link.onerror = resolve; // Continue even if stylesheet fails to load
    });
  }

  /**
   * Inject dynamic styles for platform integration
   */
  injectDynamicStyles() {
    const style = document.createElement('style');
    style.className = 'webottr-dynamic-styles';
    
    const platformStyles = this.getPlatformSpecificStyles();
    style.textContent = platformStyles;
    
    document.head.appendChild(style);
    this.styleSheets.push(style);
  }

  /**
   * Get platform-specific CSS styles
   */
  getPlatformSpecificStyles() {
    const baseStyles = `
      .webottr-component {
        font-family: inherit;
        font-size: inherit;
        color: inherit;
        background: transparent;
        border: none;
        outline: none;
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      .webottr-encryption-toggle {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 4px;
        background: rgba(0, 255, 0, 0.1);
        border: 1px solid rgba(0, 255, 0, 0.3);
      }
      
      .webottr-encryption-toggle.disabled {
        background: rgba(255, 0, 0, 0.1);
        border-color: rgba(255, 0, 0, 0.3);
      }
      
      .webottr-security-indicator {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        padding: 2px 6px;
        border-radius: 3px;
        background: rgba(0, 255, 0, 0.1);
        font-size: 0.8em;
      }
      
      .webottr-message-status {
        position: absolute;
        top: 2px;
        right: 2px;
        font-size: 0.7em;
        opacity: 0.7;
      }
      
      .webottr-decrypted-message {
        position: relative;
        padding-left: 20px;
      }
      
      .webottr-decrypted-indicator {
        position: absolute;
        left: 0;
        top: 0;
        font-size: 0.8em;
        opacity: 0.7;
      }
    `;
    
    const platformSpecificStyles = {
      discord: `
        .webottr-encryption-toggle {
          margin-left: 8px;
          height: 32px;
        }
        
        .webottr-security-indicator {
          margin-right: 8px;
        }
      `,
      
      slack: `
        .webottr-encryption-toggle {
          margin-left: 4px;
          height: 28px;
        }
        
        .webottr-security-indicator {
          margin-right: 4px;
        }
      `,
      
      teams: `
        .webottr-encryption-toggle {
          margin-left: 6px;
          height: 30px;
        }
        
        .webottr-security-indicator {
          margin-right: 6px;
        }
      `,
      
      whatsapp: `
        .webottr-encryption-toggle {
          margin-left: 8px;
          height: 36px;
        }
        
        .webottr-security-indicator {
          margin-right: 8px;
        }
      `,
      
      telegram: `
        .webottr-encryption-toggle {
          margin-left: 6px;
          height: 34px;
        }
        
        .webottr-security-indicator {
          margin-right: 6px;
        }
      `,
      
      element: `
        .webottr-encryption-toggle {
          margin-left: 8px;
          height: 32px;
        }
        
        .webottr-security-indicator {
          margin-right: 8px;
        }
      `
    };
    
    return baseStyles + (platformSpecificStyles[this.platform] || '');
  }

  /**
   * Inject UI components into platform
   */
  async injectUIComponents() {
    const injectionPoint = this.config.integration.injectLocation;
    
    switch (injectionPoint) {
      case 'toolbar':
        await this.injectIntoToolbar();
        break;
      case 'compose':
        await this.injectIntoCompose();
        break;
      case 'composer':
        await this.injectIntoComposer();
        break;
      default:
        await this.injectIntoToolbar();
    }
    
    // Inject message status indicators
    this.injectMessageStatusIndicators();
    
    // Inject security indicators
    this.injectSecurityIndicators();
  }

  /**
   * Inject components into toolbar area
   */
  async injectIntoToolbar() {
    const toolbar = this.platformDetector.getElement('toolbar');
    
    if (!toolbar) {
      console.warn(`WebOTR: Toolbar not found for ${this.platform}`);
      return;
    }
    
    // Create WebOTR toolbar container
    const webottrToolbar = this.createComponent('webottr-toolbar', `
      <div class="webottr-toolbar-container">
        ${this.templates.encryptionToggle.html}
        ${this.templates.securityIndicator.html}
        ${this.templates.keyRotationIndicator.html}
        ${this.templates.settingsButton.html}
      </div>
    `);
    
    // Insert into toolbar
    toolbar.appendChild(webottrToolbar);
    
    // Register component
    this.registerComponent('toolbar', webottrToolbar, {
      ...this.templates.encryptionToggle.events,
      ...this.templates.securityIndicator.events,
      ...this.templates.keyRotationIndicator.events,
      ...this.templates.settingsButton.events
    });
  }

  /**
   * Inject components into compose area
   */
  async injectIntoCompose() {
    const composeArea = this.platformDetector.getElement('messageInput')?.parentElement;
    
    if (!composeArea) {
      console.warn(`WebOTR: Compose area not found for ${this.platform}`);
      return;
    }
    
    // Create WebOTR compose controls
    const webottrCompose = this.createComponent('webottr-compose', `
      <div class="webottr-compose-controls">
        ${this.templates.encryptionToggle.html}
        ${this.templates.securityIndicator.html}
      </div>
    `);
    
    // Insert into compose area
    composeArea.appendChild(webottrCompose);
    
    this.registerComponent('compose', webottrCompose, {
      ...this.templates.encryptionToggle.events,
      ...this.templates.securityIndicator.events
    });
  }

  /**
   * Inject components into composer (Element-style)
   */
  async injectIntoComposer() {
    const composer = this.platformDetector.getElement('toolbar');
    
    if (!composer) {
      console.warn(`WebOTR: Composer not found for ${this.platform}`);
      return;
    }
    
    // Create WebOTR composer controls
    const webottrComposer = this.createComponent('webottr-composer', `
      <div class="webottr-composer-controls">
        ${this.templates.encryptionToggle.html}
        ${this.templates.securityIndicator.html}
      </div>
    `);
    
    composer.appendChild(webottrComposer);
    
    this.registerComponent('composer', webottrComposer, {
      ...this.templates.encryptionToggle.events,
      ...this.templates.securityIndicator.events
    });
  }

  /**
   * Inject message status indicators
   */
  injectMessageStatusIndicators() {
    // This will be called for each message to add encryption status
    const messageElements = this.platformDetector.getElements('messageItem');
    
    messageElements.forEach(messageElement => {
      this.addMessageStatusIndicator(messageElement);
    });
  }

  /**
   * Add status indicator to a specific message
   */
  addMessageStatusIndicator(messageElement) {
    // Check if already has indicator
    if (messageElement.querySelector('.webottr-message-status')) {
      return;
    }
    
    // Create status indicator
    const statusIndicator = this.createComponent('webottr-message-status', 
      this.templates.messageStatus.html
    );
    
    // Position relative to message
    messageElement.style.position = 'relative';
    messageElement.appendChild(statusIndicator);
  }

  /**
   * Inject security indicators
   */
  injectSecurityIndicators() {
    // Add security indicators to appropriate locations
    const userAvatars = this.platformDetector.getElements('userAvatar');
    
    userAvatars.forEach(avatar => {
      this.addContactVerificationIndicator(avatar);
    });
  }

  /**
   * Add contact verification indicator
   */
  addContactVerificationIndicator(avatarElement) {
    // Check if already has indicator
    if (avatarElement.querySelector('.webottr-contact-verification')) {
      return;
    }
    
    // Create verification indicator
    const verificationIndicator = this.createComponent('webottr-contact-verification',
      this.templates.contactVerification.html
    );
    
    // Position relative to avatar
    avatarElement.style.position = 'relative';
    avatarElement.appendChild(verificationIndicator);
    
    this.registerComponent(`verification-${Date.now()}`, verificationIndicator, 
      this.templates.contactVerification.events
    );
  }

  /**
   * Create UI component element
   */
  createComponent(className, html) {
    const component = document.createElement('div');
    component.className = `webottr-component ${className}`;
    component.innerHTML = html;
    return component;
  }

  /**
   * Register component with event handlers
   */
  registerComponent(name, element, events) {
    this.injectedComponents.set(name, {
      element,
      events
    });
    
    // Attach event handlers
    Object.entries(events).forEach(([selector, handler]) => {
      const [event, targetSelector] = selector.split(' ', 2);
      const targets = targetSelector ? 
        element.querySelectorAll(targetSelector) : 
        [element];
      
      targets.forEach(target => {
        target.addEventListener(event, (e) => {
          this[handler]?.(e, target, element);
        });
      });
    });
    
    this.state.componentsActive++;
  }

  /**
   * Setup UI monitoring for dynamic changes
   */
  setupUIMonitoring() {
    // Monitor for new messages to add status indicators
    const messageContainer = this.platformDetector.getElement('messageContainer');
    
    if (messageContainer) {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.handleNewContent(node);
            }
          });
        });
      });
      
      observer.observe(messageContainer, {
        childList: true,
        subtree: true
      });
      
      this.observers.push(observer);
    }
    
    // Listen for platform changes
    document.addEventListener('webottr-platform-change', (event) => {
      if (event.detail.type === 'reinitialized') {
        this.reinitialize();
      }
    });
  }

  /**
   * Handle new content added to the page
   */
  handleNewContent(element) {
    // Check if it's a message element
    if (this.isMessageElement(element)) {
      this.addMessageStatusIndicator(element);
    }
    
    // Check for user avatars
    const avatars = element.querySelectorAll(this.config.selectors.userAvatar);
    avatars.forEach(avatar => {
      this.addContactVerificationIndicator(avatar);
    });
  }

  /**
   * Check if element is a message element
   */
  isMessageElement(element) {
    return element.matches && element.matches(this.config.selectors.messageItem);
  }

  /**
   * Event handlers for UI components
   */
  handleEncryptionToggle(event, target, component) {
    event.preventDefault();
    
    // Toggle encryption state
    const messageInterceptor = window.webottrMessageInterceptor;
    if (messageInterceptor) {
      const enabled = messageInterceptor.toggleEncryption();
      this.updateEncryptionToggleUI(component, enabled);
    }
  }

  showSecurityDetails(event, target, component) {
    // Show security status popup
    console.log('WebOTR: Showing security details...');
  }

  showKeyRotationInfo(event, target, component) {
    // Show key rotation information
    console.log('WebOTR: Showing key rotation info...');
  }

  showVerificationDetails(event, target, component) {
    // Show contact verification details
    console.log('WebOTR: Showing verification details...');
  }

  openSettings(event, target, component) {
    // Open WebOTR settings
    console.log('WebOTR: Opening settings...');
  }

  /**
   * Update encryption toggle UI
   */
  updateEncryptionToggleUI(component, enabled) {
    const toggleBtn = component.querySelector('.webottr-toggle-btn');
    const statusText = component.querySelector('.webottr-status');
    const icon = component.querySelector('.webottr-icon');
    
    if (enabled) {
      toggleBtn.classList.remove('disabled');
      statusText.textContent = 'ON';
      icon.textContent = '🔒';
      component.title = 'WebOTR Encryption Enabled - Click to disable';
    } else {
      toggleBtn.classList.add('disabled');
      statusText.textContent = 'OFF';
      icon.textContent = '🔓';
      component.title = 'WebOTR Encryption Disabled - Click to enable';
    }
  }

  /**
   * Update security indicator
   */
  updateSecurityIndicator(status) {
    const indicators = document.querySelectorAll('.webottr-security-indicator');
    
    indicators.forEach(indicator => {
      const icon = indicator.querySelector('.webottr-security-icon');
      const text = indicator.querySelector('.webottr-security-text');
      
      switch (status) {
        case 'secure':
          icon.textContent = '🛡️';
          text.textContent = 'Secure';
          indicator.style.background = 'rgba(0, 255, 0, 0.1)';
          break;
        case 'warning':
          icon.textContent = '⚠️';
          text.textContent = 'Warning';
          indicator.style.background = 'rgba(255, 255, 0, 0.1)';
          break;
        case 'insecure':
          icon.textContent = '🚨';
          text.textContent = 'Insecure';
          indicator.style.background = 'rgba(255, 0, 0, 0.1)';
          break;
      }
    });
  }

  /**
   * Reinitialize UI injector
   */
  async reinitialize() {
    console.log('WebOTR: Reinitializing UI injector...');
    
    // Cleanup existing components
    this.cleanup();
    
    // Reinitialize
    await this.initialize();
  }

  /**
   * Get UI injector status
   */
  getStatus() {
    return {
      platform: this.platform,
      initialized: this.state.initialized,
      injected: this.state.injected,
      componentsActive: this.state.componentsActive,
      components: Array.from(this.injectedComponents.keys())
    };
  }

  /**
   * Cleanup UI injector
   */
  cleanup() {
    // Remove injected components
    this.injectedComponents.forEach(({ element }) => {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
    });
    this.injectedComponents.clear();
    
    // Remove stylesheets
    this.styleSheets.forEach(sheet => {
      if (sheet.parentNode) {
        sheet.parentNode.removeChild(sheet);
      }
    });
    this.styleSheets = [];
    
    // Disconnect observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    
    this.state.initialized = false;
    this.state.injected = false;
    this.state.componentsActive = 0;
  }
}

// Export for use by other components
window.webottrUIInjector = UIInjector;
