/**
 * WebOTR Content Script
 * 
 * Main content script that coordinates all WebOTR components:
 * - Platform detection and adaptation
 * - Message interception and processing
 * - UI injection and management
 * - Communication with background script
 */

class WebOTRContentScript {
  constructor() {
    this.state = {
      initialized: false,
      active: false,
      platform: null,
      startTime: Date.now()
    };
    
    this.components = {
      platformDetector: null,
      messageInterceptor: null,
      uiInjector: null,
      securityIntegration: null
    };
    
    this.metrics = {
      initializationTime: 0,
      messagesProcessed: 0,
      errorsEncountered: 0
    };
    
    this.eventHandlers = new Map();
  }

  /**
   * Initialize WebOTR content script
   */
  async initialize() {
    const startTime = performance.now();
    
    try {
      console.log('WebOTR: Initializing content script...');
      
      // Wait for DOM to be ready
      await this.waitForDOMReady();
      
      // Initialize platform detector
      await this.initializePlatformDetector();
      
      // Initialize message interceptor
      await this.initializeMessageInterceptor();
      
      // Initialize UI injector
      await this.initializeUIInjector();

      // Initialize security integration
      await this.initializeSecurityIntegration();

      // Set up component communication
      this.setupComponentCommunication();
      
      // Set up background communication
      this.setupBackgroundCommunication();
      
      // Set up error handling
      this.setupErrorHandling();
      
      this.state.initialized = true;
      this.state.active = true;
      this.state.platform = this.components.platformDetector.platform;
      
      this.metrics.initializationTime = performance.now() - startTime;
      
      console.log(`WebOTR: Content script initialized for ${this.state.platform} in ${this.metrics.initializationTime.toFixed(2)}ms`);
      
      // Notify background script
      this.notifyBackgroundScript('initialized', {
        platform: this.state.platform,
        initializationTime: this.metrics.initializationTime
      });
      
      return {
        success: true,
        platform: this.state.platform,
        initializationTime: this.metrics.initializationTime
      };
      
    } catch (error) {
      console.error('WebOTR: Content script initialization failed:', error);
      this.metrics.errorsEncountered++;
      
      this.notifyBackgroundScript('initialization-failed', {
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Wait for DOM to be ready
   */
  async waitForDOMReady() {
    if (document.readyState === 'loading') {
      return new Promise(resolve => {
        document.addEventListener('DOMContentLoaded', resolve, { once: true });
      });
    }
  }

  /**
   * Initialize platform detector
   */
  async initializePlatformDetector() {
    console.log('WebOTR: Initializing platform detector...');
    
    // Platform detector should already be available from platform-detector.js
    this.components.platformDetector = window.webottrPlatformDetector;
    
    if (!this.components.platformDetector) {
      throw new Error('Platform detector not available');
    }
    
    await this.components.platformDetector.initialize();
    
    console.log(`WebOTR: Platform detector initialized for ${this.components.platformDetector.platform}`);
  }

  /**
   * Initialize message interceptor
   */
  async initializeMessageInterceptor() {
    console.log('WebOTR: Initializing message interceptor...');
    
    // Create message interceptor instance
    this.components.messageInterceptor = new window.webottrMessageInterceptor(
      this.components.platformDetector
    );
    
    await this.components.messageInterceptor.initialize();
    
    console.log('WebOTR: Message interceptor initialized');
  }

  /**
   * Initialize UI injector
   */
  async initializeUIInjector() {
    console.log('WebOTR: Initializing UI injector...');

    // Create UI injector instance
    this.components.uiInjector = new window.webottrUIInjector(
      this.components.platformDetector
    );

    await this.components.uiInjector.initialize();

    console.log('WebOTR: UI injector initialized');
  }

  /**
   * Initialize security integration
   */
  async initializeSecurityIntegration() {
    console.log('WebOTR: Initializing security integration...');

    // Import SecurityIntegration class
    const { SecurityIntegration } = await import('../core/SecurityIntegration.js');

    // Create security integration instance
    this.components.securityIntegration = new SecurityIntegration(null); // Framework will be set up later

    // Initialize with platform information
    await this.components.securityIntegration.initialize(
      this.components.platformDetector.platform,
      null // Contact ID will be determined later
    );

    // Make available globally for message interceptor
    window.webottrSecurityIntegration = this.components.securityIntegration;

    console.log('WebOTR: Security integration initialized');
  }

  /**
   * Setup communication between components
   */
  setupComponentCommunication() {
    // Listen for platform changes
    document.addEventListener('webottr-platform-change', (event) => {
      this.handlePlatformChange(event.detail);
    });
    
    // Set up message processing coordination
    this.setupMessageProcessingCoordination();
    
    // Set up UI update coordination
    this.setupUIUpdateCoordination();
  }

  /**
   * Setup message processing coordination
   */
  setupMessageProcessingCoordination() {
    // This will coordinate between message interceptor and encryption/decryption
    // For now, we'll set up basic event handling
    
    this.eventHandlers.set('message-encrypted', (data) => {
      this.metrics.messagesProcessed++;
      this.updateUIStatus('message-encrypted', data);
    });
    
    this.eventHandlers.set('message-decrypted', (data) => {
      this.metrics.messagesProcessed++;
      this.updateUIStatus('message-decrypted', data);
    });
    
    this.eventHandlers.set('encryption-error', (data) => {
      this.metrics.errorsEncountered++;
      this.updateUIStatus('encryption-error', data);
    });
  }

  /**
   * Setup UI update coordination
   */
  setupUIUpdateCoordination() {
    // Coordinate UI updates based on system state
    
    this.eventHandlers.set('encryption-toggled', (data) => {
      this.components.uiInjector.updateEncryptionToggleUI(null, data.enabled);
    });
    
    this.eventHandlers.set('security-status-changed', (data) => {
      this.components.uiInjector.updateSecurityIndicator(data.status);
    });
  }

  /**
   * Setup communication with background script
   */
  setupBackgroundCommunication() {
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleBackgroundMessage(message, sender)
        .then(response => sendResponse(response))
        .catch(error => sendResponse({ error: error.message }));
      
      return true; // Async response
    });
    
    // Set up periodic status updates
    setInterval(() => {
      this.sendStatusUpdate();
    }, 30000); // Every 30 seconds
  }

  /**
   * Handle messages from background script
   */
  async handleBackgroundMessage(message, sender) {
    const { type, data } = message;
    
    switch (type) {
      case 'get-status':
        return this.getStatus();
      
      case 'toggle-encryption':
        return this.toggleEncryption();
      
      case 'get-metrics':
        return this.getMetrics();
      
      case 'reinitialize':
        return this.reinitialize();
      
      case 'update-settings':
        return this.updateSettings(data);
      
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
  }

  /**
   * Handle platform changes
   */
  handlePlatformChange(changeData) {
    console.log('WebOTR: Platform change detected:', changeData);
    
    switch (changeData.type) {
      case 'reinitialized':
        this.state.platform = changeData.platform;
        break;
      
      case 'error':
        this.metrics.errorsEncountered++;
        this.notifyBackgroundScript('platform-error', changeData);
        break;
      
      case 'dom-change':
        // Handle DOM changes that might affect our components
        this.handleDOMChange(changeData.data);
        break;
      
      case 'url-change':
        // Handle URL changes in SPAs
        this.handleURLChange(changeData.data);
        break;
    }
  }

  /**
   * Handle DOM changes
   */
  handleDOMChange(mutationData) {
    // Check if our injected components are still present
    const status = this.components.uiInjector.getStatus();
    
    if (status.componentsActive === 0) {
      console.log('WebOTR: UI components lost, reinitializing...');
      this.components.uiInjector.reinitialize();
    }
  }

  /**
   * Handle URL changes
   */
  handleURLChange(urlData) {
    // Update platform state if URL changed significantly
    if (urlData.platform !== this.state.platform) {
      console.log(`WebOTR: Platform changed from ${this.state.platform} to ${urlData.platform}`);
      this.state.platform = urlData.platform;
      
      this.notifyBackgroundScript('platform-changed', {
        oldPlatform: this.state.platform,
        newPlatform: urlData.platform,
        url: urlData.url
      });
    }
  }

  /**
   * Update UI status based on system events
   */
  updateUIStatus(eventType, data) {
    const handler = this.eventHandlers.get(eventType);
    if (handler) {
      handler(data);
    }
  }

  /**
   * Toggle encryption on/off
   */
  toggleEncryption() {
    if (!this.components.messageInterceptor) {
      throw new Error('Message interceptor not available');
    }
    
    const enabled = this.components.messageInterceptor.toggleEncryption();
    
    // Update UI
    this.updateUIStatus('encryption-toggled', { enabled });
    
    // Notify background
    this.notifyBackgroundScript('encryption-toggled', { enabled });
    
    return { enabled };
  }

  /**
   * Update settings
   */
  updateSettings(settings) {
    // Apply settings to components
    console.log('WebOTR: Updating settings:', settings);
    
    // This will be expanded to handle various settings
    return { success: true };
  }

  /**
   * Send status update to background script
   */
  sendStatusUpdate() {
    if (!this.state.active) {
      return;
    }
    
    const status = this.getStatus();
    this.notifyBackgroundScript('status-update', status);
  }

  /**
   * Notify background script
   */
  notifyBackgroundScript(type, data) {
    chrome.runtime.sendMessage({
      type,
      data,
      timestamp: Date.now(),
      source: 'content-script',
      platform: this.state.platform
    }).catch(error => {
      // Background script might not be available
      console.warn('WebOTR: Failed to notify background script:', error);
    });
  }

  /**
   * Setup error handling
   */
  setupErrorHandling() {
    // Global error handler
    window.addEventListener('error', (event) => {
      if (event.error && event.error.message.includes('WebOTR')) {
        this.metrics.errorsEncountered++;
        
        this.notifyBackgroundScript('error', {
          message: event.error.message,
          stack: event.error.stack,
          filename: event.filename,
          lineno: event.lineno
        });
      }
    });
    
    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      if (event.reason && event.reason.message && event.reason.message.includes('WebOTR')) {
        this.metrics.errorsEncountered++;
        
        this.notifyBackgroundScript('unhandled-rejection', {
          reason: event.reason.message,
          stack: event.reason.stack
        });
      }
    });
  }

  /**
   * Reinitialize content script
   */
  async reinitialize() {
    console.log('WebOTR: Reinitializing content script...');
    
    try {
      // Cleanup existing components
      await this.cleanup();
      
      // Reinitialize
      await this.initialize();
      
      return { success: true };
      
    } catch (error) {
      console.error('WebOTR: Reinitialization failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get content script status
   */
  getStatus() {
    return {
      initialized: this.state.initialized,
      active: this.state.active,
      platform: this.state.platform,
      uptime: Date.now() - this.state.startTime,
      components: {
        platformDetector: this.components.platformDetector?.getStatus(),
        messageInterceptor: this.components.messageInterceptor?.getStatus(),
        uiInjector: this.components.uiInjector?.getStatus()
      },
      metrics: this.metrics
    };
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      uptime: Date.now() - this.state.startTime,
      memoryUsage: performance.memory ? {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize
      } : null
    };
  }

  /**
   * Cleanup content script
   */
  async cleanup() {
    console.log('WebOTR: Cleaning up content script...');
    
    // Cleanup components
    if (this.components.securityIntegration) {
      await this.components.securityIntegration.cleanup();
    }

    if (this.components.uiInjector) {
      this.components.uiInjector.cleanup();
    }

    if (this.components.messageInterceptor) {
      this.components.messageInterceptor.cleanup();
    }

    if (this.components.platformDetector) {
      this.components.platformDetector.cleanup();
    }
    
    // Clear event handlers
    this.eventHandlers.clear();
    
    this.state.initialized = false;
    this.state.active = false;
  }
}

// Initialize WebOTR content script when DOM is ready
(async () => {
  try {
    // Create and initialize content script
    const webottrContentScript = new WebOTRContentScript();
    await webottrContentScript.initialize();
    
    // Make available globally for debugging
    window.webottrContentScript = webottrContentScript;
    
  } catch (error) {
    console.error('WebOTR: Failed to initialize content script:', error);
  }
})();
