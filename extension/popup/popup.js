/**
 * WebOTR Extension Popup
 * 
 * Provides user interface for WebOTR extension:
 * - Real-time status monitoring
 * - Security controls and settings
 * - Performance metrics display
 * - Activity logging and notifications
 */

class WebOTRPopup {
  constructor() {
    this.state = {
      initialized: false,
      currentTab: null,
      contentScriptReady: false,
      lastUpdate: null
    };
    
    this.elements = {};
    this.updateInterval = null;
    this.activityLog = [];
    
    this.maxActivityItems = 10;
  }

  /**
   * Initialize popup
   */
  async initialize() {
    try {
      console.log('WebOTR Popup: Initializing...');
      
      // Get DOM elements
      this.getElements();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Get current tab
      await this.getCurrentTab();
      
      // Initialize UI state
      this.initializeUI();
      
      // Start status updates
      this.startStatusUpdates();
      
      this.state.initialized = true;
      
      console.log('WebOTR Popup: Initialized successfully');
      
    } catch (error) {
      console.error('WebOTR Popup: Initialization failed:', error);
      this.showError('Failed to initialize WebOTR popup');
    }
  }

  /**
   * Get DOM elements
   */
  getElements() {
    this.elements = {
      // Status elements
      statusIndicator: document.getElementById('statusIndicator'),
      statusDot: document.querySelector('.status-dot'),
      statusText: document.querySelector('.status-text'),
      
      // Platform elements
      platformName: document.getElementById('platformName'),
      platformSupport: document.getElementById('platformSupport'),
      
      // Security elements
      encryptionToggle: document.getElementById('encryptionToggle'),
      encryptionStatus: document.getElementById('encryptionStatus'),
      keyGeneration: document.getElementById('keyGeneration'),
      messagesEncrypted: document.getElementById('messagesEncrypted'),
      lastKeyRotation: document.getElementById('lastKeyRotation'),
      
      // Action buttons
      rotateKeysBtn: document.getElementById('rotateKeysBtn'),
      settingsBtn: document.getElementById('settingsBtn'),
      helpBtn: document.getElementById('helpBtn'),
      
      // Metrics elements
      avgEncryptionTime: document.getElementById('avgEncryptionTime'),
      avgDecryptionTime: document.getElementById('avgDecryptionTime'),
      successRate: document.getElementById('successRate'),
      uptime: document.getElementById('uptime'),
      
      // Activity elements
      activityList: document.getElementById('activityList'),
      
      // Overlay and notifications
      loadingOverlay: document.getElementById('loadingOverlay'),
      notificationToast: document.getElementById('notificationToast'),
      toastClose: document.getElementById('toastClose')
    };
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Action button listeners
    this.elements.encryptionToggle.addEventListener('click', () => {
      this.handleEncryptionToggle();
    });
    
    this.elements.rotateKeysBtn.addEventListener('click', () => {
      this.handleKeyRotation();
    });
    
    this.elements.settingsBtn.addEventListener('click', () => {
      this.openSettings();
    });
    
    this.elements.helpBtn.addEventListener('click', () => {
      this.openHelp();
    });
    
    // Toast close listener
    this.elements.toastClose.addEventListener('click', () => {
      this.hideNotification();
    });
    
    // Auto-hide toast after 5 seconds
    setTimeout(() => {
      this.hideNotification();
    }, 5000);
  }

  /**
   * Get current active tab
   */
  async getCurrentTab() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      this.state.currentTab = tabs[0];
      
      console.log('WebOTR Popup: Current tab:', this.state.currentTab?.url);
      
    } catch (error) {
      console.error('WebOTR Popup: Failed to get current tab:', error);
    }
  }

  /**
   * Initialize UI state
   */
  initializeUI() {
    // Show loading overlay
    this.showLoading('Connecting to WebOTR...');
    
    // Set initial status
    this.updateStatus('connecting', 'Connecting...');
    
    // Add initial activity
    this.addActivity('WebOTR popup opened', 'info');
  }

  /**
   * Start status updates
   */
  startStatusUpdates() {
    // Initial update
    this.updateStatus();
    
    // Set up periodic updates
    this.updateInterval = setInterval(() => {
      this.updateStatus();
    }, 2000); // Update every 2 seconds
  }

  /**
   * Update status from content script
   */
  async updateStatus() {
    try {
      if (!this.state.currentTab) {
        this.updatePlatformStatus('unknown', false);
        this.updateSecurityStatus(null);
        return;
      }
      
      // Send message to content script
      const response = await chrome.tabs.sendMessage(this.state.currentTab.id, {
        type: 'get-status'
      });
      
      if (response && response.initialized) {
        this.state.contentScriptReady = true;
        this.hideLoading();
        
        // Update platform status
        this.updatePlatformStatus(response.platform, true);
        
        // Update security status
        this.updateSecurityStatus(response);
        
        // Update metrics
        this.updateMetrics(response.metrics);
        
        // Update overall status
        this.updateStatus('connected', 'Connected');
        
        this.state.lastUpdate = Date.now();
        
      } else {
        // Content script not ready
        this.updateStatus('disconnected', 'Not Connected');
        this.updatePlatformStatus('unknown', false);
      }
      
    } catch (error) {
      // Content script not available
      this.updateStatus('disconnected', 'Not Available');
      this.updatePlatformStatus('unknown', false);
      
      if (this.state.currentTab && this.isSupportedPlatform(this.state.currentTab.url)) {
        this.showInjectButton();
      }
    }
  }

  /**
   * Update overall status indicator
   */
  updateStatus(status, text) {
    this.elements.statusDot.className = `status-dot ${status}`;
    this.elements.statusText.textContent = text;
  }

  /**
   * Update platform status
   */
  updatePlatformStatus(platform, supported) {
    const platformNames = {
      discord: 'Discord',
      slack: 'Slack',
      teams: 'Microsoft Teams',
      whatsapp: 'WhatsApp Web',
      telegram: 'Telegram Web',
      element: 'Element',
      unknown: 'Unknown Platform'
    };
    
    this.elements.platformName.textContent = platformNames[platform] || 'Unknown Platform';
    this.elements.platformSupport.textContent = supported ? 'Supported' : 'Not Supported';
    this.elements.platformSupport.className = `platform-support ${supported ? 'supported' : 'unsupported'}`;
  }

  /**
   * Update security status
   */
  updateSecurityStatus(status) {
    if (!status) {
      this.elements.encryptionStatus.textContent = 'OFF';
      this.elements.encryptionToggle.classList.remove('enabled');
      this.elements.keyGeneration.textContent = '0';
      this.elements.messagesEncrypted.textContent = '0';
      this.elements.lastKeyRotation.textContent = 'Never';
      return;
    }
    
    // Update encryption status
    const encryptionEnabled = status.components?.messageInterceptor?.encryptionEnabled;
    this.elements.encryptionStatus.textContent = encryptionEnabled ? 'ON' : 'OFF';
    this.elements.encryptionToggle.classList.toggle('enabled', encryptionEnabled);
    
    // Update key generation
    const keyGeneration = status.components?.securityIntegration?.keyGeneration || 0;
    this.elements.keyGeneration.textContent = keyGeneration.toString();
    
    // Update messages encrypted
    const messagesEncrypted = status.components?.messageInterceptor?.metrics?.encryptedMessages || 0;
    this.elements.messagesEncrypted.textContent = messagesEncrypted.toString();
    
    // Update last key rotation (simplified)
    this.elements.lastKeyRotation.textContent = keyGeneration > 0 ? 'Recently' : 'Never';
  }

  /**
   * Update performance metrics
   */
  updateMetrics(metrics) {
    if (!metrics) {
      return;
    }
    
    // Update encryption time
    const avgEncryptionTime = metrics.averageEncryptionTime || 0;
    this.elements.avgEncryptionTime.textContent = `${avgEncryptionTime.toFixed(1)}ms`;
    
    // Update decryption time
    const avgDecryptionTime = metrics.averageDecryptionTime || 0;
    this.elements.avgDecryptionTime.textContent = `${avgDecryptionTime.toFixed(1)}ms`;
    
    // Update success rate
    const successRate = (metrics.successRate || 1) * 100;
    this.elements.successRate.textContent = `${successRate.toFixed(1)}%`;
    
    // Update uptime
    const uptime = metrics.uptime || 0;
    this.elements.uptime.textContent = this.formatUptime(uptime);
  }

  /**
   * Handle encryption toggle
   */
  async handleEncryptionToggle() {
    try {
      if (!this.state.currentTab || !this.state.contentScriptReady) {
        this.showNotification('WebOTR not connected', 'error');
        return;
      }
      
      this.elements.encryptionToggle.disabled = true;
      
      const response = await chrome.tabs.sendMessage(this.state.currentTab.id, {
        type: 'toggle-encryption'
      });
      
      if (response && response.enabled !== undefined) {
        const status = response.enabled ? 'enabled' : 'disabled';
        this.showNotification(`Encryption ${status}`, 'success');
        this.addActivity(`Encryption ${status}`, 'security');
        
        // Update UI immediately
        this.elements.encryptionStatus.textContent = response.enabled ? 'ON' : 'OFF';
        this.elements.encryptionToggle.classList.toggle('enabled', response.enabled);
      }
      
    } catch (error) {
      console.error('WebOTR Popup: Failed to toggle encryption:', error);
      this.showNotification('Failed to toggle encryption', 'error');
    } finally {
      this.elements.encryptionToggle.disabled = false;
    }
  }

  /**
   * Handle key rotation
   */
  async handleKeyRotation() {
    try {
      if (!this.state.currentTab || !this.state.contentScriptReady) {
        this.showNotification('WebOTR not connected', 'error');
        return;
      }
      
      this.elements.rotateKeysBtn.disabled = true;
      this.elements.rotateKeysBtn.textContent = 'Rotating...';
      
      const response = await chrome.tabs.sendMessage(this.state.currentTab.id, {
        type: 'rotate-keys'
      });
      
      if (response && response.success) {
        this.showNotification('Keys rotated successfully', 'success');
        this.addActivity(`Keys rotated (Gen ${response.keyGeneration})`, 'security');
        
        // Update key generation display
        this.elements.keyGeneration.textContent = response.keyGeneration.toString();
        this.elements.lastKeyRotation.textContent = 'Just now';
      } else {
        this.showNotification('Key rotation failed', 'error');
      }
      
    } catch (error) {
      console.error('WebOTR Popup: Failed to rotate keys:', error);
      this.showNotification('Key rotation failed', 'error');
    } finally {
      this.elements.rotateKeysBtn.disabled = false;
      this.elements.rotateKeysBtn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">Rotate Keys</span>';
    }
  }

  /**
   * Open settings page
   */
  openSettings() {
    chrome.runtime.openOptionsPage();
  }

  /**
   * Open help page
   */
  openHelp() {
    chrome.tabs.create({
      url: 'https://github.com/forkrul/webOTteR#readme'
    });
  }

  /**
   * Check if platform is supported
   */
  isSupportedPlatform(url) {
    if (!url) return false;
    
    const supportedDomains = [
      'discord.com',
      'slack.com',
      'teams.microsoft.com',
      'web.whatsapp.com',
      'web.telegram.org',
      'app.element.io'
    ];
    
    return supportedDomains.some(domain => url.includes(domain));
  }

  /**
   * Show inject button for supported platforms
   */
  showInjectButton() {
    // This would show a button to inject the content script
    // For now, just show a notification
    this.showNotification('Click to activate WebOTR on this page', 'info');
  }

  /**
   * Add activity to log
   */
  addActivity(message, type = 'info') {
    const activity = {
      time: new Date().toLocaleTimeString(),
      message,
      type,
      timestamp: Date.now()
    };
    
    this.activityLog.unshift(activity);
    
    // Limit activity log size
    if (this.activityLog.length > this.maxActivityItems) {
      this.activityLog = this.activityLog.slice(0, this.maxActivityItems);
    }
    
    this.updateActivityDisplay();
  }

  /**
   * Update activity display
   */
  updateActivityDisplay() {
    this.elements.activityList.innerHTML = '';
    
    this.activityLog.forEach(activity => {
      const activityElement = document.createElement('div');
      activityElement.className = 'activity-item';
      
      const typeIcons = {
        info: 'ℹ️',
        security: '🔒',
        error: '❌',
        success: '✅'
      };
      
      activityElement.innerHTML = `
        <span class="activity-time">${activity.time}</span>
        <span class="activity-text">${activity.message}</span>
        <span class="activity-status ${activity.type}">${typeIcons[activity.type] || 'ℹ️'}</span>
      `;
      
      this.elements.activityList.appendChild(activityElement);
    });
  }

  /**
   * Show loading overlay
   */
  showLoading(message = 'Loading...') {
    this.elements.loadingOverlay.style.display = 'flex';
    this.elements.loadingOverlay.querySelector('.loading-text').textContent = message;
  }

  /**
   * Hide loading overlay
   */
  hideLoading() {
    this.elements.loadingOverlay.style.display = 'none';
  }

  /**
   * Show notification toast
   */
  showNotification(message, type = 'info') {
    const toast = this.elements.notificationToast;
    const icon = toast.querySelector('.toast-icon');
    const messageEl = toast.querySelector('.toast-message');
    
    const typeIcons = {
      info: 'ℹ️',
      success: '✅',
      error: '❌',
      warning: '⚠️'
    };
    
    icon.textContent = typeIcons[type] || 'ℹ️';
    messageEl.textContent = message;
    
    toast.className = `notification-toast ${type} show`;
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
      this.hideNotification();
    }, 3000);
  }

  /**
   * Hide notification toast
   */
  hideNotification() {
    this.elements.notificationToast.classList.remove('show');
  }

  /**
   * Show error message
   */
  showError(message) {
    this.showNotification(message, 'error');
    this.addActivity(message, 'error');
  }

  /**
   * Format uptime duration
   */
  formatUptime(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Cleanup popup
   */
  cleanup() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
  const popup = new WebOTRPopup();
  await popup.initialize();
  
  // Cleanup on window unload
  window.addEventListener('beforeunload', () => {
    popup.cleanup();
  });
});
