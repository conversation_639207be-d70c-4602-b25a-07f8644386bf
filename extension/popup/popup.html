<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebOTR - Secure Messaging</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="webottr-popup">
        <!-- Header -->
        <header class="popup-header">
            <div class="logo">
                <img src="../icons/webottr-32.png" alt="WebOTR" class="logo-icon">
                <h1 class="logo-text">WebOTR</h1>
            </div>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">Initializing...</span>
            </div>
        </header>

        <!-- Main Content -->
        <main class="popup-content">
            <!-- Platform Status -->
            <section class="platform-status" id="platformStatus">
                <h2>Platform Status</h2>
                <div class="platform-info">
                    <div class="platform-name" id="platformName">Unknown Platform</div>
                    <div class="platform-support" id="platformSupport">Not Supported</div>
                </div>
            </section>

            <!-- Security Status -->
            <section class="security-status" id="securityStatus">
                <h2>Security Status</h2>
                <div class="security-grid">
                    <div class="security-item">
                        <span class="security-label">Encryption</span>
                        <div class="security-toggle">
                            <button class="toggle-btn" id="encryptionToggle" data-action="toggle-encryption">
                                <span class="toggle-slider"></span>
                            </button>
                            <span class="toggle-status" id="encryptionStatus">OFF</span>
                        </div>
                    </div>
                    
                    <div class="security-item">
                        <span class="security-label">Key Generation</span>
                        <span class="security-value" id="keyGeneration">0</span>
                    </div>
                    
                    <div class="security-item">
                        <span class="security-label">Messages Encrypted</span>
                        <span class="security-value" id="messagesEncrypted">0</span>
                    </div>
                    
                    <div class="security-item">
                        <span class="security-label">Last Key Rotation</span>
                        <span class="security-value" id="lastKeyRotation">Never</span>
                    </div>
                </div>
            </section>

            <!-- Actions -->
            <section class="actions" id="actions">
                <h2>Actions</h2>
                <div class="action-buttons">
                    <button class="action-btn primary" id="rotateKeysBtn" data-action="rotate-keys">
                        <span class="btn-icon">🔄</span>
                        <span class="btn-text">Rotate Keys</span>
                    </button>
                    
                    <button class="action-btn secondary" id="settingsBtn" data-action="open-settings">
                        <span class="btn-icon">⚙️</span>
                        <span class="btn-text">Settings</span>
                    </button>
                    
                    <button class="action-btn secondary" id="helpBtn" data-action="open-help">
                        <span class="btn-icon">❓</span>
                        <span class="btn-text">Help</span>
                    </button>
                </div>
            </section>

            <!-- Performance Metrics -->
            <section class="metrics" id="metrics">
                <h2>Performance</h2>
                <div class="metrics-grid">
                    <div class="metric-item">
                        <span class="metric-label">Avg Encryption Time</span>
                        <span class="metric-value" id="avgEncryptionTime">0ms</span>
                    </div>
                    
                    <div class="metric-item">
                        <span class="metric-label">Avg Decryption Time</span>
                        <span class="metric-value" id="avgDecryptionTime">0ms</span>
                    </div>
                    
                    <div class="metric-item">
                        <span class="metric-label">Success Rate</span>
                        <span class="metric-value" id="successRate">100%</span>
                    </div>
                    
                    <div class="metric-item">
                        <span class="metric-label">Uptime</span>
                        <span class="metric-value" id="uptime">0s</span>
                    </div>
                </div>
            </section>

            <!-- Recent Activity -->
            <section class="activity" id="activity">
                <h2>Recent Activity</h2>
                <div class="activity-list" id="activityList">
                    <div class="activity-item">
                        <span class="activity-time">--:--</span>
                        <span class="activity-text">WebOTR initialized</span>
                        <span class="activity-status success">✓</span>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="popup-footer">
            <div class="footer-info">
                <span class="version">v1.0.0</span>
                <span class="separator">•</span>
                <a href="#" class="footer-link" id="aboutLink">About</a>
                <span class="separator">•</span>
                <a href="#" class="footer-link" id="privacyLink">Privacy</a>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Initializing WebOTR...</div>
    </div>

    <!-- Notification Toast -->
    <div class="notification-toast" id="notificationToast">
        <div class="toast-content">
            <span class="toast-icon"></span>
            <span class="toast-message"></span>
        </div>
        <button class="toast-close" id="toastClose">×</button>
    </div>

    <script src="popup.js"></script>
</body>
</html>
