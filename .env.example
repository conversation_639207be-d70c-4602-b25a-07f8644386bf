# Application Settings
APP_NAME=generic_project
NODE_ENV=development
PORT=3000
LOG_LEVEL=debug

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=generic_db
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false

# API Keys and Secrets
API_KEY=your_api_key_here
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRATION=86400

# External Services
REDIS_URL=redis://localhost:6379
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=<EMAIL>

# Feature Flags
FEATURE_NEW_UI=false
FEATURE_ANALYTICS=true

# Monitoring and Logging
SENTRY_DSN=https://your_sentry_dsn
DATADOG_API_KEY=your_datadog_api_key

# Paths and URLs
API_BASE_URL=http://localhost:3000/api
FRONTEND_URL=http://localhost:8080
STORAGE_PATH=./storage

# Docker Settings
DOCKER_REGISTRY=your-registry.example.com
DOCKER_IMAGE_NAME=generic-project
DOCKER_IMAGE_TAG=latest

# Note: Copy this file to .env and update with your actual values
# DO NOT commit the actual .env file to version control 