# WebOTR Project Structure

This document outlines the cleaned and refactored project structure.

## Root Directory
```
webOTR/
├── config/                     # All configuration files
│   ├── jest.config.js         # Main Jest configuration
│   ├── jest.*.config.js       # Specialized Jest configs
│   ├── webpack.config.js      # Main webpack config (extension dev)
│   ├── webpack.config.dev.js  # Development webpack config
│   ├── webpack.config.prod.js # Production webpack config (web app)
│   ├── webpack.config.extension.prod.js # Extension production build
│   └── playwright.config.js   # E2E testing configuration
├── docs/                      # Documentation
├── extension/                 # Browser extension source (consolidated)
│   ├── background/           # Service worker
│   ├── content/              # Content scripts
│   ├── popup/                # Extension popup
│   ├── core/                 # Extension-specific core logic
│   └── manifest.json         # Extension manifest
├── src/                      # Core application source
│   ├── core/                 # OTR protocol implementation
│   ├── platforms/            # Platform integrations
│   ├── ui/                   # UI components
│   └── index.js              # Main entry point
├── tests/                    # Test files
├── scripts/                  # Build and setup scripts
├── test-chat-sim/           # Test chat simulator
└── package.json             # Dependencies and scripts
```

## Key Changes Made

### 1. **Consolidated Extension Structure**
- Removed duplicate `/src/extension` directory
- Kept the more comprehensive `/extension` directory structure
- Updated all webpack configs to use the consolidated structure

### 2. **Configuration Organization**
- Moved all config files to `/config` directory
- Updated package.json scripts to reference new locations
- Fixed path references in all config files

### 3. **Build System Improvements**
- Created missing `webpack.config.dev.js`
- Added dedicated extension production build config
- Fixed Jest configuration duplicates
- Added proper module aliases for cleaner imports

### 4. **Entry Points**
- Created proper `src/index.js` main entry point
- Updated package.json to reference correct main file
- Added extension-specific build targets

## Available Scripts

```bash
# Development
npm run dev                    # Start development server
npm run build                 # Build web application
npm run build:extension       # Build browser extension

# Testing
npm test                      # Run unit tests
npm run test:watch           # Run tests in watch mode
npm run test:coverage        # Run tests with coverage
npm run test:integration     # Run integration tests
npm run test:e2e             # Run end-to-end tests

# Code Quality
npm run lint                 # Lint code
npm run lint:fix             # Fix linting issues
npm run format               # Format code
npm run validate             # Run all quality checks

# Utilities
npm run clean                # Clean build artifacts
npm run serve                # Serve built application
```

## Module Aliases

The following aliases are available for cleaner imports:

- `@` → `src/`
- `@extension` → `extension/`
- `@core` → `src/core/`
- `@ui` → `src/ui/`
- `@platforms` → `src/platforms/`

## Next Steps

1. **Test the build system**: Run `npm run build:extension` to ensure extension builds correctly
2. **Update imports**: Use the new module aliases in existing code
3. **Documentation**: Update any documentation that references old paths
4. **CI/CD**: Update any CI/CD configurations to use new script names
