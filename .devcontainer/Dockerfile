# Use the official Node.js 20 image as base
FROM node:20-bullseye

# Avoid warnings by switching to noninteractive
ENV DEBIAN_FRONTEND=noninteractive

# Configure apt and install packages
RUN apt-get update \
    && apt-get -y install --no-install-recommends apt-utils dialog 2>&1 \
    #
    # Verify git, process tools, lsb-release (common in install instructions for CLIs) installed
    && apt-get -y install git openssh-client less iproute2 procps lsb-release \
    #
    # Install system dependencies for Playwright and browser testing
    && apt-get -y install \
        libnss3-dev \
        libatk-bridge2.0-dev \
        libdrm-dev \
        libxkbcommon-dev \
        libgtk-3-dev \
        libgbm-dev \
        libasound2-dev \
        libxrandr2 \
        libxss1 \
        libgconf-2-4 \
        libxtst6 \
        libxrandr2 \
        libasound2 \
        libpangocairo-1.0-0 \
        libatk1.0-0 \
        libcairo-gobject2 \
        libgtk-3-0 \
        libgdk-pixbuf2.0-0 \
        libxcomposite1 \
        libxcursor1 \
        libxdamage1 \
        libxi6 \
        libxtst6 \
        libnss3 \
        libcups2 \
        libxrandr2 \
        libasound2 \
        libatk1.0-0 \
        libdrm2 \
        libgtk-3-0 \
        libgbm1 \
        xvfb \
    #
    # Install additional system dependencies from shell.nix
    && apt-get -y install \
        libicu-dev \
        libxml2-dev \
        sqlite3 \
        libsqlite3-dev \
        libxslt1-dev \
        liblcms2-dev \
        libevent-dev \
        libopus-dev \
        libwebp-dev \
        libenchant-2-2 \
        libtasn1-6-dev \
        libhyphen-dev \
        libpcre2-dev \
        libpsl-dev \
        libnghttp2-dev \
        libgudev-1.0-dev \
        libffi-dev \
        libevdev-dev \
        libgnutls28-dev \
        libx264-dev \
    #
    # Install Python for node-gyp
    && apt-get -y install python3 python3-pip make g++ \
    #
    # Clean up
    && apt-get autoremove -y \
    && apt-get clean -y \
    && rm -rf /var/lib/apt/lists/*

# Switch back to dialog for any ad-hoc use of apt-get
ENV DEBIAN_FRONTEND=dialog

# Install global npm packages
RUN npm install -g \
    serve \
    @playwright/test \
    eslint \
    webpack \
    webpack-cli

# Create a directory for Playwright browsers
RUN mkdir -p /ms-playwright

# Set up the workspace
WORKDIR /workspaces

# Install Playwright browsers (this will be done in postCreateCommand but we prepare the environment)
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright

# Create non-root user
ARG USERNAME=node
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# Configure the node user
RUN groupmod --gid $USER_GID $USERNAME \
    && usermod --uid $USER_UID --gid $USER_GID $USERNAME \
    && chown -R $USER_UID:$USER_GID /home/<USER>
    && chown -R $USER_UID:$USER_GID /ms-playwright

# Switch to non-root user
USER $USERNAME

# Install Playwright browsers for the node user
RUN npx playwright install --with-deps

# Set the default shell to bash rather than sh
ENV SHELL /bin/bash
