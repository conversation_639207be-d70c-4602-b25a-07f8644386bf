# WebOTR Development Container

This directory contains the configuration for a VS Code development container that provides a complete development environment for the WebOTR project.

## What's Included

The development container includes:

- **Node.js 20** - Latest LTS version for JavaScript development
- **System Dependencies** - All required libraries for Playwright browser testing
- **Development Tools**:
  - ESLint for code linting
  - Webpack for bundling
  - Jest for unit testing
  - Playwright for end-to-end testing
  - Serve for static file serving
- **VS Code Extensions**:
  - ESLint integration
  - Playwright test runner
  - JavaScript debugging tools
  - Path IntelliSense
  - Auto rename tag
  - Prettier code formatting
- **PostgreSQL Database** - For any future database needs

## Getting Started

### Prerequisites

- [VS Code](https://code.visualstudio.com/)
- [Docker Desktop](https://www.docker.com/products/docker-desktop)
- [Dev Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)

### Using the Development Container

1. **Open the project in VS Code**
2. **Open Command Palette** (`Ctrl+Shift+P` / `Cmd+Shift+P`)
3. **Run "Dev Containers: Reopen in Container"**
4. **Wait for the container to build** (first time will take several minutes)

The container will automatically:
- Install all npm dependencies for both the main project and test-chat-sim
- Set up Playwright browsers
- Configure the development environment

### Available Ports

The following ports are automatically forwarded:

- **3000** - Main development server
- **3003** - Test chat simulator
- **8080** - Webpack dev server
- **9323** - Playwright UI mode
- **5432** - PostgreSQL database (if needed)

### Development Workflow

Once the container is running, you can:

```bash
# Start the main development server
npm start

# Run tests
npm test
npm run test:integration
npm run test:e2e

# Start the test chat simulator
cd test-chat-sim
npm start

# Run Playwright tests in UI mode
npx playwright test --ui --host=0.0.0.0 --port=9323
```

### Browser Extension Development

The container is configured for browser extension development:

1. Run `npm run build` to create the extension bundle
2. Load the `dist/` directory as an unpacked extension in your browser
3. Use `npm run dev` for development with file watching

### Troubleshooting

**Container won't start:**
- Ensure Docker Desktop is running
- Try rebuilding the container: "Dev Containers: Rebuild Container"

**Playwright tests fail:**
- The container includes all necessary browser dependencies
- Browsers are installed automatically during container creation

**Port conflicts:**
- Check if any of the forwarded ports are already in use locally
- Modify the port forwarding in `devcontainer.json` if needed

**Permission issues:**
- The container runs as the `node` user (UID 1000)
- File permissions should be preserved between host and container

### Customization

You can customize the development environment by modifying:

- `devcontainer.json` - VS Code settings, extensions, and port forwarding
- `Dockerfile` - System packages and global npm packages
- `docker-compose.yml` - Additional services (database, etc.)

### Performance Tips

- The container uses cached volume mounts for better performance
- Node modules are installed inside the container to avoid cross-platform issues
- Playwright browsers are pre-installed to speed up test execution

## Architecture

The development container uses Docker Compose to provide:

1. **Main App Container** - Node.js development environment
2. **PostgreSQL Container** - Database for future features
3. **Shared Network** - Allows services to communicate

This setup provides isolation while maintaining the ability to run complex multi-service applications.

## Files Overview

- `devcontainer.json` - Main configuration file for VS Code Dev Containers
- `Dockerfile` - Custom container image with all dependencies
- `docker-compose.yml` - Multi-service setup with database
- `setup.sh` - Automated setup script for dependencies and initial build
- `.vscode/` - VS Code specific configurations:
  - `launch.json` - Debug configurations for Jest, Webpack, etc.
  - `tasks.json` - Common development tasks
  - `settings.json` - Project-specific VS Code settings

## Advanced Usage

### Debugging

The container includes several debug configurations:
- **Debug Jest Tests** - Debug unit tests with breakpoints
- **Debug Current Jest Test** - Debug the currently open test file
- **Debug Webpack Build** - Debug the build process
- **Debug Setup Script** - Debug the project setup

### Testing

Multiple testing environments are supported:
- Unit tests with Jest
- Integration tests with custom configuration
- End-to-end tests with Playwright
- Browser extension testing

### Browser Extension Development

The container is optimized for browser extension development:
1. Webpack builds the extension bundle
2. The `dist/` folder can be loaded as an unpacked extension
3. Development mode provides file watching and hot reloading

### Multi-Project Support

The container supports both the main WebOTR project and the test-chat-sim React application:
- Dependencies are installed for both projects
- Separate npm scripts and configurations
- Port forwarding for both development servers
