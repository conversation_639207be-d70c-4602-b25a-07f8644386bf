{
  "name": "WebOTR Development",
  "dockerComposeFile": "docker-compose.yml",
  "service": "app",
  "workspaceFolder": "/workspace",
  
  // Features to add to the dev container
  "features": {
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {}
  },

  // Configure tool-specific properties
  "customizations": {
    "vscode": {
      "extensions": [
        "ms-vscode.vscode-eslint",
        "ms-vscode.vscode-json",
        "bradlc.vscode-tailwindcss",
        "ms-playwright.playwright",
        "ms-vscode.test-adapter-converter",
        "hbenl.vscode-test-explorer",
        "ms-vscode.js-debug",
        "formulahendry.auto-rename-tag",
        "christian-kohler.path-intellisense",
        "ms-vscode.vscode-typescript-next",
        "esbenp.prettier-vscode"
      ],
      "settings": {
        "terminal.integrated.defaultProfile.linux": "bash",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
          "source.fixAll.eslint": true
        },
        "eslint.workingDirectories": [
          ".",
          "./test-chat-sim"
        ],
        "javascript.preferences.includePackageJsonAutoImports": "auto",
        "typescript.preferences.includePackageJsonAutoImports": "auto"
      }
    }
  },

  // Use 'forwardPorts' to make a list of ports inside the container available locally
  "forwardPorts": [3000, 3003, 3004, 8080, 9323],
  "portsAttributes": {
    "3000": {
      "label": "Main Dev Server",
      "onAutoForward": "notify"
    },
    "3003": {
      "label": "Test Chat Simulator (Build)",
      "onAutoForward": "notify"
    },
    "3004": {
      "label": "Test Chat Simulator (Dev)",
      "onAutoForward": "notify"
    },
    "8080": {
      "label": "Webpack Dev Server",
      "onAutoForward": "notify"
    },
    "9323": {
      "label": "Playwright UI",
      "onAutoForward": "notify"
    }
  },

  // Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root
  "remoteUser": "node",

  // Use 'postCreateCommand' to run commands after the container is created
  "postCreateCommand": "bash .devcontainer/setup.sh",

  // Use 'postStartCommand' to run commands after the container starts
  "postStartCommand": "echo 'WebOTR development environment ready!'",

  // Configure container environment
  "containerEnv": {
    "NODE_ENV": "development",
    "PLAYWRIGHT_BROWSERS_PATH": "/ms-playwright",
    "PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD": "0"
  },

  // Mount the workspace and preserve file permissions
  "mounts": [
    "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=cached"
  ]
}
