version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ../..:/workspaces:cached
<<<<<<< HEAD
      - node_modules:/workspaces/webOTR/node_modules
      - test_node_modules:/workspaces/webOTR/test-chat-sim/node_modules
    working_dir: /workspaces/webOTR

    # Overrides default command so things don't shut down after the process ends.
    command: sleep infinity

    # Forward ports for development
    ports:
      - "3000:3000"
      - "3003:3003"
      - "8080:8080"
      - "9323:9323"

=======
    working_dir: /workspaces/webOTR
    
    # Overrides default command so things don't shut down after the process ends.
    command: sleep infinity
    
    # Runs app on the same network as the database container, allows "forwardPorts" in devcontainer.json function.
    network_mode: service:db
    
    # Use "forwardPorts" in **devcontainer.json** to forward an app port locally.
    # (Adding the "ports" property to this file will not forward from a Codespace.)
    
>>>>>>> 69817ad (Complete Branch Merge PRD implementation with full development infrastructure)
    environment:
      - NODE_ENV=development
      - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
      - PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0
<<<<<<< HEAD
      - CI=true

    # Add capabilities for browser testing
    cap_add:
      - SYS_ADMIN

    # Security options for browser testing
    security_opt:
      - seccomp:unconfined

volumes:
  node_modules:
  test_node_modules:
=======

  db:
    image: postgres:15-alpine
    restart: unless-stopped
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_USER: postgres
      POSTGRES_DB: webotr_dev
    
    # Add "forwardPorts": ["5432"] to **devcontainer.json** to forward PostgreSQL locally.
    # (Adding the "ports" property to this file will not forward from a Codespace.)

volumes:
  postgres-data:
>>>>>>> 69817ad (Complete Branch Merge PRD implementation with full development infrastructure)
