version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ../..:/workspaces:cached
      - node_modules:/workspaces/webOTR/node_modules
      - test_node_modules:/workspaces/webOTR/test-chat-sim/node_modules
    working_dir: /workspaces/webOTR

    # Overrides default command so things don't shut down after the process ends.
    command: sleep infinity

    # Forward ports for development
    ports:
      - "3000:3000"
      - "3003:3003"
      - "8080:8080"
      - "9323:9323"

    environment:
      - NODE_ENV=development
      - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
      - PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0
      - CI=true

    # Add capabilities for browser testing
    cap_add:
      - SYS_ADMIN

    # Security options for browser testing
    security_opt:
      - seccomp:unconfined

volumes:
  node_modules:
  test_node_modules:
