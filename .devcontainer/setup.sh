#!/bin/bash

# WebOTR Development Container Setup Script
# This script helps with common development tasks

set -e

echo "🚀 WebOTR Development Container Setup"
echo "======================================"

# Function to print colored output
print_status() {
    echo -e "\033[1;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Install main project dependencies
print_status "Installing main project dependencies..."
npm install
print_success "Main project dependencies installed"

# Install test-chat-sim dependencies
if [ -d "test-chat-sim" ]; then
    print_status "Installing test-chat-sim dependencies..."
    cd test-chat-sim
    npm install
    cd ..
    print_success "Test-chat-sim dependencies installed"
fi

# Install Playwright browsers if not already installed
print_status "Ensuring Playwright browsers are installed..."
npx playwright install
print_success "Playwright browsers ready"

# Run initial setup script if it exists
if [ -f "scripts/setup.js" ]; then
    print_status "Running project setup script..."
    node scripts/setup.js
    print_success "Project setup completed"
fi

# Create initial build
print_status "Creating initial build..."
npm run build
print_success "Initial build completed"

# Run tests to verify everything is working
print_status "Running tests to verify setup..."
if npm test -- --passWithNoTests; then
    print_success "Tests passed - setup verified"
else
    print_error "Some tests failed - please check the output above"
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Available commands:"
echo "  npm start          - Start development server"
echo "  npm run dev        - Start webpack in watch mode"
echo "  npm test           - Run unit tests"
echo "  npm run test:e2e   - Run end-to-end tests"
echo "  npm run lint       - Run ESLint"
echo ""
echo "For the test chat simulator:"
echo "  cd test-chat-sim && npm start"
echo ""
echo "Happy coding! 🚀"
