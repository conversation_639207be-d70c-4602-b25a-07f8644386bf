# OTR Status Indicator Design

## Overview

The OTR Status Indicator is a visual component that displays the current security state of the conversation. It needs to clearly communicate multiple states: whether the conversation is encrypted, whether the partner has been verified, and any warning states.

## Visual Design

### Main Status Indicators

```
┌──────────────────────────────────────┐
│                                      │
│  ● Unencrypted                       │
│                                      │
└──────────────────────────────────────┘

┌──────────────────────────────────────┐
│                                      │
│  🔒 Encrypted                        │
│                                      │
└──────────────────────────────────────┘

┌──────────────────────────────────────┐
│                                      │
│  🔒✓ Encrypted & Verified            │
│                                      │
└──────────────────────────────────────┘

┌──────────────────────────────────────┐
│                                      │
│  ⚠️ Warning: Verification Failed     │
│                                      │
└──────────────────────────────────────┘
```

### Expanded States (on hover/click)

```
┌─────────────────────────────────────────────────┐
│                                                 │
│  ● Unencrypted                                  │
│                                                 │
│  Your messages are not encrypted.               │
│  Click to enable OTR encryption.                │
│                                                 │
└─────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────┐
│                                                 │
│  🔒 Encrypted                                   │
│                                                 │
│  Your messages are encrypted.                   │
│  Partner not verified. Click to verify.         │
│                                                 │
└─────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────┐
│                                                 │
│  🔒✓ Encrypted & Verified                       │
│                                                 │
│  Your messages are encrypted.                   │
│  Partner's identity verified on [date].         │
│                                                 │
│  [Options ▼]                                    │
│                                                 │
└─────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────┐
│                                                 │
│  ⚠️ Warning: Verification Failed                │
│                                                 │
│  Partner could not be verified.                 │
│  Messages are still encrypted.                  │
│                                                 │
│  [Try Again]  [Ignore]                          │
│                                                 │
└─────────────────────────────────────────────────┘
```

### Options Menu (Verified State)

```
┌─────────────────────────────────────────────────┐
│                                                 │
│  🔒✓ Encrypted & Verified                       │
│                                                 │
│  Your messages are encrypted.                   │
│  Partner's identity verified on [date].         │
│                                                 │
│  [Options ▼]                                    │
│  ┌─────────────────────────────────────────┐    │
│  │ Re-verify Partner                       │    │
│  │ View Fingerprint                        │    │
│  │ End Encrypted Session                   │    │
│  └─────────────────────────────────────────┘    │
│                                                 │
└─────────────────────────────────────────────────┘
```

## Compact Mode for Chat Interfaces

```
[No encryption]: ●
[Encrypted]: 🔒
[Encrypted & Verified]: 🔒✓
[Warning]: ⚠️
```

## Color Coding

- **Unencrypted**: Gray
- **Encrypted**: Blue
- **Verified**: Green
- **Warning**: Amber/Yellow
- **Error**: Red

## Animations

- Fade transition between states (200ms)
- Subtle pulse animation when state changes
- Expand/collapse animation for detail view (150ms)

## States and Behavior

### State Transitions

1. **Initial State**: Unencrypted (●)
2. **After OTR Enabled**: Encrypted, Unverified (🔒)
3. **After Verification Success**: Encrypted & Verified (🔒✓)
4. **After Verification Failure**: Warning (⚠️)
5. **After Session End**: Unencrypted (●)

### Interactive Behavior

- Click/tap on indicator opens expanded state
- Click outside closes expanded state
- Click "Options" in verified state opens menu
- Hover shows tooltip with brief state description

## Accessibility Features

- State changes announced to screen readers
- Color is not sole indicator of state (uses icons and text)
- All interactive elements are keyboard navigable
- ARIA attributes for all interactive elements
- High contrast mode support

## Component Specifications

### Properties

- `status`: String - Current encryption status
  - Values: `'disabled'`, `'encrypted'`, `'verified'`, `'warning'`, `'error'`
- `detail`: String - Additional status details
- `verificationDate`: Date - When verification occurred (if verified)
- `expanded`: Boolean - Whether expanded view is shown
- `compact`: Boolean - Whether to show compact version

### Methods

- `update(status, detail)`: Update the status indicator
- `expand()`: Show expanded view
- `collapse()`: Hide expanded view
- `toggleOptions()`: Show/hide options menu
- `handleVerificationAction()`: Trigger verification dialog

### Events

- `onStatusChange`: Fired when status changes
- `onExpand`: Fired when expanded view opens
- `onCollapse`: Fired when expanded view closes
- `onVerificationRequest`: Fired when user requests verification
- `onSessionEnd`: Fired when user ends encrypted session

## Implementation Notes

1. Use SVG icons for better scaling and accessibility
2. Implement smooth transitions between states
3. Ensure tooltip text is clear and actionable
4. Store verification state in secure storage
5. Add subtle animation to draw attention to state changes
6. Ensure all text is localizable
7. Compact mode should automatically activate on small screens

## Related Components

- `VerificationDialog`: Opened when user initiates verification
- `TooltipComponent`: Used for displaying contextual information
- `MenuComponent`: Used for options menu
- `IconLibrary`: Contains all icon SVGs

## Test Cases

1. Verify correct initial state
2. Test all state transitions
3. Verify expanded view functionality
4. Test accessibility with screen readers
5. Verify keyboard navigation
6. Test compact mode rendering
7. Verify all interactive elements work correctly
8. Test localized content rendering 