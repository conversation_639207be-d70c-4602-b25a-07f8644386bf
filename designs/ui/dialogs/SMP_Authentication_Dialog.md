# SMP Authentication Dialog Design

## Overview

The SMP Authentication Dialog provides an intuitive interface for users to verify their conversation partner's identity using the Socialist Millionaire Protocol.

## Visual Design

```
┌─────────────────────────────────────────────────────┐
│ ⚠️ Verify Your Conversation Partner                 ✕ │
├─────────────────────────────────────────────────────┤
│                                                     │
│  To ensure you're chatting with the right person,   │
│  verify their identity using one of these methods:  │
│                                                     │
│  ┌─────────┐  ┌───────────────┐  ┌───────────────┐  │
│  │ 🔐      │  │ ❓            │  │ 👁️            │  │
│  │ QR Code │  │ Secret Q&A    │  │ Fingerprint   │  │
│  └─────────┘  └───────────────┘  └───────────────┘  │
│                                                     │
│  ▶ Selected Method: Secret Question & Answer        │
│  ────────────────────────────────────────────────   │
│                                                     │
│  Ask a question only the real person would know:    │
│  ┌─────────────────────────────────────────────┐   │
│  │ What was the name of our project in 2019?   │   │
│  └─────────────────────────────────────────────┘   │
│                                                     │
│  Enter the expected answer:                         │
│  ┌─────────────────────────────────────────────┐   │
│  │ BlueSkyInitiative                           │   │
│  └─────────────────────────────────────────────┘   │
│                                                     │
│                                                     │
│  ┌───────────┐               ┌───────────────────┐  │
│  │  Cancel   │               │  Verify Partner   │  │
│  └───────────┘               └───────────────────┘  │
│                                                     │
└─────────────────────────────────────────────────────┘
```

## Alternative Views

### QR Code Method
```
┌─────────────────────────────────────────────────────┐
│ ⚠️ Verify Your Conversation Partner                 ✕ │
├─────────────────────────────────────────────────────┤
│                                                     │
│  ▶ Selected Method: QR Code                         │
│  ────────────────────────────────────────────────   │
│                                                     │
│  Show this QR code to your partner in person or     │
│  via video call. They should show you theirs too.   │
│                                                     │
│  ┌───────────────────────────────┐                  │
│  │                               │                  │
│  │                               │                  │
│  │          [QR CODE]            │                  │
│  │                               │                  │
│  │                               │                  │
│  └───────────────────────────────┘                  │
│                                                     │
│  Compare the codes carefully. They should match     │
│  exactly if you're talking to the right person.     │
│                                                     │
│  ┌────────────────┐        ┌────────────────┐       │
│  │ Codes Mismatch │        │ Codes Match    │       │
│  └────────────────┘        └────────────────┘       │
│                                                     │
└─────────────────────────────────────────────────────┘
```

### Fingerprint Method
```
┌─────────────────────────────────────────────────────┐
│ ⚠️ Verify Your Conversation Partner                 ✕ │
├─────────────────────────────────────────────────────┤
│                                                     │
│  ▶ Selected Method: Fingerprint Verification        │
│  ────────────────────────────────────────────────   │
│                                                     │
│  Compare this fingerprint with your partner using   │
│  a different communication channel (phone, email).  │
│                                                     │
│  Your fingerprint:                                  │
│  ┌─────────────────────────────────────────────┐   │
│  │ 5A:F1:2B:88:C5:E4:7D:B3:9F:A2:0E:D8:63:C1:F9 │   │
│  └─────────────────────────────────────────────┘   │
│                                                     │
│  Partner's fingerprint:                             │
│  ┌─────────────────────────────────────────────┐   │
│  │ 3C:D2:72:9A:E1:4F:8B:06:7E:C9:5D:38:A0:B4:12 │   │
│  └─────────────────────────────────────────────┘   │
│                                                     │
│  ┌────────────────┐        ┌────────────────┐       │
│  │ Doesn't Match  │        │ Fingerprints   │       │
│  │                │        │ Match           │       │
│  └────────────────┘        └────────────────┘       │
│                                                     │
└─────────────────────────────────────────────────────┘
```

## States and Transitions

### Initial State
- Dialog displays with three verification method options
- No method is selected initially

### Question & Answer Method Selected
1. User enters question
2. User enters expected answer
3. User clicks "Verify Partner"
4. Dialog shows "Sending verification request..." loading state
5. Dialog updates based on partner's response

### Verification Outcomes

**Success State:**
```
┌─────────────────────────────────────────────────────┐
│ ✅ Verification Successful                          ✕ │
├─────────────────────────────────────────────────────┤
│                                                     │
│  Your partner has been successfully verified!       │
│                                                     │
│  This conversation is now authenticated and secure. │
│                                                     │
│  [Status indicator shows verified status]           │
│                                                     │
│                ┌───────────────┐                    │
│                │      Done     │                    │
│                └───────────────┘                    │
│                                                     │
└─────────────────────────────────────────────────────┘
```

**Failure State:**
```
┌─────────────────────────────────────────────────────┐
│ ❌ Verification Failed                              ✕ │
├─────────────────────────────────────────────────────┤
│                                                     │
│  The verification did not succeed.                  │
│                                                     │
│  This could happen if:                              │
│  • Your partner provided a different answer         │
│  • Someone might be interfering with your chat      │
│  • There was a technical problem                    │
│                                                     │
│  What would you like to do?                         │
│                                                     │
│  ┌───────────┐  ┌───────────────┐  ┌───────────┐    │
│  │  Cancel   │  │ Try Different │  │ Try Again │    │
│  │           │  │    Method     │  │           │    │
│  └───────────┘  └───────────────┘  └───────────┘    │
│                                                     │
└─────────────────────────────────────────────────────┘
```

## Accessibility Considerations

- All UI elements must have appropriate ARIA labels
- Dialog must be navigable via keyboard only
- Color is not the only indicator of state (uses icons and text)
- High contrast mode available
- Text size adjustable with browser settings
- Error messages are clear and actionable

## Component Specifications

### Properties

- `isOpen`: Boolean - Controls dialog visibility
- `onVerificationComplete`: Function - Callback for verification result
- `defaultMethod`: String - Initial verification method
- `partnerInfo`: Object - Information about conversation partner

### Methods

- `open()`: Open the dialog
- `close()`: Close the dialog
- `setMethod(method)`: Switch verification method
- `submitVerification()`: Submit verification request
- `handleVerificationResult(result)`: Process verification result

### Events

- `onMethodChange`: Triggered when user switches verification methods
- `onSubmit`: Triggered when user submits verification
- `onCancel`: Triggered when user cancels verification
- `onClose`: Triggered when dialog is closed

## Implementation Notes

1. Use SVG for QR code generation
2. Use secure input fields for sensitive information
3. Include help tooltips for each verification method
4. Add subtle animations for state transitions
5. Implement proper focus management for accessibility
6. Load verification methods dynamically to reduce initial load time
7. Store verification preferences securely

## Related Components

- `StatusIndicator`: Updates to show verification status
- `VerificationMethodSelector`: Component for selecting verification method
- `QRCodeGenerator`: Component for generating QR codes
- `FingerprintDisplay`: Component for displaying key fingerprints
- `QuestionAnswerForm`: Component for SMP Q&A interaction 