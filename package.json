{"name": "webotr", "version": "0.1.0", "description": "Off-The-Record messaging for web chat platforms", "type": "module", "main": "src/index.js", "scripts": {"start": "node scripts/setup.js && npm run dev", "dev": "webpack serve --config config/webpack.config.dev.js", "build": "webpack --config config/webpack.config.prod.js", "build:extension": "webpack --config config/webpack.config.extension.prod.js", "build:analyze": "webpack --config config/webpack.config.prod.js --env analyze", "build:stats": "webpack --config config/webpack.config.prod.js --json > stats.json", "verify": "node scripts/verify-start.js", "test": "jest --config config/jest.config.js", "test:watch": "jest --config config/jest.config.js --watch", "test:coverage": "jest --config config/jest.config.js --coverage", "test:unit": "jest --config config/jest.unit.config.js", "test:integration": "jest --config config/jest.integration.config.js", "test:e2e": "playwright test --config=config/playwright.config.js", "test:accessibility": "jest --config config/jest.config.js --testPathPattern=accessibility", "test:performance": "lhci autorun", "test:reference": "jest --config config/jest.reference.config.js", "test:reference:watch": "jest --config config/jest.reference.config.js --watch", "test:reference:coverage": "jest --config config/jest.reference.config.js --coverage", "test:libotr-compat": "jest --config config/jest.reference.config.js --testPathPattern=libotr-compat", "test:protocol-compliance": "jest --config config/jest.reference.config.js --testPathPattern=protocol-compliance", "test:security-validation": "jest --config config/jest.reference.config.js --testPathPattern=security", "test:all": "npm run test && npm run test:reference && npm run test:integration", "lint": "eslint src/**/*.js extension/**/*.js", "lint:fix": "eslint src/**/*.js extension/**/*.js --fix", "lint:css": "stylelint 'src/**/*.css'", "format": "prettier --write '{src,extension}/**/*.{js,jsx,css,md}'", "format:check": "prettier --check '{src,extension}/**/*.{js,jsx,css,md}'", "validate": "npm run lint && npm run format:check && npm run test", "serve": "serve -s dist -l 3000", "clean": "rimraf dist coverage .nyc_output", "security:csp": "node scripts/security/csp-validator.js", "security:audit": "npm audit --audit-level=moderate", "store:prepare": "node scripts/store-submission/prepare-stores.js", "store:validate": "npm run security:csp && npm run security:audit", "predeploy": "npm run validate && npm run build", "release:prepare": "npm run clean && npm run build:extension && npm run store:prepare && npm run store:validate", "test:e2e:playwright": "playwright test --config=config/playwright.config.js", "test:e2e:headed": "playwright test --config=config/playwright.config.js --headed", "test:e2e:debug": "playwright test --config=config/playwright.config.js --debug", "deploy:monitoring": "echo 'Configure monitoring setup'", "production:validate": "npm run validate && npm run test:e2e && npm run test:performance", "extension:test": "npm run test:e2e -- --project=chromium-extension", "extension:validate": "npm run security:csp && npm run extension:test", "extension:package": "npm run build:extension && npm run store:prepare", "extension:deploy": "npm run extension:validate && npm run extension:package", "audit": "npm audit --audit-level moderate", "docs:install": "cd docs/sphinx && pip install -r requirements.txt", "docs:build": "cd docs/sphinx && make html", "docs:serve": "cd docs/sphinx && make livehtml", "docs:clean": "cd docs/sphinx && make clean", "docs:linkcheck": "cd docs/sphinx && make linkcheck", "docs:github": "cd docs/sphinx && make github"}, "keywords": ["otr", "encryption", "privacy", "messaging", "teams", "discord", "slack"], "author": "", "license": "MIT", "devDependencies": {"@babel/core": "^7.22.5", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.22.5", "@lhci/cli": "^0.15.0", "@playwright/test": "^1.40.0", "@sentry/browser": "^7.0.0", "@sentry/webpack-plugin": "^1.21.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "archiver": "^5.3.1", "autoprefixer": "^10.4.16", "babel-jest": "^29.7.0", "babel-loader": "^9.1.2", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.1", "cssnano": "^6.0.1", "eslint": "^8.43.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.5.0", "jest-environment-jsdom": "^29.7.0", "jsdoc": "^4.0.2", "lighthouse": "^12.6.1", "mini-css-extract-plugin": "^2.7.6", "postcss": "^8.4.31", "postcss-loader": "^7.3.3", "prettier": "^3.0.3", "rimraf": "^6.0.1", "serve": "^14.2.1", "style-loader": "^3.3.3", "stylelint": "^15.11.0", "terser-webpack-plugin": "^5.3.9", "webpack": "^5.88.0", "webpack-bundle-analyzer": "^4.9.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.2.2"}, "dependencies": {"@masknet/stego-js": "^0.15.1", "core-js": "^3.33.2", "crypto-js": "^4.1.1", "jsbn": "^1.1.0", "node-fetch": "^3.3.2", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "sharp": "^0.34.2"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testTimeout": 10000, "moduleNameMapping": {"\\.(css|less|scss|sass)$": "identity-obj-proxy"}, "transform": {"^.+\\.(js|jsx)$": "babel-jest"}, "testMatch": ["<rootDir>/tests/**/*.test.js", "<rootDir>/src/**/*.test.js"], "collectCoverageFrom": ["src/**/*.js", "extension/**/*.js", "!src/**/*.test.js", "!**/node_modules/**"]}}